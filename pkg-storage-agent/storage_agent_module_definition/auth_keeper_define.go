package storage_agent_module_definition

import (
	"context"
)

type StorageAuthKeeper interface {
	<PERSON><PERSON>eeper(ctx context.Context, panic<PERSON><PERSON> chan<- error)

	// SetAgentToken 仅能在开始时执行一次. 之后不可修改.
	SetAgentToken(token string) // (atomic) e.g. seetatech666
	GetAgentToken() string      // (atomic)

	IsRegistered() bool  // (atomic)
	SetRegisterSucceed() // (atomic)

	CheckUserToken(userToken string) (int, string, bool) // sync Map

	// SetUserToken 每调用一次, token 有效期增加 30 min. userToken = "" 表示沿用之前的.
	SetUserToken(userToken string, uid int, subName string) // sync Map
}
