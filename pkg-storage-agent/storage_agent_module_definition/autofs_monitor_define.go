package storage_agent_module_definition

import (
	"context"
	"github.com/minio/minio-go/v7"
	"server/pkg-agent/messenger"
	constant "server/pkg-storage-agent/storage_agent_constant"
	serverConstant "server/pkg/constant"
)

type AutoFsMonitor interface {
	RunAutoFsMonitor(regionSign serverConstant.RegionSignType, ctx context.Context, panic<PERSON>han chan<- error, writer chan<- messenger.Message)
	AutoFsInit(params *serverConstant.AutoFsInitParams) error
	AutoFsSetQuota(params *serverConstant.AutoFsInitParams) error
	AutoFsExist(uid int) bool
	RemoveObject(bucketName, objectName string) (err error)
	RemoveDir(bucketName, dirName string) (err error)
	Mkdir(bucket, dirName string) (err error)
	DownloadFile(bucket, fileName string) (*minio.Object, error)
	UploadFilePart(bucketName string, params *constant.UploadServiceParams) (err error)
	ListDir(bucket, dirPath string) ([]constant.AutoFsObjInfo, error)
}
