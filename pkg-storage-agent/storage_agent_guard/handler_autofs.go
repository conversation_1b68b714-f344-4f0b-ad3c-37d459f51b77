package storage_agent_guard

import (
	"context"
	"encoding/json"
	"server/pkg-agent/messenger"
	serverConstant "server/pkg/constant"
	"server/pkg/logger"
)

func getAutoFSLogger(regionSign serverConstant.RegionSignType) (l *logger.Logger) {
	return logger.NewLogger("handle_autoFs_" + regionSign.String())
}
func (g *Guard) autoFsInitHandler(ctx context.Context, in messenger.Message, writer chan<- messenger.Message) {
	l := getAutoFSLogger(g.regionSign)
	l.Info("autoFsInitHandler() start. msg: %+v", in)

	var err error
	var param serverConstant.AutoFsInitParams
	var result serverConstant.AutoFsInitResult

	defer func() {
		if err != nil {
			result.IsSucceed = false
			result.Msg = err.Error()
			l.ErrorE(err, "autoFsInitHandler() failed. uid=%d, region=%s", param.UID, g.regionSign)
		}

		writer <- messenger.Message{
			MsgID:   in.MsgID,
			Type:    messenger.AutoFsInitResultType,
			Payload: result.String(),
		}

		l.Info("initFileStorageHandler() done. uid=%d, region=%s", param.UID, g.regionSign)
	}()

	err = json.Unmarshal([]byte(in.Payload), &param)
	if err != nil {
		l.WarnE(err, "initFileStorageHandler() Unmarshal RegionRegisterResult failed.")
		return
	}
	result.UID = param.UID
	result.RegionSign = g.regionSign

	err = g.autoFs.AutoFsInit(&param)
	if err != nil {
		l.ErrorE(err, "InitUserStorage failed")
		return
	}

	result.IsSucceed = true
}

func (g *Guard) autoFsSetQuotaHandler(ctx context.Context, in messenger.Message, writer chan<- messenger.Message) {
	l := getAutoFSLogger(g.regionSign)
	l.Info("autoFsSetQuotaHandler() start. msg: %+v", in)

	var err error
	var param serverConstant.AutoFsInitParams

	err = json.Unmarshal([]byte(in.Payload), &param)
	if err != nil {
		l.WarnE(err, "autoFsSetQuotaHandler() Unmarshal AutoFsInitParams failed.")
		return
	}

	err = g.autoFs.AutoFsSetQuota(&param)
	if err != nil {
		l.ErrorE(err, "autoFsSetQuotaHandler failed")
		return
	}

}
