package storage_agent_guard

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"runtime"
	"server/entrance/storage_agent"
	"server/pkg-agent/messenger"
	"server/pkg-storage-agent/storage_agent_libs"
	"server/pkg-storage-agent/storage_agent_middleware"
	"server/pkg-storage-agent/storage_agent_module_definition"
	serverConstant "server/pkg/constant"
	"server/pkg/global_kv/kv_libs"
	h "server/pkg/http"
	"server/pkg/libs/cert"
	"server/pkg/logger"
	"server/plugin/redis_plugin"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

/**
 * test cmd: ./ga storage -u http://**************:33300 -p 7000 -r huabei-02 -t seetatech666
 */

type Guard struct {
	regionSign   serverConstant.RegionSignType
	serverURL    string
	messenger    storage_agent_module_definition.Messenger
	handlers     map[messenger.MessageType]handler
	apiPort      int
	health       storage_agent_module_definition.StorageHealthMonitor
	auth         storage_agent_module_definition.StorageAuthKeeper
	nfs          storage_agent_module_definition.StorageNFSMonitor
	adfs   storage_agent_module_definition.StorageADFSMonitor
	autoFs storage_agent_module_definition.AutoFsMonitor
	mw     *storage_agent_middleware.AgentStorageMiddleware
	adfsPlugin   *redis_plugin.ADFSPlugin
	autoFsPlugin *redis_plugin.AutoFsPlugin
	spac   *SPAC
	fsType serverConstant.FsType
	needFrpc bool

	// map[int]float64, key: 由前端指定 file_uuid (timestamp), 仅存在内存中, 作为文件上传进度记录.
	uploadProgress sync.Map

	// map[int]int, key: 由前端指定 file_uuid (timestamp), 仅存在内存中, 作为文件上传断点记录. 注意是当前 [已完成] 的偏移量.
	uploadOffset sync.Map

	// map[string]string, key: file_token, val: {rel_path, uid}, 仅存在内存中, 作为文件下载 token.
	downloadTokenSet sync.Map

	// 存在内存中的 flag
	isFrpcStarted atomic.Value
	l *logger.Logger
}

func NewGuard(
	regionSign serverConstant.RegionSignType,
	serverURL string,
	apiPort int,
	messenger storage_agent_module_definition.Messenger,
	health storage_agent_module_definition.StorageHealthMonitor,
	auth storage_agent_module_definition.StorageAuthKeeper,
	nfs storage_agent_module_definition.StorageNFSMonitor,
	adfs storage_agent_module_definition.StorageADFSMonitor,
	adfsPlugin *redis_plugin.ADFSPlugin,
	autoFs storage_agent_module_definition.AutoFsMonitor,
	autoFsPlugin *redis_plugin.AutoFsPlugin,
	mw *storage_agent_middleware.AgentStorageMiddleware,
	fsType serverConstant.FsType,
	spac *SPAC,
	needFrpc bool,
) *Guard {
	return &Guard{
		regionSign:   regionSign,
		serverURL:    serverURL,
		handlers:     nil, // handlers 稍后再初始化
		apiPort:      apiPort,
		messenger:    messenger,
		health:       health,
		auth:         auth,
		nfs:          nfs,
		adfs:         adfs,
		adfsPlugin:   adfsPlugin,
		mw:           mw,
		autoFs: autoFs,
		autoFsPlugin: autoFsPlugin,
		fsType:   fsType,
		spac:   spac,
		needFrpc: needFrpc,
		l:        logger.NewLogger("guard"),
	}
}

func (g *Guard) Run(ctx context.Context, panicChan chan<- error) {
	l := logger.NewLogger("StorageGuard_" + g.regionSign.String())
	l.Info("ready to run storage guard...")
	defer func() {
		l.Info("storage guard quit...")
	}()

	reader, writer, err := g.messenger.StartHandler(ctx, panicChan)
	if err != nil {
		err = errors.Wrap(err, "failed to start messenger handler")
		l.E(err)
		panicChan <- err
		return
	}
	l.Info("start messenger success...")

	// self cron jobs
	if g.needFrpc {
		go g.cronFrpcCheck(ctx, panicChan, writer)
	}
	go func() {
		// fixme：据观察 storage agent 内存占用持续增长且不释放
		//  可见默认的GCPercent并无法解决当前程序的内存占用问题
		//  此处暂时直接调用GC触发回收，等找到更好的办法再去掉
		for {
			time.Sleep(time.Hour * 3)
			runtime.GC()
		}
	}()

	// 1. auth keeper cron job
	go g.auth.RunKeeper(ctx, panicChan)
	l.Info("* AuthKeeper is running.")

	// 2. monitors
	go g.health.RunHealthMonitor(ctx, panicChan, writer)
	l.Info("* HealthMonitor is running...")

	if g.fsType.IsAdfs() {
		go g.runCronJobSyncADFSUsage(ctx, panicChan, writer)
		l.Info("* runCronJobSyncADFSUsage is running...")
		go g.adfs.RunADFSMonitor(g.regionSign, ctx, panicChan, writer)
		l.Info("* RunADFSMonitor is running...")
	} else if g.fsType.IsAutoFs() {
		go g.autoFs.RunAutoFsMonitor(g.regionSign, ctx, panicChan, writer)
		l.Info("* autoFsMonitor is running...")
	} else {
		go g.nfs.RunNFSMonitor(g.regionSign, ctx, panicChan, writer)
		l.Info("* NFSMonitor is running...")
	}

	// 3. api server
	go g.runApiServer(ctx, panicChan)
	l.Info("* Agent api server is running...")

	// 4. handler, 阻塞
	g.runHandleReader(ctx, panicChan, reader, writer)
}

func (g *Guard) cronFrpcCheck(ctx context.Context, panicChan chan<- error, writer chan<- messenger.Message) {
	l := logger.NewLogger("StorageAgentGuard.CronJobs")
	l.Info("CronJobs are running...")

	for {
		if g.CheckFrpcStarted() {
			l.Info("The frpc has been started. CronJobs exited...")
			return
		}

		typ := messenger.StorageAgentFrpcRequest
		if g.fsType.IsAutoFs() {
			typ = messenger.AutoFsFrpcRequest
		} else if g.fsType.IsAdfs() {
			typ = messenger.ADFSStorageAgentFrpcRequest
		}

		// adfs agent sends frpc info req to server
		writer <- messenger.Message{
			MsgID:   time.Now().Format(time.RFC3339Nano),
			Type:    typ,
			Payload: g.regionSign.String(),
		}

		newTicker := time.NewTicker(time.Second * 10)
		select {
		case <-ctx.Done():
			return
		case <-newTicker.C:
			newTicker.Stop()
		}
	}
}

// runCronJobSyncADFSUsage 定时上报 ADFS 使用数据
func (g *Guard) runCronJobSyncADFSUsage(ctx context.Context, panicChan chan<- error, writer chan<- messenger.Message) {
	l := logger.NewLogger("StorageAgentGuard.runCronJobSyncADFSUsage").WithField("region", g.regionSign)
	g.runCronJobSyncADFSUsageDo(l)

	newTicker := time.NewTicker(time.Minute * 5)

	for {
		select {
		case <-ctx.Done():
			return
		case <-newTicker.C:
			g.runCronJobSyncADFSUsageDo(l)
		}
	}
}

func (g *Guard) runCronJobSyncADFSUsageDo(l *logger.Logger) {
	l.Info("runCronJobSyncADFSUsage are running...")
	// 获取所有用户
	uids, err := g.adfs.GetUids()
	if err != nil {
		l.WithError(err).Error("adfs.GetUids failed")
		return
	}

	data := make([]serverConstant.ADFSSyncUsageData, 0, len(uids))

	for _, uid := range uids {
		usageInfo, err := g.adfsPlugin.GetUserUsage(uid, time.Now())
		if err != nil {
			l.WithField("uid", uid).WithError(err).Error("adfsPlugin.GetUserUsage failed")
			continue
		}
		data = append(data, serverConstant.ADFSSyncUsageData{
			UID:          uid,
			MaxUsage:     usageInfo.MaxUsage,
			CurrentUsage: usageInfo.CurrentUsage,
			QuotaSize:    usageInfo.QuotaSize,
			QuotaInode:   usageInfo.QuotaInode,
			Size:         usageInfo.Size,
			Inode:        usageInfo.Inode,
		})
	}

	kvB, err := json.Marshal(data)
	if err != nil {
		l.WithError(err).Error("marshal usage json failed")
		return
	}
	value := map[string]interface{}{
		"data": string(kvB),
	}
	valueB, err := json.Marshal(value)
	if err != nil {
		l.WithError(err).Error("marshal value json failed")
		return
	}

	key := fmt.Sprintf(serverConstant.ADFSKVAllUserUsageInfoKey, g.regionSign)

	err = storage_agent_libs.CallApiServerSetKV(l, g.serverURL, kv_libs.GenerateAuthorization(), key, string(valueB))
	if err != nil {
		l.WithError(err).Error("callApiServerSetKV failed.")
		return
	}
	l.Info("callApiServerSetKV %+v, %+v", key, string(valueB))
}

func (g *Guard) runHandleReader(ctx context.Context, panicChan chan<- error, reader <-chan messenger.Message, writer chan<- messenger.Message) {
	l := logger.NewLogger("Guard.runHandleReader_" + g.regionSign.String())
	l.Info("* HandleReader is running.")

	for {
		select {
		case <-ctx.Done():
			l.Info("guard handler quit gracefully...")
			return
		case in, ok := <-reader:
			if !ok {
				panicChan <- errors.New("handle reader reader is not ok")
				return
			}

			g.handle(ctx, in, writer)
		}
	}
}

func (g *Guard) runApiServer(ctx context.Context, panicChan chan<- error) {
	l := logger.NewLogger("RunStorageAgentServer")
	l.Info("ready to init storage agent api server...")
	defer func() {
		l.Info("storage agent api server quit...")
	}()

	gin.SetMode(gin.DebugMode)

	r := gin.New()
	r.Use(h.GetGinPanicHandler(logger.NewLogger("StorageAgentGinHandler")))
	r.Use(storage_agent_libs.LoggerWithConfig([]string{}, []string{
		"/api/v1/net_disk/file/progress",
		"/api/v1/file_storage/file/progress",
		"/api/v1/file_storage/usage",
	}))
	r.Use(gin.Recovery())

	r.Use(cors.New(cors.Config{
		AllowMethods: []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"},
		//AllowHeaders:     []string{"Origin", "Content-Length", "Content-Type", "Authorization"},
		AllowHeaders:     []string{"*"},
		AllowCredentials: false,
		MaxAge:           12 * time.Hour,
		AllowAllOrigins:  true, // 前端跨域
		AllowWebSockets:  true,
	}))

	storage_agent.InitRouter(r, g, g.mw, g.fsType)

	srv := &http.Server{
		Addr:              fmt.Sprintf("0.0.0.0:%d", g.apiPort),
		Handler:           r,
		ReadTimeout:       24 * time.Hour,
		ReadHeaderTimeout: 24 * time.Hour,
		WriteTimeout:      24 * time.Hour,
	}

	go func() {
		for {
			select {
			case <-ctx.Done():
				ctxWithTimeout, cancel := context.WithTimeout(context.Background(), 10*time.Second)
				shutdownErr := srv.Shutdown(ctxWithTimeout)
				if shutdownErr != nil {
					l.ErrorE(shutdownErr, "shut down server with 10s ctx failed.")
				}
				cancel()
				return
			}
		}
	}()

	certDir := "/tmp"
	keyPath, pemPath, err := cert.CopyCertToPath(certDir)
	if err != nil {
		l.Error("Copy certs to path '%s' failed. err=%s", certDir, err.Error())
		panicChan <- err
		return
	}

	l.Info("Copy cert.key to: %s", keyPath)
	l.Info("Copy cert.pem to: %s", pemPath)

	l.Info("Listen and serve with TLS...")
	err = srv.ListenAndServeTLS(pemPath, keyPath)
	if err != nil {
		l.Info("ListenAndServe stopped. err=%s", err.Error())
		panicChan <- err
	}
	return
}
