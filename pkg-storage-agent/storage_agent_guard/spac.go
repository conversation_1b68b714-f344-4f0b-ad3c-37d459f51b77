package storage_agent_guard

import (
	"path/filepath"
	server_api "server/pkg/api"
	"server/pkg/logger"
	"server/plugin/redis_plugin"
	"strings"
)

type SPAC struct {
	spacPlugin *redis_plugin.SPACPlugin
	serverApi  *server_api.Api
	l          *logger.Logger
}

func NewSpac(spacPlugin *redis_plugin.SPACPlugin, api *server_api.Api) *SPAC {
	return &SPAC{
		spacPlugin: spacPlugin,
		serverApi:  api,
		l: logger.NewLogger("spac"),
	}
}

func (s *SPAC) PACGet(uid int, subName string) (string, error) {
	if uid == 0 || subName == "" {
		return "", nil
	}

	//return s.spacPlugin.PACGet(uid,subName)

	res, _, err := s.serverApi.GetSubUserSPAC(server_api.GetSubUserSPACReq{
		UID:     uid,
		SubName: subName,
	})
	if err != nil {
		return "", err
	}

	// 返回不带首尾的 "/" 的格式
	return strings.TrimPrefix(strings.TrimSuffix(res.Data, "/"), "/"), nil
}

func (s *SPAC) PACBuildPath(subDir, reqDir string) string {
	// 保证返回的是 / 或者 /sub/ 这种格式
	return "/" + strings.TrimPrefix(strings.TrimSuffix(filepath.Join(subDir, reqDir), "/")+"/", "/")
}

func (s *SPAC) PACGetAndBuildPath(uid int, subName, reqDir string) (string, string, error) {
	path, err := s.PACGet(uid, subName)
	if err != nil {
		return "", "", err
	}

	return path, s.PACBuildPath(path, reqDir), nil
}
