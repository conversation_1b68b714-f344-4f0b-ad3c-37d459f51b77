package storage_agent_guard

import (
	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"path/filepath"
	constant "server/pkg-storage-agent/storage_agent_constant"
	"server/pkg/businesserror"
	serverConstant "server/pkg/constant"
	h "server/pkg/http"
	"server/pkg/logger"
	"strings"
	"time"
)

func (g *Guard) AutoFsGetUploadProgressController(c *gin.Context) {
	var req constant.GetUploadProgressRequest
	var l = logger.NewLogger(secNamePrefix + "AutoFsGetUploadProgressController")
	if err := c.ShouldBindUri(&req); err != nil { // 解析 uri
		l.Info("GetUploadProgressController() Bind uri failed. err=%v", err)
		h.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	progress := g.getFileUploadProgress(req.FileUUID)

	// 当进度 100% 已经发给前端后, map 中稍后删除此记录
	if progress >= 1 {
		go func() {
			time.Sleep(10 * time.Second)
			g.deleteFileUploadProgress(req.FileUUID)
		}()
	}

	h.SendOK(c, progress)
}

// AutoFsUploadFileController 分片
func (g *Guard) AutoFsUploadFileController(c *gin.Context) {
	l := logger.NewLogger(secNamePrefix + "AutoFsUploadFileController")

	var req constant.UploadRequestParams
	if err := c.ShouldBind(&req); err != nil {
		l.Info("Bind UploadRequestParams failed. err=%+v", err)
		h.AbortAndSendErr(c, businesserror.ErrInvalidRequestParams)
		return
	}

	uid := h.GetUid(c)

	// 检查此用户是否有网盘
	autoFsSetting := g.autoFsPlugin.GetSetting(uid)
	if autoFsSetting == nil {
		h.SendError(c, businesserror.ErrStorageAgentUninitialized)
		return
	}

	subName := h.GetSubName(c)
	var err error
	_, req.DirPath, err = g.spac.PACGetAndBuildPath(uid, subName, req.DirPath)
	if err != nil {
		g.l.ErrorE(err, "Get subPath failed.uid:%d, subName:%s", uid, subName)
		h.SendError(c, businesserror.ErrInternalError)
		return
	}

	// 追加进 uid, FileHeader 等信息
	var uploadParams = constant.UploadServiceParams{
		UploadRequestParams: req,
		UID:                 uid,
		FileHeader:          nil,
	}

	// 从 form 中解析文件头
	file, err := c.FormFile("file")
	if err != nil {
		l.Info("Get FileHeader by c.FormFile('file') failed. err=%+v", err)
		h.AbortAndSendErr(c, businesserror.ErrInvalidRequestParams)
		return
	}

	uploadParams.FileHeader = file

	// 1. 检查 params
	if !uploadParams.Validate() {
		l.Info("uploadParams.Validate() failed. req=%+v", uploadParams)
		h.AbortAndSendErr(c, businesserror.ErrInvalidRequestParams)
		return
	}

	// 检查是否超过限额
	if uploadParams.ChunkOffset == 0 {
		usedSpace := g.autoFsPlugin.GetUsage(uploadParams.UID)
		if usedSpace+uploadParams.TotalSizeInByte > autoFsSetting.Capacity {
			h.SendHttpCode(c, h.HTTPQuotaLimit) // NOTE: 前端组件需要 abort
			return
		}
	}

	uploadParams.Filename = strings.TrimPrefix(filepath.Join(uploadParams.DirPath, uploadParams.Filename), "/")

	// 2. 分片上传当前用户的文件
	err = g.autoFs.UploadFilePart(autoFsSetting.Name, &uploadParams)
	if err != nil {
		l.WarnE(err, "UploadFile() to autofs failed. params: %+v", uploadParams)
		h.SendHttpCode(c, h.HTTPNfsDiskLimit) // NOTE: 前端组件需要 abort
		return
	}

	progress := float64(int64(uploadParams.ChunkOffset)*uploadParams.BatchSizeInByte) / float64(uploadParams.TotalSizeInByte)

	// 3. 如果最后一片上传成功, 置进度为 1, 且删掉偏移量的记录. 否则继续记录当前已完成的偏移量.
	if uploadParams.IsLast != 0 {
		progress = 1
		g.deleteFileUploadOffset(req.FileUUID)
	} else {
		g.setFileUploadOffset(req.FileUUID, uploadParams.ChunkOffset)
	}

	g.setFileUploadProgress(req.FileUUID, progress)
	h.SendOK(c, progress)
}

// AutoFsBeforeUploadFileController 断点续传, 目前需求中没有, 如果加入, 只需要前段访问此接口获取 chunk_offset
// NOTE: 上传完毕后 (进度 = 1) 会重置偏移量 offset = 0. 一次成功的上传后如果再次上传同名文件, 视为重新上传, offset = 0.
// 尚未考虑用户开多个网页传同一文件的情况, 此时前端会拿到错乱的偏移量.
func (g *Guard) AutoFsBeforeUploadFileController(c *gin.Context) {
	var req constant.GetUploadProgressRequest

	if err := c.ShouldBindUri(&req); err != nil { // 解析 uri
		h.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	offset := g.getFileUploadOffset(req.FileUUID)
	h.SendOK(c, offset)
}

func (g *Guard) AutoFsRemoveFileController(c *gin.Context) {
	var req constant.RemoveFileRequest
	if err := c.ShouldBind(&req); err != nil {
		h.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	uid := h.GetUid(c)

	autoFsSetting := g.autoFsPlugin.GetSetting(uid)
	if autoFsSetting == nil {
		h.SendError(c, businesserror.ErrStorageAgentUninitialized)
		return
	}
	subName := h.GetSubName(c)
	var err error
	_, req.DirPath, err = g.spac.PACGetAndBuildPath(uid, subName, req.DirPath)
	if err != nil {
		g.l.ErrorE(err, "AutoFsRemoveFileController Get subPath failed.uid:%d, subName:%s", uid, subName)
		h.SendError(c, businesserror.ErrInternalError)
		return
	}
	req.Filename = strings.TrimPrefix(filepath.Join(req.DirPath, req.Filename), "/")

	err = g.autoFs.RemoveObject(autoFsSetting.Name, req.Filename)
	if err != nil {
		h.SendError(c, err)
		return
	}

	g.l.Info("user:%d, subName:%s, fileName:%s, remove success...", uid, subName, req.Filename)

	h.SendOK(c, nil)
}

func (g *Guard) AutoFsGetFileTokenController(c *gin.Context) {
	var req constant.GetDownloadFileTokenRequest
	if err := c.ShouldBind(&req); err != nil {
		h.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	uid := h.GetUid(c)

	autoFsSetting := g.autoFsPlugin.GetSetting(uid)
	if autoFsSetting == nil {
		h.SendError(c, businesserror.ErrStorageAgentUninitialized)
		return
	}

	subName := h.GetSubName(c)
	var err error
	_, req.DirPath, err = g.spac.PACGetAndBuildPath(uid, subName, req.DirPath)
	if err != nil {
		g.l.ErrorE(err, "AutoFsGetFileTokenController Get subPath failed.uid:%d, subName:%s", uid, subName)
		h.SendError(c, businesserror.ErrInternalError)
		return
	}

	token := uuid.NewV4().String()
	g.setDownloadInfoByToken(token, constant.FileDownloadInfo{
		UID:              uid,
		DirPath:          req.DirPath,
		FileName:         req.Filename,
		AutoFsBucketName: autoFsSetting.Name,
	})
	h.SendOK(c, token)
}

func (g *Guard) AutoFsMakeDirController(c *gin.Context) {
	var req constant.MakeDirRequest
	if err := c.ShouldBind(&req); err != nil {
		h.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	uid := h.GetUid(c)
	autoFsSetting := g.autoFsPlugin.GetSetting(uid)
	if autoFsSetting == nil {
		h.SendError(c, businesserror.ErrStorageAgentUninitialized)
		return
	}

	subName := h.GetSubName(c)
	var err error
	_, req.DirPath, err = g.spac.PACGetAndBuildPath(uid, subName, req.DirPath)
	if err != nil {
		g.l.ErrorE(err, "AutoFsMakeDirController Get subPath failed.uid:%d, subName:%s", uid, subName)
		h.SendError(c, businesserror.ErrInternalError)
		return
	}
	req.DirName = strings.TrimPrefix(filepath.Join(req.DirPath, req.DirName), "/")

	err = g.autoFs.Mkdir(autoFsSetting.Name, req.DirName)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)
}

func (g *Guard) AutoFsRemoveDirController(c *gin.Context) {
	var req constant.RemoveDirRequest
	var l = logger.NewLogger("storageAgentApi.RemoveDirController")
	if err := c.ShouldBind(&req); err != nil {
		h.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	uid := h.GetUid(c)

	l = l.WithFields(logger.Fields{
		"user":     uid,
		"dir_path": req.DirPath,
		"dir_name": req.DirName,
	})

	autoFsSetting := g.autoFsPlugin.GetSetting(uid)
	if autoFsSetting == nil {
		h.SendError(c, businesserror.ErrStorageAgentUninitialized)
		return
	}

	subName := h.GetSubName(c)
	var err error
	_, req.DirPath, err = g.spac.PACGetAndBuildPath(uid, subName, req.DirPath)
	if err != nil {
		g.l.ErrorE(err, "Get subPath failed.uid:%d, subName:%s", uid, subName)
		h.SendError(c, businesserror.ErrInternalError)
		return
	}
	req.DirName = strings.TrimPrefix(filepath.Join(req.DirPath, req.DirName), "/")

	err = g.autoFs.RemoveDir(autoFsSetting.Name, req.DirName)
	if err != nil {
		l.WarnE(err, "User remove the dir failed.")
		h.SendError(c, err)
		return
	}

	l.Info("User removed the dir.")
	h.SendOK(c, nil)
}

/*
 * Common Download
 */

func (g *Guard) AutoFsDownloadFileController(c *gin.Context) {
	var req constant.DownloadFileRequest
	if err := c.ShouldBindUri(&req); err != nil {
		h.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	uid := h.GetUid(c)
	l := logger.NewLogger(secNamePrefix + "AutoFsDownloadFileController")

	downloadInfo, ok := g.getDownloadInfoByToken(req.Token)
	if !ok {
		l.Info("getDownloadInfoByToken failed! Token: %s", req.Token)
		h.SendError(c, businesserror.ErrFileGetFailed)
		return
	}

	if downloadInfo.UID != uid {
		l.Warn("Check uid in download token failed! token: '%s', user in token: %d, user in req: %d", req.Token, downloadInfo.UID, uid)
		h.SendError(c, businesserror.ErrFileGetFailed)
		return
	}

	l.Info("Prepare to download file: %+v", downloadInfo)

	downloadInfo.FileName = strings.TrimPrefix(filepath.Join(downloadInfo.DirPath, downloadInfo.FileName), "/")

	// TODO: get header and safe path
	objInfo, err := g.autoFs.DownloadFile(downloadInfo.AutoFsBucketName, downloadInfo.FileName)
	if err != nil {
		h.SendError(c, err)
		return
	}

	objStat, err := objInfo.Stat()
	if err != nil {
		h.SendError(c, businesserror.ErrInternalError)
		return
	}

	fileName := objStat.Key
	nameSlice := strings.Split(fileName, "/")
	fileName = nameSlice[len(nameSlice)-1]

	//c.Header("Content-Type", "application/octet-stream")
	//c.Header("Content-Disposition", "attachment; filename="+downloadInfo.FileName)
	//c.Header("content-Type", "application/octet-stream")
	c.DataFromReader(200, objStat.Size, objStat.ContentType, objInfo, map[string]string{
		"Content-Disposition": "attachment; filename=" + fileName,
	})
	return
}

func (g *Guard) AutoFsGetUsage(c *gin.Context) {
	uid := h.GetUid(c)

	setting := g.autoFsPlugin.GetSetting(uid)
	usageInByte := g.autoFsPlugin.GetUsage(uid)
	data := map[string]interface{}{
		"current_usage": usageInByte,
	}

	if setting != nil {
		data["total"] = setting.Capacity
	}

	h.SendOK(c, data)
}

func (g *Guard) AutoFsGetList(c *gin.Context) {
	var req serverConstant.AutoFsListDirRequest
	if err := c.ShouldBind(&req); err != nil {
		h.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	uid := h.GetUid(c)

	list, err := g.autoFsPlugin.AutoFsFileList(uid, req.Inode)
	if err != nil {
		h.SendError(c, err)
		return
	}
	h.SendOK(c, list)
}

func (g *Guard) AutoFsGetListV2(c *gin.Context) {
	var req serverConstant.AutoFsListDirRequestV2
	if err := c.ShouldBind(&req); err != nil {
		h.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	uid := h.GetUid(c)
	autoFsSetting := g.autoFsPlugin.GetSetting(uid)
	if autoFsSetting == nil {
		h.SendError(c, businesserror.ErrStorageAgentUninitialized)
		return
	}

	subName := h.GetSubName(c)
	var err error
	_, req.DirPath, err = g.spac.PACGetAndBuildPath(uid, subName, req.DirPath)
	if err != nil {
		g.l.ErrorE(err, "AutoFsGetListV2 Get subPath failed.uid:%d, subName:%s", uid, subName)
		h.SendError(c, businesserror.ErrInternalError)
		return
	}

	list, err := g.autoFs.ListDir(autoFsSetting.Name, req.DirPath)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, list)
}

func (g *Guard) AutoFsGetUserDetailController(c *gin.Context) {
	uid := h.GetUid(c)
	detail, err := g.autoFsPlugin.GetUserDetail(c.Request.Context(), uid)
	if err != nil {
		h.SendError(c, err)
		return
	}
	h.SendOK(c, FSUserDetailDTO{
		TotalSpace:  detail.Capacity,
		TotalInodes: detail.Inodes,
		UsedSpace:   detail.UsedSpace,
		UsedInodes:  detail.TotalInodes,
	})
}

func (g *Guard) AutoFsClearUploadingCacheController(c *gin.Context) {
	uid := h.GetUid(c)
	autoFsSetting := g.autoFsPlugin.GetSetting(uid)
	if autoFsSetting == nil {
		h.SendError(c, businesserror.ErrStorageAgentUninitialized)
		return
	}

	dir := ".sys/"
	err := g.autoFs.RemoveDir(autoFsSetting.Name, dir)
	if err != nil {
		g.l.WithError(err).Warn("failed to clear uploading cache dir %s", dir)
		h.SendError(c, err)
		return
	}
	g.l.Info("cleared uploading cache dir %s", dir)
	h.SendOK(c, nil)
}
