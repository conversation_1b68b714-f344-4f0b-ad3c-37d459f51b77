package storage_agent_nfs

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"os/exec"
	"regexp"
	"server/pkg-agent/messenger"
	constant "server/pkg-storage-agent/storage_agent_constant"
	storageAgentLibs "server/pkg-storage-agent/storage_agent_libs"
	"server/pkg/businesserror"
	serverConstant "server/pkg/constant"
	"server/pkg/libs"
	"server/pkg/logger"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/shopspring/decimal"

	"github.com/pkg/errors"
)

type Cmd interface {
	InitStorage(uid int, limit int64) error
	SetUserStorageQuota(uid int, limit int64) error
	ExistStorage(uid int) bool

	ListDir(uid int, dirPath string, limit int) (files []constant.NFSListFileInfo, err error)

	ExistFile(uid int, dirPath string, filename string) (exist bool)
	RemoveFile(uid int, dirPath string, filename string) error
	RenameFile(uid int, dirPath string, oldFilename, newFilename string) error

	MakeDir(uid int, dirPath string, dirName string) error
	RemoveDir(uid int, dirPath string, dirName string) error
	RenameDir(uid int, dirPath string, dirName, newDirname string) error

	DoMonitor(regionSign serverConstant.RegionSignType, writer chan<- messenger.Message)
	DiskAlerts(regionSign serverConstant.RegionSignType, writer chan<- messenger.Message)
	CheckNfsQuota(uid int, batchSize int64) (ok, checkAgain bool, err error) // 上传文件检测是否超过限额
	CheckNfsQuotaAgain(uid int, batchSize int64) (ok bool, err error)        // 备用方法
}

type DoCmd struct {
	l *logger.Logger
}

func NewDoCmd() *DoCmd {
	return &DoCmd{
		l: logger.NewLogger("NFS_cmd"),
	}
}

// InitStorage 初始化网盘 /data/#{uid}
func (c *DoCmd) InitStorage(uid int, limit int64) error {
	cmdStr := fmt.Sprintf(`mkdir -p %s/%d`, constant.StorageDir, uid)
	cmdStr += fmt.Sprintf(` && mkdir -p %s/%d`, constant.StorageDirDataMount, uid)
	cmdStr += fmt.Sprintf(` && chmod 777 %s/%d`, constant.StorageDir, uid)
	cmdStr += fmt.Sprintf(` && chattr -p %d %s/%d`, uid, constant.StorageDir, uid)
	cmdStr += fmt.Sprintf(` && chattr +P %s/%d`, constant.StorageDir, uid)
	cmdStr += fmt.Sprintf(` && setquota -P %d %d %d 0 0 %s`, uid, limit, limit, constant.StorageDir)

	cmd := exec.Command("/bin/bash", "-c", cmdStr)
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	// 执行命令
	if err := cmd.Run(); err != nil {
		err = errors.Wrap(err, stderr.String())
		c.l.WithError(err).Error("cmd.Run: init user storage failed.")
		return err
	}

	return nil
}

/*
mkdir -p /data/1410065407-nfs-warning && mkdir -p /data-mount/1410065407-nfs-warning && chmod 777 /data/1410065407-nfs-warning && chattr -p 1410065407 /data/1410065407-nfs-warning && chattr +P /data/1410065407-nfs-warning  && setquota -P 1410065407 1024 1024 0 0 /data
&& echo "由于您的网盘使用量超出免费容量，已停止挂载，请在控制台中清理数据或续费后**重启**实例再次挂载～" >> /data/1410065407-nfs-warning/README.md
*/

// SetUserStorageQuota 初始化网盘 /data/#{uid}
func (c *DoCmd) SetUserStorageQuota(uid int, limit int64) error {
	cmdStr := fmt.Sprintf(`setquota -P %d %d %d 0 0 %s`, uid, limit, limit, constant.StorageDir)

	cmd := exec.Command("/bin/bash", "-c", cmdStr)
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	// 执行命令
	if err := cmd.Run(); err != nil {
		err = errors.Wrap(err, stderr.String())
		c.l.WithError(err).Error("cmd.Run: set user storage quota failed.")
		return err
	}

	return nil
}

func (c *DoCmd) ExistStorage(uid int) (exist bool) {
	return c.existStorage(uid)
}

// existStorage 判断用户网盘是否存在 /data/#{uid}
func (c *DoCmd) existStorage(uid int) (exist bool) {
	dir := joinUserDir(uid)
	if !libs.ExistPath(dir) {
		return false
	}

	return true
}

func (c *DoCmd) ListDir(uid int, dirPath string, limit int) (filesInfo []constant.NFSListFileInfo, err error) {
	dir := joinAbsPath(uid, dirPath)

	// ls | head -n 300 防止用户文件过多
	files, err := libs.LsDirWithHead(dir, limit)
	if err != nil {
		c.l.WarnE(err, "LsDirWithHead failed. dir: '%s'", dir)
		return filesInfo, err
	}

	if len(files) == 0 {
		return filesInfo, nil
	}

	for _, info := range files {
		var createdAt time.Time
		var updatedAt time.Time
		var visitedAt time.Time

		if fileAttr, ok := info.Sys().(*syscall.Stat_t); ok {
			createdAt = time.Unix(fileAttr.Ctim.Sec, 0)
			updatedAt = time.Unix(fileAttr.Mtim.Sec, 0)
			visitedAt = time.Unix(fileAttr.Atim.Sec, 0)
		}

		filesInfo = append(filesInfo, constant.NFSListFileInfo{
			Filename:   info.Name(),
			SizeInByte: info.Size(),
			IsDir:      info.IsDir(),
			FileMode:   info.Mode().String(),
			CreatedAt:  createdAt,
			UpdatedAt:  updatedAt,
			VisitedAt:  visitedAt,
		})
	}

	return
}

func (c *DoCmd) ExistFile(uid int, dirPath string, filename string) (exist bool) {
	path := joinAbsPath(uid, dirPath, filename)
	exist = libs.ExistPath(path)
	return
}

// RemoveFile 允许重复删除
func (c *DoCmd) RemoveFile(uid int, dirPath string, filename string) error {
	path := joinAbsPath(uid, dirPath, filename)
	fmt.Printf("User_%d is removing path '%s'\n", uid, path)

	if !libs.ExistPath(path) {
		return nil
	}

	// 忽略此错误, 允许重复删除: no such file or directory
	err := os.Remove(path)
	if os.IsExist(err) {
		return err
	}
	return nil
}

func (c *DoCmd) RenameFile(uid int, dirPath string, oldFilename, newFilename string) error {
	oldPath := joinAbsPath(uid, dirPath, oldFilename)
	newPath := joinAbsPath(uid, dirPath, newFilename)

	if !libs.ExistPath(oldPath) {
		return nil
	}

	err := os.Rename(oldPath, newPath)
	if err != nil {
		return err
	}
	return nil
}

// MakeDir 单级目录
func (c *DoCmd) MakeDir(uid int, dirPath string, dirName string) error {
	path := joinAbsPath(uid, dirPath, dirName)
	if libs.ExistPath(path) {
		return nil
	}

	err := os.Mkdir(path, os.ModeDir) // TODO: FileMode
	if err != nil {
		return err
	}
	return nil
}

func (c *DoCmd) RemoveDir(uid int, dirPath string, dirName string) error {
	path := joinAbsPath(uid, dirPath, dirName)
	fmt.Printf("User_%d is removing path '%s'\n", uid, path)

	if !libs.ExistPath(path) {
		return nil
	}

	err := os.RemoveAll(path)
	if err != nil {
		return err
	}
	return nil
}

func (c *DoCmd) RenameDir(uid int, dirPath string, oldDirName, newDirname string) error {
	oldPath := joinAbsPath(uid, dirPath, oldDirName)
	newPath := joinAbsPath(uid, dirPath, newDirname)

	if !libs.ExistPath(oldPath) {
		return nil
	}

	err := os.Rename(oldPath, newPath)
	if err != nil {
		return err
	}
	return nil
}

// func --------------------------------------------------------------------

// TODO: 检测上传是否被截断
func uploadFileFromReaderWithProgress(
	file io.Reader,
	uploadPath string,
	chunkIndex int,
	batchSizeInByte,
	totalSizeInByte int64,
) (progress float64, currentSizeInByte int64, err error) {

	l := logger.NewLogger("uploadFileFromReaderWithProgress")

	if totalSizeInByte <= 0 {
		return 0, 0, nil // finished. do not cal progress
	}

	// do not mkdir in nfs
	// err = os.MkdirAll(filepath.Dir(uploadPath), os.ModePerm)
	// if err != nil {
	// 	return 0, err
	// }

	writer, err := os.OpenFile(uploadPath, os.O_CREATE|os.O_WRONLY, os.ModePerm)
	if err != nil {
		l.ErrorE(err, "Open upload path failed. path:'%s'", uploadPath)
		return 0, 0, errors.New("open upload path failed")
	}
	defer writer.Close()

	// seek
	offset := int64(chunkIndex) * batchSizeInByte
	_, err = writer.Seek(offset, io.SeekStart)
	if err != nil {
		return 0, 0, err
	}

	// copying...
	if _, err = io.Copy(writer, file); err != nil {
		l.WithError(err).Error("write upload chunk file failed. path:'%s'", uploadPath)
		return 0, 0, errors.New("write upload chunk file failed")
	}

	// cal progress
	fileStat, err := writer.Stat()
	if err != nil {
		l.WithField("err", err).Error("get writer file stat failed. path:'%s'", uploadPath)
		return 0, 0, errors.New("get writer file stat failed")
	}

	currentSizeInByte = fileStat.Size()
	progress = decimalWithPlaces(float64(currentSizeInByte)/float64(totalSizeInByte), 4)
	return
}

// 获取绝对路径的方法抽象为一处.
func joinAbsPath(uid int, dirPath string, filename ...string) string {
	path := libs.SafeFilePathJoin(joinUserDir(uid), dirPath)

	if len(filename) == 0 {
		return path
	}

	for i := range filename {
		path = libs.SafeFilePathJoin(path, filename[i])
	}
	return path
}

func joinUserDir(uid int) string {
	return libs.SafeFilePathJoin(constant.StorageDirDataMount, strconv.Itoa(uid))
}

// 四位小数
func decimalWithPlaces(number float64, places int) float64 {
	p32 := int32(places)
	f, _ := decimal.NewFromFloat(number).Round(p32).Float64()
	return f
}

// fixme: zt 与cmd中方法部分内容重复，简化
// 定期上报用户网盘用量
func (c *DoCmd) DoMonitor(regionSign serverConstant.RegionSignType, writer chan<- messenger.Message) {
	var err error
	var stdout bytes.Buffer
	var stderr bytes.Buffer
	cmdStr := fmt.Sprintf(`repquota -P %s`, constant.StorageDir)
	re := regexp.MustCompile(`#(\d*)\s*\S*\s*(\d*)\s*(\d*)\s*(\d*)`) // uid used soft hard
	msg := serverConstant.StorageSyncUsageModel{
		Sign: regionSign,
	}

	cmd := exec.Command("/bin/bash", "-c", cmdStr)
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		err = errors.Wrap(err, stderr.String())
		c.l.WithError(err).Error("cmd.Run: get quota info failed.")
		return
	}

	// 处理数据
	lines := strings.Split(stdout.String(), "\n")
	for _, v := range lines {
		if strings.HasPrefix(v, "#") {
			outStr := re.FindStringSubmatch(v)
			if len(outStr) != 5 {
				c.l.WithField("outStr", outStr).Warn("re find string failed.")
				continue
			}
			content := serverConstant.StorageSyncUsageUserModel{}
			content.UID, err = strconv.Atoi(outStr[1])
			if err != nil {
				return
			}
			content.Used, err = strconv.ParseInt(outStr[2], 10, 64)
			if err != nil {
				return
			}
			content.Quota, err = strconv.ParseInt(outStr[3], 10, 64)
			if err != nil {
				return
			}

			msg.Content = append(msg.Content, content)
		}
	}

	msgJson, err := json.Marshal(msg)
	if err != nil {
		c.l.WithError(err).Error("json marshal failed")
		return
	}

	writer <- messenger.Message{
		MsgID:   storageAgentLibs.GenerateMsgID(),
		Type:    messenger.RegionSyncUsageType,
		Payload: string(msgJson),
	}
}

// 定期上报nfs磁盘用量。 可以考虑直接将磁盘用量信息直接集成到heartbeat中
func (c *DoCmd) DiskAlerts(regionSign serverConstant.RegionSignType, writer chan<- messenger.Message) {
	var err error
	var stdout bytes.Buffer
	var stderr bytes.Buffer
	cmdStr := fmt.Sprintf(`df %s`, constant.StorageDir)
	msg := serverConstant.StorageSyncDiskAlertsModel{Sign: regionSign}
	cmd := exec.Command("/bin/bash", "-c", cmdStr)
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		err = errors.Wrap(err, stderr.String())
		c.l.WithError(err).Error("cmd.Run: get quota info failed.")
		return
	}

	// 处理数据
	lines := strings.Split(stdout.String(), "\n")
	if len(lines) != 3 { // 会在最后有一个空行
		c.l.Warn("exec df /data: should have two lines, header and '/dev/... ... ... ... ... /data'")
		return
	}
	if !strings.Contains(lines[1], constant.StorageDir) {
		c.l.Warn("exec df -hk /data: have no line contain '/data'")
		return
	}

	outStr := strings.Fields(lines[1])
	if len(outStr) != 5 {
		c.l.WithField("outStr", outStr).Warn("re find string failed.")
	}
	msg.UsedRate, err = strconv.Atoi(outStr[4])
	if err != nil {
		return
	}

	msg.TotalSize, err = strconv.ParseInt(outStr[1], 10, 64)
	if err != nil {
		return
	}
	msg.Used, err = strconv.ParseInt(outStr[2], 10, 64)
	if err != nil {
		return
	}
	msg.Available, err = strconv.ParseInt(outStr[3], 10, 64)
	if err != nil {
		return
	}

	msgJson, err := json.Marshal(msg)
	if err != nil {
		c.l.WithError(err).Error("json marshal failed")
		return
	}

	writer <- messenger.Message{
		MsgID:   storageAgentLibs.GenerateMsgID(),
		Type:    messenger.RegionSyncDiskAlertsType,
		Payload: string(msgJson),
	}
}

// CheckNfsQuota 校验用户网盘容量
func (c *DoCmd) CheckNfsQuota(uid int, batchSize int64) (ok, checkAgain bool, err error) {
	var stdout bytes.Buffer
	var stderr bytes.Buffer
	cmdStr := fmt.Sprintf("quota -P %d", uid)
	re := regexp.MustCompile(`\s*\S*\s*(\d*)\D\s*(\d*)\s*(\d*)\s*\d*\s*\d*\s*\d`) // used soft hard

	cmd := exec.Command("/bin/bash", "-c", cmdStr)
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	if err = cmd.Run(); err != nil {
		err = nil
		checkAgain = true
		return
	}

	// 处理数据
	lines := strings.Split(stdout.String(), "\n")
	if len(lines) < 4 { // 会在最后有一个空行
		err = businesserror.ErrRegionUserNotInit
		return
	}

	// re find
	outStr := re.FindStringSubmatch(lines[2])
	if len(outStr) != 4 {
		c.l.Error("re find used info failed")
		err = businesserror.ErrInternalError
		return
	}

	var (
		used  int64
		limit int64
	)
	used, err = strconv.ParseInt(outStr[1], 10, 64)
	used = used * 1024
	if err != nil {
		c.l.WithError(err).WithField("used str:", outStr[1]).Error("parse int failed.")
		err = businesserror.ErrInternalError
		return
	}

	limit, err = strconv.ParseInt(outStr[2], 10, 64)
	limit = limit * 1024
	if err != nil {
		c.l.WithError(err).WithField("limit str:", outStr[2]).Error("parse int failed.")
		err = businesserror.ErrInternalError
		return
	}

	if batchSize+used > limit {
		c.l.WithFields(map[string]interface{}{
			"upload":      batchSize,
			"used":        used,
			"upload+used": batchSize + used,
			"limit":       limit,
		}).Info("size-------------------------------")
		return false, false, nil
	}
	return true, false, nil
}

// CheckNfsQuotaAgain 校验用户网盘容量, 方法2
func (c *DoCmd) CheckNfsQuotaAgain(uid int, batchSize int64) (ok bool, err error) {
	var (
		stdout       bytes.Buffer
		stderr       bytes.Buffer
		prefix       = fmt.Sprintf("#%d", uid)
		cmdStr       = fmt.Sprintf(`repquota -P %s`, constant.StorageDir)
		re           = regexp.MustCompile(`#(\d*)\s*\S*\s*(\d*)\s*(\d*)\s*(\d*)`) // uid used soft hard
		findUserLine = ""
	)

	cmd := exec.Command("/bin/bash", "-c", cmdStr)
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	if err = cmd.Run(); err != nil {
		err = errors.Wrap(err, stderr.String())
		c.l.WithError(err).Error("cmd.Run: get quota info failed.")
		return
	}

	// 处理数据
	lines := strings.Split(stdout.String(), "\n")
	for _, v := range lines {
		if strings.HasPrefix(v, prefix) {
			findUserLine = v
			break
		}
	}

	if len(findUserLine) == 0 {
		err = businesserror.ErrRegionUserNotInit
		return
	}

	outStr := re.FindStringSubmatch(findUserLine)
	if len(outStr) != 5 {
		c.l.WithField("outStr", outStr).Warn("re find string failed.")
		err = businesserror.ErrInternalError
		return
	}

	content := serverConstant.StorageSyncUsageUserModel{}
	content.UID, err = strconv.Atoi(outStr[1])
	if err != nil {
		err = businesserror.ErrInternalError
		return
	}
	content.Used, err = strconv.ParseInt(outStr[2], 10, 64)
	if err != nil {
		err = businesserror.ErrInternalError
		return
	}
	content.Quota, err = strconv.ParseInt(outStr[3], 10, 64)
	if err != nil {
		err = businesserror.ErrInternalError
		return
	}

	if content.UID != uid {
		c.l.WithField("content.uid", content.UID).WithField("uid", uid).Error("re find uid != request uid")
		err = businesserror.ErrInternalError
		return
	}
	content.Used = content.Used * 1024
	content.Quota = content.Quota * 1024
	if batchSize+content.Used > content.Quota {
		c.l.WithFields(map[string]interface{}{
			"upload":                   batchSize,
			" content.Used":            content.Used,
			"batchSize + content.Used": batchSize + content.Used,
			"content.Quota":            content.Quota,
		}).Info("size-------------------------------")
		return false, nil
	}
	return true, nil
}
