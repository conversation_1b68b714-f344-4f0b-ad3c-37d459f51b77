package storage_agent_autofs

import (
	"github.com/minio/minio-go/v7"
	module_definition "server/pkg-storage-agent/storage_agent_module_definition"
	serverConstant "server/pkg/constant"
	"server/pkg/logger"
	"server/plugin/redis_plugin"
	"sync"
)

type AutoFSCmd struct {
	l *logger.Logger

	AccessKey      string
	SecretKey      string
	autofsAlertURL *string
}

func NewAutoFSCmd(AccessKey, SecretKey string, autofsAlertURL *string) *AutoFSCmd {
	return &AutoFSCmd{
		l:              logger.NewLogger("AutoFS_cmd"),
		AccessKey:      AccessKey,
		SecretKey:      SecretKey,
		autofsAlertURL: autofsAlertURL,
	}
}

const secNameMonitor = "autofs_"

type AutoFSMonitor struct {
	regionSign serverConstant.RegionSignType
	auth       module_definition.StorageAuthKeeper

	cmd *AutoFSCmd

	l *logger.Logger

	autoFsPlugin *redis_plugin.AutoFsPlugin
	serverURL    string

	accessKey string // blob存储redis集群
	secretKey string // juice_fs redis集群
	minIO     *minio.Core

	MultipartUploadID sync.Map
}

func NewAutoFsMonitor(
	regionSign serverConstant.RegionSignType,
	auth module_definition.StorageAuthKeeper,
	autoFsPlugin *redis_plugin.AutoFsPlugin,
	serverURL string,
	accessKey string,
	secretKey string,
	autofsAlertURL *string,
	core *minio.Core,
) *AutoFSMonitor {
	return &AutoFSMonitor{
		regionSign:   regionSign,
		auth:         auth,
		cmd:          NewAutoFSCmd(accessKey, secretKey, autofsAlertURL),
		l:            logger.NewLogger(secNameMonitor + regionSign.String()),
		autoFsPlugin: autoFsPlugin,
		serverURL:    serverURL,
		accessKey:    accessKey,
		secretKey:    secretKey,
		minIO:        core,
	}
}
