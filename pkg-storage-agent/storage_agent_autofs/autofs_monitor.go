package storage_agent_autofs

import (
	"context"
	"github.com/pkg/errors"
	"server/pkg-agent/messenger"
	"server/pkg/businesserror"
	serverConstant "server/pkg/constant"
	"time"
)

func (fs *AutoFSMonitor) RunAutoFsMonitor(regionSign serverConstant.RegionSignType, ctx context.Context, panic<PERSON><PERSON> chan<- error, writer chan<- messenger.Message) {
	fs.l.Info("AutoFsMonitor beginning....")
	// 毎5分钟上报一次所有用户的总用量
	t := time.NewTicker(time.Minute * 5)

	for {
		select {
		case <-ctx.Done():
			fs.l.Info("autoFs quit now....")
			return
		case <-t.C:
			usageInfo := serverConstant.AutoFsSyncUsage{
				RegionSign: fs.regionSign,
				UsageInfo:  fs.autoFsPlugin.GetAllUsageCurrent(),
			}

			fs.l.Info("GetUserUsage: autofs usage info. all in map: %+v", usageInfo.UsageInfo)
			writer <- messenger.Message{
				MsgID:   time.Now().Format(time.RFC3339Nano),
				Type:    messenger.AutoFsSyncUsageRequest,
				Payload: usageInfo.String(),
			}
		}
	}
}

func (fs *AutoFSMonitor) AutoFsInit(params *serverConstant.AutoFsInitParams) error {
	if params.UID == 0 {
		return errors.New("params error: uid is 0")
	}

	if fs.autoFsExist(params.UID) {
		return nil
	}

	err := fs.cmd.AutoFsInitCmd(params)
	if err != nil {
		return err
	}

	if params.ConcurrentLimit != 0 {
		err = fs.autoFsPlugin.SetConcurrentLimit(params.UID, params.ConcurrentLimit)
		if err != nil {
			fs.l.ErrorE(err, "SetConcurrentLimit failed, params:%+v", params)
		}
	}
	return nil
}

func (fs *AutoFSMonitor) AutoFsSetQuota(params *serverConstant.AutoFsInitParams) error {
	if params.UID == 0 {
		return errors.New("params error: uid is 0")
	}
	if params.Capacity == 0 || params.Inodes == 0 {
		return errors.New("params error: limit is 0")
	}

	if !fs.autoFsExist(params.UID) {
		return businesserror.ErrRegionUserNotInit
	}
	err := fs.cmd.AutoFsSetQuotaCmd(params)
	if err != nil {
		return err
	}

	if params.ConcurrentLimit != 0 {
		err = fs.autoFsPlugin.SetConcurrentLimit(params.UID, params.ConcurrentLimit)
		if err != nil {
			fs.l.ErrorE(err, "SetConcurrentLimit failed, params:%+v", params)
		}
	}
	return nil
}

func (fs *AutoFSMonitor) AutoFsExist(uid int) bool {
	return fs.autoFsExist(uid)
}

func (fs *AutoFSMonitor) autoFsExist(uid int) (exist bool) {
	return fs.autoFsPlugin.GetSetting(uid) != nil
}
