package storage_agent_autofs

import (
	"bytes"
	"fmt"
	"github.com/pkg/errors"
	"os/exec"
	serverConstant "server/pkg/constant"
	"strconv"
)

func (c *AutoFSCmd) AutoFsInitCmd(params *serverConstant.AutoFsInitParams) error {
	bucketName := strconv.Itoa(params.UID)
	if params.Prefix != "" {
		bucketName = fmt.Sprintf("%s%d", params.Prefix, params.UID)
	}
	cmdStr := fmt.Sprintf(
		"/bin/juicefs format --storage=autofs --bucket=%s --access-key=%s --secret-key=%s --capacity=%d --inodes=%d --trash-days=0",
		bucketName, c.<PERSON>, c.<PERSON>, params.Capacity, params.Inodes,
	)
	if c.autofsAlertURL != nil && *c.autofsAlertURL != "" {
		cmdStr += fmt.Sprintf(" --alert-url=%s", *c.autofsAlertURL)
	}
	cmdStr += fmt.Sprintf(" redis://%s/%d fs%s", c.<PERSON>, params.UID, bucketName)
	c.l.Info("format cmd: %s", cmdStr)

	cmd := exec.Command("/bin/bash", "-c", cmdStr)
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	// 执行命令
	if err := cmd.Run(); err != nil {
		err = errors.Wrap(err, stderr.String())
		c.l.ErrorE(err, "cmd.Run: init autofs failed. params:%+v, cmd: %s", params, cmdStr)
		return err
	}

	return nil
}

func (c *AutoFSCmd) AutoFsSetQuotaCmd(params *serverConstant.AutoFsInitParams) error {
	return c.AutoFsInitCmd(params)
}
