package model

import (
	"encoding/json"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/libs"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const TableNameSubUser = "sub_user"

type SubUser struct {
	ID        int            `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`

	UID                           int        `gorm:"column:uid;index;default 0" json:"uid"`
	SubName                       string     `gorm:"column:sub_name;type:varchar(255);index;default ''" json:"sub_name"`
	Password                      string     `gorm:"column:password;type:varchar(255);default ''" json:"-"`
	UserName                      string     `gorm:"column:user_name;type:varchar(255);default ''" json:"user_name"`
	LastLogin                     *time.Time `gorm:"column:last_login;type:datetime" json:"last_login"`
	PhoneArea                     string     `gorm:"column:phone_area;type:varchar(255);default ''" json:"phone_area"`
	Phone                         string     `gorm:"column:phone;type:varchar(255);default ''" json:"phone"`
	InSufficientIsBalanceShutdown bool       `gorm:"column:insufficient_is_balance_shutdown;type:tinyint(1);default 0" json:"insufficient_is_balance_shutdown"`
	LimitRecharge                 bool       `gorm:"column:limit_recharge;type:tinyint(1)" json:"limit_recharge"`

	// 暂时不使用连接表, 只在此处记录角色信息
	RolesJson datatypes.JSON `gorm:"column:roles;type:json" json:"-"`
	Roles     SubUserRoles   `gorm:"-" json:"roles"`

	// fill more
	UUID                  string                            `gorm:"-" json:"uuid"` // 主账号uuid
	PaymentMethod         constant.SUPaymentMethod          `gorm:"-" json:"payment_method"`
	Quota                 int64                             `gorm:"-" json:"quota"`
	MaxInstanceNum        int64                             `gorm:"-" json:"max_instance_num"`
	MaxRegionInstanceNum  map[constant.RegionSignType]int64 `gorm:"-" json:"max_region_instance_num"`
	TotalConsumption      int64                             `gorm:"-" json:"total_consumption"`
	MaxFileTransferPerDay int                               `gorm:"-" json:"max_file_transfer_per_day"`
	RealNameAuth          bool                              `gorm:"-" json:"real_name_auth"`
	Setting               UserSettingInfo                   `gorm:"-" json:"setting"`
	MasterWxBind          bool                              `gorm:"-" json:"master_wx_bind"`
	MasterLimitRecharge   bool                              `gorm:"-" json:"master_limit_recharge"`

	User *User `gorm:"-" json:"-"`
}

type SubUserRoles struct {
	Instance                  constant.SubUserAuthorityType `json:"instance"`
	InstanceAuthorizedAt      string                        `json:"instance_authorized_at"`
	Deployment                constant.SubUserAuthorityType `json:"deployment"`
	DeploymentAuthorizedAt    string                        `json:"deployment_authorized_at"`
	Image                     constant.SubUserAuthorityType `json:"image"`
	ImageAuthorizedAt         string                        `json:"image_authorized_at"`
	FileStorage               constant.SubUserAuthorityType `json:"file_storage"`
	FileStorageSubPath        string                        `json:"file_storage_sub_path"`
	FileStorageAuthorizedAt   string                        `json:"file_storage_authorized_at"`
	BillingCenter             constant.SubUserAuthorityType `json:"billing_center"`
	BillingCenterAuthorizedAt string                        `json:"billing_center_authorized_at"`
}

func (u *SubUser) TableName() string {
	return TableNameSubUser
}

// Init 实现 db_helper 接口.
func (u *SubUser) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&SubUser{})
}

func (u *SubUser) BeforeCreate(db *gorm.DB) error {
	u.RolesJson, _ = json.Marshal(u.Roles)
	return nil
}

func (u *SubUser) AfterFind(db *gorm.DB) error {
	if len(u.RolesJson) != 0 {
		_ = json.Unmarshal(u.RolesJson, &u.Roles)
	}
	return nil
}

func (u *SubUser) SubUserCreate(tx *gorm.DB) (err error) {
	return db_helper.InsertOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         u,
		InsertPayload:           u,
	}).GetError()
}

func (u *SubUser) SubUserUpdate(tx *gorm.DB, f *db_helper.QueryFilters, um map[string]interface{}) (int64, error) {
	count, errDB := db_helper.UpdateOneAffectRows(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         u,
		Filters:                 *f,
	}, um)
	return count, errDB.GetError()
}

func (u *SubUser) SubUserGet(f *db_helper.QueryFilters) (err error) {
	err = db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: u,
		Filters:         *f,
	}, &u).GetError()
	return
}

func (u *SubUser) SubUserDelete(tx *gorm.DB, f *db_helper.QueryFilters) (err error) {
	return db_helper.Delete(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         u,
		Filters:                 *f,
	}, &u).GetError()
}

func (u *SubUser) SubUserCount(f *db_helper.QueryFilters) (count int64, err error) {
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: u,
		Filters:         *f,
	}, &count).GetError()
	return
}

func (u *SubUser) SubUserGetAll(f *db_helper.QueryFilters) (ucList []SubUser, err error) {
	ucList = make([]SubUser, 0)
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: u,
		Filters:         *f,
	}, &ucList).GetError()
	return
}

func (u *SubUser) SubUserGetList(uid int, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []SubUser, err error) {
	list = make([]SubUser, 0)
	db := db_helper.GlobalDBConn().Table(TableNameSubUser).
		Where("deleted_at is null").
		Where("uid = ?", uid)

	if pageReq.DateFrom != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') >= ?", pageReq.DateFrom)
	}
	if pageReq.DateTo != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') <= ?", pageReq.DateTo)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		return
	}

	paged = db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, int(count), 0)

	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("id desc").
		Find(&list).
		Error
	if err != nil {
		return
	}

	paged.List = &list
	return
}

func (u *SubUser) Mask() {
	u.Phone = libs.Mask(u.Phone)
}
