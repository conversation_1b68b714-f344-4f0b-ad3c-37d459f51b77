package model

import (
	"gorm.io/gorm"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"
)

const TableNameUserContainerMonitorSetting = "user_container_monitor_setting"

type UserContainerMonitorSetting struct {
	ID        int            `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	UID       int            `gorm:"column:uid;index:uid" json:"uid"`
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`

	StatusEnable        bool                 `gorm:"type:tinyint(1);column:status_enable;default 0" json:"status_enable"`         // 监控开启状态,
	MonitorScope        constant.ProductType `gorm:"type:varchar(255);column:monitor_scope;default ''" json:"monitor_scope"`      // 监控范围, all/instance/deployment
	PushGatewayUrl      string               `gorm:"type:varchar(255);column:push_gateway_url" json:"push_gateway_url"`           // 推送网关地址
	PushGatewayUsername string               `gorm:"type:varchar(255);column:push_gateway_username" json:"push_gateway_username"` // 推送网关用户名
	PushGatewayPassword string               `gorm:"type:varchar(255);column:push_gateway_password" json:"-"`                     // 推送网关密码

}

func (u *UserContainerMonitorSetting) TableName() string {
	return TableNameUserContainerMonitorSetting
}

// Init 实现 db_helper 接口.
func (u *UserContainerMonitorSetting) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&UserContainerMonitorSetting{})
}

func (u *UserContainerMonitorSetting) UCMSCreate(tx *gorm.DB) error {
	return db_helper.InsertOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         u,
		InsertPayload:           u,
	}).GetError()
}

func (u *UserContainerMonitorSetting) UCMSUpdate(tx *gorm.DB, filter *db_helper.QueryFilters, um map[string]interface{}) error {
	return db_helper.UpdateOne(db_helper.QueryDefinition{
		ModelDefinition:         u,
		DBTransactionConnection: tx,
		Filters:                 *filter,
		NoLimit:                 false,
	}, um).GetError()
}

func (u *UserContainerMonitorSetting) UCMSGet(tx *gorm.DB, filter *db_helper.QueryFilters) error {
	return db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition:         u,
		DBTransactionConnection: tx,
		Filters:                 *filter,
	}, &u).GetError()

}

func (u *UserContainerMonitorSetting) UCMSDelete(tx *gorm.DB, filter *db_helper.QueryFilters) error {
	return db_helper.Delete(db_helper.QueryDefinition{
		ModelDefinition:         u,
		DBTransactionConnection: tx,
		Filters:                 *filter,
	}, u).GetError()
}

func (u *UserContainerMonitorSetting) ToMonitorSetting() *constant.ContainerMonitorSetting {
	return &constant.ContainerMonitorSetting{
		PushGatewayUrl:      u.PushGatewayUrl,
		PushGatewayUsername: u.PushGatewayUsername,
		PushGatewayPassword: u.PushGatewayPassword,
	}
}
