package model

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"
)

const TableNameOperateRecord = "operate_record"

type OperateRecord struct {
	ID        int       `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	CreatedAt time.Time `gorm:"type:datetime;column:created_at;" json:"created_at"`

	UID           int                           `gorm:"column:uid;index" json:"uid"`
	SubName       string                        `gorm:"column:sub_name;type:varchar(255);default ''" json:"sub_name"`
	Section       constant.OperateRecordSection `gorm:"column:section;type:varchar(255);default ''" json:"section"`
	Operate       constant.OperateRecordType    `gorm:"column:operate;type:varchar(255);default ''" json:"operate"`
	EntityUUID    string                        `gorm:"column:entity_uuid;type:varchar(255);index;default ''" json:"entity_uuid"`
	IP            string                        `gorm:"column:ip;type:varchar(255);default ''" json:"ip"`
	UserAgent     string                        `gorm:"column:user_agent;type:varchar(255);default ''" json:"user_agent"`
	Referer       string                        `gorm:"column:referer;type:varchar(255);default ''" json:"referer"`
	PayloadJson   datatypes.JSON                `gorm:"column:payload;type:json" json:"-"`
	PayloadEntity interface{}                   `gorm:"-" json:"-"`
}

func (u *OperateRecord) TableName() string {
	return TableNameOperateRecord
}

// Init 实现 db_helper 接口.
func (u *OperateRecord) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&OperateRecord{})
}

func (u *OperateRecord) OperateRecordCreate(c *gin.Context) (err error) {
	if c != nil {
		// ip
		if u.IP == "" {
			u.IP = c.ClientIP()
		}

		// user-agent
		u.UserAgent = c.GetHeader("user-agent")
		if u.UserAgent == "" {
			u.UserAgent = c.GetHeader("User-Agent")
		}

		// referer
		u.Referer = c.GetHeader("referer")
		if len(u.Referer) > 255 {
			u.Referer = u.Referer[:255] // 限制 referer 长度
		}
	}

	// section
	u.Section = u.Operate.ToSection()

	// payload
	if u.PayloadEntity != nil {
		u.PayloadJson, _ = json.Marshal(u.PayloadEntity)
	}
	return db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: u,
		InsertPayload:   u,
	}).GetError()
}

type OperateRecordForFrontend struct {
	CreatedAt   time.Time                     `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UID         int                           `gorm:"column:uid;index" json:"uid"`
	Phone       string                        `json:"phone"`
	SubName     string                        `gorm:"column:sub_name;type:varchar(255);default ''" json:"sub_name"`
	Section     constant.OperateRecordSection `gorm:"column:section;type:varchar(255);default ''" json:"section"`
	Operate     constant.OperateRecordType    `gorm:"column:operate;type:varchar(255);default ''" json:"operate"`
	EntityUUID  string                        `gorm:"column:entity_uuid;type:varchar(255);default ''" json:"entity_uuid"`
	IP          string                        `gorm:"column:ip;type:varchar(255);default ''" json:"ip"`
	UserAgent   string                        `gorm:"column:user_agent;type:varchar(255);default ''" json:"user_agent"`
	PayloadJson datatypes.JSON                `gorm:"column:payload;type:json" json:"payload_json"`
}

type LoginRecordListResp struct {
	ID        int       `json:"id"`
	CreatedAt time.Time `json:"created_at"`
	Username  string    `json:"username"`
	Ip        string    `json:"ip"`
	Region    string    `json:"region"`     // 地区
	LoginType string    `json:"login_type"` //登录类型
}
