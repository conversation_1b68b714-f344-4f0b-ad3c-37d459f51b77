package model

import (
	"server/pkg/db_helper"
	"time"

	"gorm.io/gorm"
)

const TableNameUserPersonalToken string = "user_personal_token"

type UserPersonalToken struct {
	ID        int            `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`

	// token基本信息
	TokenUUID   string `gorm:"type:varchar(500);column:token_uuid" json:"token_uuid"`
	UID         int    `gorm:"type:integer;column:uid;index:uid" json:"uid"`
	Description string `gorm:"type:varchar(255);column:description" json:"description"` // token备注
}

func (u *UserPersonalToken) TableName() string {
	return TableNameUserPersonalToken
}

// Init 实现 db_helper 接口.
func (u *UserPersonalToken) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&UserPersonalToken{})
}

func (u *UserPersonalToken) PersonalTokenCount(filter *db_helper.QueryFilters) (count int64, err error) {
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: u,
		Filters:         *filter,
	}, &count).GetError()
	return
}
