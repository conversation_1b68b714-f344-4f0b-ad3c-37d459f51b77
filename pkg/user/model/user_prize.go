package model

import (
	"gorm.io/gorm"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"
)

const TableNameUserPrize string = "user_prize"

type UserPrize struct {
	ID        int                    `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	UID       int                    `gorm:"column:uid" json:"uid"`
	PrizeType constant.UserPrizeType `gorm:"column:prize_type" json:"prize_type"`
	PrizeCode int64                  `gorm:"column:prize_code" json:"prize_code"`
	CreatedAt time.Time              `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time              `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt         `gorm:"type:datetime;column:deleted_at;index" json:"-"`
}

func (u *UserPrize) TableName() string {
	return TableNameUserPrize
}

// Init 实现 db_helper 接口.
func (u *UserPrize) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&UserPrize{})
}

type PrizeTypeInfo struct {
	PrizeType          constant.UserPrizeType `json:"prize_type"`
	ParticipantUserNum int64                  `json:"participant_user_num"`
}

type UserPrizeType struct {
	ExistUserPrize bool                             `json:"exist_user_prize"` //用户是否已经抽奖
	PrizeCode      int64                            `json:"prize_code"`       //用户抽奖抽奖码
	PrizeTypeInfo  map[constant.UserPrizeType]int64 `json:"prize_type_info"`  //各种奖品详情(人数和类型)
	PrizeDrawType  []constant.UserPrizeType         `json:"prize_draw_type"`  //用户能抽的奖品类型
	PrizeType      constant.UserPrizeType           `json:"prize_type"`       //用户选择抽奖后奖品类型
	IsInWhitelist  bool                             `json:"is_in_whitelist"`  //是否在白名单里
}

func (u *UserPrize) GetOneUserPrize(filter db_helper.QueryFilters) (userPrize *UserPrize, err error) {
	err = db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: u,
		Filters:         filter,
	}, &userPrize).GetError()
	return
}

type CalcWinNumRequest struct {
	WinNum int64 `json:"win_num"` //中奖号码
}
