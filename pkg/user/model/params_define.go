package model

import (
	"database/sql"
	_ "embed"
	"encoding/xml"
	"io"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"
	"unicode"
)

type DefaultNullRequest struct {
}

type UserRegisterRequest struct {
	InviteCode string `form:"invite_code" json:"invite_code"` // 邀请人uuid
	Password   string `form:"password" json:"password" binding:"required"`
	PhoneArea  string `form:"phone_area" json:"phone_area"`
	Phone      string `form:"phone" json:"phone" binding:"required"`
	VCode      string `form:"v_code" json:"v_code" binding:"required"` // 短信验证码
	Passcode   string `form:"passcode" json:"passcode"`                // 通行码，用于前后端接口加密混淆
}

type UserRegisterRequestNoInviteCode struct {
	Password  string `form:"password" json:"password" binding:"required"`
	PhoneArea string `form:"phone_area" json:"phone_area"`
	Phone     string `form:"phone" json:"phone" binding:"required"`
	VCode     string `form:"v_code" json:"v_code" binding:"required"` // 短信验证码
	Passcode  string `form:"passcode" json:"passcode"`                // 通行码，用于前后端接口加密混淆
}

// ParamsCreateUser 创建用户请求参数
type ParamsCreateUser struct {
	InviteUserId int    `json:"invite_user_id"` // 邀请注册参数
	PhoneArea    string `json:"phone_area"`
	Username     string `json:"username" binding:"required"`
	Password     string `json:"password" binding:"required"`
	Phone        string `json:"phone" binding:"required"`
	Email        string `json:"email"`
	IsAdmin      bool   `json:"is_admin"`
	IP           string `json:"ip"`
	OpenID       string `json:"open_id"`
}

// ParamsInitAdmin 初始化管理员请求参数
type ParamsInitAdmin struct {
	Username string `json:"username" binding:"required"`
	// 初始化管理员传递密码为明文
	Password     string `json:"password" binding:"required"`
	Phone        string `json:"phone" binding:"required"`
	Email        string `json:"email"`
	IsSuperAdmin bool   `json:"is_super_admin"`
}

// ParamsUpdateUser 编辑用户请求参数
type ParamsUpdateUser struct {
	ID        int    `json:"id"`
	Username  string `json:"username" binding:"required"`
	Password  string `json:"password" binding:"required"`
	PhoneArea string `json:"phone_area"`
	Phone     string `json:"phone" binding:"required"`
	Email     string `json:"email"`
}

type UpdatePhoneRequest struct {
	OldPhone string `form:"old_phone" json:"old_phone"`
	NewPhone string `form:"new_phone" json:"new_phone"`
}

type UpdateNicknameRequest struct {
	ID          int    `form:"id" json:"id"`
	NewNickname string `form:"new_nickname" json:"new_nickname"`
}

type UpdatePasswordRequest struct {
	VCode           string `form:"v_code" json:"v_code"`                     // 短信验证码
	NewPassword     string `form:"new_password" json:"new_password"`         // 新密码
	ConfirmPassword string `form:"confirm_password" json:"confirm_password"` // 确认密码
}

type UpdateEmailRequest struct {
	OldEmail string `form:"old_email" json:"old_email" binding:"required"` // 原邮箱
	NewEmail string `form:"new_email" json:"new_email" binding:"required"` // 新输入邮箱
	Code     string `form:"code" json:"code" binding:"required"`           // 验证码
}

type BindEmailRequest struct {
	Email string `form:"email" json:"email" binding:"required"`
	Code  string `form:"code" json:"code" binding:"required"` // 邮箱验证码
}

type FastLoginRequest struct {
	PhoneArea string `form:"phone" json:"phone_area"`
	Phone     string `json:"phone" form:"phone" binding:"required"`
	VCode     string `json:"v_code" form:"v_code" binding:"required"`
}

type UpdateUserNameRequest struct {
	Id   int    `form:"id" json:"id"`
	Name string `form:"name" json:"name"`
}

// UserListReply 获取用户列表返回响应
type UserListReply struct {
	User
	Assets  int64 `gorm:"-" form:"column:assets" json:"assets"`
	Voucher int64 `gorm:"-" form:"column:voucher" json:"voucher"`
}

// UserMemberListReply 获取用户列表返回相应
type UserMemberListReply struct {
	UserMember
	Phone    string `gorm:"-" form:"column:phone" json:"phone"`
	UserName string `gorm:"-" form:"column:userName" json:"userName"`
}

// ResetPasswordRequest 忘记密码登录请求参数
type ResetPasswordRequest struct {
	PhoneArea string `form:"phone_area" json:"phone_area"`
	Phone     string `form:"phone" json:"phone"`
	Password  string `form:"password" json:"password"`
	VCode     string `form:"v_code" json:"v_code" binding:"required"` // 短信验证码
}

// BlockPuzzleCaptchaResponse 请求滑块验证码响应
type BlockPuzzleCaptchaResponse struct {
	ID                  string `form:"id" json:"id"`
	YPosition           int    `form:"y_position" json:"y_position"`
	OriginalImageBase64 string `form:"original_image_base_64" json:"original_image_base_64"`
	JigsawImageBase64   string `form:"jigsaw_image_base_64" json:"jigsaw_image_base_64"`
}

// CaptchaCheckRequest 滑块验证码请求参数
type CaptchaCheckRequest struct {
	ID        string `form:"id" json:"id"`
	XPosition int    `form:"x_position" json:"x_position"`
	YPosition int    `form:"y_position" json:"y_position"`
}

// LoginRequest 用户新登录请求参数(三次错误跳验证码)
type LoginRequest struct {
	PhoneArea string `form:"phone_area" json:"phone_area"`
	Phone     string `json:"phone" form:"phone" binding:"required"`
	Password  string `json:"password" form:"password" binding:"required"`
	PictureID string `json:"picture_id" form:"picture_id"` // 滑块验证码id, 校验是否通过滑块验证
}

type LoginCaptchaRequest struct {
	PhoneArea string `form:"phone_area" json:"phone_area"`
	Phone     string `json:"phone" form:"phone" binding:"required"`
	Password  string `json:"password" form:"password" binding:"required"`
	CaptchaRequest
}

// CaptchaRequest 图形验证码请求参数
type CaptchaRequest struct {
	CaptchaID    string `form:"captcha_id" json:"captcha_id"`
	CaptchaValue string `form:"captcha_value" json:"captcha_value"`
}

// CaptchaResponse 图形验证码返回参数
type CaptchaResponse struct {
	Id      string `form:"id" json:"id"`
	Captcha string `form:"captcha" json:"captcha"`
}

type WxAppletLoginResp struct {
	OpenId     string `json:"openid"`
	SessionKey string `json:"session_key"`
	UnionId    string `json:"unionid"`
	ErrCode    int    `json:"errcode"`
	ErrMsg     string `json:"errmsg"`
}

// SMSRequest 发送短信请求参数
type SMSRequest struct {
	PhoneArea string `form:"phone_area" json:"phone_area"`
	Phone     string `form:"phone" json:"phone" binding:"required"`
	SMSType   string `form:"sms_type" json:"sms_type"`
	PictureID string `json:"picture_id" form:"picture_id"` // 滑块验证码id, 校验是否通过滑块验证
}

type SMSRequestBySubName struct {
	PhoneArea string `form:"phone_area" json:"phone_area"`
	Phone     string `form:"phone" json:"phone"`
	SMSType   string `form:"sms_type" json:"sms_type"`
	PictureID string `json:"picture_id" form:"picture_id"` // 滑块验证码id, 校验是否通过滑块验证
}

type SimpleSMSRequest struct {
	PhoneArea string `form:"phone_area" json:"phone_area"`
	Phone     string `form:"phone" json:"phone" binding:"required"`
	SMSType   string `form:"sms_type" json:"sms_type"`
}

// CheckVCodeRequest 校验短信验证码参数
type CheckVCodeRequest struct {
	PhoneArea string `form:"phone_area" json:"phone_area"`
	Phone     string `form:"phone" json:"phone" binding:"required"`
	VCode     string `form:"v_code" json:"v_code" binding:"required"`
}

// SendEmailRequest 发送邮件请求参数
type SendEmailRequest struct {
	Email     string `form:"email" json:"email" binding:"required"`
	PictureID string `json:"picture_id" form:"picture_id"` // 滑块验证码id, 校验是否通过滑块验证
}

// CheckEmailRequest
type CheckEmailRequest struct {
	Phone string `json:"phone" form:"phone" binding:"required"`
	Code  string `json:"code" form:"code" binding:"required"`
}

// UserInviteDetailResponse 用户邀请有礼详情
type UserInviteDetailResponse struct {
	InviteCount       int         `form:"invite_count" json:"invite_count"`               // 成功邀请人数
	InviteRewards     int64       `form:"invite_rewards" json:"invite_rewards"`           // 已获得奖励
	InviteCode        string      `form:"invite_link" json:"invite_link"`                 // 邀请码
	InviteRewardRules interface{} `form:"invite_reward_rules" json:"invite_reward_rules"` // 邀请奖励规则
}

// ---------------------------------------user feedback----------------------------------------------

// FeedBackRequest 用户反馈与建议请求
type FeedBackRequest struct {
	QuestionType string `form:"question_type" json:"question_type"` // 问题类型
	Description  string `form:"description" json:"description"`     // 问题描述
	InstanceUuid string `form:"instance_uuid" json:"instance_uuid"` // 实例编号
}

// FeedBackFullRequest 用户反馈请求参数
type FeedBackFullRequest struct {
	QuestionType string `form:"question_type" json:"question_type"` // 问题类型
	Description  string `form:"description" json:"description"`     // 问题描述
	InstanceUuid string `form:"instance_uuid" json:"instance_uuid"` // 实例编号
	UserId       int    `form:"user_id" json:"user_id"`             // 提出反馈的用户id
	ImageFileID  int    `form:"image_file_id" json:"image_file_id"` // 截图在文件表中的id
}

// GetUserFeedBackListRequest 用户反馈列表请求
type GetUserFeedBackListRequest struct {
	QuestionType string             `form:"question_type" json:"question_type"` // 问题类型
	Status       UserFeedBackStatus `form:"status" json:"status"`               // 问题状态
	db_helper.GetPagedRangeRequest
}

// ---------------------------------------------- wechat applet login------------------------------------
type WechatAppletLoginReq struct {
	Code string `form:"code" json:"code"`
}

type WechatAppletPhone struct {
	ErrCode   int       `json:"errcode"`
	ErrMsg    string    `json:"errmsg"`
	PhoneInfo PhoneInfo `json:"phone_info"`
}

type PhoneInfo struct {
	PhoneNumber     string    `json:"phoneNumber"`
	PurePhoneNumber string    `json:"purePhoneNumber"`
	CountryCode     string    `json:"countryCode"`
	Watermark       Watermark `json:"watermark"`
}

type Watermark struct {
	TimeStamp int64  `json:"timestamp"`
	AppID     string `json:"appid"`
}

// ----------------------------------------------wechat login----------------------------------------------
type CreateQRCodeRequest struct {
	ExpireSeconds int64      `form:"expire_seconds" json:"expire_seconds"`
	ActionName    string     `form:"action_name" json:"action_name"`
	ActionInfo    ActionInfo `form:"action_info" json:"action_info"`
}

type ActionInfo struct {
	Scene Scene `form:"scene" json:"scene"`
}

type Scene struct {
	SceneStr string `form:"scene_str" json:"scene_str"`
}

type AccessTokenResponse struct {
	AccessToken string `form:"access_token" json:"access_token"` // 获取到的凭证
	ExpiresIn   int64  `form:"expires_in" json:"expires_in"`     // SessionKey超时时间（秒）
}

type WxAccessTicketRsp struct {
	Ticket        string `form:"ticket" json:"ticket"`
	ExpireSeconds int64  `form:"expire_seconds" json:"expire_seconds"`
	Url           string `form:"url" json:"url"`
}

type AccessTicketResponse struct {
	Uuid          string `form:"uuid" json:"uuid"`                     // 生成的uuid作为凭证,前端去进行轮询查询
	QrcodeUrl     string `form:"qrcode_url" json:"qrcode_url"`         // 根据ticket换取二维码的 url
	ExpireSeconds int64  `form:"expire_seconds" json:"expire_seconds"` // ticket过期时间
}

type WxPhoneBindRequest struct {
	OpenId    string `json:"open_id" form:"open_id"` // 微信用户openId
	PhoneArea string `form:"phone_area" json:"phone_area"`
	Phone     string `json:"phone" form:"phone"`                      // 用户手机号
	VCode     string `json:"v_code" form:"v_code" binding:"required"` // 短信验证码
}

// 微信用户信息详细介绍:https://developers.weixin.qq.com/doc/offiaccount/User_Management/Get_users_basic_information_UnionID.html#UinonId
type UserWxInfo struct {
	Subscribe      int    `form:"subscribe" json:"subscribe"`             // 用户是否订阅该公众号标识，值为0时，代表此用户没有关注该公众号，拉取不到其余信息。
	Openid         string `form:"openid" json:"openid"`                   // 用户的标识,对当前公众号唯一
	Nickname       string `form:"nickname" json:"nickname"`               // 用户昵称
	Sex            int    `form:"sex" json:"sex"`                         // 用户性别,值为1时是男性，值为2时是女性，值为0时是未知
	Language       string `form:"language" json:"language"`               // 用户的语言,简体中文为zh_CN
	City           string `form:"city" json:"city"`                       // 用户所在城市
	Province       string `form:"province" json:"province"`               // 用户所在省份
	Country        string `form:"country" json:"country"`                 // 用户所在国家
	HeadImgUrl     string `form:"head_img_url" json:"head_img_url"`       // 用户头像
	SubscribeTime  int64  `form:"subscribe_time" json:"subscribe_time"`   // 用户关注时间
	UnionId        string `form:"union_id" json:"union_id"`               // 只有在用户将公众号绑定到微信开放平台帐号后，才会出现该字段。
	Remark         string `form:"remark" json:"remark"`                   // 公众号运营者对粉丝的备注，公众号运营者可在微信公众平台用户管理界面对粉丝添加备注
	GroupId        int    `form:"group_id" json:"group_id"`               // 用户所在的分组ID（兼容旧的用户分组接口）
	TagIdList      []int  `form:"tag_id_list" json:"tag_id_list"`         // 用户被打上的标签ID列表
	SubscribeScene string `form:"subscribe_scene" json:"subscribe_scene"` // 返回用户关注的渠道来源
	QrScene        int    `form:"qr_scene" json:"qr_scene"`               // 二维码扫码场景（开发者自定义）
	QrSceneStr     string `form:"qr_scene_str" json:"qr_scene_str"`       // 二维码扫码场景描述（开发者自定义）
}

type WxCallBackParams struct {
	IncomingParams io.Reader `form:"incoming_params" json:"incoming_params"` // 传入微信进行解析的参数
	Signature      string    `form:"signature" json:"signature"`             // 签名
	Timestamp      string    `form:"timestamp" json:"timestamp"`             // 时间戳
	Nonce          string    `form:"" json:"nonce"`                          // 签名的随机参数
	EncryptType    string    `form:"encrypt_type" json:"encrypt_type"`       // 加密方式
	MsgSignature   string    `form:"msg_signature" json:"msg_signature"`     // 加密后的消息签名
}

type ReplyText struct {
	XMLName      xml.Name  `xml:"xml"`
	ToUserName   CDATAText // 接受方帐号
	FromUserName CDATAText // 开发者微信号
	CreateTime   string    // 消息创建时间
	MsgType      CDATAText // 消息类型,文本为text
	Content      CDATAText // 回复的消息内容(换行:在content中能够换行,微信客户端就支持换行显示)
}

type CDATAText struct {
	Text string `xml:",innerxml"`
}

type CustomServiceMsg struct {
	ToUser  string         `json:"touser"`
	MsgType string         `json:"msgtype"`
	Text    TextMsgContent `json:"text"`
}

type TextMsgContent struct {
	Content string `json:"content"`
}

type TemplateMessageParam struct {
	Token   string `form:"token" json:"token"` // WechatMessageSend 接口里没有用到这个 token，WechatMessagePush 用到了
	Title   string `form:"title" json:"title"`
	Name    string `form:"name" json:"name"`
	Time    string `form:"time" json:"time"`
	Content string `form:"content" json:"content"`
}

// --------------------------------------group-----------------------------------

type CreateGroupRequest struct {
	GroupName   string `form:"group_name" json:"group_name"`   // 用户组名称
	Description string `form:"description" json:"description"` // 用户组描述
	// MaxCapacity int    `form:"max_capacity" json:"max_capacity"` //用户组的容量
}

type UpdateGroupRequest struct {
	GroupId     int    `form:"group_id" json:"group_id"`       // 用户组id
	GroupName   string `form:"group_name" json:"group_name"`   // 用户组名称
	Description string `form:"description" json:"description"` // 用户组描述
	// MaxCapacity int    `form:"max_capacity" json:"max_capacity"` //用户组的容量
}

type GetGroupListRequest struct {
	GroupName string `form:"group_name" json:"group_name"` // 用户组名称
	db_helper.GetPagedRangeRequest
}

type GetUserGroupListRequest struct {
	GroupID int `form:"group_id" json:"group_id"` // 组id
	db_helper.GetPagedRangeRequest
}

type GroupIDRequest struct {
	GroupId int `form:"group_id" json:"group_id"` // 用户组id
}

// --------------------------------------user_group-----------------------------------

type CreateUserGroupRequest struct {
	GroupID    int   `form:"group_id" json:"group_id"`         // 组id
	UserIDList []int `form:"user_id_list" json:"user_id_list"` // 要添加的用户id列表
}

type UserGroupInfoReply struct {
	GroupID   int       `json:"group_id"`
	UID       int       `json:"uid"`
	Phone     string    `json:"phone"`
	UserName  string    `json:"user_name"`
	CreatedAt time.Time `json:"created_at"`
}

// -------------------------------user member-----------------------------------------

// UpdateUserLevelRequest 修改用户等级参数
type UpdateUserLevelRequest struct {
	UID int `form:"uid" json:"uid"`
	// AdminSetLevel          constant.MemberLevelName `form:"user_level" json:"user_level"`
	AdminSetDeadline string `form:"admin_set_deadline" json:"admin_set_deadline"`
}

// UpdateUserCertificationRequest 修改用户身份
type UpdateUserCertificationRequest struct {
	UID                int    `form:"uid" json:"uid"`
	RealName           int    `form:"real_name" json:"real_name"`
	Student            int    `form:"student" json:"student"`
	Enterprise         int    `form:"enterprise" json:"enterprise"`
	RealNameDeadline   string `form:"real_name_deadline" json:"real_name_deadline"`
	StudentDeadline    string `form:"student_deadline" json:"student_deadline"`
	EnterpriseDeadline string `form:"enterprise_deadline" json:"enterprise_deadline"`
}

type UserClickUpgradeRequest struct {
	UserGrowthValue int64 `form:"user_growth_value" json:"user_growth_value"`
}

type AlteredUserGrowthValueParams struct {
	UserMemberLevel constant.MemberLevelName `json:"user_member_level"`
	AlteredGrowth   int64                    `json:"altered_growth"`
}

type UserMemberListRequest struct {
	UserMemberListFilter
	db_helper.GetPagedRangeRequest
}

type UserMemberListFilter struct {
	Phone    string                `form:"phone" json:"phone"`
	Level    string                `form:"level" json:"level"`
	Identify constant.UserIdentify `form:"identify" json:"identify"`
	UID      int                   `form:"-" json:"-"`
}

type StudentVerificationRequest struct {
	EducationEmail string `form:"education_email" json:"education_email"`
}

type CheckEducationEmailRequest struct {
	StudentVerificationRequest
	Code string `form:"code" json:"code"`
}

type GetGrowthValueListReq struct {
	UserId int `form:"user_id" json:"user_id"`
	db_helper.GetPagedRangeRequest
}

type UpdateUserInfoInTable struct {
	UserId          int                      `json:"user_id"`
	UserMemberLevel constant.MemberLevelName `json:"user_member_level"`
	GrowthValue     int64                    `json:"growth_value"`
}

type UserIdRequest struct {
	Id int `form:"id" json:"id"`
}

type UserPhoneRequest struct {
	Phone string `form:"phone" json:"phone"`
}

type UserUUIDRequest struct {
	Uuid string `form:"uuid" json:"uuid"`
}

type PassportRequest struct {
	Ticket string `form:"ticket" json:"ticket" binding:"required"`
}

type GetUserListRequest struct {
	Username string `form:"username" json:"username"`
	Phone    string `form:"phone" json:"phone"`
	UUID     string `form:"uuid" json:"uuid"`
	db_helper.GetPagedRangeRequest
}

// ------------------------------ user ssh pub key --------------------------

type CreatePubKeyRequest struct {
	PublicKey string `json:"public_key"`
	Note      string `json:"note"`
}

type UpdatePubKeyRequest struct {
	KeyID     int    `json:"key_id"`
	PublicKey string `json:"public_key"`
	Note      string `json:"note"`
}

type GetPubKeyListRequest struct {
	UID int `form:"uid" json:"uid"`
}

type DeletePubKeyRequest struct {
	KeyID int `form:"key_id" json:"key_id"`
}

// ------------------------------- user personal token ---------------------------------

type CreatePersonalTokenRequest struct {
	Description string `form:"description" json:"description"`
}

type DeletePersonalTokenRequest struct {
	ID int `form:"id" json:"id"`
}

type PersonalTokenListRequest struct {
	db_helper.GetPagedRangeRequest
}

type SubUserCreateParams struct {
	SubName   string `json:"sub_name"`
	Username  string `json:"username"`
	Password  string `json:"password"`
	PhoneArea string `json:"phone_area"`
	Phone     string `json:"phone"`
	VCode     string `json:"v_code"`
}

func (u *SubUserCreateParams) Check() bool {
	if u.SubName == "" || u.Username == "" || u.Password == "" {
		return false
	}
	return true
}

type SubUserGetDetailParams struct {
	SubName string `form:"sub_name" json:"sub_name"`
}

type SubUserPasswordChangeParams struct {
	SubName  string `json:"sub_name"`
	Password string `json:"password"`
}

type SubUserSettingByAdminParams struct {
	UID        int `form:"uid" json:"uid"`
	SubUserNum int `form:"sub_user_num" json:"sub_user_num"`
}

type SubUserUpdatePasswordParams struct {
	SrcPassword string `json:"src_password"`
	PhoneArea   string `json:"phone_area"`
	Phone       string `json:"phone"`
	VCode       string `json:"v_code"`
	NewPassword string `json:"new_password"`
}

type SubUserPhoneBindParams struct {
	SubName   string `json:"sub_name"`
	PhoneArea string `json:"phone_area"`
	Phone     string `json:"phone"`
	VCode     string `json:"v_code"`
}

func (u *SubUserPasswordChangeParams) Check() bool {
	if u.SubName == "" || u.Password == "" {
		return false
	}
	return true
}

type SubUserLoginParams struct {
	SubName  string `form:"sub_name" json:"sub_name"`
	Password string `form:"password" json:"password"`
}

// ------------------------------- user setting ---------------------------------

// UpdateSettingRequest 修改用户设置
type UpdateSettingRequest map[string]interface{}

// FollowMachineParams 订阅空闲 GPU
type FollowMachineParams struct {
	MachineID        string `form:"machine_id" json:"machine_id" binding:"required"`
	NotifyIdleGPUNum uint   `form:"notify_idle_gpu_num" json:"notify_idle_gpu_num" binding:"required"` // 空闲 GPU 大于多少时发送通知
}

// FollowMachineRequest 订阅空闲 GPU
type FollowMachineRequest []FollowMachineParams

// UnfollowMachineRequest 取消订阅空闲 GPU
type UnfollowMachineRequest struct {
	MachineID string `form:"machine_id" json:"machine_id" binding:"required"`
}

type GetFollowedMachineListReply struct {
	RegionName       string                  `json:"region_name"`
	RegionSign       constant.RegionSignType `json:"region_sign"`
	MachineAlias     string                  `json:"region_alias"`
	MachineID        string                  `json:"machine_id"`
	NotifyIdleGPUNum uint                    `json:"notify_idle_gpu_num"`
}

type GetFollowedMachineCountReply struct {
	Count int64 `json:"count"`
}

type RealNameAuthorizationParams struct {
	UID      int    `json:"-"`
	RealName string `json:"real_name"`
	IDNumber string `json:"id_number"`
}

func (r RealNameAuthorizationParams) Valid() bool {
	if r.RealName == "" || len(r.IDNumber) != 18 {
		return false
	}

	for k, v := range r.IDNumber {
		if unicode.IsDigit(v) {
			continue
		}

		if k != 17 {
			return false
		}
	}
	return true
}

type RealNameAuthAliResp struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Result  struct {
		Res      string `json:"res"`
		Describe string `json:"describe"`
	} `json:"result"`
}

type CtrlChangePhoneReq struct {
	Password  string `form:"password" json:"password" binding:"required"`
	PhoneArea string `form:"phone_area" json:"phone_area"`
	NewPhone  string `form:"new_phone" json:"new_phone" binding:"required"`
	VCode     string `form:"v_code" json:"v_code" binding:"required"` // 短信验证码
}

type UserCertificationReviewParams struct {
	UserCertificationID int                              `json:"user_certification_id"`
	Status              constant.UserCertificationStatus `json:"status"`
	RejectReason        string                           `json:"reject_reason"`
}

type UserCertificationDetailParams struct {
	UID      int                            `form:"-" json:"-"`
	AuthType constant.UserCertificationType `form:"auth_type" json:"auth_type"`
}

type UserCertificationDetailRes struct {
	ID           int                              `json:"id"`
	AuthType     constant.UserCertificationType   `json:"auth_type"`
	Name         string                           `json:"name"`
	IdNumber     string                           `json:"id_number"`
	AuthEntity   UserCertificationAuthEntity      `json:"auth_entity"`
	Status       constant.UserCertificationStatus `json:"status"`
	RejectReason string                           `json:"reject_reason"`
	CreatedAt    time.Time                        `json:"created_at"`
}

type AdminUserCertificationDetailParams struct {
	ID int `form:"id" json:"id"`
}

type UserCertificationRepealParams struct {
	ID       int    `json:"id"`
	AuthType string `json:"auth_type"`
}

type UserCertificationCreateParams struct {
	Name     string `gorm:"column:name;type:varchar(255);default ''" json:"name"`
	IDNumber string `gorm:"column:id_number;type:varchar(255);default ''" json:"id_number"`
	// 认证方式 个人认证/银行对公账户认证/企业证件认证
	AuthType   constant.UserCertificationType `gorm:"column:auth_type;t ype:varchar(255);default ''" json:"auth_type"`
	AuthEntity UserCertificationAuthEntity    `gorm:"-" json:"auth_info"`
	// 是否人脸验证
	IsFaceCertify bool `gorm:"-" json:"is_face_certify"`
}

type UserCertificationResp struct {
	FaceCertifyUrl string `json:"face_certify_url"`
	CertifyId      string `json:"certify_id"`
}

func (u *UserCertificationCreateParams) Valid() bool {
	switch u.AuthType {
	case constant.UserCertificationPersonal:
		if u.Name == "" || u.IDNumber == "" {
			return false
		}
	case constant.UserCertificationPersonalStudent:
		if u.AuthEntity.EducationEmail == "" {
			return false
		}
	case constant.UserCertificationEnterpriseBankAccount:
		if u.Name == "" || u.IDNumber == "" {
			return false
		}
	case constant.UserCertificationEnterpriseCertificate:
		if u.Name == "" || u.IDNumber == "" {
			return false
		}
		// 企业证件认证
		if u.AuthEntity.CertificationType != "enterprise_legal_person_business_license" {
			return false
		}
		if u.AuthEntity.Attachment == "" {
			return false
		}
		if u.AuthEntity.FrontOfIDCard == "" || u.AuthEntity.BackOfIDCard == "" ||
			u.AuthEntity.Name == "" || u.AuthEntity.IDNumber == "" {
			return false
		}
		switch u.AuthEntity.Authentication {
		case "legal_representative":
		case "authorized_person":
			if u.AuthEntity.AuthorisationForm == "" {
				return false
			}
		default:
			return false
		}
	}
	return true
}

type UserCertificationListParams struct {
	//UID      int    `json:"uid"`
	Username string                         `form:"usernames" json:"username"`
	UserUUID string                         `form:"user_uuid" json:"user_uuid"`
	Phone    string                         `form:"phone" json:"phone"`
	AuthType constant.UserCertificationType `form:"auth_type" json:"auth_type"`
	Status   []string                       `form:"status" json:"status"`
}

type SubUserRolesUpdateParams struct {
	UID     int    `json:"-"`
	SubName string `json:"sub_name"`
	//Instance           bool   `json:"instance"`
	//Deployment         bool   `json:"deployment"`
	//FileStorage        bool   `json:"file_storage"`
	FileStorageSubPath string `json:"file_storage_sub_path"`
	//BillingCenter      bool   `json:"billing_center"`
	//Image              bool   `json:"image"`
	Authority     string `json:"authority"` // 权限
	AuthorityName string `json:"authority_name"`
}

type SubUserInInSufficientBalanceShutdownParams struct {
	SubName                       string `json:"sub_name"`
	InSufficientIsBalanceShutdown bool   `json:"insufficient_is_balance_shutdown"`
}

type OperateRecordListForAdminParams struct {
	Phone      string `form:"phone" json:"phone"`
	EntityUUID string `form:"entity_uuid" json:"entity_uuid"`
	db_helper.GetPagedRangeRequest
}

type QueryUserFaceCertificationReq struct {
	CertifyId string `json:"certify_id"`
}

type QueryUserFaceCertificationResp struct {
	CertifyRes constant.UserCertificationStatus `json:"certify_res"`
}

type UserCertificationResultReq struct {
	OuterOrderNo string `form:"outer_order_no" json:"outer_order_no"`
}

type GetUserInstanceNumLimitReq struct {
	Phone string `form:"phone" json:"phone"`
}

type SetUserInstanceNumLimitReq struct {
	Phone  string `json:"phone"`
	MaxNum int64  `json:"max_num"`
}

type AdminGetTransInsPrePayListReq struct {
	db_helper.GetPagedRangeRequest
}

type AdminGetTransInsPrePayListFilters struct {
	Url string `json:"url"`
}

type AdminGetTransInsPrePayPayload struct {
	Phone           string `json:"phone"`
	OldInstanceUuid string `json:"old_instance_uuid"`
	NewInstanceUuid string `json:"new_instance_uuid"`
}

type AdminGetTransInsPrePayListItem struct {
	Phone           string    `json:"phone"`
	OldInstanceUuid string    `json:"old_instance_uuid"`
	NewInstanceUuid string    `json:"new_instance_uuid"`
	CreatedAt       time.Time `json:"created_at"`
}

var (
	//go:embed certifyFiles/successCertify.html
	SuccessCertify string

	//go:embed certifyFiles/failCertify.html
	FailCertify string
)

type AdminIdentityTransferRequest struct {
	Phone string `form:"phone" json:"phone"`
}

type InstanceCloneNumUpdateRequest struct {
	Phone         string `json:"phone"`
	CopyNumber    int    `json:"copy_number"`
	CloneNumber   int    `json:"clone_number"`
	ExpirationDay int    `json:"expiration_day"`
}

type SubUserListForPaygSettle struct {
	SubName                       string                   `json:"sub_name"`
	Quota                         int64                    `json:"quota"`
	PaymentMethod                 constant.SUPaymentMethod `json:"payment_method"`                   // 类型
	InSufficientIsBalanceShutdown bool                     `json:"insufficient_is_balance_shutdown"` // 是否余额不足关机
}

type AdminOperateLogListReq struct {
	Phone      string `form:"phone" json:"phone"`
	EntityUUID string `form:"entity_uuid" json:"entity_uuid"`
	db_helper.GetPagedRangeRequest
}

type AdminOperateLogListResp struct {
	CreatedAt  time.Time `json:"created_at"`
	UID        int       `json:"uid"`
	Phone      string    `json:"phone"`
	EntityUUID string    `json:"entity_uuid"`
	IP         string    `json:"ip"`
	UserAgent  string    `json:"user_agent"`
}

type UserBalanceNegativeInfo struct {
	UID                           int
	SubName                       string
	PhoneArea                     string
	Phone                         string
	LastNotifyBalanceNegativeTime sql.NullTime
	Status                        UserStatusType
}

type ContainerMonitorSetParams struct {
	UID                 int                  `form:"-" json:"-"`
	PushGatewayUrl      string               `form:"push_gateway_url" json:"push_gateway_url"`           // 推送网关地址
	PushGatewayUsername string               `form:"push_gateway_username" json:"push_gateway_username"` // 推送网关用户名
	PushGatewayPassword string               `form:"push_gateway_password" json:"push_gateway_password"` // 推送网关密码
	StatusEnable        bool                 `form:"status_enable" json:"status_enable"`                 // 状态监控开关
	MonitorScope        constant.ProductType `form:"monitor_scope" json:"monitor_scope"`
}
