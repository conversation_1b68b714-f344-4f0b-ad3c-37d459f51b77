package service

import (
	"encoding/json"
	bcm "server/pkg/billing_center/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/libs"
	"server/pkg/user/model"
	"time"

	"github.com/pkg/errors"
	"gorm.io/gorm"
)

func (svc *UserService) SubUserLoginCheck(params *model.SubUserLoginParams) (su *model.SubUser, err error) {
	su, err = svc.SubUserGetWithUser(params.SubName)
	if err != nil {
		return
	}
	if su.User.Status == model.Disable || su.User.SubUserNum == 0 {
		err = businesserror.ErrUserDisableLogin
		return
	}

	if su.Password != libs.SaltPassword(params.Password) {
		err = businesserror.ErrUserSubUserPhoneOrPasswordWrong
		return
	}

	err = svc.SubUserUpdate(params.SubName, map[string]interface{}{"last_login": time.Now()})
	return
}

func (svc *UserService) SubUserValidateUserTicket(ticket string) (su *model.SubUser, err error) {
	err = svc.isStringNull(ticket)
	if err != nil {
		return
	}

	subName, err := svc.plugin.GetTicketSubUser(ticket)
	if err != nil {
		err = businesserror.ErrDatabaseError.New().Append(businesserror.ErrGetTicketFailed)
		svc.l.WithField("err", err).Error("Get subName by ticket failed.")
		return
	}

	su, err = svc.SubUserGet(subName)
	if err != nil {
		return
	}
	return
}

func (svc *UserService) SubUserCreate(uid int, params *model.SubUserCreateParams) (err error) {
	if !params.Check() {
		return businesserror.ErrInvalidRequestParams
	}
	user, err := svc.FindByUserId(uid)
	if err != nil {
		return
	}
	if user.SubUserNum == 0 {
		return businesserror.ErrUserSubUserNotAllow
	}

	now := time.Now()
	nowFormat := now.Format(constant.FormatTimeString)

	subUserRoles := model.SubUserRoles{
		Instance:                  constant.SubUserMyselfData,
		InstanceAuthorizedAt:      nowFormat,
		Deployment:                constant.SubUserNoPermission,
		DeploymentAuthorizedAt:    nowFormat,
		Image:                     constant.SubUserMyselfData,
		ImageAuthorizedAt:         nowFormat,
		FileStorage:               constant.SubUserNoPermission,
		FileStorageAuthorizedAt:   nowFormat,
		BillingCenter:             constant.SubUserNoPermission,
		BillingCenterAuthorizedAt: nowFormat,
	}

	userMember, err := svc.GetUserMemberInfoByUid(uid)
	if err != nil {
		return err
	}

	if userMember.Enterprise.Valid() {
		subUserRoles.Deployment = constant.SubUserMyselfData
	}

	su := &model.SubUser{
		UID:           uid,
		PhoneArea:     params.PhoneArea,
		Phone:         params.Phone,
		SubName:       user.BuildSubUserName(params.SubName),
		Password:      libs.SaltPassword(params.Password),
		UserName:      params.Username,
		PaymentMethod: constant.SUPaymentMethodShare,
		Roles:         subUserRoles,
	}

	subNum, err := su.SubUserCount(&db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{"uid": su.UID},
		NullField:    []string{"deleted_at"},
	})
	if err != nil {
		svc.l.WithField("su", su).ErrorE(err, "count sub user num failed")
		err = businesserror.ErrDatabaseError
		return
	}
	if int(subNum) >= user.SubUserNum {
		return businesserror.ErrUserSubUserNumNotEnough
	}
	subNum, err = su.SubUserCount(&db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{"sub_name": su.SubName},
		NullField:    []string{"deleted_at"},
	})
	if err != nil {
		svc.l.WithField("su", su).ErrorE(err, "count sub user num failed")
		err = businesserror.ErrDatabaseError
		return
	}
	if subNum != 0 {
		return businesserror.ErrUserSubUserAlreadyExist
	}

	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) error {
		err = su.SubUserCreate(tx)
		if err != nil {
			svc.l.WithField("su", su).ErrorE(err, "create sub user failed")
			return businesserror.ErrDatabaseError
		}

		err = svc.bc.SubUserWalletCreate(tx, su.UID, su.SubName)
		if err != nil {
			return err
		}
		return nil
	})

	return
}

func (svc *UserService) SubUserUpdate(subName string, um map[string]interface{}) (err error) {
	su := &model.SubUser{}
	_, err = su.SubUserUpdate(nil, &db_helper.QueryFilters{EqualFilters: map[string]interface{}{"sub_name": subName}}, um)
	if err != nil {
		svc.l.WithField("name", subName).ErrorE(err, "update sub user failed")
		return businesserror.ErrDatabaseError
	}
	return
}

func (svc *UserService) SubUserGet(subName string) (su *model.SubUser, err error) {
	su = &model.SubUser{}
	err = su.SubUserGet(&db_helper.QueryFilters{EqualFilters: map[string]interface{}{"sub_name": subName}})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, businesserror.ErrUserNotExist
		}
		svc.l.WithField("name", subName).ErrorE(err, "get sub user failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *UserService) SubUserGetInList(subName string) (su *model.SubUser, err error) {
	su = &model.SubUser{}
	err = su.SubUserGet(&db_helper.QueryFilters{EqualFilters: map[string]interface{}{"sub_name": subName}})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return su, nil
		}
		svc.l.WithField("name", subName).ErrorE(err, "get sub user failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *UserService) SubUserGetWithUser(subName string) (su *model.SubUser, err error) {
	su, err = svc.SubUserGet(subName)
	if err != nil {
		return
	}

	su.User, err = svc.FindByUserId(su.UID)
	if err != nil {
		return
	}
	return
}

func (svc *UserService) SubUserGetWithBalance(subName string) (su *model.SubUser, err error) {
	su, err = svc.SubUserGet(subName)
	if err != nil {
		return
	}
	suw, err := svc.bc.SubUserWalletGetWithBalance(subName)
	if err != nil {
		return
	}

	user, err := svc.FindUserById(suw.UID)
	if err != nil {
		return
	}

	su.MaxInstanceNum, _ = user.GetMaxInstanceNum("")
	su.MaxRegionInstanceNum = user.MaxRegionInstanceNumEntity
	su.UUID = user.UUID
	su.MaxFileTransferPerDay = user.MaxFileTransferPerDay
	su.RealNameAuth = user.RealNameAuth
	su.Setting = user.Setting
	su.MasterWxBind = user.OpenId != ""
	su.MasterLimitRecharge = user.LimitRecharge

	su.Quota = suw.Quota
	return
}

func (svc *UserService) SubUserGetList(uid int, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []model.SubUser, err error) {
	uc := &model.SubUser{}
	paged, list, err = uc.SubUserGetList(uid, pageReq)
	if err != nil {
		svc.l.WithField("uid", uid).ErrorE(err, "get sub user list failed")
		err = businesserror.ErrDatabaseError
		return
	}

	subNameList := make([]string, len(list))
	for k, v := range list {
		subNameList[k] = v.SubName
	}

	if len(subNameList) == 0 {
		return
	}

	walletList, err := svc.bc.SubUserWalletGetList(subNameList)
	if err != nil {
		svc.l.ErrorE(err, "get sub user wallet list failed")
		return
	}

	walletMap := map[string]bcm.SubUserWallet{}
	for k, v := range walletList {
		walletMap[v.SubName] = walletList[k]
	}

	for k, v := range list {
		if v.Phone != "" {
			list[k].Mask()
		}
		if wallet, ok := walletMap[v.SubName]; ok {
			list[k].PaymentMethod = wallet.PaymentMethod
			list[k].Quota = wallet.Quota
			list[k].TotalConsumption = wallet.TotalConsumption
		}
	}
	return
}

func (svc *UserService) SubUserDelete(uid int, subName string) (err error) {
	su, err := svc.SubUserGet(subName)
	if err != nil {
		return
	}
	if uid != su.UID {
		return businesserror.ErrAuthorizeFailed
	}

	exist, err := svc.instance.InstanceSubUserExist(subName)
	if err != nil {
		return
	}
	if exist {
		return businesserror.ErrUserSubUserCanNotDelete
	}

	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) error {
		err = su.SubUserDelete(tx, &db_helper.QueryFilters{EqualFilters: map[string]interface{}{"sub_name": subName}})
		if err != nil {
			svc.l.WithField("subname", subName).ErrorE(err, "delete sub user failed")
			err = businesserror.ErrDatabaseError
			return err
		}

		err = svc.bc.SubUserWalletDelete(tx, subName)
		if err != nil {
			return err
		}

		return nil
	})

	return
}

func (svc *UserService) SubUserCheckBelong(uid int, suList []string) (ok bool, err error) {
	su := &model.SubUser{}
	count, err := su.SubUserCount(&db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{"uid": uid},
		InFilters:    []db_helper.In{{Key: "sub_name", InSet: suList}},
		NullField:    []string{"deleted_at"},
	})
	if err != nil {
		svc.l.WithField("uid", uid).WithField("list", suList).ErrorE(err, "count su failed")
		err = businesserror.ErrDatabaseError
		return
	}
	if len(suList) != int(count) {
		return false, nil
	}
	return true, nil
}

func (svc *UserService) SubUserSettingByAdmin(uid, subUserNum int) (err error) {
	u := &model.User{}
	err = u.UserUpdate(nil,
		db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": uid}},
		map[string]interface{}{"sub_user_num": subUserNum})
	if err != nil {
		svc.l.WithField("uid", uid).ErrorE(err, "update user subUserNum failed")
	}
	return
}

func (svc *UserService) SubUserUpdatePassword(subName, newPassword string) (err error) {
	su := &model.SubUser{}
	_, err = su.SubUserUpdate(nil, &db_helper.QueryFilters{EqualFilters: map[string]interface{}{"sub_name": subName}},
		map[string]interface{}{"password": libs.SaltPassword(newPassword)})
	if err != nil {
		svc.l.WithField("subName", subName).ErrorE(err, "update subuser password failed")
		return
	}
	return
}

func (svc *UserService) SubUserRolesUpdate(params *model.SubUserRolesUpdateParams) (err error) {
	// 更新subUser roles
	su, err := svc.SubUserGet(params.SubName)
	if err != nil {
		return
	}
	if su.UID != params.UID {
		return businesserror.ErrResourceAccessAuthFailed
	}

	now := time.Now()
	nowFormat := now.Format(constant.FormatTimeString)
	clearInstance := false
	clearDeployment := false
	clearImage := false

	switch constant.SubUserAuthorityName(params.AuthorityName) {
	case constant.SubUserInstance:
		// 实例有权限--> 无权限
		if constant.SubUserAuthorityType(params.Authority) == constant.SubUserNoPermission && su.Roles.Instance != constant.SubUserNoPermission {
			clearInstance = true
		}

		su.Roles.Instance = constant.SubUserAuthorityType(params.Authority)
		su.Roles.InstanceAuthorizedAt = nowFormat

		break
	case constant.SubUserDeployment:
		// 弹性部署无权限--> 有权限
		if constant.SubUserAuthorityType(params.Authority) == constant.SubUserNoPermission && su.Roles.Deployment != constant.SubUserNoPermission {
			userMember, err := svc.GetUserMemberInfoByUid(su.UID)
			if err != nil {
				return err
			}

			if !userMember.Enterprise.Valid() {
				return businesserror.ErrUserNotEnterpriseCanNotDeployment
			}

			clearDeployment = true
		}

		su.Roles.Deployment = constant.SubUserAuthorityType(params.Authority)
		su.Roles.DeploymentAuthorizedAt = nowFormat

		break
	case constant.SubUserImage:
		if constant.SubUserAuthorityType(params.Authority) == constant.SubUserNoPermission && su.Roles.Image != constant.SubUserNoPermission {
			clearImage = true
		}

		su.Roles.Image = constant.SubUserAuthorityType(params.Authority)
		su.Roles.ImageAuthorizedAt = nowFormat

		break
	case constant.SubUserFileStore:
		su.Roles.FileStorage = constant.SubUserAuthorityType(params.Authority)
		su.Roles.FileStorageAuthorizedAt = nowFormat

		if su.Roles.FileStorageSubPath != params.FileStorageSubPath {
			err = svc.spacPlugin.PacSet(su.UID, su.SubName, params.FileStorageSubPath)
			if err != nil {
				svc.l.ErrorE(err, "sub user role update, spac set failed, params:%+v", params)
				return err
			}
		}

		su.Roles.FileStorageSubPath = params.FileStorageSubPath
		break
	case constant.SubUserBill:
		su.Roles.BillingCenter = constant.SubUserAuthorityType(params.Authority)
		su.Roles.BillingCenterAuthorizedAt = nowFormat

		if constant.SubUserAuthorityType(params.Authority) != constant.SubUserNoPermission {
			// 更新subUserWallet的payment_method
			updatePaymentMethod := map[string]interface{}{
				"payment_method": "share",
			}
			subUserWallet := bcm.SubUserWallet{}
			_, err = subUserWallet.SUWUpdate(nil, &db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"sub_name": params.SubName,
				},
			}, updatePaymentMethod)
			if err != nil {
				svc.l.WithField("name", params.SubName).ErrorE(err, "update sub user wallet payment_method failed")
				return err
			}
		}

		break
	}

	rolesJson, _ := json.Marshal(su.Roles)

	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) error {
		if clearInstance {
			// instance 去除subname
			err = svc.instance.InstanceUnbindSubName(tx, params.SubName)
			if err != nil {
				svc.l.WithField("subName", params.SubName).ErrorE(err, "instance unbind subname failed")
				return err
			}

			// charging 去除subname
			err = svc.bc.ChargingUnbindSubName(tx, params.SubName, constant.ContainerRuntimeOfInstance)
			if err != nil {
				svc.l.WithField("subName", params.SubName).ErrorE(err, "charging unbind subName failed")
				return err
			}
		}

		if clearDeployment {
			// deployment 去除subname
			err = svc.deployment.DeploymentUnbindSubName(tx, params.SubName)
			if err != nil {
				svc.l.WithField("subName", params.SubName).ErrorE(err, "deployment unbind subname failed")
				return err
			}

			err = svc.deployment.DDPUnbindSubName(tx, params.SubName)
			if err != nil {
				svc.l.WithField("subName", params.SubName).ErrorE(err, "deployment  duration pkg unbind subname failed")
				return err
			}

			// charging 去除subname
			err = svc.bc.ChargingUnbindSubName(tx, params.SubName, constant.ContainerRuntimeOfDeployment)
			if err != nil {
				svc.l.WithField("subName", params.SubName).ErrorE(err, "charging unbind subName failed")
				return err
			}
		}

		if clearImage {
			// image 去除subname
			err = svc.privateImage.PrivateImageUnbindSubName(tx, params.SubName)
			if err != nil {
				svc.l.WithField("subName", params.SubName).ErrorE(err, "private image unbind subname failed")
				return err
			}
		}

		_, err = su.SubUserUpdate(tx, &db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"id": su.ID,
			},
		},
			map[string]interface{}{"roles": rolesJson})
		if err != nil {
			svc.l.WithField("subName", params.SubName).ErrorE(err, "update sub user roles failed")
			return err
		}
		return nil
	})
	return
}

func (svc *UserService) SubUserInInSufficientBalanceShutdownUpdate(subName string, inInSufficientBalanceShutdown bool) (err error) {
	sw := &model.SubUser{}
	_, err = sw.SubUserUpdate(nil, &db_helper.QueryFilters{EqualFilters: map[string]interface{}{"sub_name": subName}},
		map[string]interface{}{"insufficient_is_balance_shutdown": inInSufficientBalanceShutdown})
	if err != nil {
		svc.l.WithField("subName", subName).ErrorE(err, "update sub user failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return

}
