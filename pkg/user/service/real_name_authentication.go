package service

import (
	"server/pkg/db_helper"
	"server/pkg/user/model"
)

func (svc *UserService) UserAgreementSign(uid int, subName, agreementID string) (err error) {
	var count int64
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: &model.UserAgreement{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{
			"uid":          uid,
			"sub_name":     subName,
			"agreement_id": agreementID,
		}},
	}, &count).GetError()
	if err != nil {
		svc.l.WithField("uid", uid).ErrorE(err, "count user by uid and agreement_id failed")
		return
	}
	if count > 0 {
		return
	}

	err = db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: &model.UserAgreement{},
		InsertPayload: &model.UserAgreement{
			UID:         uid,
			AgreementID: agreementID,
			SubName:     subName,
		},
	}).GetError()
	if err != nil {
		svc.l.<PERSON><PERSON>ield("uid", uid).ErrorE(err, "user sign agreement failed")
	}

	return
}

func (svc *UserService) UserAgreementSignedCheck(uid int, subName, agreementID string) (signed bool, err error) {
	var count int64
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: &model.UserAgreement{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{
			"uid":          uid,
			"sub_name":     subName,
			"agreement_id": agreementID,
		}},
	}, &count).GetError()
	if err != nil {
		svc.l.WithField("uid", uid).ErrorE(err, "count user by uid and agreement_id failed")
		return
	}
	if count > 0 {
		signed = true
	}
	return
}
