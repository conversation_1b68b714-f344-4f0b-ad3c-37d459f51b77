package service

import (
	"database/sql"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gorm.io/gorm"
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	machineModel "server/pkg/machine/model"
	"server/pkg/user/model"
)

// UpdateSetting 修改设置
func (svc *UserService) UpdateSetting(uid int, req model.UpdateSettingRequest) error {
	if uid < 1 {
		return nil
	}
	log := svc.l.With<PERSON>ield("method", "UpdateSetting")
	log.WithField("uid", uid).WithField("req", req).Info("to update user setting")

	update := make(map[string]interface{})

	for k, v := range req {
		switch k {
		case "notify_instance_warning_status":
			value, err := svc.transNotifyInstanceWarningStatus(v)
			if err != nil {
				return err
			}
			// 只有开启通知的时候才验证是否绑定微信
			if value == constant.NotifyInstanceWarningStatusNormalOpened {
				user, err := svc.FindByUserId(uid)
				if err != nil {
					log.WithField("uid", uid).ErrorE(err, "find user failed")
					return err
				}
				if user.OpenId == "" {
					return biz.ErrNeedBindWeixinOpenID
				}
			}
			update["notify_instance_warning_status"] = value
		case "notify_balance_warning_status":
			value, err := svc.transNotifyBalanceWarningStatus(v)
			if err != nil {
				return err
			}
			// 只有开启通知的时候才验证是否绑定微信
			if value == constant.NotifyBalanceWarningStatusNormalOpened {
				user, err := svc.FindByUserId(uid)
				if err != nil {
					log.WithField("uid", uid).ErrorE(err, "find user failed")
					return err
				}
				if user.OpenId == "" {
					return biz.ErrNeedBindWeixinOpenID
				}
			}
			update["notify_balance_warning_status"] = value
			update["last_notify_balance_warning_time"] = sql.NullTime{}
			update["notify_balance_warning_count"] = 0
		case "notify_balance_warning_money":
			value, err := svc.transNotifyBalanceWarningMoney(v)
			if err != nil {
				return err
			}
			update["notify_balance_warning_money"] = value
			update["last_notify_balance_warning_time"] = sql.NullTime{}
			update["notify_balance_warning_count"] = 0
		default:
			return biz.ErrInvalidRequestParams
		}
	}

	err := db_helper.InsertOrUpdateOne(
		db_helper.QueryDefinition{
			ModelDefinition: &model.UserSetting{},
			Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{
				"uid": uid,
			}},
			InsertPayload: update,
		}, &model.UserSetting{}).GetError()
	if err != nil {
		log.WithField("uid", uid).ErrorE(err, "update user setting failed: %+v", update)
		return biz.ErrDatabaseError
	}
	log.WithField("uid", uid).WithField("payload", update).Info("update user setting successfully")

	return nil
}

// transNotifyInstanceWarningStatus 转换通知实例预警状态
func (svc *UserService) transNotifyInstanceWarningStatus(v interface{}) (interface{}, error) {
	status := constant.NotifyInstanceWarningStatus(cast.ToInt(v))
	if !status.Valid() {
		return nil, biz.ErrInvalidRequestParams
	}
	return status, nil
}

// transNotifyBalanceWarningStatus 转换通知余额预警状态
func (svc *UserService) transNotifyBalanceWarningStatus(v interface{}) (interface{}, error) {
	status := constant.NotifyBalanceWarningStatus(cast.ToInt(v))
	if !status.Valid() {
		return nil, biz.ErrInvalidRequestParams
	}
	return status, nil
}

// transNotifyBalanceWarningStatus 转换通知余额预警触发金额
func (svc *UserService) transNotifyBalanceWarningMoney(v interface{}) (interface{}, error) {
	value := cast.ToInt(v)
	if value < 0 {
		return nil, biz.ErrInvalidRequestParams
	}
	return value, nil
}

// GetSetting 获取用户设置
func (svc *UserService) GetSetting(uid int) (*model.UserSetting, error) {
	if uid < 1 {
		return nil, nil
	}

	setting := &model.UserSetting{
		UID: uid,
	}

	err := db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: &model.UserSetting{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uid": uid,
			},
		},
	}, setting).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			setting, err = svc.insertDefaultUserSetting(uid)
			if err != nil {
				return setting, nil
			}
			return setting, nil
		}
		svc.l.WithField("uid", uid).ErrorE(err, "get user setting failed")
		return nil, biz.ErrDatabaseError
	}

	return setting, nil
}

// GetSettings 批量获取用户设置
func (svc *UserService) GetSettings(uids []int) ([]model.UserSetting, error) {
	settings := make([]model.UserSetting, 0, len(uids))

	if len(uids) == 0 {
		return settings, nil
	}

	err := db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.UserSetting{},
		Filters: db_helper.QueryFilters{
			InFilters: []db_helper.In{
				{
					Key:   "uid",
					InSet: uids,
				},
			},
		},
	}, &settings).GetError()
	if err != nil {
		svc.l.ErrorE(err, "get user setting failed")
		return nil, biz.ErrDatabaseError
	}

	return settings, nil
}

// FollowMachine 订阅空闲 GPU
func (svc *UserService) FollowMachine(uid int, req model.FollowMachineRequest) error {
	if uid < 1 {
		return nil
	}

	user, err := svc.FindByUserId(uid)
	if err != nil {
		svc.l.WithField("uid", uid).ErrorE(err, "find user failed")
		return err
	}
	if user.OpenId == "" {
		return biz.ErrNeedBindWeixinOpenID
	}

	// 预计一个请求里面不会有非常多的参数（订阅空闲 GPU），所以没必要批量查询和使用事务，让处理逻辑简单点
	for _, r := range req {
		err := svc.followMachine(uid, r)
		if err != nil {
			svc.l.WithField("uid", uid).ErrorE(err, "follow machine failed")
			return err
		}
	}

	return nil
}

// followMachine 订阅空闲 GPU
func (svc *UserService) followMachine(uid int, req model.FollowMachineParams) error {
	if req.NotifyIdleGPUNum < 1 {
		return biz.ErrInvalidRequestParams
	}

	machine, err := svc.machine.Get(req.MachineID)
	if err != nil {
		// 这个地方调用 machine.Get 后如果记录不存在也会返回 DatabaseError，返回给前端不合适
		// 所以返回请求参数错误，如果真的是 DB 错误的话通过查日志解决
		svc.l.WithField("uid", uid).WithField("machine_id", req.MachineID).ErrorE(err, "get machine failed")
		return biz.ErrInvalidRequestParams
	}

	if req.NotifyIdleGPUNum > uint(machine.GpuNumber) {
		req.NotifyIdleGPUNum = uint(machine.GpuNumber)
	}

	err = db_helper.InsertOrUpdateOne(
		db_helper.QueryDefinition{
			ModelDefinition: &model.UserFollowMachine{},
			Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{
				"uid":        uid,
				"machine_id": req.MachineID,
			}},
			InsertPayload: map[string]interface{}{
				"notify_idle_gpu_num": req.NotifyIdleGPUNum,
				"status":              1,
			},
		}, &model.UserFollowMachine{}).GetError()
	if err != nil {
		svc.l.WithField("uid", uid).WithField("machine_id", req.MachineID).ErrorE(err, "user follow machine failed")
		return biz.ErrDatabaseError
	}

	return nil
}

// UnfollowMachine 取消订阅空闲 GPU
func (svc *UserService) UnfollowMachine(uid int, req model.UnfollowMachineRequest) error {
	if uid < 1 {
		return nil
	}

	_, err := svc.machine.Get(req.MachineID)
	if err != nil {
		// 这个地方调用 machine.Get 后如果记录不存在也会返回 DatabaseError，返回给前端不合适
		// 所以返回请求参数错误，如果真的是 DB 错误的话通过查日志解决
		svc.l.WithField("uid", uid).WithField("machine_id", req.MachineID).ErrorE(err, "get machine failed")
		return biz.ErrInvalidRequestParams
	}

	err = db_helper.InsertOrUpdateOne(
		db_helper.QueryDefinition{
			ModelDefinition: &model.UserFollowMachine{},
			Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{
				"uid":        uid,
				"machine_id": req.MachineID,
			}},
			InsertPayload: map[string]interface{}{
				"notify_idle_gpu_num": 0,
				"status":              0,
			},
		}, &model.UserFollowMachine{}).GetError()
	if err != nil {
		svc.l.WithField("uid", uid).WithField("machine_id", req.MachineID).ErrorE(err, "user unfollow machine failed")
		return biz.ErrDatabaseError
	}

	return nil
}

// GetAllFollowedMachines 获取订阅空闲 GPU 的记录（开启状态）
func (svc *UserService) GetAllFollowedMachines() ([]model.UserFollowMachine, error) {
	followed := make([]model.UserFollowMachine, 0)

	err := db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.UserFollowMachine{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"status": 1,
			},
		},
		NoLimit: true,
	}, &followed).GetError()
	if err != nil {
		svc.l.ErrorE(err, "get user followed machines failed")
		return nil, biz.ErrDatabaseError
	}

	return followed, nil
}

// GetFollowedMachines 获取用户订阅空闲 GPU 的记录（简单结构）
func (svc *UserService) GetFollowedMachines(uid int) ([]model.UserFollowMachine, error) {
	if uid < 1 {
		return nil, nil
	}

	followed := make([]model.UserFollowMachine, 0)

	err := db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.UserFollowMachine{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uid":    uid,
				"status": 1,
			},
		},
		NoLimit: true,
	}, &followed).GetError()
	if err != nil {
		svc.l.WithField("uid", uid).ErrorE(err, "get user followed machines failed")
		return nil, biz.ErrDatabaseError
	}

	return followed, nil
}

// GetFollowedMachineList 获取用户订阅空闲 GPU 的列表，给前端用
func (svc *UserService) GetFollowedMachineList(uid int) ([]model.GetFollowedMachineListReply, error) {
	if uid < 1 {
		return nil, nil
	}

	followed := make([]model.UserFollowMachine, 0)

	err := db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.UserFollowMachine{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uid":    uid,
				"status": 1,
			},
		},
		NoLimit: true,
	}, &followed).GetError()
	if err != nil {
		svc.l.WithField("uid", uid).ErrorE(err, "get user followed machines failed")
		return nil, biz.ErrDatabaseError
	}

	res := make([]model.GetFollowedMachineListReply, 0, len(followed))

	if len(followed) == 0 {
		return res, nil
	}

	machineIDs := make([]string, 0, len(followed))

	for _, f := range followed {
		machineIDs = append(machineIDs, f.MachineID)
	}

	machines, err := svc.machine.GetMachineByIDs(machineIDs)
	if err != nil {
		svc.l.WithField("uid", uid).ErrorE(err, "get machines failed")
		return nil, err
	}

	machineIDRelate := make(map[string]*machineModel.Machine)

	for _, machine := range machines {
		machineIDRelate[machine.MachineID] = machine
	}

	for _, f := range followed {
		// 可能机器下架导致没有找到这台机器，需要判断下
		machine, ok := machineIDRelate[f.MachineID]
		if !ok {
			continue
		}

		r := model.GetFollowedMachineListReply{
			RegionName:       machine.RegionName,
			RegionSign:       machine.RegionSign,
			MachineAlias:     machine.MachineAlias,
			MachineID:        machine.MachineID,
			NotifyIdleGPUNum: f.NotifyIdleGPUNum,
		}

		res = append(res, r)
	}

	return res, nil
}

// GetFollowedMachineCount 获取用户订阅空闲 GPU 的记录数
func (svc *UserService) GetFollowedMachineCount(uid int) (*model.GetFollowedMachineCountReply, error) {
	if uid < 1 {
		return nil, nil
	}

	var count int64

	err := db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: &model.UserFollowMachine{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uid":    uid,
				"status": 1,
			},
		},
		NoLimit: true,
	}, &count).GetError()
	if err != nil {
		svc.l.WithField("uid", uid).ErrorE(err, "get user followed machines failed")
		return nil, biz.ErrDatabaseError
	}

	return &model.GetFollowedMachineCountReply{
		Count: count,
	}, nil
}

// insertDefaultUserSetting 默认开启实例到期预警、释放预警 默认关闭余额预警通知
func (svc *UserService) insertDefaultUserSetting(uid int) (setting *model.UserSetting, err error) {
	setting = &model.UserSetting{
		UID:                         uid,
		NotifyInstanceWarningStatus: constant.NotifyInstanceWarningStatusNormalOpened,
		NotifyBalanceWarningStatus:  constant.NotifyBalanceWarningStatusClosed,
	}
	err = db_helper.InsertOne(
		db_helper.QueryDefinition{
			ModelDefinition: &model.UserSetting{},
			InsertPayload:   setting,
		}).GetError()
	if err != nil {
		svc.l.WithField("uid", uid).ErrorE(err, "insert user setting failed")
		err = biz.ErrDatabaseError
		return
	}
	return
}
