package controller

import (
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	h "server/pkg/http"
	"server/pkg/libs"
	"server/pkg/logger"
	"server/pkg/user/model"
	"server/plugin/redis_plugin"

	_ "server/pkg/db_helper" // import for swagger

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// InitAdmin godoc
// @Summary 初始化系统管理员
// @Description author: yff
// @Tags  admin
// @Accept  json
// @Produce  json
// @Param req body model.ParamsInitAdmin true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /admin/v1/init_admin [post]
func (uc *UserController) InitAdmin(c *gin.Context) {
	var req model.ParamsInitAdmin
	if err := c.<PERSON>(&req); err != nil {
		uc.l.<PERSON>("err", err).Error("InitAdmin invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	userId, err := uc.userService.InitAdmin(&req)
	if err != nil {
		uc.l.Info("Init admin failed.")
		h.SendError(c, err)
		return
	}

	h.SendOK(c, map[string]interface{}{
		"id": userId,
	})

	ro := model.OperateRecord{
		UID:           userId,
		Operate:       constant.OperateSignupAdmin,
		PayloadEntity: &req,
	}
	err = ro.OperateRecordCreate(c)
	if err != nil {
		uc.l.WarnE(err, "insert operate record failed")
	}
}

// NewAdminLogin godoc
// @Summary 管理员登录(三次错误跳验证码)
// @Description author: yff
// @Tags admin
// @Accept  json
// @Produce  json
// @Param req body model.LoginRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /admin/v1/new_login [post]
func (uc *UserController) NewAdminLogin(c *gin.Context) {
	var req model.LoginRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("user login invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	// 1: 进行错误次数计数, PV为错误次数
	pv, _ := uc.plugin.PV(redis_plugin.IpPVKeyPrefix, req.Phone)
	if pv >= constant.LoginFailedPVLimit {
		err := uc.userService.ForbiddenUserLogin(req.Phone)
		if err != nil {
			uc.l.WithField("err", err).Info("Forbidden user login failed.")
			h.SendPVError(c, pv+1, biz.ErrInternalError)
			return
		}
		uc.l.WithField("pv", pv).WithError(err).Info("User login over limit.")
		h.SendPVError(c, pv+1, biz.ErrLoginIsLimitedFmt.New().Format(constant.LoginFailedPVLimit, constant.LoginFailedLimitTimeout.Minutes()))
		return
	}

	// 2: 连续输入错误的计数的过期时间. 间隔5分钟内出错就算连续
	err := uc.plugin.PVIncrement(redis_plugin.IpPVKeyPrefix, req.Phone, constant.FailedConsequentInterval)
	if err != nil {
		uc.l.WithField("err", err).Info("Login PVIncrement in redis failed.")
		h.SendError(c, biz.ErrInternalError)
		return
	}

	// 3: 校验用户权限(如果非管理员则限制登录)
	userInfo, err := uc.userService.FindByUserPhone(req.Phone)
	if err != nil {
		err = biz.ErrGetUserFailed
		uc.l.WithField("phone", req.Phone).WithError(err).Info("User not exist")
		h.SendPVError(c, pv+1, biz.ErrGetUserFailed)
		return
	}
	if !userInfo.IsAdmin {
		err = biz.ErrUserNotAdmin
		uc.l.WithFields(logger.Fields{
			"phone":    req.Phone,
			"password": req.Password,
			"is_admin": userInfo.IsAdmin,
		}).WithError(err).Info("User is forbidden to login")
		h.SendPVError(c, pv+1, biz.ErrUserNotAdmin)
		return
	}

	// 4: 用户进行帐号密码登录(当pv<3的时候)
	if pv < 3 {
		userInfo, err = uc.userService.LoginAndGet(req.Phone, req.Password)
		if err != nil {
			uc.l.WithField("err", err).Info("User login failed.")
			h.SendPVError(c, pv+1, err)
			return
		}
	}

	// 5: 当pv大于等于3的时候就需要进行验证码的校验
	if pv >= 3 {
		userInfo, err = uc.userService.LoginWithCaptcha(req.Phone, req.Password, req.PictureID)
		if err != nil {
			uc.l.WithField("err", err).Info("User login failed.")
			h.SendPVError(c, pv+1, err)
			return
		}
	}

	// 6: 用户登录成功后清除之前的计数
	_ = uc.plugin.PVClear(req.Phone)
	_ = uc.plugin.ClearLoginLimit(req.Phone)

	// 7: 生成 ticket 并将 ticket 存入 redis 中
	ticket := libs.GenerateTicket(userInfo.ID)
	err = uc.plugin.SetTicket(ticket, userInfo.ID)
	if err != nil {
		uc.l.WithField("err", err).Info("Set ticket failed.")
		h.SendError(c, biz.ErrSetTicketFailed)
		return
	}

	// 8. 登录成功, 返回信息.
	h.SendOK(c, map[string]interface{}{"ticket": ticket, "user": userInfo})

	uc.userService.OperateRecordCreateForLogin(c, userInfo.ID, "", constant.OperateLogin, req)
	return
}

// AdminLogin godoc
// @Summary 管理员登录
// @Description author: yff
// @Tags user
// @Accept  json
// @Produce  json
// @Param req body model.LoginCaptchaRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /admin/v1/login [post]
func (uc *UserController) AdminLogin(c *gin.Context) {
	var req model.LoginCaptchaRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("user login invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	// 1: 校验用户权限
	userInfo, err := uc.userService.FindByUserPhone(req.Phone)
	if err != nil {
		h.SendError(c, biz.ErrGetUserFailed)
		uc.l.WithField("phone", req.Phone).WithError(err).Info("User not exist")
		return
	}
	if !userInfo.IsAdmin {
		err = biz.ErrUserNotAdmin
		uc.l.WithFields(logger.Fields{
			"phone":    req.Phone,
			"password": req.Password,
			"is_admin": userInfo.IsAdmin,
		}).WithError(err).Info("User is forbidden to login")
		h.SendError(c, biz.ErrUserNotAdmin)
		return
	}

	// 2: 进行错误次数计数, PV为错误次数
	pv, _ := uc.plugin.PV(redis_plugin.IpPVKeyPrefix, req.Phone)
	if pv >= constant.LoginFailedPVLimit {
		err := uc.userService.ForbiddenUserLogin(req.Phone)
		if err != nil {
			h.SendError(c, biz.ErrInternalError)
			return
		}
		h.SendError(c, biz.ErrLoginIsLimitedFmt.New().Format(constant.LoginFailedPVLimit, constant.LoginFailedLimitTimeout.Minutes()))
		return
	}

	// 3: 连续输入错误的计数的过期时间. 间隔5分钟内出错就算连续
	PVErr := uc.plugin.PVIncrement(redis_plugin.IpPVKeyPrefix, req.Phone, constant.FailedConsequentInterval)
	if PVErr != nil {
		uc.l.WithField("err", PVErr).Info("Login PVIncrement in redis failed.")
		h.SendError(c, biz.ErrInternalError)
		return
	}

	// 4: 校验验证码是否输入正确
	right, err := uc.captchaPlugin.CheckCaptchaValueIsRight(req.CaptchaID, req.CaptchaValue)
	if err != nil {
		if err == redis.Nil {
			h.SendError(c, biz.ErrCaptchaExpired)
			return
		}
		uc.l.WithField("err", err).Info("Captcha plugin check captcha value failed.")
		h.SendError(c, biz.ErrCheckCaptchaFailed)
		return
	}

	if !right {
		if pv > 4 {
			err = uc.plugin.SetLoginLimit(req.Phone, constant.LoginFailedLimitTimeout)
			if err != nil {
				uc.l.WithField("err", err).Info("Set login limit in redis failed.")
				h.SendError(c, biz.ErrInternalError)
				return
			}
			h.SendError(c, biz.ErrLoginIsLimitedFmt.New().Format(constant.LoginFailedPVLimit, constant.LoginFailedLimitTimeout.Minutes()))
			return
		}
		h.SendError(c, biz.ErrRequestCaptchaIsWrong)
		return
	}

	// 5: 用户进行登录(帐号密码校验)
	AdminUser, err := uc.userService.LoginAndGet(req.Phone, req.Password)
	if err != nil {
		// 如果是手机号密码错误, 则连续错误计数+1. 其他错误不增加连续错误计数.
		if err != biz.ErrInternalError {
			if pv > 5 {
				err = uc.plugin.SetLoginLimit(req.Phone, constant.LoginFailedLimitTimeout)
				if err != nil {
					uc.l.WithField("err", err).Info("Set login limit in redis failed.")
					h.SendError(c, biz.ErrInternalError)
					return
				}
				h.SendError(c, biz.ErrLoginIsLimitedFmt.New().Format(constant.LoginFailedPVLimit, constant.LoginFailedLimitTimeout.Minutes()))
				return
			}
		}
		uc.l.WithField("err", err).Info("User login failed.")
		h.SendError(c, biz.ErrUserPhoneOrPasswordWrong)
		return
	}

	// 6: 用户登录成功后清除之前的计数
	_ = uc.plugin.PVClear(req.Phone)
	_ = uc.plugin.ClearLoginLimit(req.Phone)

	// 7: 生成 ticket 并将 ticket 存入 redis 中
	ticket := libs.GenerateTicket(AdminUser.ID)
	err = uc.plugin.SetTicket(ticket, AdminUser.ID)
	if err != nil {
		uc.l.WithField("err", err).Info("Set ticket failed.")
		h.SendError(c, biz.ErrSetTicketFailed)
		return
	}

	// 8. 登录成功, 返回信息.
	h.SendOK(c, map[string]interface{}{"ticket": ticket, "user": AdminUser})

	uc.userService.OperateRecordCreateForLogin(c, userInfo.ID, "", constant.OperateLogin, req)
	return

}

// AdminFastLogin godoc
// @Summary 管理员短信登录
// @Description author: yff
// @Tags admin
// @Accept  json
// @Produce  json
// @Param req body model.FastLoginRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /admin/v1/fast_login [post]
func (uc *UserController) AdminFastLogin(c *gin.Context) {
	var req model.FastLoginRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("User fast login invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	// 1: 进行错误次数判断, PV为错误次数
	pv, _ := uc.plugin.PV(redis_plugin.IpPVKeyPrefix, req.Phone)
	if pv >= constant.LoginFailedPVLimit {
		err := uc.userService.ForbiddenUserLogin(req.Phone)
		if err != nil {
			h.SendError(c, biz.ErrInternalError)
			return
		}
		h.SendError(c, biz.ErrLoginIsLimitedFmt.New().Format(constant.LoginFailedPVLimit, constant.LoginFailedLimitTimeout.Minutes()))
		return
	}

	// 2: 验证手机号是否符合标准
	phone, err := uc.userService.PhoneAndAreaCheck(req.PhoneArea, req.Phone)
	if err != nil {
		h.SendError(c, err)
		return
	}

	// 3: 检查用户状态,用户未注册或处于禁用状态都禁止登录
	userInfo, err := uc.userService.FindByUserPhone(req.Phone)
	if err != nil {
		// 用户未注册提示先进行注册
		if err == gorm.ErrRecordNotFound {
			uc.l.WithField("phone", req.Phone).WithError(err).Info("User not exist.")
			h.SendError(c, biz.ErrUserNotRegister)
			return
		}
		uc.l.WithField("phone", req.Phone).Info("search user in db failed.")
		h.SendError(c, biz.ErrGetUserFailed)
		return
	}

	if userInfo.Status == model.Disable {
		uc.l.WithFields(logger.Fields{
			"phone":  req.Phone,
			"status": userInfo.Status,
		}).WithField("err", err).Info("User status in disable can not login.")
		h.SendError(c, biz.ErrUserDisableLogin)
		return
	}

	if !userInfo.IsAdmin {
		h.SendPVError(c, pv+1, biz.ErrUserNotAdmin)
		return
	}

	// 4: 校验短信验证码(校验短信验证码的时间,过期的话就失效)
	right, err := uc.plugin.CheckVCodeValueIsRight(phone, req.VCode)
	if err != nil {
		uc.l.WithField("err", err).Info("Captcha plugin check captcha value failed.")
		h.SendError(c, biz.ErrCheckUserVCodeFailed)
		return
	}

	if !right {
		if pv > 4 {
			err = uc.userService.ForbiddenUserLogin(req.Phone)
			if err != nil {
				h.SendError(c, biz.ErrInternalError)
				return
			}
			h.SendError(c, biz.ErrLoginIsLimitedFmt.New().Format(constant.LoginFailedPVLimit, constant.LoginFailedLimitTimeout.Minutes()))
			return
		}
		uc.l.WithFields(logger.Fields{
			"phone":  req.Phone,
			"v_code": req.VCode,
		}).WithError(err).Info("User VCode wrong.")
		h.SendError(c, biz.ErrCheckUserVCodeFailed)
		return
	}

	// 记录登录时间
	user, err := uc.userService.UserFastLogin(userInfo.ID)
	if err != nil {
		uc.l.WithField("err", err).Info("Set user login_at failed.")
		return
	}

	// 5: 用户登录成功后清除之前的计数
	_ = uc.plugin.PVClear(req.Phone)
	_ = uc.plugin.ClearLoginLimit(req.Phone)

	// 6: 开始执行后续 ticket 流程. 前端根据 ticket 访问 passport 接口获取 token.
	ticket := libs.GenerateTicket(userInfo.ID)
	err = uc.plugin.SetTicket(ticket, userInfo.ID)
	if err != nil {
		uc.l.WithField("err", err).Info("Set ticket into redis failed.")
		h.SendError(c, biz.ErrSetTicketFailed)
		return
	}

	// 7. 登录成功, 返回信息.
	h.SendOK(c, map[string]interface{}{"ticket": ticket, "user": user})

	uc.userService.OperateRecordCreateForLogin(c, userInfo.ID, "", constant.OperateLoginCode, req)
}

// CheckUserExist godoc
// @Summary 检查用户是否存在
// @Description  author: yff
// @Description limit-roles:admin,finance,customerService
// @Tags  admin
// @Accept  json
// @Produce  json
// @Param req body model.UserPhoneRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /admin/v1/user/check_user_exist [post]
func (uc *UserController) CheckUserExist(c *gin.Context) {
	var req model.UserPhoneRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("CheckUserExist invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	exist, err := uc.userService.CheckUserExistFromRO(req.Phone)
	if err != nil {
		h.SendError(c, err)
		return
	}
	h.SendOK(c, map[string]interface{}{
		"exist": exist,
	})
	return
}

// CreateUser godoc
// @Summary 管理员创建用户
// @Description author: yff
// @Description limit-roles:admin,finance,customerService
// @Tags  admin
// @Accept  json
// @Produce  json
// @Param req body model.ParamsCreateUser true "请求体"
// @Success 200 {object} h.SendOKResponse{data=model.User} "Success"
// @Router /admin/v1/user/create [post]
func (uc *UserController) CreateUser(c *gin.Context) {
	var req model.ParamsCreateUser
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("create user list invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	if req.Email != "" {
		if !libs.TestRegexpEmail(req.Email) {
			h.SendError(c, biz.ErrEmailFormatWrong)
			return
		}

		exist, err := uc.userService.CheckEmailExist(req.Email)
		if err != nil {
			h.SendError(c, biz.ErrInvalidRequestParams)
			return
		}
		if exist {
			h.SendError(c, biz.ErrEmailAlreadyInUse)
			return
		}
	}

	_, err := uc.userService.PhoneAndAreaCheck(req.PhoneArea, req.Phone)
	if err != nil {
		h.SendError(c, err)
		return
	}

	req.PhoneArea = libs.PhoneAreaCodeCorrect(req.PhoneArea)

	req.IP = c.ClientIP()
	user, err := uc.userService.CreateUser(&req)
	if err != nil {
		h.SendError(c, err)
		return
	}
	h.SendOK(c, map[string]interface{}{
		"id": user.ID,
	})

	ro := model.OperateRecord{
		UID:           user.ID,
		Operate:       constant.OperateSignupAdmin,
		PayloadEntity: &req,
	}
	err = ro.OperateRecordCreate(c)
	if err != nil {
		uc.l.WarnE(err, "insert operate record failed")
	}
}

// GetUserDetail godoc
// @Summary 获取用户详情
// @Description author: yff
// @Description limit-roles:admin,finance,customerService
// @Tags admin
// @Accept  json
// @Produce  json
// @Param req body model.UserIdRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /admin/v1/user/detail [get]
func (uc *UserController) GetUserDetail(c *gin.Context) {
	var req model.UserIdRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("Get user detail invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	userInfo, err := uc.userService.FindByUserIdFromRO(req.Id)
	if err != nil {
		h.SendError(c, err)
		return
	}
	h.SendOK(c, userInfo)
}

// GetUserList godoc
// @Summary 获取用户列表
// @Description author: yff
// @Description limit-roles:admin,finance,customerService
// @Tags  admin
// @Accept  json
// @Produce  json
// @Param req body model.GetUserListRequest true "请求体"
// @Success 200 {object} h.SendOKResponse{data=db_helper.PagedData{list=model.User}} "Success"
// @Router /admin/v1/user/list [get]
func (uc *UserController) GetUserList(c *gin.Context) {
	var req model.GetUserListRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("get user list invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	paged, _, err := uc.userService.GetUserList(db_helper.GlobalDBConnForRead(), &model.User{
		Username: req.Username,
		Phone:    req.Phone,
		UUID:     req.UUID,
	}, &req.GetPagedRangeRequest)
	if err != nil {
		uc.l.WithField("err", err).Error("get user paged failed.")
		h.SendError(c, err)
		return
	}
	h.SendOK(c, paged)
}

// UpdateUser godoc
// @Summary 管理员编辑用户
// @Description  author: yff.
// @Description limit-roles:admin,finance,customerService
// @Tags  admin
// @Accept  json
// @Produce  json
// @Param req body model.ParamsUpdateUser true "Add account"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /admin/v1/user/update [put]
func (uc *UserController) UpdateUser(c *gin.Context) {
	var req model.ParamsUpdateUser
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("Update user invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	_, err := uc.userService.PhoneAndAreaCheck(req.PhoneArea, req.Phone)
	if err != nil {
		h.SendError(c, err)
		return
	}

	req.PhoneArea = libs.PhoneAreaCodeCorrect(req.PhoneArea)

	// check email format
	if req.Email != "" && !libs.TestRegexpEmail(req.Email) {
		h.SendError(c, biz.ErrEmailFormatWrong)
		return
	}

	exist, err := uc.userService.CheckEmailExist(req.Email)
	if err != nil {
		return
	}
	if exist {
		h.SendError(c, biz.ErrEmailAlreadyInUse)
		return
	}

	userInfo, err := uc.userService.UpdateUser(&req)
	if err != nil {
		uc.l.WithField("req", req).WithError(err).Info("User update failed.")
		h.SendError(c, err)
		return
	}
	h.SendOK(c, userInfo)
}

// EnableUser godoc
// @Summary 管理员启用用户
// @Description author: yff
// @Description limit-roles:admin,finance,customerService
// @Tags admin
// @Accept  json
// @Produce  json
// @Param req body model.UserIdRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /admin/v1/user/enable [post]
func (uc *UserController) EnableUser(c *gin.Context) {
	var req model.UserIdRequest
	if err := c.ShouldBind(&req); err != nil || req.Id == 0 {
		uc.l.WarnE(err, "Enable user invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	err := uc.userService.EnableUser(req.Id)
	if err != nil {
		uc.l.WithField("err", err).Info("Enable user failed.")
		h.SendError(c, err)
	}

	h.SendOK(c, nil)
}

// DisableUser godoc
// @Summary 管理员禁用用户
// @Description author: yff
// @Description limit-roles:admin,finance,customerService
// @Tags admin
// @Accept  json
// @Produce  json
// @Param req body model.UserIdRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /admin/v1/user/disable [post]
func (uc *UserController) DisableUser(c *gin.Context) {
	var req model.UserIdRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("Id", req.Id).WithError(err).Error("Enable user invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	err := uc.userService.DisableUser(req.Id)
	if err != nil {
		uc.l.WithField("err", err).Info("disable user failed.")
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)
}

type UpdateUserAuthorityRequest struct {
	Id           int  `form:"id" json:"id"`
	CloneToOther bool `json:"clone_to_other"` //展示克隆给他人
}

// UpdateUserAuthority godoc
// @Summary 更新用户相关权限
// @Description  author: yff
// @Description limit-roles:admin,finance,customerService
// @Tags  admin
// @Accept  json
// @Produce  json
// @Param req body UpdateUserAuthorityRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /admin/v1/user/authority [post]
func (uc *UserController) UpdateUserAuthority(c *gin.Context) {
	var req UpdateUserAuthorityRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("Id", req.Id).WithError(err).Error("UserController.UpdateUserAuthority invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	// 更新用户相关权限
	err := uc.userService.UpdateAuthority(req.Id, req.CloneToOther)
	if err != nil {
		uc.l.WithField("err", err).Info("update user Authority failed.")
		h.SendError(c, biz.ErrIdleJobUserNotAuthority)
		return
	}
	h.SendOK(c, nil)
}

// SetUserAdmin godoc
// @Summary 超级管理员设置普通用户为管理员
// @Description  author: yff
// @Tags admin
// @Accept  json
// @Produce  json
// @Param req body model.UserIdRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /admin/v1/user/admin_set [put]
func (uc *UserController) SetUserAdmin(c *gin.Context) {
	var req model.UserIdRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("Id", req.Id).WithError(err).Error("Enable user invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	userInfo := h.GetUserInfo(c)
	if !userInfo.IsSuperAdmin {
		h.SendError(c, biz.ErrAuthorizeFailed)
		return
	}

	err := uc.userService.SetNormalUserToAdmin(req.Id)
	if err != nil {
		uc.l.WithField("err", err).Info("Set user admin failed.")
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)
}

// CancelUserAdmin godoc
// @Summary 超级管理员设置普通用户为管理员
// @Description  author: yff
// @Tags admin
// @Accept  json
// @Produce  json
// @Param req body model.UserIdRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /admin/v1/user/admin_cancel [put]
func (uc *UserController) CancelUserAdmin(c *gin.Context) {
	var req model.UserIdRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("Id", req.Id).WithError(err).Error("Enable user invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	// only super admin can operate
	userInfo := h.GetUserInfo(c)
	if !userInfo.IsSuperAdmin {
		h.SendError(c, biz.ErrAuthorizeFailed)
		return
	}

	err := uc.userService.SetAdminToNormalUser(req.Id)
	if err != nil {
		uc.l.WithField("err", err).Info("Set user admin failed.")
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)
}

// SetUserInstanceNumLimit godoc
// @Summary 设置用户可创建实例数量限制
// @Description  author: lwh
// @Tags admin
// @Accept  json
// @Produce  json
// @Param req body model.SetUserInstanceNumLimitReq true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /admin/v1/tools/user/instance/limit [post]
func (uc *UserController) SetUserInstanceNumLimit(c *gin.Context) {
	var req model.SetUserInstanceNumLimitReq
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithError(err).Error("bind failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	err := uc.userService.UserUpdateMaxInstanceNum(req.Phone, req.MaxNum)
	if err != nil {
		h.SendError(c, biz.ErrUserSetMaxInstanceNum)
		return
	}

	h.SendOK(c, nil)
}
