package controller

import (
	"github.com/gin-gonic/gin"
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	_ "server/pkg/db_helper"
	h "server/pkg/http"
	"server/pkg/user/model"
)

// UpdateSetting godoc
// @Summary 修改设置
// @Description author: liang<PERSON><PERSON>
// @Description key 目前支持的有 notify_instance_warning_status（实例预警开关 int 0关闭 1开启）、notify_balance_warning_status（余额预警开关 int 0关闭 1开启）、notify_balance_warning_money（余额低于多少时触发预警 int 大于0）。
// @Tags userSetting
// @Accept  json
// @Produce  json
// @Param req body model.UpdateSettingRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success
// @Router /v1/user/setting [post]
func (uc *UserController) UpdateSetting(c *gin.Context) {
	var req model.UpdateSettingRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.ErrorE(err, "bind request failed")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	if len(req) == 0 {
		h.SendOK(c, nil)
		return
	}

	user := h.GetUserInfo(c)

	err := uc.userService.UpdateSetting(user.UID, req)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)

	ro := model.OperateRecord{
		UID:           user.UID,
		Operate:       constant.OperateUpdateUserSetting,
		PayloadEntity: &req,
	}
	err = ro.OperateRecordCreate(c)
	if err != nil {
		uc.l.WarnE(err, "insert operate record failed")
	}
}

// GetSetting godoc
// @Summary 获取用户设置
// @Description author: liangjunmo
// @Tags userSetting
// @Accept  json
// @Produce  json
// @Success 200 {object} h.SendOKResponse{data=model.UserSetting} "Success"
// @Router /v1/user/setting [get]
func (uc *UserController) GetSetting(c *gin.Context) {
	user := h.GetUserInfo(c)
	setting, err := uc.userService.GetSetting(user.UID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	h.SendOK(c, setting)
}

// FollowMachine godoc
// @Summary 订阅空闲 GPU
// @Description author: liangjunmo
// @Description 订阅空闲 GPU
// @Tags userSetting
// @Accept  json
// @Produce  json
// @Param req body model.FollowMachineRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success
// @Router /v1/user/machine/follow [post]
func (uc *UserController) FollowMachine(c *gin.Context) {
	var req model.FollowMachineRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.ErrorE(err, "bind request failed")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	user := h.GetUserInfo(c)

	err := uc.userService.FollowMachine(user.UID, req)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)
}

// UnfollowMachine godoc
// @Summary 取消订阅空闲 GPU
// @Description author: liangjunmo
// @Description 取消订阅空闲 GPU
// @Tags userSetting
// @Accept  json
// @Produce  json
// @Param req body model.UnfollowMachineRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success
// @Router /v1/user/machine/unfollow [post]
func (uc *UserController) UnfollowMachine(c *gin.Context) {
	var req model.UnfollowMachineRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.ErrorE(err, "bind request failed")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	user := h.GetUserInfo(c)

	err := uc.userService.UnfollowMachine(user.UID, req)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)
}

// GetFollowedMachineList godoc
// @Summary 订阅空闲 GPU 列表
// @Description author: liangjunmo
// @Description 订阅空闲 GPU 列表
// @Tags userSetting
// @Accept  json
// @Produce  json
// @Success 200 {object} h.SendOKResponse{data=[]model.GetFollowedMachineListReply} "Success"
// @Router /v1/user/followed_machine/list [get]
func (uc *UserController) GetFollowedMachineList(c *gin.Context) {
	user := h.GetUserInfo(c)

	followedMachines, err := uc.userService.GetFollowedMachineList(user.UID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, followedMachines)
}

// GetFollowedMachineCount godoc
// @Summary 订阅空闲 GPU 数
// @Description author: liangjunmo
// @Description 订阅空闲 GPU 列表
// @Tags userSetting
// @Accept  json
// @Produce  json
// @Success 200 {object} h.SendOKResponse{data=model.GetFollowedMachineCountReply} "Success"
// @Router /v1/user/followed_machine/count [get]
func (uc *UserController) GetFollowedMachineCount(c *gin.Context) {
	user := h.GetUserInfo(c)

	count, err := uc.userService.GetFollowedMachineCount(user.UID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, count)
}

// ContainerMonitorPushGatewaySet godoc
// @Description author: zt
// @Description 设置pushgateway监控地址
// @Tags userSetting
// @Accept  json
// @Produce  json
// @Success 200 {object} h.SendOKResponse{data=model.ContainerMonitorSetParams} "Success"
// @Router /v1/user/container_monitor [put]
func (uc *UserController) ContainerMonitorPushGatewaySet(c *gin.Context) {
	var req model.ContainerMonitorSetParams
	if err := c.ShouldBind(&req); err != nil {
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}
	u := h.GetUserInfo(c)
	req.UID = u.UID

	err := uc.userService.ContainerMonitorPushGatewaySet(&req)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)
}

// ContainerMonitorStatusSet godoc
// @Description author: zt
// @Description 设置pushgateway监控状态
// @Tags userSetting
// @Accept  json
// @Produce  json
// @Success 200 {object} h.SendOKResponse{data=model.ContainerMonitorSetParams} "Success"
// @Router /v1/user/container_monitor/status [put]
func (uc *UserController) ContainerMonitorStatusSet(c *gin.Context) {
	var req model.ContainerMonitorSetParams
	if err := c.ShouldBind(&req); err != nil {
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}
	u := h.GetUserInfo(c)
	req.UID = u.UID

	err := uc.userService.ContainerMonitorStatusSet(&req)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)
}

// ContainerMonitorGet godoc
// @Description author: zt
// @Description 获取pushgateway监控设置
// @Tags userSetting
// @Produce  json
// @Success 200 {object} h.SendOKResponse{data=model.UserContainerMonitorSetting} "Success"
// @Router /v1/user/container_monitor [get]
func (uc *UserController) ContainerMonitorGet(c *gin.Context) {
	u := h.GetUserInfo(c)

	ucms, err := uc.userService.ContainerMonitorGet(u.UID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	if ucms == nil {
		h.SendOK(c, model.UserContainerMonitorSetting{})
		return
	}

	h.SendOK(c, ucms)
}
