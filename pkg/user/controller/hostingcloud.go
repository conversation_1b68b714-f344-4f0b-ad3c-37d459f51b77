package controller

import (
	"encoding/json"
	"server/conf"
	biz "server/pkg/businesserror"
	h "server/pkg/http"
	"server/pkg/libs"
	"server/pkg/user/model"

	"github.com/gin-gonic/gin"
	"github.com/liangjunmo/gorsautil"
)

type HostingcloudGetUserDetailRequest struct {
	model.UserIdRequest
	model.UserPhoneRequest
	model.UserUUIDRequest
}

func (uc *UserController) HostingcloudGetUserDetail(c *gin.Context) {
	signature := c.GetHeader("SIGNATURE")
	if signature == "" {
		h.SendError(c, biz.ErrAuthorizeFailed)
		return
	}

	var baseReq libs.HostingcloudBaseRequest

	if err := c.ShouldBindJSON(&baseReq); err != nil {
		uc.l.WithError(err).Error("invalid request")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	publicKey, err := gorsautil.NewPublicKeyWithFile(conf.GetGlobalGsConfig().App.HostingcloudPublicKeyFile)
	if err != nil {
		uc.l.WithError(err).Error("new public key failed")
		h.SendError(c, biz.ErrServerBusy)
		return
	}

	message := gorsautil.BuildSignMessage(baseReq.Method, baseReq.Url, baseReq.Timestamp, baseReq.Random, baseReq.Data)

	if err := gorsautil.VerifySignatureWithSHA256(publicKey, message, signature); err != nil {
		uc.l.WithError(err).Error("verify signature failed")
		h.SendError(c, biz.ErrAuthorizeFailed)
		return
	}

	var req HostingcloudGetUserDetailRequest

	if err = json.Unmarshal([]byte(baseReq.Data), &req); err != nil {
		uc.l.WithError(err).Error("unmarshal data failed")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	if req.Id == 0 && req.Phone == "" && req.Uuid == "" {
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	if req.Id != 0 {
		userInfo, err := uc.userService.FindByUserId(req.Id)
		if err != nil {
			h.SendError(c, err)
			return
		}

		h.SendOK(c, userInfo)

		return
	} else if req.Phone != "" {
		userInfo, err := uc.userService.FindByUserPhone(req.Phone)
		if err != nil {
			h.SendError(c, err)
			return
		}

		h.SendOK(c, userInfo)

		return
	} else if req.Uuid != "" {
		userInfo, err := uc.userService.FindByUserUuid(req.Uuid)
		if err != nil {
			h.SendError(c, err)
			return
		}

		h.SendOK(c, userInfo)

		return
	}
}

type HostingcloudPassportRequest struct {
	Ticket string `json:"ticket"`
}

type HostingcloudPassportResponse struct {
	*model.User
	SubName string `json:"sub_name"`
}

func (uc *UserController) HostingcloudPassport(c *gin.Context) {
	signature := c.GetHeader("SIGNATURE")
	if signature == "" {
		h.SendError(c, biz.ErrAuthorizeFailed)
		return
	}

	var baseReq libs.HostingcloudBaseRequest

	if err := c.ShouldBindJSON(&baseReq); err != nil {
		uc.l.WithError(err).Error("invalid request")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	publicKey, err := gorsautil.NewPublicKeyWithFile(conf.GetGlobalGsConfig().App.HostingcloudPublicKeyFile)
	if err != nil {
		uc.l.WithError(err).Error("new public key failed")
		h.SendError(c, biz.ErrServerBusy)
		return
	}

	message := gorsautil.BuildSignMessage(baseReq.Method, baseReq.Url, baseReq.Timestamp, baseReq.Random, baseReq.Data)

	if err := gorsautil.VerifySignatureWithSHA256(publicKey, message, signature); err != nil {
		uc.l.WithError(err).Error("verify signature failed")
		h.SendError(c, biz.ErrAuthorizeFailed)
		return
	}

	var req HostingcloudPassportRequest

	if err = json.Unmarshal([]byte(baseReq.Data), &req); err != nil {
		uc.l.WithError(err).Error("unmarshal data failed")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	payload, err := uc.plugin.GetSSOTicketV2(req.Ticket)
	if err != nil {
		err = biz.ErrDatabaseError.New().Append(biz.ErrGetTicketFailed)
		uc.l.WithError(err).Error("Get userId by ticket failed.")
		h.SendError(c, biz.ErrAuthorizeFailed)
		return
	}

	user, err := uc.userService.FindByUserId(payload.UID)
	if err != nil {
		uc.l.WithError(err).Error("get user failed")
		h.SendError(c, biz.ErrAuthorizeFailed)
		return
	}

	resp := HostingcloudPassportResponse{
		User:    user,
		SubName: payload.SubName,
	}

	h.SendOK(c, resp)
	return
}
