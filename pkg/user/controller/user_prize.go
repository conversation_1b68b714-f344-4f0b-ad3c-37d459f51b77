package controller

import (
	"github.com/gin-gonic/gin"
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	h "server/pkg/http"
	"server/pkg/user/model"
)

type UserPrizeDrawRequest struct {
	PrizeType constant.UserPrizeType `json:"prize_type"`
}

// UserPrizeDraw godoc
// @Summary UserPrizeDraw
// @Description  author: yff
// @Tags  user
// @Accept  json
// @Produce  json
// @Param req body UserPrizeDrawRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/user/prize/draw [post]

func (uc *UserController) UserPrizeDraw(c *gin.Context) {
	var req UserPrizeDrawRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("UserController.UserPrizeDraw invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}
	u := h.GetUserInfo(c)
	invitationCode, err := uc.userService.SetUserPriceDrawRecord(u.UID, req.PrizeType)
	if err != nil {
		uc.l.WithField("params", req).WithError(err).Info("user prize draw failed.")
		h.SendError(c, err)
		return
	}
	h.SendOK(c, map[string]int64{"invitation_code": invitationCode})
}

// CheckUserPrizeDrawType godoc
// @Summary CheckUserPrizeDrawType
// @Description  author: yff
// @Tags  user
// @Accept  json
// @Produce  json
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/user/prize/get [get]

func (uc *UserController) CheckUserPrizeDrawType(c *gin.Context) {
	u := h.GetUserInfo(c)
	userPrizeType, err := uc.userService.CheckUserPrizeDrawType(u.UID)
	if err != nil {
		uc.l.WithField("err", err).Error("get user prize type failed.")
		h.SendError(c, err)
		return
	}
	h.SendOK(c, userPrizeType)
}

func (uc *UserController) UserPrizeDrawNew(c *gin.Context) {
	var req UserPrizeDrawRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("UserController.UserPrizeDraw invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}
	u := h.GetUserInfo(c)
	if u.SubName != "" {
		h.SendError(c, biz.ErrResourceAccessAuthFailed)
		return
	}
	invitationCode, err := uc.userService.SetUserPriceDrawRecordNew(u.UID, req.PrizeType)
	if err != nil {
		uc.l.WithField("params", req).WithError(err).Info("user prize draw failed.")
		h.SendError(c, err)
		return
	}
	h.SendOK(c, map[string]int64{"invitation_code": invitationCode})
}

func (uc *UserController) GetUserPrizeDrawType(c *gin.Context) {
	u := h.GetUserInfo(c)
	userPrizeType, err := uc.userService.GetUserPrizeDrawType(u.UID)
	if err != nil {
		uc.l.WithField("err", err).Error("get user prize type failed.")
		h.SendError(c, err)
		return
	}
	h.SendOK(c, userPrizeType)
}

func (uc *UserController) AddUserPrizeDrawWhitelist(c *gin.Context) {
	var req model.AddUserPrizeWhitelistRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("AddUserPrizeDrawWhitelist invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	err := uc.userService.AddUserPrizeDrawWhitelist(req)
	if err != nil {
		uc.l.WithField("err", err).Error("add user prize draw whitelist failed.")
		h.SendError(c, err)
		return
	}
	h.SendOK(c, nil)
}

func (uc *UserController) SetWinNum(c *gin.Context) {
	var req model.CalcWinNumRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("SetWinNum invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}
	err := uc.userService.SetWinNum(req)
	if err != nil {
		uc.l.WithField("err", err).Error("set user prize winning num failed.")
		h.SendError(c, err)
		return
	}
	h.SendOK(c, nil)
}

func (uc *UserController) GetWinNum(c *gin.Context) {
	winNum := uc.userService.GetWinNum()
	h.SendOK(c, winNum)
}
