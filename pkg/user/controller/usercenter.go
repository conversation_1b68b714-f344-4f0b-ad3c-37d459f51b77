package controller

import (
	"encoding/json"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"server/conf"
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	h "server/pkg/http"
	"server/pkg/libs"
	cap "server/pkg/libs/captcha"
	"server/pkg/logger"
	machineModel "server/pkg/machine/model"
	"server/pkg/middleware/claim"
	"server/pkg/storage"
	"server/pkg/user/model"
	"server/plugin/redis_plugin"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// Register godoc
// @Summary 用户注册
// @Description author: yff
// @Tags  user
// @Accept  json
// @Produce  json
// @Param req body model.UserRegisterRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/register [post]
func (uc *UserController) Register(c *gin.Context) {
	var req model.UserRegisterRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("Register invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	// 0.判断通行码是否正确
	var passCodeParams interface{} = req
	if req.InviteCode == "" {
		passCodeParams = model.UserRegisterRequestNoInviteCode{
			Password:  req.Password,
			PhoneArea: req.PhoneArea,
			Phone:     req.Phone,
			VCode:     req.VCode,
			Passcode:  req.Passcode,
		}
	}

	isPass := libs.ConfuseCodeAesDecrypt(passCodeParams, req.Passcode)
	if !isPass {
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	// 1.用户重置密码时需要跟用户登录校验错误次数进行区分
	registerKey := libs.StringCombination(req.Phone, "register")

	// 2.如果错误次数超过6次,那么就禁止注册5分钟
	pv, _ := uc.plugin.PV(redis_plugin.IpPVKeyPrefix, registerKey)
	if pv >= constant.LoginFailedPVLimit {
		err := uc.userService.ForbiddenUserLogin(registerKey)
		if err != nil {
			h.SendError(c, biz.ErrInternalError)
			return
		}
		h.SendError(c, biz.ErrRegisterIsLimitedFmt.New().Format(constant.LoginFailedPVLimit, constant.LoginFailedLimitTimeout.Minutes()))
		return
	}

	err := uc.plugin.PVIncrement(redis_plugin.IpPVKeyPrefix, registerKey, constant.FailedConsequentInterval)
	if err != nil {
		uc.l.WithField("err", err).Info("Login PVIncrement in redis failed.")
		h.SendError(c, biz.ErrInternalError)
		return
	}

	if strings.HasPrefix(req.Phone, "1621090") {
		h.SendError(c, biz.ErrPhoneWrongFormat)
		return
	}
	// 3.校验短信验证码,手机号带区号
	phoneWithArea, err := uc.userService.PhoneAndAreaCheck(req.PhoneArea, req.Phone)
	if err != nil {
		h.SendError(c, err)
		return
	}
	right, err := uc.plugin.CheckVCodeValueIsRight(phoneWithArea, req.VCode)
	if err != nil {
		uc.l.WithField("err", err).Info("Check vCode value failed.")
		h.SendError(c, biz.ErrCheckUserVCodeFailed)
		return
	}
	if !right {
		if pv > 4 {
			err = uc.userService.ForbiddenUserLogin(registerKey)
			if err != nil {
				h.SendError(c, biz.ErrInternalError)
				return
			}
			h.SendError(c, biz.ErrLoginIsLimitedFmt.New().Format(constant.LoginFailedPVLimit, constant.LoginFailedLimitTimeout.Minutes()))
			return
		}
		uc.l.WithFields(logger.Fields{
			"phone":  req.Phone,
			"v_code": req.VCode,
		}).WithError(err).Info("User VCode wrong.")
		h.SendError(c, biz.ErrCheckUserVCodeFailed)
		return
	}

	// 4.注册
	inviterUserID := 0
	if req.InviteCode != "" {
		inviteUserInfo, err := uc.userService.FindByUserUuid(req.InviteCode)
		if err != nil {
			if errors.Is(err, biz.ErrUserNotRegister) {
				uc.l.WithField("uuid", req.InviteCode).WarnE(err, "Get user by uuid failed.")
			} else {
				uc.l.WithField("uuid", req.InviteCode).WithError(err).Error("Get user by uuid failed.")
			}
			h.SendError(c, err)
			return
		}
		inviterUserID = inviteUserInfo.ID
	}

	params := &model.ParamsCreateUser{
		InviteUserId: inviterUserID,
		Username:     constant.GetUserName(req.Phone),
		Password:     req.Password,
		PhoneArea:    libs.PhoneAreaCodeCorrect(req.PhoneArea),
		Phone:        req.Phone,
		IP:           c.ClientIP(),
	}

	user, err := uc.userService.CreateUser(params)
	if err != nil {
		h.SendError(c, err)
		return
	}

	// clear login fail numbers in redis
	_ = uc.plugin.PVClear(registerKey)
	_ = uc.plugin.ClearLoginLimit(registerKey)
	_ = uc.plugin.PVClear(req.Phone)
	_ = uc.plugin.ClearLoginLimit(req.Phone)

	// generate ticket --> passport --> token
	ticket := libs.GenerateTicket(user.ID)
	err = uc.plugin.SetTicket(ticket, user.ID)
	if err != nil {
		uc.l.WithField("err", err).Info("Set ticket into redis failed.")
		h.SendError(c, biz.ErrSetTicketFailed)
		return
	}

	h.SendOK(c, map[string]interface{}{"ticket": ticket, "user": user})

	uc.userService.OperateRecordCreateForLogin(c, user.ID, "", constant.OperateLogin, req)
}

// Passport godoc
// @Summary ticket换token
// @Description author: yff
// @Tags  user
// @Accept  json
// @Produce  json
// @Param req body model.PassportRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/passport [post]
func (uc *UserController) Passport(c *gin.Context) {
	var req model.PassportRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("Passport invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	user, err := uc.userService.ValidateUserTicket(req.Ticket)
	if err != nil {
		uc.l.WithField("err", err).Info("Validate user ticket failed.")
		h.SendError(c, biz.ErrAuthorizeFailed)
		return
	}

	// online user when passport
	err = uc.plugin.SetUserOnline(user.UUID)
	if err != nil {
		uc.l.WithError(err).Info("Error when set user online in redis by user uuid.")
		h.SendError(c, biz.ErrInternalError)
		return
	}

	userClaim := claim.UserClaims{
		UID:           user.ID,
		IsAdmin:       user.IsAdmin,
		UUID:          user.UUID,
		BackstageRole: user.BackstageRole,
		IsSuperAdmin:  user.IsSuperAdmin,
		Tenant:        constant.TenantAutoDL,

		UPK: user.UpdatePasswordKey,
	}

	if user.IsAdmin {
		userClaim.StandardClaims.ExpiresAt = time.Now().Add(time.Minute * 30).Unix()
	}

	if conf.GetGlobalGsConfig().App.DebugApi {
		userClaim.StandardClaims.ExpiresAt = time.Now().Add(time.Hour * 2).Unix()
	}

	token, err := constant.GenerateToken(userClaim)
	if err != nil {
		uc.l.WithField("err", err).Info("Generate token failed.")
		h.SendError(c, biz.ErrInternalError)
		return
	}

	h.SendOK(c, map[string]interface{}{"token": token})
	return
}

// GetCaptcha godoc
// @Summary 用户获取登录验证码
// @Description author: yff
// @Tags  user
// @Accept  json
// @Produce  json
// @Param req body model.CaptchaRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/captcha/get [get]
func (uc *UserController) GetCaptcha(c *gin.Context) {
	var captcha model.CaptchaResponse
	var captchaValue string

	captcha.Id, captchaValue, captcha.Captcha = libs.GenerateStringCaptcha()
	err := uc.captchaPlugin.Set(captcha.Id, captchaValue)
	if err != nil {
		uc.l.WithField("err", err).Info("captchaPlugin.Set failed.")
		h.SendError(c, biz.ErrInternalError)
		return
	}
	h.SendOK(c, captcha)
}

// DebugGetCaptchaIDAndValue 直接返回验证码id及value
func (uc *UserController) DebugGetCaptchaIDAndValue(c *gin.Context) {
	var captcha model.CaptchaResponse
	var captchaValue string

	captcha.Id, captchaValue, captcha.Captcha = libs.GenerateStringCaptcha()
	err := uc.captchaPlugin.Set(captcha.Id, captchaValue)
	if err != nil {
		uc.l.WithError(err).Error("captchaPlugin.Set failed.")
		h.SendError(c, biz.ErrInternalError)
		return
	}

	h.SendOK(c, map[string]string{"id": captcha.Id, "value": captchaValue})
	return
}

// SimplySendMessage godoc
// @Summary 发送短信
// @Description author: yff
// @Tags user
// @Accept  json
// @Produce  json
// @Param req body model.SimpleSMSRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/simply/send_message [post]
func (uc *UserController) SimplySendMessage(c *gin.Context) {
	var req model.SimpleSMSRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		uc.l.WithField("err", err).Error("Simply SendVCode invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	// 1: 验证手机号是否符合标准
	phoneWithArea, err := uc.userService.PhoneAndAreaCheck(req.PhoneArea, req.Phone)
	if err != nil {
		h.SendError(c, err)
		return
	}
	if req.PhoneArea != "" && req.PhoneArea != "+86" && req.PhoneArea != "+1" {
		req.SMSType = constant.Abroad
	}

	// get last vcode send time
	timeLast, err := uc.plugin.GetLastSendVCodeTime(phoneWithArea)
	if err == nil {
		h.SendOK(c, timeLast)
		return
	}

	if err != redis.Nil {
		uc.l.WithField("err", err).Info("Get last verification send time from redis failed.")
		h.SendError(c, err)
		return
	}

	vCode := libs.RandVCodeString(1000000)
	errDB := uc.plugin.SetVCode(phoneWithArea, vCode)
	if errDB != nil {
		uc.l.WithField("err", errDB).Error("plugin.SetMessage failed.")
		h.SendError(c, biz.ErrInternalError)
		return
	}

	var code string
	phone := phoneWithArea

	if req.PhoneArea == "+1" {
		code = vCode
		phone = strings.TrimPrefix(phoneWithArea, "+")
	} else {
		code = libs.BuildSMSTemplateParamCode(vCode)
	}

	_, err = libs.SendSMS(req.PhoneArea, phone, code, req.SMSType, "")
	if err != nil {
		uc.l.WithField("err", err).Error("Send message failed.")
		h.SendError(c, biz.ErrSendSMSFailed)
		return
	}

	// storage vcode send time
	timeNow := uc.plugin.Now()
	err = uc.plugin.SetSendVCodeTime(phoneWithArea, timeNow)
	if err != nil {
		uc.l.WithField("err", err).Error("Set vCode countDown failed.")
		h.SendError(c, err)
		return
	}
	h.SendOK(c, timeNow)
}

// SendVCode godoc
// @Summary 发送短信
// @Description author: yff
// @Tags user
// @Accept  json
// @Produce  json
// @Param req body model.SMSRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/send_message [post]
func (uc *UserController) SendVCode(c *gin.Context) {
	var req model.SMSRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		uc.l.WithField("err", err).Error("SendVCode invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	// count user send vcode times per day.
	vCodeSendNum, err := uc.plugin.PV(redis_plugin.VCodeSendLimitPrefix, req.Phone)
	if err != nil {
		return
	}
	if vCodeSendNum > constant.VCodeSendLimitPerDay {
		uc.l.WithField("pv", vCodeSendNum).Error("pv over per day limit")
		h.SendError(c, biz.ErrInternalError)
		return
	}

	// 1: 验证手机号是否符合标准
	phoneWithArea, err := uc.userService.PhoneAndAreaCheck(req.PhoneArea, req.Phone)
	if err != nil {
		h.SendError(c, err)
		return
	}
	if req.PhoneArea != "" && req.PhoneArea != "+86" && req.PhoneArea != "+1" {
		req.SMSType = constant.Abroad
	}

	// get last vcode send time
	timeLast, err := uc.plugin.GetLastSendVCodeTime(phoneWithArea)
	if err == nil {
		h.SendOK(c, timeLast)
		return
	}
	if err != redis.Nil {
		uc.l.WithField("err", err).Info("Get last verification send time from redis failed.")
		h.SendError(c, err)
		return
	}

	// check captcha correct or not
	err = uc.captchaPlugin.GetCheckResult(req.PictureID)
	if err != nil {
		uc.l.WithField("pictureId", req.PictureID).WithError(err).Info("Get check captcha result failed.")
		h.SendError(c, biz.ErrCheckCaptchaFailed)
		return
	}

	vCode := libs.RandVCodeString(1000000)
	errDB := uc.plugin.SetVCode(phoneWithArea, vCode)
	if errDB != nil {
		uc.l.WithField("err", errDB).Info("plugin.SetMessage failed.")
		h.SendError(c, biz.ErrInternalError)
		return
	}

	var code string
	phone := phoneWithArea

	if req.PhoneArea == "+1" {
		code = vCode
		phone = strings.TrimPrefix(phoneWithArea, "+")
	} else {
		code = libs.BuildSMSTemplateParamCode(vCode)
	}

	uc.l.WithFields(map[string]interface{}{
		"code": code,
	}).Info("code")
	_, err = libs.SendSMS(req.PhoneArea, phone, code, req.SMSType, "")
	if err != nil {
		uc.l.WithField("err", err).Info("Send message failed.")
		h.SendError(c, biz.ErrSendSMSFailed)
		return
	}

	tokenExpired := libs.ExpiredTokenTime()
	err = uc.plugin.PVIncrement(redis_plugin.PersonalTokenKeyPerDayPrefix, req.Phone, tokenExpired)
	if err != nil {
		uc.l.WithField("err", err).Error("PersonalToken PVIncrement in redis failed.")
		return
	}

	// storage vcode send time
	timeNow := uc.plugin.Now()
	err = uc.plugin.SetSendVCodeTime(phoneWithArea, timeNow)
	if err != nil {
		uc.l.WithField("err", err).Info("Set vCode countDown failed.")
		h.SendError(c, err)
		return
	}
	h.SendOK(c, timeNow)
}

func (uc *UserController) SendVCodeInner(c *gin.Context) {
	var req model.SMSRequestBySubName
	if err := c.ShouldBindJSON(&req); err != nil {
		uc.l.WithField("err", err).Error("SendVCode invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	u := h.GetUserInfo(c)
	if u.SubName != "" {
		su, err := uc.userService.SubUserGet(u.SubName)
		if err != nil {
			uc.l.WithField("subName", u.SubName).WithError(err).Error("get subUser failed")
			h.SendError(c, err)
			return
		}
		if su.Phone != "" {
			req.Phone = su.Phone
		} else {
			h.SendError(c, biz.ErrUserPhoneNotExist)
			return
		}
	} else {
		user, err := uc.userService.FindByUserId(u.UID)
		if err != nil {
			uc.l.WithField("subName", u.SubName).WithError(err).Error("get subUser failed")
			h.SendError(c, err)
			return
		}
		if user.Phone != "" {
			req.Phone = user.Phone
		} else {
			h.SendError(c, biz.ErrUserPhoneNotExist)
			return
		}
	}

	// count user send vcode times per day.
	vCodeSendNum, err := uc.plugin.PV(redis_plugin.VCodeSendLimitPrefix, req.Phone)
	if err != nil {
		return
	}
	if vCodeSendNum > constant.VCodeSendLimitPerDay {
		uc.l.WithField("pv", vCodeSendNum).Error("pv over per day limit")
		h.SendError(c, biz.ErrInternalError)
		return
	}

	// 1: 验证手机号是否符合标准
	phoneWithArea, err := uc.userService.PhoneAndAreaCheck(req.PhoneArea, req.Phone)
	if err != nil {
		h.SendError(c, err)
		return
	}
	if req.PhoneArea != "" && req.PhoneArea != "+86" && req.PhoneArea != "+1" {
		req.SMSType = constant.Abroad
	}

	// get last vcode send time
	timeLast, err := uc.plugin.GetLastSendVCodeTime(phoneWithArea)
	if err == nil {
		h.SendOK(c, timeLast)
		return
	}
	if err != redis.Nil {
		uc.l.WithField("err", err).Info("Get last verification send time from redis failed.")
		h.SendError(c, err)
		return
	}

	// check captcha correct or not
	err = uc.captchaPlugin.GetCheckResult(req.PictureID)
	if err != nil {
		uc.l.WithField("pictureId", req.PictureID).WithError(err).Info("Get check captcha result failed.")
		h.SendError(c, biz.ErrCheckCaptchaFailed)
		return
	}

	vCode := libs.RandVCodeString(1000000)
	errDB := uc.plugin.SetVCode(phoneWithArea, vCode)
	if errDB != nil {
		uc.l.WithField("err", errDB).Info("plugin.SetMessage failed.")
		h.SendError(c, biz.ErrInternalError)
		return
	}

	var code string
	phone := phoneWithArea

	if req.PhoneArea == "+1" {
		code = vCode
		phone = strings.TrimPrefix(phoneWithArea, "+")
	} else {
		code = libs.BuildSMSTemplateParamCode(vCode)
	}

	uc.l.WithFields(map[string]interface{}{
		"code": code,
	}).Info("code")
	_, err = libs.SendSMS(req.PhoneArea, phone, code, req.SMSType, "")
	if err != nil {
		uc.l.WithField("err", err).Info("Send message failed.")
		h.SendError(c, biz.ErrSendSMSFailed)
		return
	}

	tokenExpired := libs.ExpiredTokenTime()
	err = uc.plugin.PVIncrement(redis_plugin.PersonalTokenKeyPerDayPrefix, req.Phone, tokenExpired)
	if err != nil {
		uc.l.WithField("err", err).Error("PersonalToken PVIncrement in redis failed.")
		return
	}

	// storage vcode send time
	timeNow := uc.plugin.Now()
	err = uc.plugin.SetSendVCodeTime(phoneWithArea, timeNow)
	if err != nil {
		uc.l.WithField("err", err).Info("Set vCode countDown failed.")
		h.SendError(c, err)
		return
	}
	h.SendOK(c, timeNow)
}

// CheckVCode godoc
// @Summary 校验短信验证码
// @Description author: yff
// @Tags user
// @Accept  json
// @Produce  json
// @Param req body model.CheckVCodeRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/message/check [post]
func (uc *UserController) CheckVCode(c *gin.Context) {
	var req model.CheckVCodeRequest
	ll := uc.l.WithFields(logger.Fields{
		"phone":  req.Phone,
		"v_code": req.VCode,
	})
	if err := c.ShouldBind(&req); err != nil {
		ll.WithError(err).Info("Check v_code invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	// check fail times
	pv, _ := uc.plugin.PV(redis_plugin.IpPVKeyPrefix, req.Phone)
	if pv >= constant.LoginFailedPVLimit {
		err := uc.userService.ForbiddenUserLogin(req.Phone)
		if err != nil {
			h.SendError(c, biz.ErrInternalError)
			return
		}
		h.SendError(c, biz.ErrLoginIsLimitedFmt.New().Format(constant.LoginFailedPVLimit, constant.LoginFailedLimitTimeout.Minutes()))
		return
	}

	err := uc.plugin.PVIncrement(redis_plugin.IpPVKeyPrefix, req.Phone, constant.FailedConsequentInterval)
	if err != nil {
		uc.l.WithField("err", err).Error("Login PVIncrement in redis failed.")
		h.SendError(c, biz.ErrInternalError)
		return
	}

	phoneWithArea := libs.PhoneBuildWithArea(req.PhoneArea, req.Phone)
	right, err := uc.plugin.CheckVCodeValueIsRight(phoneWithArea, req.VCode)
	if err != nil {
		ll.WithError(err).Info("Captcha plugin check captcha value failed.")
		h.SendError(c, biz.ErrCheckUserVCodeFailed)
		return
	}

	if !right {
		if pv > 4 {
			err = uc.userService.ForbiddenUserLogin(req.Phone)
			if err != nil {
				h.SendError(c, biz.ErrInternalError)
				return
			}
			h.SendError(c, biz.ErrLoginIsLimitedFmt.New().Format(constant.LoginFailedPVLimit, constant.LoginFailedLimitTimeout.Minutes()))
			return
		}
		ll.WithError(err).Info("User VCode wrong.")
		h.SendError(c, biz.ErrCheckUserVCodeFailed)
		return
	}
	h.SendOK(c, nil)
}

// Login godoc
// @Summary 用户登录
// @Description author: yff
// @Tags user
// @Accept  json
// @Produce  json
// @Param req body model.LoginCaptchaRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/login [post]
func (uc *UserController) Login(c *gin.Context) {
	var req model.LoginCaptchaRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("User login invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	// 1: 进行错误次数判断, PV为错误次数
	pv, _ := uc.plugin.PV(redis_plugin.IpPVKeyPrefix, req.Phone)
	if pv > constant.LoginFailedPVLimit {
		err := uc.userService.ForbiddenUserLogin(req.Phone)
		if err != nil {
			h.SendError(c, biz.ErrInternalError)
			return
		}
		h.SendError(c, biz.ErrLoginIsLimitedFmt.New().Format(constant.LoginFailedPVLimit, constant.LoginFailedLimitTimeout.Minutes()))
		return
	}

	// 2: 连续输入错误的计数的过期时间. 间隔5分钟内出错就算连续
	err := uc.plugin.PVIncrement(redis_plugin.IpPVKeyPrefix, req.Phone, constant.FailedConsequentInterval)
	if err != nil {
		uc.l.WithField("err", err).Error("Login PVIncrement in redis failed.")
		h.SendError(c, biz.ErrInternalError)
		return
	}

	// 3: 检查用户状态,用户处于禁用状态时禁止登录
	userInfo, err := uc.userService.UserGetForLogin(req.PhoneArea, req.Phone)
	if err == nil && userInfo.Status == model.Disable {
		uc.l.WithField("status", userInfo.Status).Error("User can not login when status is disable!")
		h.SendError(c, biz.ErrUserDisableLogin)
		return
	}

	// 4: 检查验证码是否输入正确
	right, err := uc.captchaPlugin.CheckCaptchaValueIsRight(req.CaptchaID, req.CaptchaValue)
	if err != nil {
		if err == redis.Nil {
			uc.l.WithField("err", err).Info("vCode has expired.")
			h.SendError(c, biz.ErrCaptchaExpired)
			return
		}
		uc.l.WithField("err", err).Info("Captcha plugin check captcha value failed.")
		h.SendError(c, biz.ErrCheckCaptchaFailed)
		return
	}

	if !right {
		if pv > 4 {
			err = uc.userService.ForbiddenUserLogin(req.Phone)
			if err != nil {
				h.SendError(c, biz.ErrInternalError)
				return
			}
			h.SendError(c, biz.ErrLoginIsLimitedFmt.New().Format(constant.LoginFailedPVLimit, constant.LoginFailedLimitTimeout.Minutes()))
			return
		}
		h.SendError(c, biz.ErrRequestCaptchaIsWrong)
		return
	}

	// 5: 用户进行登录
	user, err := uc.userService.LoginAndGet(req.Phone, req.Password)
	if err != nil {
		// 如果是手机号密码错误, 则连续错误计数+1. 其他错误不增加连续错误计数.
		if err != biz.ErrInternalError {
			if pv > 4 {
				err = uc.userService.ForbiddenUserLogin(req.Phone)
				if err != nil {
					h.SendError(c, biz.ErrInternalError)
					return
				}
				h.SendError(c, biz.ErrLoginIsLimitedFmt.New().Format(constant.LoginFailedPVLimit, constant.LoginFailedLimitTimeout.Minutes()))
				return
			}
		}
		uc.l.WithField("err", err).Info("User login failed.")
		h.SendError(c, err)
		return
	}

	// 6: 用户登录成功后清除之前的计数
	_ = uc.plugin.PVClear(req.Phone)
	_ = uc.plugin.ClearLoginLimit(req.Phone)

	// 7: 开始执行后续 ticket 流程. 前端根据 ticket 访问 passport 接口获取 token.
	ticket := libs.GenerateTicket(user.ID)
	err = uc.plugin.SetTicket(ticket, user.ID)
	if err != nil {
		uc.l.WithField("err", err).Info("Set ticket into redis failed.")
		h.SendError(c, biz.ErrSetTicketFailed)
		return
	}

	// 8. 登录成功, 返回信息.
	h.SendOK(c, map[string]interface{}{"ticket": ticket, "user": user})

	uc.userService.OperateRecordCreateForLogin(c, userInfo.ID, "", constant.OperateLogin, req)
}

// WXLogin godoc
// @Summary 用户点击微信登录生成二维码
// @Description author: yff
// @Tags user
// @Accept  json
// @Produce  json
// @Param req body model.DefaultNullRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/wx/login [get]
func (uc *UserController) WXLogin(c *gin.Context) {
	res, err := uc.userService.CreateQRCode(0)
	if err != nil {
		h.SendError(c, err)
		return
	}
	h.SendOK(c, res)
}

// WxCallBack 微信扫码回调(回调之后获取用户信息,进行其他操作)
func (uc *UserController) WxCallBack(c *gin.Context) {
	uc.l.Info("get wechat call back after wechat login")
	req := c.Request.URL.Query()
	signature := req.Get("signature")
	timestamp := req.Get("timestamp")
	nonce := req.Get("nonce")

	// 校验服务器配置是否正确
	token := constant.Token
	if conf.GetGlobalGsConfig().App.DebugApi {
		token = constant.TestToken
	}
	legal := libs.ValidateURL(token, timestamp, nonce, signature)
	if !legal {
		c.String(http.StatusUnauthorized, "Validate url error")
		return
	}

	switch c.Request.Method {
	case "GET":
		echostr := req.Get("echostr")
		c.String(http.StatusOK, echostr)
		return
	case "POST":
		msgSignature := req.Get("msg_signature")
		encryptType := req.Get("encrypt_type")

		// 读取报文
		body, err := io.ReadAll(c.Request.Body)
		if err != nil {
			uc.l.Info("Read request body err")
			err = biz.ErrReadWechatBodyFailed
			return
		}

		// 解析报文实体
		msg, err := uc.userService.ParseBody(encryptType, timestamp, nonce, msgSignature, body)
		if err != nil {
			log.Warnf("parse body err: %v", err)
			err = biz.ErrParseXmlFailed
			return
		}
		if msg.MsgType == "event" {
			err := uc.userService.HandleEventMessage(msg)
			if err != nil {
				c.String(http.StatusInternalServerError, c.GetString("success"))
				return
			}
			c.String(http.StatusOK, c.GetString("success"))
		} else {
			c.String(http.StatusOK, c.GetString("success"))
		}

	default:
		c.String(http.StatusForbidden, c.GetString("Only GET or POST method allowed"))
		return
	}
}

// WxLoginPollingQuery godoc
// @Summary 前端轮询查询微信登录的结果
// @Description author: yff
// @Tags user
// @Accept  json
// @Produce  json
// @Param req body model.UserUUIDRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/wx/polling/query [post]
func (uc *UserController) WxLoginPollingQuery(c *gin.Context) {
	var req model.UserUUIDRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("User wechat login polling query invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	value, err := uc.plugin.GetWxCallBackResult(req.Uuid)
	if err != nil {
		h.SendOK(c, nil)
		return
	}
	str := strings.Split(value, "**")
	if len(str) < 2 {
		uc.l.WithField("str", str).Info("split failed.")
		h.SendOK(c, nil)
		return
	}
	switch str[0] {
	case "3":
		// 是3的话就证明是微信绑定,然后openId已存在,此时直接返回错误:此微信号已绑定其他手机号
		err := biz.ErrPhoneHasBound
		h.SendError(c, err)
		return
	case "2":
		// 是2的话就证明是微信绑定,但是openId在表中不存在,此时将两者信息相结合
		userID, err := strconv.Atoi(str[1])
		if err != nil {
			h.SendError(c, biz.ErrInvalidRequestParams)
			return
		}
		uUser, err := uc.userService.FindByUserId(userID)
		if err != nil {
			h.SendError(c, err)
			return
		}
		// 将信息合并
		newUserInfo, err := uc.userService.MergeUserInfo(uUser.Phone, str[2])
		if err != nil {
			h.SendError(c, err)
			return
		}
		err = uc.userService.AddUserGrowthValueCmd(newUserInfo.ID, 0, constant.VipWechatBind)
		if err != nil {
			h.SendError(c, err)
			return
		}
		h.SendOK(c, newUserInfo)
		return
	case "1":
		// 如果是1的话就证明是微信登录,然后openId在表中存在,此时直接登录
		user, err := uc.userService.FindByUserOpenId(str[1])
		if err != nil {
			h.SendError(c, err)
			return
		}

		if user.Status == model.Disable {
			uc.l.WithField("status", user.Status).Error("User can not login when status is disable!")
			h.SendError(c, biz.ErrUserDisableLogin)
			return
		}

		_ = uc.plugin.PVClear(user.Phone)
		_ = uc.plugin.ClearLoginLimit(user.Phone)

		ticket := libs.GenerateTicket(user.ID)
		err = uc.plugin.SetTicket(ticket, user.ID)
		if err != nil {
			uc.l.WithField("err", err).Error("Set ticket into redis failed.")
			h.SendError(c, biz.ErrSetTicketFailed)
			return
		}
		h.SendOK(c, map[string]interface{}{"ticket": ticket, "user": user, "openId": str[1]})

		uc.userService.OperateRecordCreateForLogin(c, user.ID, "", constant.OperateLoginWX, req)

		return
	case "0":
		// 是0的话就证明是微信登录,然后openId在表中不存在,此时返回openId,进行前端跳绑定手机号接口
		h.SendOK(c, map[string]interface{}{"openId": str[1]})
		return
	default:
		h.SendOK(c, nil)
		return
	}
}

// WeChatBind godoc
// @Summary 用户在用户中心点击绑定微信
// @Description author: yff
// @Tags user
// @Accept  json
// @Produce  json
// @Param req body model.DefaultNullRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/user/wechat/bind [get]
func (uc *UserController) WeChatBind(c *gin.Context) {
	u := h.GetUserInfo(c)
	res, err := uc.userService.CreateQRCode(u.UID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	h.SendOK(c, res)
}

// WeChatUnbind godoc
// @Summary 用户在用户中心点击解绑微信
// @Description author: zt
// @Tags user
// @Accept  json
// @Produce  json
// @Param req body model.DefaultNullRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/user/wechat [delete]
func (uc *UserController) WeChatUnbind(c *gin.Context) {
	u := h.GetUserInfo(c)
	err := uc.userService.WxOpenIDUnbind(u.UID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	h.SendOK(c, nil)
}

// WechatBindClick godoc
// @Summary 用户在绑定微信页面点击我已关注
// @Description author: yff
// @Tags user
// @Accept  json
// @Produce  json
// @Param req body model.DefaultNullRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/user/wechat/bind/click [get]
func (uc *UserController) WechatBindClick(c *gin.Context) {
	u := h.GetUserInfo(c)
	bind, err := uc.userService.IsUserBindWechat(u.UID)
	if err != nil || !bind {
		err = biz.ErrUserBindWechatFailed
		h.SendError(c, err)
		return
	}
	h.SendOK(c, nil)
	return
}

// WechatBindClickGpuHub todo: delete it
func (uc *UserController) WechatBindClickGpuHub(c *gin.Context) {
	u := h.GetUserInfo(c)
	user, err := uc.userService.FindByUserId(u.UID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	if user.OpenId != "" {
		h.SendOK(c, nil)
		return
	}

	uc.l.WithField("uid", u.UID).Trace("user register from gpuhub !!!")

	err = db_helper.UpdateOne(db_helper.QueryDefinition{
		ModelDefinition: &model.User{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": u.UID}},
	}, map[string]interface{}{"open_id": "gpuhub-fake-open-id", "updated_at": time.Now()}).GetError()
	if err != nil {
		uc.l.WithField("uid", u.UID).ErrorE(err, "update user set openid failed")
		h.SendError(c, biz.ErrDatabaseError)
		return
	}

	h.SendOK(c, nil)
	return
}

// WxPhoneBind godoc
// @Summary 用户微信登录后绑定手机号
// @Description author: yff
// @Tags user
// @Accept  json
// @Produce  json
// @Param req body model.WxPhoneBindRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/wx/phone/bind [post]
func (uc *UserController) WxPhoneBind(c *gin.Context) {
	var req model.WxPhoneBindRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("User fast login invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	// 校验短信验证码是否正确
	phoneWithArea := libs.PhoneBuildWithArea(req.PhoneArea, req.Phone)
	right, err := uc.plugin.CheckVCodeValueIsRight(phoneWithArea, req.VCode)
	if err != nil {
		uc.l.WithField("err", err).Info("Captcha plugin check captcha value failed.")
		h.SendError(c, biz.ErrCheckUserVCodeFailed)
		return
	}

	if !right {
		h.SendError(c, biz.ErrCheckUserVCodeFailed)
		return
	}

	exist, err := uc.userService.CheckUserExist(req.Phone)
	if err != nil {
		h.SendError(c, err)
		return
	}
	if exist {
		newUserInfo, err := uc.userService.MergeUserInfo(req.Phone, req.OpenId)
		if err != nil {
			h.SendError(c, err)
			return
		}

		_ = uc.plugin.PVClear(newUserInfo.Phone)
		_ = uc.plugin.ClearLoginLimit(newUserInfo.Phone)

		ticket := libs.GenerateTicket(newUserInfo.ID)
		err = uc.plugin.SetTicket(ticket, newUserInfo.ID)
		if err != nil {
			uc.l.WithField("err", err).Info("Set ticket into redis failed.")
			h.SendError(c, biz.ErrSetTicketFailed)
			return
		}
		h.SendOK(c, map[string]interface{}{"ticket": ticket, "user": newUserInfo})
		return
	}

	// 不存在就新创建用户
	params := &model.ParamsCreateUser{
		PhoneArea: libs.PhoneAreaCodeCorrect(req.PhoneArea),
		Phone:     req.Phone,
		OpenID:    req.OpenId,
		Username:  constant.GetUserName(req.Phone),
		IP:        c.ClientIP(),
	}

	newUser, err := uc.userService.CreateUser(params)
	if err != nil {
		h.SendError(c, err)
		return
	}

	ticket := libs.GenerateTicket(newUser.ID)
	err = uc.plugin.SetTicket(ticket, newUser.ID)
	if err != nil {
		uc.l.WithField("err", err).Info("Set ticket into redis failed.")
		h.SendError(c, biz.ErrSetTicketFailed)
		return
	}
	h.SendOK(c, map[string]interface{}{"ticket": ticket, "user": newUser, "type": "register"})

	uc.userService.OperateRecordCreateForLogin(c, newUser.ID, "", constant.OperateSignupWX, req)
	return
}

// NewLogin godoc
// @Summary 新的登录方式(三次错误之后跳验证码)
// @Description author: yff
// @Tags user
// @Accept  json
// @Produce  json
// @Param req body model.LoginRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/new_login [post]
func (uc *UserController) NewLogin(c *gin.Context) {
	var req model.LoginRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("User login invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	// 1: 进行错误次数判断, PV为错误次数
	pv, _ := uc.plugin.PV(redis_plugin.IpPVKeyPrefix, req.Phone)
	if pv >= constant.LoginFailedPVLimit {
		err := uc.userService.ForbiddenUserLogin(req.Phone)
		if err != nil {
			uc.l.WithField("err", err).Info("Forbidden user login failed.")
			h.SendPVError(c, pv+1, biz.ErrInternalError)
			return
		}
		uc.l.WithField("pv", pv).WithError(err).Info("User login over limit.")
		h.SendPVError(c, pv+1, biz.ErrLoginIsLimitedFmt.New().Format(constant.LoginFailedPVLimit, constant.LoginFailedLimitTimeout.Minutes()))
		return
	}

	// 2: 连续输入错误的计数的过期时间. 间隔5分钟内出错就算连续
	err := uc.plugin.PVIncrement(redis_plugin.IpPVKeyPrefix, req.Phone, constant.FailedConsequentInterval)
	if err != nil {
		uc.l.WithField("err", err).Info("Login PVIncrement in redis failed.")
		h.SendError(c, biz.ErrInternalError)
		return
	}

	// 3: 检查用户状态,用户处于禁用状态时禁止登录
	userInfo, err := uc.userService.UserGetForLogin(req.PhoneArea, req.Phone)
	if err != nil {
		err = biz.ErrGetUserFailed
		uc.l.WithField("phone", req.Phone).WithError(err).Warn("User not exist")
		h.SendPVError(c, pv+1, biz.ErrGetUserFailed)
		return
	}

	if userInfo.Status == model.Disable {
		uc.l.WithField("status", userInfo.Status).Info("User can not login when status is disable!")
		h.SendPVError(c, pv+1, biz.ErrUserStatusError)
		return
	}

	// 4: 用户进行登录(当pv<3的时候)
	if pv < 3 {
		userInfo, err = uc.userService.LoginAndGet(req.Phone, req.Password)
		if err != nil {
			uc.l.WithField("err", err).Info("User login failed.")
			h.SendPVError(c, pv+1, err)
			return
		}
	}

	// 5: 当pv大于等于3的时候就需要进行验证码的校验
	if pv >= 3 {
		userInfo, err = uc.userService.LoginWithCaptcha(req.Phone, req.Password, req.PictureID)
		if err != nil {
			uc.l.WithField("err", err).Info("User login failed.")
			h.SendPVError(c, pv+1, err)
			return
		}
	}
	userInfo.Mask()

	// 6: 用户登录成功后清除之前的计数
	_ = uc.plugin.PVClear(req.Phone)
	_ = uc.plugin.ClearLoginLimit(req.Phone)

	// 7: 开始执行后续 ticket 流程. 前端根据 ticket 访问 passport 接口获取 token.
	ticket := libs.GenerateTicket(userInfo.ID)
	err = uc.plugin.SetTicket(ticket, userInfo.ID)
	if err != nil {
		uc.l.WithField("err", err).Info("Set ticket into redis failed.")
		h.SendError(c, biz.ErrSetTicketFailed)
		return
	}

	// 8. 登录成功, 返回信息.
	h.SendOK(c, map[string]interface{}{"ticket": ticket, "user": userInfo})

	uc.userService.OperateRecordCreateForLogin(c, userInfo.ID, "", constant.OperateLogin, req)
}

// GetUserOpenIdByCode godoc
// @Summary 微信小程序通过code获取openId
// @Description author: yff
// @Tags user
// @Accept  json
// @Produce  json
// @Param req body model.WechatAppletLoginReq true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/user/wx/applet/openId [post]
func (uc *UserController) GetUserOpenIdByCode(c *gin.Context) {
	var req model.WechatAppletLoginReq
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("User fast login invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	u := h.GetUserInfo(c)
	err := uc.userService.GetWechatAppletOpenIdByCode(u.UID, req.Code)
	if err != nil {
		h.SendError(c, err)
		return
	}
	h.SendOK(c, nil)
}

// GetUserPhoneByCode godoc
// @Summary 微信小程序通过code获取手机号
// @Description author: yff
// @Tags user
// @Accept  json
// @Produce  json
// @Param req body model.WechatAppletLoginReq true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/wx/applet/phone [post]
func (uc *UserController) GetUserPhoneByCode(c *gin.Context) {
	var req model.WechatAppletLoginReq
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("User fast login invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}
	data, err := uc.userService.GetUserPhoneByJsCode(req.Code)
	if err != nil {
		h.SendError(c, err)
		return
	}
	if data.ErrCode != 0 {
		uc.l.WithField("data", data).Warn("data err code")
		h.SendError(c, biz.ErrInternalError)
		return
	}

	ip := c.ClientIP()
	user, create, ticket, err := uc.userService.GenWechatAppletTicket(data.PhoneInfo, ip)
	if err != nil {
		h.SendError(c, err)
		return
	}
	h.SendOK(c, ticket)

	operate := constant.OperateLoginApplet
	if create {
		operate = constant.OperateSignupApplet
	}

	uc.userService.OperateRecordCreateForLogin(c, user.ID, "", operate, req)
}

// FastLogin godoc
// @Summary 短信登录
// @Description author: yff
// @Tags user
// @Accept  json
// @Produce  json
// @Param req body model.FastLoginRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/fast_login [post]
func (uc *UserController) FastLogin(c *gin.Context) {
	var req model.FastLoginRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("User fast login invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	// 1: 进行错误次数判断, PV为错误次数
	pv, _ := uc.plugin.PV(redis_plugin.IpPVKeyPrefix, req.Phone)
	if pv >= constant.LoginFailedPVLimit {
		err := uc.userService.ForbiddenUserLogin(req.Phone)
		if err != nil {
			h.SendError(c, biz.ErrInternalError)
			return
		}
		h.SendError(c, biz.ErrLoginIsLimitedFmt.New().Format(constant.LoginFailedPVLimit, constant.LoginFailedLimitTimeout.Minutes()))
		return
	}

	err := uc.plugin.PVIncrement(redis_plugin.IpPVKeyPrefix, req.Phone, constant.FailedConsequentInterval)
	if err != nil {
		uc.l.WithField("err", err).Info("Login PVIncrement in redis failed.")
		h.SendError(c, biz.ErrInternalError)
		return
	}

	// 2: 验证手机号是否符合标准
	phone, err := uc.userService.PhoneAndAreaCheck(req.PhoneArea, req.Phone)
	if err != nil {
		h.SendError(c, err)
		return
	}

	// 3: 校验短信验证码(校验短信验证码的时间,过期的话就失效)
	right, err := uc.plugin.CheckVCodeValueIsRight(phone, req.VCode)
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			uc.l.WithField("err", err).Error("Captcha plugin check captcha value failed.")
		}
		h.SendError(c, biz.ErrCheckUserVCodeFailed)
		return
	}

	if !right {
		if pv > 4 {
			err = uc.userService.ForbiddenUserLogin(req.Phone)
			if err != nil {
				h.SendError(c, biz.ErrInternalError)
				return
			}
			h.SendError(c, biz.ErrLoginIsLimitedFmt.New().Format(constant.LoginFailedPVLimit, constant.LoginFailedLimitTimeout.Minutes()))
			return
		}
		uc.l.WithFields(logger.Fields{
			"phone":  req.Phone,
			"v_code": req.VCode,
		}).WithError(err).Info("User VCode wrong.")
		h.SendError(c, biz.ErrCheckUserVCodeFailed)
		return
	}

	// 4: 检查用户状态,用户未注册或处于禁用状态都禁止登录
	userInfo, err := uc.userService.UserGetForLogin(req.PhoneArea, req.Phone)
	if err != nil {
		// 用户未注册提示先进行注册
		if err == gorm.ErrRecordNotFound {
			uc.l.WithField("phone", req.Phone).WithError(err).Info("User not exist.")
			h.SendError(c, biz.ErrUserNotRegister)
			return
		}
		uc.l.WithField("phone", req.Phone).Info("search user in db failed.")
		h.SendError(c, biz.ErrGetUserFailed)
		return
	}

	if userInfo.Status == model.Disable {
		uc.l.WithFields(logger.Fields{
			"phone":  req.Phone,
			"status": userInfo.Status,
		}).WithField("err", err).Info("User status in disable can not login.")
		h.SendError(c, biz.ErrUserDisableLogin)
		return
	}

	// 记录登录时间
	user, err := uc.userService.UserFastLogin(userInfo.ID)
	if err != nil {
		uc.l.WithField("err", err).Error("Set user login_at failed.")
		return
	}

	// 5: 用户登录成功后清除之前的计数
	_ = uc.plugin.PVClear(req.Phone)
	_ = uc.plugin.ClearLoginLimit(req.Phone)

	// 6: 开始执行后续 ticket 流程. 前端根据 ticket 访问 passport 接口获取 token.
	ticket := libs.GenerateTicket(userInfo.ID)
	err = uc.plugin.SetTicket(ticket, userInfo.ID)
	if err != nil {
		uc.l.WithField("err", err).Info("Set ticket into redis failed.")
		h.SendError(c, biz.ErrSetTicketFailed)
		return
	}

	// 7. 登录成功, 返回信息.
	h.SendOK(c, map[string]interface{}{"ticket": ticket, "user": user})

	uc.userService.OperateRecordCreateForLogin(c, userInfo.ID, "", constant.OperateLoginCode, req)
}

// LoginFailedCount godoc
// @Summary 用户登录错误次数
// @Description author: yff
// @Tags  user
// @Accept  json
// @Produce  json
// @Param req body model.UserPhoneRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/login_failed/count [post]
func (uc *UserController) LoginFailedCount(c *gin.Context) {
	var req model.UserPhoneRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		uc.l.WithField("err", err).Error("User login failed count invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	// get login failed count in redis
	pv, err := uc.plugin.PV(redis_plugin.IpPVKeyPrefix, req.Phone)
	if err != nil {
		if err == redis.Nil {
			h.SendOK(c, 0)
			return
		}
		uc.l.WithField("phone", req.Phone).WithError(err).Info("Get user login failed count failed.")
		return
	}

	h.SendOK(c, pv)
}

// Logout godoc
// @Summary 退出登录
// @Description author: yff
// @Tags  user
// @Accept  json
// @Produce  json
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/user/logout [post]
func (uc *UserController) Logout(c *gin.Context) {
	u := h.GetUserInfo(c)
	// 用户退出登录就是将用户下线(不会对实例等产生任何影响)
	err := uc.plugin.SetUserOffline(u.UUID)
	if err != nil {
		uc.l.WithField("err", err).Error("User logout failed.")
		return
	}
	h.SendOK(c, nil)
}

// SendEmail godoc
// @Summary 发送邮箱验证码邮件
// @Description author: yff
// @Tags  user
// @Accept  json
// @Produce  json
// @Param req body model.SendEmailRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/user/email/send [post]
//func (uc *UserController) SendEmail(c *gin.Context) {
//	var req model.SendEmailRequest
//	if err := c.ShouldBindJSON(&req); err != nil {
//		uc.l.WithField("err", err).Error("Send email to user invalid request params.")
//		h.SendError(c, biz.ErrInvalidRequestParams)
//		return
//	}
//
//	// 获取上次发送邮件验证码时间
//	timeString, err := uc.plugin.GetLastSendVCodeTime(req.Email)
//	if err == nil {
//		h.SendOK(c, timeString)
//		return
//	}
//	if err != redis.Nil {
//		uc.l.WithField("err", err).Info("Get last email verification send time from redis failed.")
//		h.SendError(c, err)
//		return
//	}
//
//	// 验证邮箱格式是否符合规范
//	if !libs.TestRegexpEmail(req.Email) {
//		err = biz.ErrEmailFormatWrong
//		h.SendError(c, err)
//		return
//	}
//
//	// 验证邮箱是否已经被使用
//	exist, err := uc.userService.CheckEmailExist(req.Email)
//	if err != nil {
//		return
//	}
//	if exist {
//		h.SendError(c, biz.ErrEmailAlreadyInUse)
//		return
//	}
//
//	// 验证滑块验证码
//	err = uc.captchaPlugin.GetCheckResult(req.PictureID)
//	if err != nil {
//		uc.l.WithField("pictureId", req.PictureID).WithError(err).Info("Get check captcha result failed.")
//		h.SendError(c, biz.ErrCheckCaptchaFailed)
//		return
//	}
//
//	code := libs.RandVCodeString(10000000)
//	errDB := uc.plugin.SetEmail(req.Email, code)
//	if errDB != nil {
//		uc.l.WithField("err", errDB).Info("plugin.SetMessage failed.")
//		h.SendError(c, biz.ErrInternalError)
//		return
//	}
//	// 发送带有验证码的邮件
//	emailCode, _, err := libs.SendEmail(code, req.Email)
//	if err != nil {
//		uc.l.WithField("err", err).Info("Send email failed.")
//		h.SendError(c, biz.ErrSendSMSFailed)
//		return
//	}
//	if emailCode == 404 {
//		u := h.GetUserInfo(c)
//		err = uc.plugin.SetSendEmailInvalidTime(u.UID, u.SubName)
//		if err != nil {
//			uc.l.WithField("uid", u.UID).WarnE(err, "user SetSendEmailInvalidTime failed")
//		}
//		h.SendError(c, biz.ErrInvalidEmail)
//		return
//	}
//
//	h.SendOK(c, nil)
//}

// CheckEmail godoc
// @Summary 校验邮箱验证码
// @Description author: yff
// @Tags  user
// @Accept  json
// @Produce  json
// @Param req body model.CheckEmailRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/user/email/check [post]
func (uc *UserController) CheckEmail(c *gin.Context) {
	var req model.CheckEmailRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("Check email code invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	// 校验短信验证码(校验短信验证码的时间,过期的话就失效)
	right, err := uc.plugin.CheckEmailCodeValueIsRight(req.Phone, req.Code)
	if err != nil {
		uc.l.WithField("err", err).Info("Captcha plugin check captcha value failed.")
		h.SendError(c, biz.ErrCheckUserVCodeFailed)
		return
	}

	if !right {
		uc.l.WithFields(logger.Fields{
			"phone": req.Phone,
			"code":  req.Code,
		}).WithError(err).Info("User VCode wrong.")
		h.SendError(c, biz.ErrCheckUserVCodeFailed)
		return
	}
	h.SendOK(c, nil)
}

// BindEmail godoc
// @Summary 用户绑定邮箱
// @Description author: yff
// @Tags  user
// @Accept  json
// @Produce  json
// @Param req body model.BindEmailRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/user/email/bind [post]
// 用户绑定邮箱需要经过邮箱验证码验证,避免了用户随意填写邮箱所造成的重复.
func (uc *UserController) BindEmail(c *gin.Context) {
	var req model.BindEmailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		uc.l.WithField("err", err).Error("User bind email invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}
	userInfo := h.GetUserInfo(c)
	right, err := uc.plugin.CheckEmailCodeValueIsRight(req.Email, req.Code)
	if err != nil {
		h.SendError(c, biz.ErrGetVCodeFailed)
		return
	}
	if !right {
		h.SendError(c, biz.ErrEmailCodeWrong)
		return
	}

	// 输入验证码从而进行用户与邮箱之间的真正绑定(后续就是一一对应)
	err = uc.userService.UserBindEmail(userInfo.UID, req.Email)
	if err != nil {
		uc.l.WithField("err", err).Info("User bind email failed.")
		h.SendError(c, biz.ErrEmailBindFailed)
		return
	}
	h.SendOK(c, nil)
}

// UpdateEmail godoc
// @Summary 用户更新邮箱
// @Description author: yff
// @Tags  user
// @Accept  json
// @Produce  json
// @Param req body model.UpdateEmailRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/user/email/update [put]
// NOTE: phone or oldEmail check -> newEmail check -> update email && email bind
func (uc *UserController) UpdateEmail(c *gin.Context) {
	var req model.UpdateEmailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		uc.l.WithField("err", err).Error("User update email invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	// 验证邮箱验证码
	right, err := uc.plugin.CheckEmailCodeValueIsRight(req.OldEmail, req.Code)
	if err != nil {
		h.SendError(c, biz.ErrEmailCodeWrong)
		return
	}
	if !right {
		uc.l.WithFields(logger.Fields{
			"email": req.OldEmail,
			"code":  req.Code,
		}).Info("Email code verification failed.")
		return
	}

	userInfo := h.GetUserInfo(c)
	err = uc.userService.UserBindEmail(userInfo.UID, req.NewEmail)
	if err != nil {
		uc.l.WithFields(logger.Fields{
			"old_email": req.OldEmail,
			"new_email": req.NewEmail,
		}).WithError(err).Info("User update email failed.")
		h.SendError(c, biz.ErrUserUpdateEmailFailed)
		return
	}
	h.SendOK(c, nil)
}

// UpdatePassword godoc
// @Summary 用户设置/修改密码
// @Description author: yff
// @Tags  user
// @Accept  json
// @Produce  json
// @Param req body model.UpdatePasswordRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/user/password/update [put]
func (uc *UserController) UpdatePassword(c *gin.Context) {
	var req model.UpdatePasswordRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("User update password invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	// 先根据用户Id获取用户详情,错误6次对用户禁5分钟
	u := h.GetUserInfo(c)
	user, err := uc.userService.FindByUserId(u.UID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	// 1: 进行错误次数判断, PV为错误次数
	pv, _ := uc.plugin.PV(redis_plugin.IpPVKeyPrefix, user.Phone)
	if pv >= constant.LoginFailedPVLimit {
		err := uc.userService.ForbiddenUserLogin(user.Phone)
		if err != nil {
			h.SendError(c, biz.ErrInternalError)
			return
		}
		h.SendError(c, biz.ErrResetPasswordIsLimitedFmt.New().Format(constant.LoginFailedPVLimit, constant.LoginFailedLimitTimeout.Minutes()))
		return
	}

	err = uc.plugin.PVIncrement(redis_plugin.IpPVKeyPrefix, user.Phone, constant.FailedConsequentInterval)
	if err != nil {
		uc.l.WithField("err", err).Info("Login PVIncrement in redis failed.")
		h.SendError(c, biz.ErrInternalError)
		return
	}

	// 2: 校验短信验证码(校验短信验证码的时间,过期的话就失效)
	phoneWithArea := libs.PhoneBuildWithArea(user.PhoneArea, user.Phone)
	right, err := uc.plugin.CheckVCodeValueIsRight(phoneWithArea, req.VCode)
	if err != nil {
		uc.l.WithField("err", err).Error("Captcha plugin check captcha value failed.")
		h.SendError(c, biz.ErrCheckUserVCodeFailed)
		return
	}
	if !right {
		if pv > 4 {
			err = uc.userService.ForbiddenUserLogin(user.Phone)
			if err != nil {
				h.SendError(c, biz.ErrInternalError)
				return
			}
			h.SendError(c, biz.ErrLoginIsLimitedFmt.New().Format(constant.LoginFailedPVLimit, constant.LoginFailedLimitTimeout.Minutes()))
			return
		}
		uc.l.WithFields(logger.Fields{
			"phone":  user.Phone,
			"v_code": req.VCode,
		}).WithError(err).Info("User VCode wrong.")
		h.SendError(c, biz.ErrCheckUserVCodeFailed)
		return
	}

	// 3: 用户修改密码(应该对密码进行格式校验,前端校验)
	if req.ConfirmPassword != req.NewPassword {
		h.SendError(c, biz.ErrUserPasswordInconsistent)
		return
	}
	err = uc.userService.UpdatePassword(u.UID, req.NewPassword)
	if err != nil {
		uc.l.WithField("err", err).Info("User update password failed.")
		h.SendError(c, biz.ErrUpdatePasswordFailed)
		return
	}

	// 4: 修改成功后,清除掉之前错误的次数
	_ = uc.plugin.PVClear(user.Phone)
	_ = uc.plugin.ClearLoginLimit(user.Phone)

	h.SendOK(c, nil)
}

// UpdateNickName godoc
// @Summary 用户修改昵称
// @Description author: yff
// @Tags  user
// @Accept  json
// @Produce  json
// @Param req body model.UpdateNicknameRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/user/nickname/update [put]
func (uc *UserController) UpdateNickName(c *gin.Context) {
	var req model.UpdateNicknameRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("User update password invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	err := uc.userService.UserUpdateNickname(h.GetUserInfo(c).UID, req.NewNickname)
	if err != nil {
		h.SendError(c, err)
		return
	}
	h.SendOK(c, nil)
}

// ResetPassword godoc
// @Summary 用户重新设置密码(忘记密码)
// @Description author: yff
// @Tags  user
// @Accept  json
// @Produce  json
// @Param req body model.ResetPasswordRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/password/reset [put]
func (uc *UserController) ResetPassword(c *gin.Context) {
	var req model.ResetPasswordRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("User reset password invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	// 用户重置密码时需要跟用户登录校验错误次数进行区分
	redisKey := libs.StringCombination(req.Phone, "reset_password")
	// 1: 统计用户错误次数
	pv, _ := uc.plugin.PV(redis_plugin.IpPVKeyPrefix, redisKey)
	if pv >= constant.LoginFailedPVLimit {
		err := uc.userService.ForbiddenUserLogin(req.Phone)
		if err != nil {
			h.SendError(c, biz.ErrInternalError)
			return
		}
		h.SendError(c, biz.ErrResetPasswordIsLimitedFmt.New().Format(constant.LoginFailedPVLimit, constant.LoginFailedLimitTimeout.Minutes()))
		return
	}
	_ = uc.plugin.PVIncrement(redis_plugin.IpPVKeyPrefix, redisKey, constant.FailedConsequentInterval)

	phoneWithArea := libs.PhoneBuildWithArea(req.PhoneArea, req.Phone)
	// 3: 校验短信验证码
	right, err := uc.plugin.CheckVCodeValueIsRight(phoneWithArea, req.VCode)
	if err != nil {
		uc.l.WithField("phone", req.Phone).WithError(err).Error("Check user v_code failed.")
		h.SendError(c, biz.ErrCheckUserVCodeFailed)
		return
	}

	if !right {
		if pv >= 4 {
			err = uc.userService.ForbiddenUserLogin(redisKey)
			if err != nil {
				uc.l.WithField("phone", req.Phone).WithError(err).Info("Forbidden user login failed.")
				h.SendError(c, biz.ErrInternalError)
				return
			}
			h.SendError(c, biz.ErrLoginIsLimitedFmt.New().Format(constant.LoginFailedPVLimit, constant.LoginFailedLimitTimeout.Minutes()))
			return
		}
		uc.l.WithFields(logger.Fields{
			"phone":  req.Phone,
			"v_code": req.VCode,
		}).WithError(err).Info("User VCode wrong.")
		h.SendError(c, biz.ErrCheckUserVCodeFailed)
		return
	}

	// 2: 获取用户信息用于直接跳转登录页并校验用户是否已注册(只有已注册用户才能通过此方式登录)
	user, err := uc.userService.UserGetForLogin(req.PhoneArea, req.Phone)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			uc.l.WithField("phone", req.Phone).WithError(err).Info("User not register before login.")
			h.SendError(c, biz.ErrUserNotRegister)
			return
		}
		uc.l.WithField("phone", req.Phone).WithError(err).Info("Get user by user phone failed.")
		h.SendError(c, biz.ErrGetUserFailed)
		return
	}

	// 4: 更换密码
	err = uc.userService.UpdatePassword(user.ID, req.Password)
	if err != nil {
		err = biz.ErrUserResetPasswordFailed
		uc.l.WithField("phone", req.Phone).Info("User reset password failed.")
		h.SendError(c, err)
		return
	}

	// 5: 用户登录成功后清除之前的计数
	_ = uc.plugin.PVClear(redisKey)
	_ = uc.plugin.ClearLoginLimit(redisKey)
	_ = uc.plugin.PVClear(req.Phone)
	_ = uc.plugin.ClearLoginLimit(req.Phone)

	// 6: 生成用户ticket
	ticket := libs.GenerateTicket(user.ID)
	err = uc.plugin.SetTicket(ticket, user.ID)
	if err != nil {
		uc.l.WithField("err", err).Info("Set ticket into redis failed.")
		h.SendError(c, biz.ErrSetTicketFailed)
		return
	}

	h.SendOK(c, map[string]interface{}{"ticket": ticket, "user": user})
}

// GetBlockPuzzleCaptcha godoc
// @Summary 获取块拼图验证码
// @Description  author: yff
// @Tags  user
// @Accept  json
// @Produce  json
// @Param req body model.DefaultNullRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/captcha/block_puzzle [get]
func (uc *UserController) GetBlockPuzzleCaptcha(c *gin.Context) {
	// 1: 生成需要返回的信息
	point := cap.GetPoints()
	pictureId, OriginalImageBase64, JigsawImageBase64, err := cap.GenerateBlockPuzzleCaptcha(point)
	if err != nil {
		uc.l.WithField("pictureId", pictureId).WithError(err).Info("Generate block puzzle captcha failed.")
		return
	}
	captcha := &model.BlockPuzzleCaptchaResponse{
		YPosition:           point.Y,
		ID:                  pictureId,
		OriginalImageBase64: OriginalImageBase64,
		JigsawImageBase64:   JigsawImageBase64,
	}

	// 2: 将信息存入redis中(存入redis中时还需要进行point的存储),在验证时进行验证
	data, err := json.Marshal(point)
	if err != nil {
		uc.l.WithField("point", point).Info("Marshal point failed.")
		return
	}
	err = uc.captchaPlugin.SetBlockPuzzleCaptcha(pictureId, data)
	if err != nil {
		uc.l.WithFields(logger.Fields{
			"token": pictureId,
		}).WithError(err).Info("Set BlockPuzzleCaptcha into redis failed.")
		return
	}
	h.SendOK(c, captcha)
}

// CaptchaCheck godoc
// @Summary 验证码校验
// @Description  author: yff
// @Tags  user
// @Accept  json
// @Produce  json
// @Param req body model.CaptchaCheckRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/captcha/check [post]
func (uc *UserController) CaptchaCheck(c *gin.Context) {
	var req model.CaptchaCheckRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("Captcha check invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	content, err := uc.captchaPlugin.GetAndDeleteBlockPuzzleCaptchaValue(req.ID)
	if err != nil {
		if err == redis.Nil {
			h.SendError(c, biz.ErrCaptchaExpired)
			return
		}
		uc.l.WithField("Id", req.ID).WithError(err).Info("Get value from redis by picture id failed.")
		h.SendError(c, biz.ErrCheckCaptchaFailed)
		return
	}
	value := &cap.PicturePoint{}
	_ = json.Unmarshal(content, value)
	inputPosition := &cap.PicturePoint{
		X: req.XPosition,
		Y: req.YPosition,
	}

	if !cap.CheckPointIsValid(inputPosition, value, 10) {
		h.SendError(c, biz.ErrCheckCaptchaFailed)
		return
	}

	err = uc.captchaPlugin.SetCheckResult(req.ID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	h.SendOK(c, nil)
}

// GetLoginLogo godoc
// @Summary 获取登录logo
// @Description  author: yff
// @Tags  user
// @Accept  json
// @Produce  json
// @Param req body model.DefaultNullRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/login/logo [get]
func (uc *UserController) GetLoginLogo(c *gin.Context) {
	dir, err := os.ReadDir(storage.LogoStorageRootFSPath())
	if err != nil {
		log.Error(err)
		h.SendError(c, biz.ErrInternalError)
		return
	}
	logoPath := ""
	for _, fileInfo := range dir {
		if matched, err := regexp.MatchString("loginlogo\\.*", fileInfo.Name()); err != nil || !matched {
			continue
		}
		logoPath = filepath.Join(storage.LogoStorageRootFSPath(), fileInfo.Name())
	}
	if len(logoPath) == 0 {
		log.Error("Login logo not found")
		h.SendError(c, biz.ErrInternalError)
		return
	}
	c.File(logoPath)
}

// GetDetail godoc
// @Summary 用户查看个人信息
// @Description  author: yff
// @Tags  user
// @Accept  json
// @Produce  json
// @Param req body model.UserIdRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/user/detail [get]
func (uc *UserController) GetDetail(c *gin.Context) {
	userInfo, err := uc.userService.FindByUserId(h.GetUserInfo(c).UID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	//userInfo.Mask()

	h.SendOK(c, userInfo)
}

// UpdateUserName godoc
// @Summary 用户修改用户名
// @Description  author: yff
// @Tags  user
// @Accept  json
// @Produce  json
// @Param req body model.UpdateUserNameRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/user/name/update [post]
func (uc *UserController) UpdateUserName(c *gin.Context) {
	var req model.UpdateUserNameRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("Update user name invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	err := uc.userService.UserUpdateName(h.GetUserInfo(c).UID, req.Name)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)
}

// GetUserInviteDetail godoc
// @Summary 获取用户邀请有礼详情
// @Description  author: yff
// @Tags  user
// @Accept  json
// @Produce  json
// @Param req body model.UserIdRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/user/invite/detail [get]
func (uc *UserController) GetUserInviteDetail(c *gin.Context) {
	// 获取相关信息
	uid := h.GetUserInfo(c).UID
	detail, err := uc.userService.UserInviteDetail(uid)
	if err != nil {
		err = biz.ErrGetUserInviteDetailFailed
		uc.l.WithField("id", uid).WithError(err).Error("Get user invite detail failed.")
		return
	}
	h.SendOK(c, detail)
}

// SyncTime godoc
// @Summary 同步时间
// @Description  author: yff
// @Tags  user
// @Accept  json
// @Produce  json
// @Param req body model.DefaultNullRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/time_sync [get]
func (uc *UserController) SyncTime(c *gin.Context) {
	timeNow := uc.plugin.Now()
	h.SendOK(c, timeNow)
}

type UserMachineListRequest struct {
	*machineModel.GetMachineListParams
	*db_helper.GetPagedRangeRequest
}

// GetUserMachineList godoc
// @Summary GetUserMachineList
// @Description  author: yff
// @Tags  user
// @Accept  json
// @Produce  json
// @Param req body UserMachineListRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/user/machine/list [post]
func (uc *UserController) GetUserMachineList(c *gin.Context) {
	var req UserMachineListRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("UserController.GetUserMachineList invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}
	u := h.GetUserInfo(c)
	paged, machineInfoList, err := uc.userService.GetUserMachineList(u.UID, req.GetMachineListParams, req.GetPagedRangeRequest)
	if err != nil {
		uc.l.WithField("params", req).WithError(err).Info("get user machine list failed.")
		h.SendError(c, err)
		return
	}
	for k, v := range machineInfoList {
		v.Mask()
		if v.HighestCudaVersion == "" {
			machineInfoList[k].HighestCudaVersion = "99"
		}
	}
	if len(machineInfoList) == 0 {
		paged.List = []*machineModel.MachineInfo{}
	} else {
		paged.List = machineInfoList
	}
	h.SendOK(c, paged)
}

type UserUUIDRequest struct {
	UserUUID string `json:"user_uuid"`
}

func (uc *UserController) GetUserByUUID(c *gin.Context) {
	var req UserUUIDRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("UserController.GetUserByUUID invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}
	user, err := uc.userService.FindByUserUuid(req.UserUUID)
	if err != nil {
		uc.l.WithField("params", req).WithError(err).Info("get user by uuid failed.")
		h.SendError(c, err)
		return
	}
	user.Mask()
	h.SendOK(c, user)
}

type UserUUIDResp struct {
	ID       int    `json:"id"`
	UserName string `json:"user_name"`
}

func (uc *UserController) GetUserNameByUUID(c *gin.Context) {
	var req UserUUIDRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("UserController.GetUserByUUID invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}
	user, err := uc.userService.FindByUserUuid(req.UserUUID)
	if err != nil {
		uc.l.WithField("params", req).WithError(err).Info("get user by uuid failed.")
		h.SendError(c, err)
		return
	}

	userInfo := &UserUUIDResp{
		ID:       user.ID,
		UserName: user.Username,
	}

	h.SendOK(c, userInfo)
}

func (uc *UserController) UserAgreementSign(c *gin.Context) {
	agreementID := c.Query("agreement")
	if !libs.UserAgreementExist(agreementID) {
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	u := h.GetUserInfo(c)
	err := uc.userService.UserAgreementSign(u.UID, u.SubName, agreementID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	h.SendOK(c, nil)
}

func (uc *UserController) UserAgreementSignedGet(c *gin.Context) {
	agreementID := c.Query("agreement")
	if !libs.UserAgreementExist(agreementID) {
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	u := h.GetUserInfo(c)
	signed, err := uc.userService.UserAgreementSignedCheck(u.UID, u.SubName, agreementID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	h.SendOK(c, signed)
}

func (uc *UserController) ChangePhone(c *gin.Context) {
	var req model.CtrlChangePhoneReq
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("UserController.ChangePhone invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	if strings.HasPrefix(req.NewPhone, "1621090") {
		h.SendError(c, biz.ErrPhoneWrongFormat)
		return
	}

	u := h.GetUserInfo(c)
	_, err := uc.userService.FindByUserPhone(req.NewPhone)
	if err == nil {
		h.SendError(c, biz.ErrChangePhoneAlreadyRegister)
		return
	}

	user, err := uc.userService.FindUserById(u.UID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	if user.Phone == req.NewPhone {
		h.SendError(c, biz.ErrChangePhoneAlreadyRegister)
		return
	}

	phoneWithArea, err := uc.userService.PhoneAndAreaCheck(req.PhoneArea, req.NewPhone)
	if err != nil {
		h.SendError(c, err)
		return
	}

	// 错误次数校验
	pvCheckKey := libs.StringCombination(phoneWithArea, "change_phone")
	pv, _ := uc.plugin.PV(redis_plugin.IpPVKeyPrefix, pvCheckKey)
	if pv >= constant.LoginFailedPVLimit {
		err := uc.userService.ForbiddenUserLogin(pvCheckKey)
		if err != nil {
			h.SendError(c, biz.ErrInternalError)
			return
		}
		h.SendError(c, biz.ErrChangePhoneIsLimitedFmt.New().Format(constant.LoginFailedPVLimit, constant.LoginFailedLimitTimeout.Minutes()))
		return
	}
	err = uc.plugin.PVIncrement(redis_plugin.IpPVKeyPrefix, pvCheckKey, constant.FailedConsequentInterval)
	if err != nil {
		uc.l.WithField("err", err).Info("Login PVIncrement in redis failed.")
		h.SendError(c, biz.ErrInternalError)
		return
	}

	// 短信校验
	right, err := uc.plugin.CheckVCodeValueIsRight(phoneWithArea, req.VCode)
	if err != nil {
		uc.l.WithField("err", err).Info("Check vCode value failed.")
		h.SendError(c, biz.ErrCheckUserVCodeFailed)
		return
	}
	if !right {
		if pv > 4 {
			err = uc.userService.ForbiddenUserLogin(phoneWithArea)
			if err != nil {
				h.SendError(c, biz.ErrInternalError)
				return
			}
			h.SendError(c, biz.ErrChangePhoneIsLimitedFmt.New().Format(constant.LoginFailedPVLimit, constant.LoginFailedLimitTimeout.Minutes()))
			return
		}
		uc.l.WithFields(logger.Fields{"phone": req.NewPhone, "v_code": req.VCode}).WithError(err).Info("User VCode wrong.")
		h.SendError(c, biz.ErrCheckUserVCodeFailed)
		return
	}

	// 密码校验
	err = uc.userService.CheckLoginPassword(user.Password, req.Password)
	if err != nil {
		h.SendError(c, err)
		return
	}

	err = uc.userService.UserUpdatePhone(u.UID, req.PhoneArea, req.NewPhone)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)

	ro := model.OperateRecord{
		UID:     u.UID,
		Operate: constant.OperateChangePhone,
		PayloadEntity: map[string]interface{}{
			"uid":            u.UID,
			"old_phone":      user.Phone,
			"old_phone_area": user.PhoneArea,
			"new_phone":      req.NewPhone,
			"new_phone_area": req.PhoneArea,
		},
	}
	err = ro.OperateRecordCreate(c)
	if err != nil {
		uc.l.WarnE(err, "insert change phone operate record failed")
	}

	return
}

type GenerateSSOTicketResponse struct {
	Ticket string `json:"ticket"`
}

func (uc *UserController) GenerateSSOTicket(c *gin.Context) {
	u := h.GetUserInfo(c)

	ticket := libs.GenerateTicket(u.UID)
	err := uc.plugin.SetSSOTicketV2(ticket, u.UID, u.SubName)
	if err != nil {
		uc.l.WithField("err", err).Info("Set sso ticket into redis failed.")
		h.SendError(c, biz.ErrSetTicketFailed)
		return
	}

	resp := GenerateSSOTicketResponse{
		Ticket: ticket,
	}

	h.SendOK(c, resp)
}

type GetLoginRecordRequest struct {
	*db_helper.GetPagedRangeRequest
}

func (uc *UserController) GetLoginRecord(c *gin.Context) {
	var req GetLoginRecordRequest
	if err := c.ShouldBind(&req); err != nil {
		uc.l.WithField("err", err).Error("UserController.GetLoginRecord invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}
	u := h.GetUserInfo(c)
	paged, _, err := uc.userService.GetLoginRecord(u.UID, req.GetPagedRangeRequest)
	if err != nil {
		uc.l.WithFields(map[string]interface{}{
			"params": req,
			"uid":    u.UID,
		}).ErrorE(err, "get user login record list failed.")
		h.SendError(c, err)
		return
	}

	h.SendOK(c, paged)
}
