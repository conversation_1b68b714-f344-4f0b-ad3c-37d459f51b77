package controller

import (
	"github.com/gin-gonic/gin"
	serverApi "server/pkg/api"
	biz "server/pkg/businesserror"
	h "server/pkg/http"
)

func (uc *UserController) SubUserSPAC(c *gin.Context) {
	var req serverApi.GetSubUserSPACReq
	if err := c.ShouldBind(&req); err != nil {
		uc.l.ErrorE(err, "SubUserSPAC bind failed")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	if req.UID == 0 {
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	subPath, err := uc.spacPlugin.PACGet(req.UID, req.SubName)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, subPath)
}
