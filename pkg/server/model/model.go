package model

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

const TableNameSku = "server_sku"
const TableNameCartProduct = "server_cart_product"
const TableNameCartProductShare = "server_cart_product_share"

type SKU struct {
	ID        int             `gorm:"column:id;type:int;not null;auto_increment;primary key;" json:"-"`
	CreatedAt time.Time       `gorm:"column:created_at;type:datetime;not null;" json:"-"`
	UpdatedAt time.Time       `gorm:"column:updated_at;type:datetime;not null;" json:"-"`
	DeletedAt gorm.DeletedAt  `gorm:"column:deleted_at;type:datetime;default:null;index;" json:"-"`
	SKU       string          `gorm:"column:sku;type:varchar(100);not null;" json:"-"`
	Type      string          `gorm:"column:type;type:varchar(100);not null;" json:"-"`
	TypeName  string          `gorm:"column:type_name;type:varchar(100);not null;" json:"-"`
	BaseInfo  json.RawMessage `gorm:"column:base_info;type:text;default:null;" json:"-"`
	Config    json.RawMessage `gorm:"column:config;type:text;default:null;" json:"-"`
	Version   string          `gorm:"column:version;type:varchar(100);not null;" json:"-"`
}

func (*SKU) TableName() string {
	return TableNameSku
}

func (*SKU) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&SKU{})
}

type CartProduct struct {
	ID        int             `gorm:"column:id;type:int;not null;auto_increment;primary key;" json:"-"`
	CreatedAt time.Time       `gorm:"column:created_at;type:datetime;not null;" json:"-"`
	UpdatedAt time.Time       `gorm:"column:updated_at;type:datetime;not null;" json:"-"`
	DeletedAt gorm.DeletedAt  `gorm:"column:deleted_at;type:datetime;default:null;index;" json:"-"`
	UID       int             `gorm:"column:uid;type:int;not null;" json:"-"`
	Phone     string          `gorm:"column:phone;type:varchar(255);not null;" json:"-"`
	Name      string          `gorm:"column:name;type:varchar(100);not null;" json:"-"`
	Price     int64           `gorm:"column:price;type:bigint;not null;" json:"-"`
	Detail    json.RawMessage `gorm:"column:detail;type:text;default:null;" json:"-"`
	Version   string          `gorm:"column:version;type:varchar(100);not null;" json:"-"`
}

func (*CartProduct) TableName() string {
	return TableNameCartProduct
}

func (*CartProduct) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&CartProduct{})
}

type CartProductDetailV1 struct {
	Name  string `json:"name" validator:"required"`
	Price int64  `json:"price" validator:"required, gte=0"`
	CPU   struct {
		SkuName string `json:"sku_name" validator:"required"`
		Desc    string `json:"desc" validator:"required"`
	} `json:"cpu"`
	GPU struct {
		SkuName string `json:"sku_name" validator:"required"`
		Desc    string `json:"desc" validator:"required"`
	} `json:"gpu"`
	Memory struct {
		SkuName string `json:"sku_name" validator:"required"`
		Desc    string `json:"desc" validator:"required"`
	} `json:"memory"`
	SystemDisk struct {
		SkuName string `json:"sku_name" validator:"required"`
		Desc    string `json:"desc" validator:"required"`
	} `json:"system_disk"`
	DataDisk struct {
		SkuName string `json:"sku_name" validator:"required"`
		Desc    string `json:"desc" validator:"required"`
	} `json:"data_disk"`
	Warranty struct {
		SkuName string `json:"sku_name" validator:"required"`
		Desc    string `json:"desc" validator:"required"`
	} `json:"warranty"`
	Images []string `json:"images"`
	Others []struct {
		Name    string `json:"name"`
		Content struct {
			Title string `json:"title"`
			Desc  string `json:"desc"`
		} `json:"content"`
	} `json:"others"`
}

type CartProductShare struct {
	ID            int            `gorm:"column:id;type:int;not null;auto_increment;primary key;" json:"-"`
	CreatedAt     time.Time      `gorm:"column:created_at;type:datetime;not null;" json:"-"`
	UpdatedAt     time.Time      `gorm:"column:updated_at;type:datetime;not null;" json:"-"`
	DeletedAt     gorm.DeletedAt `gorm:"column:deleted_at;type:datetime;default:null;index;" json:"-"`
	UID           int            `gorm:"column:uid;type:int;not null;" json:"-"`
	CartProductID int            `gorm:"column:cart_product_id;type:int;not null;" json:"-"`
	Code          string         `gorm:"column:code;type:varchar(100);not null;" json:"-"`
}

func (*CartProductShare) TableName() string {
	return TableNameCartProductShare
}

func (*CartProductShare) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&CartProductShare{})
}
