package controller

import (
	"github.com/gin-gonic/gin"
	"server/pkg/businesserror"
	"server/pkg/http"
	"server/pkg/region/model"
)

// HSFSGetRegionList 获取支持高速文件存储的地区列表
func (ctrl *RegionController) HSFSGetRegionList(c *gin.Context) {
	user := http.GetUserInfo(c)
	list, err := ctrl.region.HSFSGetRegionList(user.UID)
	if err != nil {
		http.SendError(c, err)
		return
	}
	http.SendOK(c, list)
}

// HSFSInitPreview 初始化高速文件存储，预览
func (ctrl *RegionController) HSFSInitPreview(c *gin.Context) {
	var req model.HSFSInitRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.Error("failed to bind request HSFSInitPreview, err %s", err)
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user := http.GetUserInfo(c)
	req.UID = user.UID

	resp, err := ctrl.region.HSFSInitPreview(req)
	if err != nil {
		ctrl.log.Error("failed to HSFSInitPreview, req %+v, err %s", req, err)
		http.SendError(c, err)
		return
	}
	http.SendOK(c, resp)
}

// HSFSInit 初始化高速文件存储（开通、购买、支付）
func (ctrl *RegionController) HSFSInit(c *gin.Context) {
	var req model.HSFSInitRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.Error("failed to bind request HSFSInit, err %s", err)
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user := http.GetUserInfo(c)
	req.UID = user.UID
	req.SubName = user.SubName

	resp, err := ctrl.region.HSFSInit(req)
	if err != nil {
		ctrl.log.Error("failed to HSFSInit, req %+v, err %s", req, err)
		http.SendError(c, err)
		return
	}
	http.SendOK(c, resp)
}

// HSFSRenewalPreview 续费高速文件存储，预览
func (ctrl *RegionController) HSFSRenewalPreview(c *gin.Context) {
	var req model.HSFSRenewalRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.Error("failed to bind request HSFSRenewalPreview, err %s", err)
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user := http.GetUserInfo(c)
	req.UID = user.UID

	resp, err := ctrl.region.HSFSRenewalPreview(req)
	if err != nil {
		ctrl.log.Error("failed to HSFSRenewalPreview, req %+v, err %s", req, err)
		http.SendError(c, err)
		return
	}
	http.SendOK(c, resp)
}

// HSFSRenewal 续费高速文件存储
func (ctrl *RegionController) HSFSRenewal(c *gin.Context) {
	var req model.HSFSRenewalRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.Error("failed to bind request HSFSRenewal, err %s", err)
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user := http.GetUserInfo(c)
	req.UID = user.UID
	req.SubName = user.SubName

	resp, err := ctrl.region.HSFSRenewal(req)
	if err != nil {
		ctrl.log.Error("failed to HSFSRenewal, req %+v, err %s", req, err)
		http.SendError(c, err)
		return
	}
	http.SendOK(c, resp)
}

// HSFSExpandPreview 扩容高速文件存储，预览
func (ctrl *RegionController) HSFSExpandPreview(c *gin.Context) {
	var req model.HSFSExpandRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.Error("failed to bind request HSFSExpandPreview, err %s", err)
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user := http.GetUserInfo(c)
	req.UID = user.UID

	resp, err := ctrl.region.HSFSExpandPreview(req)
	if err != nil {
		ctrl.log.Error("failed to HSFSExpandPreview, req %+v, err %s", req, err)
		http.SendError(c, err)
		return
	}
	http.SendOK(c, resp)
}

// HSFSExpand 扩容高速文件存储
func (ctrl *RegionController) HSFSExpand(c *gin.Context) {
	var req model.HSFSExpandRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.Error("failed to bind request HSFSExpand, err %s", err)
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user := http.GetUserInfo(c)
	req.UID = user.UID
	req.SubName = user.SubName

	resp, err := ctrl.region.HSFSExpand(req)
	if err != nil {
		ctrl.log.Error("failed to HSFSExpand, req %+v, err %s", req, err)
		http.SendError(c, err)
		return
	}
	http.SendOK(c, resp)
}

// HSFSGetDetail 获取高速文件存储详情
func (ctrl *RegionController) HSFSGetDetail(c *gin.Context) {
	var req model.HSFSGetDetailRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.Error("failed to bind request HSFSGetDetail, err %s", err)
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user := http.GetUserInfo(c)
	req.UID = user.UID

	detail, err := ctrl.region.HSFSGetDetail(req)
	if err != nil {
		ctrl.log.Error("failed to HSFSGetDetail, err %s", err)
		http.SendError(c, err)
		return
	}
	http.SendOK(c, detail)
}

// HSFSUpdateSetting 更新高速文件存储配置
func (ctrl *RegionController) HSFSUpdateSetting(c *gin.Context) {
	var req model.HSFSUpdateSettingRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.Error("failed to bind request HSFSUpdateSetting, err %s", err)
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user := http.GetUserInfo(c)
	req.UID = user.UID
	req.SubName = user.SubName

	if err := ctrl.region.HSFSUpdateSetting(req); err != nil {
		ctrl.log.Error("failed to HSFSUpdateSetting, req %+v, err %s", req, err)
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)
}
