package controller

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"io"
	"server/pkg-core/api/coreapi"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/http"
	"server/pkg/libs"
	"server/pkg/region/model"
)

// 文件存储相关

func (ctrl *RegionController) GetFileStorageListForUser(c *gin.Context) {
	u := http.GetUserInfo(c)
	res, err := ctrl.region.GetFileStorageListForUser(u.UID)
	if err != nil {
		http.SendError(c, err)
		return
	}

	for i := range res {
		if res[i].ExportAddr1 == "***************" {
			res[i].ExportAddr1 = "region-4.autodl.com"
		}
	}

	http.SendOK(c, res)
}

// AdminFileStorageList
// @Summary admin file storage list
// @Description author: liangjunmo
// @Description limit-roles:admin,customer_service
// @Tags file storage
// @Accept  json
// @Produce  json
// @Param req body model.AdminFileStorageListReq true "admin file storage list"
// @Success 200 {object} http.CommonResponse{}
// @Router /admin/v1/file_storage/list [post]
func (ctrl *RegionController) AdminFileStorageList(c *gin.Context) {
	var req model.AdminFileStorageListReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Error("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	paged, err := ctrl.region.AdminFileStorageList(req)
	if err != nil {
		ctrl.log.WithError(err).Error("AdminFileStorageList failed")
		http.SendError(c, err)
		return
	}

	http.SendOK(c, paged)
}

// AdminUpdateFileStorageSetting
// @Summary admin update file storage setting
// @Description author: liangjunmo
// @Description limit-roles:admin,customer_service
// @Tags file storage
// @Accept  json
// @Produce  json
// @Param req body coreapi.UpdateFileStorageSettingReq true "update admin file storage setting"
// @Success 200 {object} http.CommonResponse{}
// @Router /admin/v1/file_storage/update_setting [post]
func (ctrl *RegionController) AdminUpdateFileStorageSetting(c *gin.Context) {
	var req coreapi.UpdateFileStorageSettingReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Error("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	ctrl.log.Info("AdminUpdateFileStorageSetting req: %+v", req)

	err := ctrl.region.AdminUpdateFileStorageSetting(&req)
	if err != nil {
		ctrl.log.WithError(err).Error("AdminUpdateFileStorageSetting failed")
		http.SendError(c, err)
		return
	}

	ctrl.log.Info("AdminUpdateFileStorageSetting set successfully: %+v", req)

	http.SendOK(c, nil)
}

func (ctrl *RegionController) FileStorageMountAccessList(c *gin.Context) {
	u := http.GetUserInfo(c)
	list, err := ctrl.region.FileStorageMountAccessList(u.UID)
	if err != nil {
		http.SendError(c, err)
		return
	}
	http.SendOK(c, list)
	return
}

func (ctrl *RegionController) FileStorageMountCtrl(c *gin.Context) {
	var req model.FsMountCtrlParams
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Error("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	u := http.GetUserInfo(c)
	req.UID = u.UID
	err := ctrl.region.FileStorageMountCtrl(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)
}

// AutoFsGetUsage godoc
// @Summary autofs最大使用量
// @Description author:zt
// @Tags autofs
// @Produce json
// @Success 200 {object} http.SendOKResponse "Success"
// @Router /api/v1/autofs/init [post]
func (ctrl *RegionController) AutoFsGetUsage(c *gin.Context) {
	var req struct {
		RegionSign constant.RegionSignType `form:"region_sign" json:"region_sign"`
	}
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Error("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	u := http.GetUserInfo(c)
	res, err := ctrl.region.AutoFsGetUsage(u.UID, req.RegionSign)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, res)
}

func (ctrl *RegionController) PublicApiGetFileStorageList(c *gin.Context) {
	b, err := io.ReadAll(c.Request.Body)
	if err != nil {
		ctrl.log.WithError(err).Error("read request failed")
		c.AbortWithStatus(404)
		return
	}

	m := make(map[string]interface{})
	if err = json.Unmarshal(b, &m); err != nil {
		ctrl.log.WithError(err).Error("unmarshal request failed")
		c.AbortWithStatus(404)
		return
	}
	ctrl.log.Info("PublicApiGetFileStorageList %+v", m)

	if !libs.CheckPublicApiRequest(m) {
		ctrl.log.Warn("CheckPublicApiRequest failed")
		c.AbortWithStatus(404)
		return
	}

	var req model.PublicApiGetFileStorageListRequest
	if err = json.Unmarshal(b, &req); err != nil {
		ctrl.log.WithError(err).Error("unmarshal request failed")
		c.AbortWithStatus(404)
		return
	}
	if req.RegionSign == "" {
		c.AbortWithStatus(404)
		return
	}

	res, err := ctrl.region.PublicApiGetFileStorageList(req)
	if err != nil {
		http.SendError(c, err)
		return
	}
	http.SendOK(c, res)
}

func (ctrl *RegionController) PublicApiReformatFileStorage(c *gin.Context) {
	b, err := io.ReadAll(c.Request.Body)
	if err != nil {
		ctrl.log.WithError(err).Error("read request failed")
		c.AbortWithStatus(404)
		return
	}

	m := make(map[string]interface{})
	if err = json.Unmarshal(b, &m); err != nil {
		ctrl.log.WithError(err).Error("unmarshal request failed")
		c.AbortWithStatus(404)
		return
	}
	ctrl.log.Info("PublicApiReformatFileStorage %+v", m)

	if !libs.CheckPublicApiRequest(m) {
		ctrl.log.Warn("CheckPublicApiRequest failed")
		c.AbortWithStatus(404)
		return
	}

	var req model.PublicApiReformatFileStorageRequest
	if err = json.Unmarshal(b, &req); err != nil {
		ctrl.log.WithError(err).Error("unmarshal request failed")
		c.AbortWithStatus(404)
		return
	}
	if req.RegionSign == "" || req.UID < 1 {
		c.AbortWithStatus(404)
		return
	}

	err = ctrl.region.PublicApiReformatFileStorage(req)
	if err != nil {
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)
}
