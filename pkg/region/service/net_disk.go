package service

import (
	"context"
	"errors"
	"server/pkg-agent/messenger"
	"server/pkg-core/api/coreapi"
	coreRegionModel "server/pkg-core/region/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/region/model"
	userModel "server/pkg/user/model"
	"server/plugin/queue_interface"
	"time"
)

// net disk

func (svc *RegionService) InitNetDiskWithRegionForUser(uid int, rs constant.RegionSignType, tenant string) (ru *coreRegionModel.NetDisk, err error) {
	var (
		user *userModel.User
	)
	user, err = svc.user.FindByUserId(uid)
	if err != nil {
		svc.log.WithError(err).WithField("uid", uid).Error("get user failed")
		return nil, err
	}

	if user.OpenId == "" {
		return nil, businesserror.ErrBindWeXinPlease
	}

	if user.Status == userModel.Disable {
		return nil, businesserror.ErrUserDisabled
	}

	params := coreapi.InitNetDiskWithRegionForUserReq{
		UID:            uid,
		RegionSignType: rs,
		Tenant:         tenant,
	}
	coreRes, err := svc.coreApi.InitNetDiskWithRegionForUser(params, nil)
	if err != nil {
		svc.log.WithField("params", params).ErrorE(err, "core api: InitNetDiskWithRegionForUser failed")
		return
	}

	err = coreRes.GetErr()
	if err != nil {
		return
	}

	ru = coreRes.Data
	return
}

func (svc *RegionService) GetUserNetDiskMountForInstance(uid int, rs constant.RegionSignType) (path string, exist, quotaOK bool, err error) {
	coreRes, err := svc.coreApi.GetUserNetDiskMountForInstance(coreapi.GetUserNetDiskMountForInstanceReq{UID: uid, RegionSignType: rs}, nil)
	if err != nil {
		svc.log.WithFields(map[string]interface{}{"uid": uid, "rs": rs}).ErrorE(err, "core api: GetUserNetDiskMountForInstance failed")
		return
	}

	err = coreRes.GetErr()
	if err != nil {
		return
	}

	path = coreRes.Data.Path
	exist = coreRes.Data.Exist
	quotaOK = coreRes.Data.QuotaOK
	return
}

func (svc *RegionService) GuardCronJobRegister(ctx context.Context, regionSign constant.RegionSignType, writer chan<- messenger.Message) {
	// 需要轮询发给 agent 的任务写在此处
	return
}

// NetDiskSetQuota 管理员为用户单独设置容量
func (svc *RegionService) NetDiskSetQuota(sign constant.RegionSignType, uid int, quota int64) error {
	params := coreapi.NetDiskSetQuotaReq{
		UID:            uid,
		RegionSignType: sign,
		Quota:          quota,
	}
	coreRes, err := svc.coreApi.NetDiskSetQuota(params, nil)
	if err != nil {
		svc.log.WithField("params", params).ErrorE(err, "core api: NetDiskSetQuota failed")
		return err
	}

	return coreRes.GetErr()
}

// GetSetQuotaMessage 获取外部设置quota的msg
func (svc *RegionService) GetSetQuotaMessage(sign constant.RegionSignType, uid int, quota int64, operateType messenger.MessageType) (msg *queue_interface.NewQueueForStorageAgentOnMachine, err error) {
	payloadStr := ""
	payload := constant.NetDiskInit{
		UID:             uid,
		RegionSign:      sign,
		UserQuotaInByte: quota,
	}

	payloadStr, err = payload.Marshal()
	if err != nil {
		svc.log.WithError(err).Error("marshal net disk init failed")
		err = businesserror.ErrInternalError
		return
	}

	return &queue_interface.NewQueueForStorageAgentOnMachine{
		RegionSign: sign,
		Message: messenger.Message{
			MsgID:   time.Now().Format(time.RFC3339Nano),
			Type:    operateType,
			Payload: payloadStr,
		},
	}, nil
}

func (svc *RegionService) GetUserNetDiskQuotaList(req model.UserNetDiskQuotaParams) (paged *db_helper.PagedData, list []coreRegionModel.UserNetDiskQuotaForAdminInfo, err error) {
	var (
		uids []int
	)
	if req.Phone != "" {
		uid, err := svc.user.CachePhoneToIDGet(req.Phone)
		if err != nil {
			if errors.Is(err, businesserror.ErrUserNotRegister) {
				return &db_helper.PagedData{List: []int{}}, nil, nil
			}
			svc.log.WithError(err).Error("user.CachePhoneToIDGet failed")
			return nil, nil, err
		}
		uids = []int{uid}
	}
	params := coreapi.GetUserNetDiskQuotaListReq{
		UIDs:     uids,
		PagedReq: &req.GetPagedRangeRequest,
		Sorts:    req.Sorts,
		Region:   req.Region,
	}
	coreRes, err := svc.coreApi.GetUserNetDiskQuotaList(params, nil)
	if err != nil {
		svc.log.WithField("params", params).ErrorE(err, "core api: NetDiskSetQuota failed")
		return
	}

	err = coreRes.GetErr()
	if err != nil {
		return
	}

	coreRes.Data.List, err = svc.fillNetDisksDetail(coreRes.Data.List)
	if err != nil {
		return
	}
	coreRes.Data.Paged.List = coreRes.Data.List
	paged = coreRes.Data.Paged
	list = coreRes.Data.List
	return
}

func (svc *RegionService) fillNetDisksDetail(netDisks []coreRegionModel.UserNetDiskQuotaForAdminInfo) (list []coreRegionModel.UserNetDiskQuotaForAdminInfo, err error) {
	if len(netDisks) == 0 {
		svc.log.Info("user net disks is empty")
		return
	}

	var (
		netDiskExpandInfo *model.NetDiskExpansion
	)

	userPhoneMap := make(map[int]string)

	for i, v := range netDisks {
		netDiskExpandInfo, err = svc.GetLastExpansionRecord(v.UID, v.RegionSign)
		if err != nil {
			svc.log.WithError(err).WithField("uid", v.UID).WithField("sign", v.RegionSign).Error("get expansionRecord failed")
			return
		}
		if netDiskExpandInfo == nil {
			var t time.Time
			netDisks[i].ExpandExpiredAt = t
			netDisks[i].IsExpanded = false
		} else {
			netDisks[i].ExpandExpiredAt = netDiskExpandInfo.ExpiredAt
			netDisks[i].IsExpanded = true
		}

		if _, ok := userPhoneMap[v.UID]; !ok {
			phone := svc.tryToGetUserPhone(v.UID)
			userPhoneMap[v.UID] = phone
		}
		netDisks[i].UserPhone = userPhoneMap[v.UID]
		netDisks[i].RegionSign = v.RegionSign
	}

	return netDisks, nil
}

func (svc *RegionService) tryToGetUserPhone(uid int) string {
	user, err := svc.user.FindByUserId(uid)
	if err != nil {
		svc.log.WithField("uid", uid).WarnE(err, "FindByUserId() in TryToGetUserPhone() failed.")
		return ""
	}
	return user.Phone
}

func (svc *RegionService) getNetDisk(uid int, rs constant.RegionSignType) (netDisk *coreRegionModel.NetDisk, err error) {
	coreRes, err := svc.coreApi.GetNetDisk(coreapi.GetNetDiskReq{UID: uid, RS: rs}, nil)
	if err != nil {
		svc.log.WithFields(map[string]interface{}{"uid": uid, "rs": rs}).ErrorE(err, "core api: GetNetDisk failed")
		return
	}

	err = coreRes.GetErr()
	if err != nil {
		return
	}

	netDisk = coreRes.Data
	return
}
