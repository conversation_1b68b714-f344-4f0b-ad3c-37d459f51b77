package service

import (
	"context"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/region/model"
	"server/plugin/queue"
	"time"
)

func (svc *RegionService) CronJobRegister(ctx context.Context) {
	go svc.cornCheckExpiredExpansion(ctx)
}

func (svc *RegionService) MsgRegister() (queues []queue.RegisterInfo) {
	return []queue.RegisterInfo{}
}

func (svc *RegionService) cornCheckExpiredExpansion(ctx context.Context) {
	ticker := time.NewTicker(constant.RegionCheckNetDiskExpansionExpired)

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			list, err := svc.GetExpiredExpansionRecordList()
			if err != nil {
				svc.log.WithError(err).Warn("corn job: jet expansion record list failed")
				continue
			}

			for _, record := range list {
				if time.Now().Before(record.ExpiredAt) {
					continue
				}

				netDisk, err := svc.getNetDisk(record.UID, record.RegionSign)
				if err != nil {
					svc.log.WithField("uid", record.UID).WithError(err).Warn("corn job: get net disk info failed")
					continue
				}

				region, err := svc.GetRegionDetail(record.RegionSign)
				if err != nil {
					svc.log.WithField("region", record.RegionSign).ErrorE(err, "get region failed")
					continue
				}

				// 现在出现了此处计算之后为0甚至小于0的情况
				// 小于0的情况知道是因为core与上层同步为题,但是等于0的情况(netDisk 记录中缺少了默认容量),暂时还不知道原因,此处加一层这种情况的拦截
				targetCapacity := netDisk.UserQuotaInByte - record.TotalExpandCapacity
				if targetCapacity < region.DefaultUserQuotaInByte {
					svc.log.WithFields(map[string]interface{}{"record_id": record.ID, "netdisk_id": netDisk.ID}).ErrorE(err, "corn job: net disk targetCapacity < 0!")
					targetCapacity = region.DefaultUserQuotaInByte
				}

				err = svc.NetDiskSetQuota(record.RegionSign, record.UID, targetCapacity)
				if err != nil {
					svc.log.WithError(err).Warn("set quota failed")
					continue
				}

				err = svc.DeleteExpansionRecordByID(nil, record.ID)
				if err != nil {
					svc.log.WithError(err).Warn("delete expansion record failed.")
				}
			}

		}
	}
}

// CoreModifyRegion 用于更新machine的设置
func (svc *RegionService) CoreModifyRegion(payload *constant.MQCoreBusinessModifyDataPayload) (err error) {
	switch payload.DBAction {
	case constant.DBInsert:
		return svc.coreInsertRegionRecord(payload)
	case constant.DBUpdate:
		return svc.coreUpdateRegionRecord(payload)
	case constant.DBDelete:
		return svc.coreDeleteRegionRecord(payload)
	}
	svc.log.WithField("payload", payload).Error("payload.DBAction mismatch, skip it")
	return nil
}

func (svc *RegionService) coreInsertRegionRecord(payload *constant.MQCoreBusinessModifyDataPayload) (err error) {
	err = db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: &model.Region{},
		InsertPayload:   payload.Data,
	}).GetError()
	if err != nil {
		svc.log.WithField("payload", payload).ErrorE(err, "core insert Region failed")
		return
	}
	return
}

func (svc *RegionService) coreUpdateRegionRecord(payload *constant.MQCoreBusinessModifyDataPayload) (err error) {
	payload.Data["updated_at"] = time.Now()
	err = db_helper.UpdateOne(db_helper.QueryDefinition{
		ModelDefinition: &model.Region{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{
			payload.Key.String(): payload.Data[payload.Key.String()],
		}},
	}, payload.Data).GetError()
	if err != nil {
		svc.log.WithField("payload", payload).ErrorE(err, "core update Region failed")
		return
	}
	return
}

func (svc *RegionService) coreDeleteRegionRecord(payload *constant.MQCoreBusinessModifyDataPayload) (err error) {
	err = db_helper.Delete(db_helper.QueryDefinition{
		ModelDefinition: &model.Region{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{
			payload.Key.String(): payload.Data[payload.Key.String()],
		}},
	}, &model.Region{}).GetError()
	if err != nil {
		svc.log.WithField("payload", payload).ErrorE(err, "core delete Region failed")
		return
	}
	return
}
