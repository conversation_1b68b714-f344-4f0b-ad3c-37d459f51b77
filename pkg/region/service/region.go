package service

import (
	"server/conf"
	"server/pkg-agent/agent_constant"
	"server/pkg-core/api/coreapi"
	coreRegionModel "server/pkg-core/region/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/libs"
	"server/pkg/logger"
	"server/pkg/module_definition"
	"server/pkg/notify"
	notifyModel "server/pkg/notify/model"
	"server/pkg/region/model"
	"server/plugin/queue"
	redisPlugin "server/plugin/redis_plugin"

	"github.com/pkg/errors"
	"gorm.io/gorm"
)

const ModuleName = "region_service"

type RegionService struct {
	storagePlugin    *redisPlugin.RegionPlugin
	log              *logger.Logger
	queue            *queue.Q
	user             module_definition.UserInterface
	coreApi          *coreapi.Api
	bc               module_definition.BCInterface
	notify           module_definition.NotifyInterface
	smsNotifyChannel notify.Channel
}

func NewRegionServiceProvider(
	q *queue.Q,
	redis *redisPlugin.RegionPlugin,
	user module_definition.UserInterface,
	coreApi *coreapi.Api,
	bc module_definition.BCInterface,
	notify module_definition.NotifyInterface,
	smsNotifyChannel notify.Channel,
) *RegionService {
	return &RegionService{
		log:              logger.NewLogger(ModuleName),
		queue:            q,
		storagePlugin:    redis,
		user:             user,
		coreApi:          coreApi,
		bc:               bc,
		notify:           notify,
		smsNotifyChannel: smsNotifyChannel,
	}
}

// crud

// CreateRegion 创建地区: by core api
func (svc *RegionService) CreateRegion(params *model.CreateRegionParams) (res *coreRegionModel.Region, err error) {
	newRegion := coreapi.CreateRegionReq{
		Sign:                   params.Sign,
		Name:                   params.Name,
		PortRangeMin:           params.PortRangeMin,
		PortRangeMax:           params.PortRangeMax,
		ExportAddr1:            params.ExportAddr1,
		ExportAddr2:            params.ExportAddr2,
		ProxyToken:             constant.FrpcDefaultProxyToken, // default: 'seetatech666'
		NFSAddr:                "",                             // by FillNFSAddrIfAvailable()
		NFSPort:                params.NFSPort,
		NFSHealth:              constant.NFSMachineUnknown, // update by redis ping later
		IsNFSAvailable:         params.IsNFSAvailable,
		DefaultUserQuotaInByte: params.DefaultUserQuotaInByte,
	}
	if newRegion.IsNFSAvailable {
		newRegion.NFSAddr = newRegion.ExportAddr1
	}

	exist := false
	exist, err = svc.checkRegionSignExist(newRegion.Sign)
	if err != nil {
		return
	}
	if exist {
		err = businesserror.ErrRegionSionAlreadyExist
		return
	}
	exist, err = svc.checkRegionNameExist(newRegion.Name)
	if err != nil {
		return
	}
	if exist {
		err = businesserror.ErrRegionNameAlreadyExist
		return
	}

	coreRes, err := svc.coreApi.CreateRegion(newRegion, nil)
	if err != nil {
		svc.log.WithField("newRegion", newRegion).ErrorE(err, "core api: create region failed")
		err = businesserror.ErrInternalError
		return
	}

	err = coreRes.GetErr()
	if err != nil {
		svc.log.WithField("newRegion", newRegion).ErrorE(err, "core api: create region failed")
		return
	}
	res = coreRes.Data

	// note:core api创建完成后，本地的记录由core回调msg进行创建，不在上层直接创建
	return
}

func (svc *RegionService) GetRegionDetail(rs constant.RegionSignType) (region *model.Region, err error) {
	return svc.getRegionDetail(db_helper.GlobalDBConn(), rs)
}

func (svc *RegionService) GetRegionDetailFromRO(rs constant.RegionSignType) (region *model.Region, err error) {
	return svc.getRegionDetail(db_helper.GlobalDBConnForRead(), rs)
}

func (svc *RegionService) getRegionDetail(db *gorm.DB, rs constant.RegionSignType) (region *model.Region, err error) {
	region = new(model.Region)
	err = db_helper.GetFirst(db_helper.QueryDefinition{
		DBTransactionConnection: db,
		ModelDefinition:         &model.Region{},
		Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"sign": rs}},
	}, &region).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = businesserror.ErrRegionSionNotFound
			return
		}
		svc.log.WithError(err).WithField("sign", rs).Error("get region by sign failed")
		err = businesserror.ErrDatabaseError
		return
	}

	return
}

func (svc *RegionService) GetRegionDetailWithStorageInfo(rs constant.RegionSignType) (region *coreRegionModel.Region, storage coreRegionModel.RegionStorageOSSDetailList, err error) {
	coreRes, err := svc.coreApi.GetRegionDetailWithStorageInfo(coreapi.GetRegionDetailWithStorageInfoReq{RegionSignType: rs}, nil)
	if err != nil {
		svc.log.WithField("region", rs).ErrorE(err, "core api: GetStorageDetail failed")
		err = businesserror.ErrInternalError
		return
	}

	err = coreRes.GetErr()
	if err != nil {
		svc.log.WithField("region", rs).ErrorE(err, "core api: GetStorageDetail failed")
		return
	}
	region = coreRes.Data.Region
	storage = coreRes.Data.StorageList
	//region = new(model.Region)
	//err = db_helper.GetFirst(db_helper.QueryDefinition{
	//	ModelDefinition: &model.Region{},
	//	Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"sign": rs}},
	//}, &region).GetError()
	//if err != nil {
	//	if errors.Is(err, gorm.ErrRecordNotFound) {
	//		err = businesserror.ErrRegionSionNotFound
	//		return
	//	}
	//	svc.log.WithError(err).WithField("sign", rs).Error("get region by sign failed")
	//	err = businesserror.ErrDatabaseError
	//	return
	//}
	//storage, err = model.GetRegionStorageOssList(rs)
	svc.log.WithFields(logger.Fields{"region": *region, "storage": storage, "region_sign": rs, "err": err}).Info("-------------GetRegionDetailWithStorageInfo")
	return
}

func (svc *RegionService) GetStorageDetail(rs agent_constant.StorageOSSSignType) (oss *coreRegionModel.StorageOSS, err error) {
	coreRes, err := svc.coreApi.GetStorageDetail(coreapi.GetStorageDetailReq{SignType: rs}, nil)
	if err != nil {
		svc.log.WithField("region", rs).ErrorE(err, "core api: GetStorageDetail failed")
		err = businesserror.ErrInternalError
		return
	}

	err = coreRes.GetErr()
	if err != nil {
		svc.log.WithField("region", rs).ErrorE(err, "core api: GetStorageDetail failed")
		return
	}
	oss = coreRes.Data
	svc.log.WithFields(logger.Fields{"oss": *oss, "region_sign": rs, "err": err}).Info("-------------GetStorageDetail")

	//oss = new(model.StorageOSS)
	//err = db_helper.GetFirst(db_helper.QueryDefinition{
	//	ModelDefinition: &model.StorageOSS{},
	//	Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"sign": rs}},
	//}, &oss).GetError()
	//if err != nil {
	//	if errors.Is(err, gorm.ErrRecordNotFound) {
	//		err = businesserror.ErrRegionSionNotFound
	//		return
	//	}
	//	svc.log.WithError(err).WithField("sign", rs).Error("get storage oss by sign failed")
	//	err = businesserror.ErrDatabaseError
	//	return
	//}

	return
}

func (svc *RegionService) GetRegionList() (regions []model.Region, err error) {
	// 不超过 100 个
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.Region{},
		Filters:         db_helper.QueryFilters{Orders: []string{"rank desc"}},
	}, &regions).GetError()
	if err != nil {
		svc.log.WithError(err).Error("get regions failed")
		err = businesserror.ErrDatabaseError
		return
	}

	return
}

func (svc *RegionService) GetRegionListByDatacenter(datacenterList []string, datacenter string) (regions []model.Region, err error) {
	// 不超过 100 个
	if len(datacenterList) != 0 {
		err = db_helper.GetAll(db_helper.QueryDefinition{
			ModelDefinition: &model.Region{},
			Filters: db_helper.QueryFilters{
				Orders: []string{"rank desc"},
				InFilters: []db_helper.In{
					{
						Key:   "data_center",
						InSet: datacenterList,
					},
				},
			}}, &regions).GetError()
		if err != nil {
			svc.log.WithField("data_center_list", datacenterList).ErrorE(err, "get regions with data_center_list failed")
			return
		}
	} else {
		err = db_helper.GetAll(db_helper.QueryDefinition{
			ModelDefinition: &model.Region{},
			Filters: db_helper.QueryFilters{
				Orders:       []string{"rank desc"},
				EqualFilters: map[string]interface{}{"data_center": datacenter},
			}}, &regions).GetError()

		if err != nil {
			svc.log.WithField("data_center", datacenter).ErrorE(err, "get regions with data_center failed")
			return
		}
	}

	return
}

func (svc *RegionService) checkRegionSignExist(rs constant.RegionSignType) (exist bool, err error) {
	var count int64
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: &model.Region{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"sign": rs}},
	}, &count).GetError()
	if err != nil {
		svc.log.WithError(err).Warn("count region by sign failed.")
		err = businesserror.ErrDatabaseError
		return
	}

	exist = count != 0
	return
}

func (svc *RegionService) checkRegionNameExist(name string) (exist bool, err error) {
	var count int64
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: &model.Region{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"name": name}},
	}, &count).GetError()
	if err != nil {
		svc.log.WithError(err).Error("count region by name failed")
		err = businesserror.ErrDatabaseError
		return
	}

	exist = count != 0
	return
}

func (svc *RegionService) GetNetDiskListForUser(uid int) (res []model.RegionUsageInfo, err error) {
	params := coreapi.GetNetDiskListForUserReq{
		UID: uid,
	}
	coreRes, err := svc.coreApi.GetNetDiskListForUser(params, nil)
	if err != nil {
		svc.log.WithField("params", params).ErrorE(err, "core api: GetNetDiskListForUser failed")
		return
	}

	err = coreRes.GetErr()
	if err != nil {
		return
	}
	var (
		expansionList      []model.NetDiskExpansion
		regionExpansionMap = make(map[constant.RegionSignType][]model.NetDiskExpansion)
	)

	// 扩容情况
	expansionList, err = svc.getExpansionRecordActiveListByUID(uid)
	if err != nil {
		svc.log.WithError(err).Error("getExpansionRecordActiveListByUID failed")
		return nil, err
	}
	for _, v := range expansionList {
		regionExpansionMap[v.RegionSign] = append(regionExpansionMap[v.RegionSign], v)
	}

	res = make([]model.RegionUsageInfo, 0, len(coreRes.Data))

	for _, r := range coreRes.Data {
		expansion := regionExpansionMap[r.Sign]
		rr := model.RegionUsageInfo{
			Sign:                   r.Sign,
			Name:                   r.Name,
			ExportAddr1:            r.ExportAddr1,
			ExportAddr2:            r.ExportAddr1,
			DefaultUserQuotaInByte: r.DefaultUserQuotaInByte,
			NfsStatus:              r.NfsStatus,
			NFSPort:                r.NFSPort,
			Usage:                  r.Usage,
			ExpansionInfo:          expansion,
		}
		res = append(res, rr)
	}

	return
}

func (svc *RegionService) GetRegionListForIndex() (res []model.GetRegionListForIndexRes, err error) {
	var (
		regionList []model.Region
	)
	regionList, err = svc.GetRegionList()
	if err != nil {
		return
	}

	for _, v := range regionList {
		if v.Visible == "" {
			continue
		}
		res = append(res, model.GetRegionListForIndexRes{
			RegionSign:    v.Sign,
			RegionName:    v.Name,
			DataCenter:    v.DataCenter,
			Visible:       v.Visible,
			UsedFor:       v.UsedFor,
			SettingEntity: v.SettingEntity,
		})
	}
	return
}

// agent

func (svc *RegionService) AuthAgent(token string) (err error) {
	// FIXME: 此处临时套用 frps 的 token
	if token != conf.GetGlobalGsConfig().Frps.Token {
		svc.log.WithField("token", token).Info("Invalid storage agent token.")
		return businesserror.ErrAuthorizeFailed
	}
	return
}

func (svc *RegionService) RegisterAgent(rs constant.RegionSignType, token string) error {
	if err := svc.AuthAgent(token); err != nil {
		return err
	}

	// NOTE: 目前尚无其他特殊操作.

	return nil
}

func (svc *RegionService) CheckAgentRealHealth(rs constant.RegionSignType) bool {
	// TODO:
	return true
}

func (svc *RegionService) DistributeFrpcProxy(rs constant.RegionSignType) (proxyHosts []string, proxyHost, proxyHostPublic string, proxyPort, proxyApiPort int, proxyToken string, region *model.Region, err error) {
	if len(rs) == 0 {
		rs = constant.RegionDefault
	}

	region, err = svc.GetRegionDetail(rs)
	if err != nil {
		svc.log.ErrorE(err, "Get region failed. region_sign: %s", rs)
		return
	}

	proxyHost = svc.selectExportAddr(rs, region.ExportAddr1, region.ExportAddr2) // select one addr, 可能是公网,可能是内网

	// 上面的proxyHost没动，可以理解为是主的proxy服务
	// 下面的proxyHosts所有支持的proxyHost，现在最多只会有两个，第二个是热备，为后面方便扩展保存为数组
	// 只有配置了热备（配置了export_addr_3）的地区proxyHosts才会有两个，容器里会起热备的proxy客户端，没配置的就不会起热备的proxy客户端
	proxyHosts = make([]string, 0)
	proxyHosts = append(proxyHosts, proxyHost)
	if region.ExportAddr3 != "" {
		proxyHosts = append(proxyHosts, region.ExportAddr3)
	}

	proxyHostPublic = region.ExportAddr1 // 一定是公网地址
	if region.ProxyPort != 0 {
		proxyPort = region.ProxyPort
	} else {
		proxyPort = constant.FrpcDefaultProxyPort // default 7000
	}
	proxyApiPort = region.NFSPort  // storage agent api
	proxyToken = region.ProxyToken // default seetatech666
	return
}

func (svc *RegionService) selectExportAddr(rs constant.RegionSignType, addr1, addr2 string) string {
	// 用于 jupyter 等, 封装选择出口地址的方法, 目前默认用第一个, 以后更改策略时修改此处.
	// 前端在网盘列表中可以获得所有出口地址, 由前端自行判断.

	if addr2 != "" {
		return addr2
	}
	return addr1
}

func (svc *RegionService) AdminSendRegionReleaseSms(params model.SendSmsReq) (err error) {
	if params.RegionName == "" || params.RegionSign == "" || params.Phone == "" {
		return businesserror.ErrInvalidRequestParams
	}

	region := &model.Region{}
	err = region.RegionGet(&db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"name": params.RegionName,
			"sign": params.RegionSign,
		},
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return businesserror.ErrRegionNameOrSignNotFound
		}
		svc.log.WithField("name", params.RegionName).ErrorE(err, "get region by name failed")
		return err
	}

	user, err := svc.user.FindByUserPhone(params.Phone)
	if err != nil {
		return
	}

	subName, area, phone, _, ok := svc.user.GetPhoneForSmsNotify(user.ID, "")
	if !ok {
		return
	}

	phoneNum := libs.PhoneBuildWithArea(area, phone)

	if area == "+1" {
		phoneNum = libs.PhoneBuildWithOverseasArea(area, phone)
	}

	input := notify.SmsNotifyInput{
		PhoneArea:        area,
		Phone:            phoneNum,
		SmsTemplateParam: libs.BuildSMSTemplateParamRegionRelease(params.RegionName),
		SMSType:          params.SmsType,
	}

	_, err = svc.smsNotifyChannel.Notify(input)
	if err != nil {
		svc.log.WithField("uid", user.ID).ErrorE(err, "notify failed")
		return
	}

	err = svc.notify.SaveNotifyRecord(nil, &notifyModel.NotifyRecord{
		UID:     user.ID,
		SubName: subName,
		Type:    notifyModel.NotifyTypeRegionRelease,
		Channel: notify.ChannelSMS,
		Content: input.String(),
		Phone:   phone,
	})
	if err != nil {
		svc.log.ErrorE(err, "save notify failed")
		return err
	}

	return
}
