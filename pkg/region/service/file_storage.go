package service

import (
	"errors"
	"github.com/spf13/cast"
	"gorm.io/gorm"
	"server/conf"
	"server/pkg-core/api/coreapi"
	coreRegionModel "server/pkg-core/region/model"
	bcm "server/pkg/billing_center/model"
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/region/model"
	userModel "server/pkg/user/model"
	"time"
)

// 文件存储相关

func (svc *RegionService) InitFileStorageWithRegionForUser(user *userModel.User, rs constant.RegionSignType) (ru *coreRegionModel.FileStorage, err error) {
	params := coreapi.InitFileStorageWithRegionForUserReq{
		UID:            user.ID,
		RegionSignType: rs,
		Tenant:         conf.GetGlobalGsConfig().App.Platform,
	}
	coreRes, err := svc.coreApi.InitFileStorageWithRegionForUser(params, nil)
	if err != nil {
		svc.log.WithField("params", params).ErrorE(err, "core api: InitFileStorageWithRegionForUser failed")
		return
	}

	err = coreRes.GetErr()
	if err != nil {
		return
	}

	ru = coreRes.Data
	return
}

func (svc *RegionService) InitFileStorageFinal(payload string) (err error) {
	fsPayload := &constant.MQCoreBusinessInitFileStoragePayload{}
	err = fsPayload.ParseFromContent(payload)
	if err != nil {
		svc.log.ErrorE(err, "parse inner content failed, skip")
		return err
	}
	user, err := svc.user.FindByUserId(fsPayload.UID)
	if err != nil {
		return
	}

	if !fsPayload.IsSuccess {
		return
	}

	// 创建订单，文件存储初始化不扣钱，所以不需要创建账单
	now := time.Now()
	var cost int64 = 0
	order := &bcm.Order{
		UID:           fsPayload.UID,
		Username:      user.Username,
		UserPhone:     user.Phone,
		UUID:          fsPayload.OrderUUID,
		Status:        constant.OrderStatusSuccess,
		OrderType:     constant.OrderTypeFileStorage,
		ChargeType:    constant.ChargeTypePayg,
		PriceEntity:   &bcm.PriceInfo{OriginPrice: cost, DealPrice: cost},
		PayByBalance:  cost,
		DealPrice:     cost,
		DetailsEntity: map[string]interface{}{"region_sign": fsPayload.RS},
		CreatedAt:     now,
		UpdatedAt:     now,
		PayAt:         &now,
	}

	_, err = svc.bc.CreateOrder(order)
	if err != nil {
		svc.log.WithError(err).WithField("sign", fsPayload.RS).WithField("uid", fsPayload.UID).Error("CreateOrder error")
		return
	}

	err = svc.user.UpdateUserMap(fsPayload.UID, map[string]interface{}{"mount_adfs_disk_authority": 1})
	if err != nil {
		svc.log.WithError(err).WithField("sign", fsPayload.RS).WithField("uid", fsPayload.UID).Error("update user mount_net_disk_authority error")
		return
	}

	return nil
}

func (svc *RegionService) GetFileStorageListForUser(uid int) (res []coreRegionModel.RegionFileStorageInfo, err error) {
	params := coreapi.GetFileStorageListForUserReq{
		UID: uid,
	}
	coreRes, err := svc.coreApi.GetFileStorageListForUser(params, nil)
	if err != nil {
		svc.log.WithField("params", params).ErrorE(err, "core api: GetFileStorageListForUser failed")
		return
	}

	err = coreRes.GetErr()
	if err != nil {
		return
	}

	res = coreRes.Data
	return
}

func (svc *RegionService) GetFileStorageByUids(uids []int, rs constant.RegionSignType) (fs []coreRegionModel.FileStorage, err error) {
	params := coreapi.GetFileStorageByUidsReq{
		UIDs:           uids,
		RegionSignType: rs,
	}
	coreRes, err := svc.coreApi.GetFileStorageByUids(params, nil)
	if err != nil {
		svc.log.WithField("params", params).ErrorE(err, "core api: GetFileStorageListForUser failed")
		return
	}

	err = coreRes.GetErr()
	if err != nil {
		return
	}

	fs = coreRes.Data
	return
}

func (svc *RegionService) GetFileStorageForCharge(rs constant.RegionSignType, fsType constant.FsType, settleTime time.Time) (data *coreapi.GetFileStorageForChargeData, err error) {
	params := coreapi.GetFileStorageForChargeReq{
		Tenant:     conf.GetGlobalGsConfig().App.Platform,
		RegionSign: rs,
		FsType:     fsType,
		SettleDate: settleTime,
	}
	coreRes, err := svc.coreApi.GetFileStorageForCharge(params, nil)
	if err != nil {
		svc.log.WithField("params", params).ErrorE(err, "core api: GetFileStorageForCharge failed")
		return
	}

	err = coreRes.GetErr()
	if err != nil {
		return
	}

	data = &coreRes.Data
	return
}

func (svc *RegionService) AdminFileStorageList(req model.AdminFileStorageListReq) (*db_helper.PagedData, error) {
	var (
		err  error
		list []model.AdminFileStorage
	)

	params := coreapi.GetFileStorageAdminListReq{
		GetPagedRangeRequest: req.GetPagedRangeRequest,
		Tenant:               constant.TenantAutoDL,
		RegionSignType:       req.RegionSign,
		Sorts:                req.Sorts,
	}
	if req.Phone != "" {
		uid, err := svc.user.CachePhoneToIDGet(req.Phone)
		if err != nil {
			svc.log.WithError(err).Error("user.FindByUserPhone failed")
			if errors.Is(err, biz.ErrUserNotRegister) {
				return &db_helper.PagedData{List: []int{}}, nil
			}
			return nil, err
		}
		params.UIDs = []int{uid}
	}
	coreRes, err := svc.coreApi.GetFileStorageAdminList(params, nil)
	if err != nil {
		svc.log.WithField("params", params).ErrorE(err, "core api: GetFileStorageForCharge failed")
		return nil, err
	}

	err = coreRes.GetErr()
	if err != nil {
		return nil, err
	}

	userPhoneMap := make(map[int]string)

	for _, fs := range coreRes.Data.FileStorages {
		if _, ok := userPhoneMap[fs.UID]; !ok {
			phone := svc.tryToGetUserPhone(fs.UID)
			userPhoneMap[fs.UID] = phone
		}
		data := model.AdminFileStorage{
			UID:             fs.UID,
			Phone:           userPhoneMap[fs.UID],
			RegionSign:      fs.RegionSign,
			QuotaTotal:      fs.QuotaTotal,
			QuotaUsage:      fs.QuotaUsage,
			QuotaUsageRate:  fs.QuotaUsageRate,
			QuotaInodeTotal: fs.QuotaInodeTotal,
			QuotaInodeUsage: fs.QuotaInodeUsage,
			ConcurrentLimit: fs.ConcurrentLimit,
			FsType:          fs.FsType,
			MaxUploads:      fs.MaxUploads,
			CacheSize:       fs.CacheSize,
			BufferSize:      fs.BufferSize,
			CreatedAt:       fs.CreatedAt,
			FsConfigVersion: fs.FsConfigVersion,
		}
		list = append(list, data)
	}

	paged := db_helper.BuildPagedDataUtil(req.PageIndex, req.PageSize, coreRes.Data.Total, 0)
	paged.List = list

	return paged, nil
}

func (svc *RegionService) AdminUpdateFileStorageSetting(req *coreapi.UpdateFileStorageSettingReq) error {
	coreRes, err := svc.coreApi.UpdateFileStorageSetting(*req, nil)
	if err != nil {
		svc.log.WithField("params", req).ErrorE(err, "core api: GetFileStorageForCharge failed")
		return err
	}

	err = coreRes.GetErr()
	if err != nil {
		return err
	}

	return nil
}

func (svc *RegionService) FileStorageMountAccessList(uid int) (list []model.FsUnmountForUser, err error) {
	list = []model.FsUnmountForUser{}

	fileStorageList, err := svc.GetFileStorageListForUser(uid)
	if err != nil {
		svc.log.WithField("uid", uid).ErrorE(err, "FileStorageMountAccess.GetFileStorageListForUser failed")
		return
	}

	unmount := &model.FileStorageUnmount{}
	unmountMap := make(map[constant.RegionSignType]bool)
	unmountList, err := unmount.FileStorageUnmountGetAll(&db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{"uid": uid},
		NullField:    []string{"deleted_at"},
	})
	if err != nil {
		svc.log.WithField("uid", uid).ErrorE(err, "FileStorageMountAccess.FileStorageUnmountGetAll failed")
		return
	}
	for _, v := range unmountList {
		unmountMap[v] = true
	}

	for k, v := range fileStorageList {
		if v.Status != constant.FileStorageCreated {
			continue
		}

		list = append(list, model.FsUnmountForUser{
			RegionSign:    fileStorageList[k].Sign,
			InitializedAt: *fileStorageList[k].InitializedAt,
			Mount:         !unmountMap[v.Sign],
		})
	}
	return
}

func (svc *RegionService) FileStorageMountCtrl(params *model.FsMountCtrlParams) (err error) {
	l := svc.log.WithFields(map[string]interface{}{"uid": params.UID, "sign": params.RegionSign, "mount": params.Mount})
	unmount := &model.FileStorageUnmount{}

	// 挂载, 删除禁止挂载记录
	if params.Mount {
		err = unmount.FileStorageUnmountDelete(&db_helper.QueryFilters{EqualFilters: map[string]interface{}{
			"uid":         params.UID,
			"region_sign": params.RegionSign,
		}})
		if err != nil {
			l.ErrorE(err, "FileStorageMountCtrl.FileStorageUnmountDelete failed")
			return err
		}
		return
	}

	// 不挂载, 添加禁止挂载记录.
	ok, err := svc.FileStorageMount(params.UID, params.RegionSign)
	if err != nil || !ok {
		return
	}
	unmount.UID = params.UID
	unmount.RegionSign = params.RegionSign
	err = unmount.FileStorageUnmountCreate()
	if err != nil {
		l.ErrorE(err, "FileStorageMountCtrl.FileStorageUnmountCreate failed")
		return err
	}
	return
}

func (svc *RegionService) FileStorageMount(uid int, sign constant.RegionSignType) (mount bool, err error) {
	/*
		mount 为true代表要挂载, false为不挂载
	*/

	l := svc.log.WithFields(map[string]interface{}{"uid": uid, "sign": sign})

	rs, err := svc.getRegionDetail(nil, sign)
	if err != nil {
		return true, err
	}

	rsList, err := svc.GetRegionListByDatacenter(nil, rs.DataCenter)
	if err != nil {
		return true, err
	}

	signList := []constant.RegionSignType{}
	for _, v := range rsList {
		signList = append(signList, v.Sign)
	}

	unmount := &model.FileStorageUnmount{}
	err = unmount.FileStorageUnmountGet(&db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{"uid": uid},
		InFilters:    []db_helper.In{{Key: "region_sign", InSet: signList}},
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return true, nil
		}
		l.ErrorE(err, "FileStorageMountAccess.FileStorageUnmountGet failed")
		return true, err
	}

	// 获取到了, 有禁止挂载记录, 不挂载
	return false, nil
}

func (svc *RegionService) AutoFsGetUsage(uid int, rs constant.RegionSignType) (resp *model.AutoFsGetUsageResp, err error) {
	// 只获取峰值用量
	coreRes, err := svc.coreApi.FsGetDetail(coreapi.FsGetDetailReq{UID: uid, RegionSign: rs}, nil)
	if err != nil {
		svc.log.WithField("uid", uid).ErrorE(err, "core api: GetFileStorageForCharge failed")
		return
	}

	err = coreRes.GetErr()
	if err != nil {
		return
	}

	cost := constant.ADFSCost(coreRes.Data.QuotaMaxUsage)

	return &model.AutoFsGetUsageResp{
		MaxUsage: coreRes.Data.QuotaMaxUsage,
		Cost:     cost,
	}, nil
}

func (svc *RegionService) PublicApiGetFileStorageList(req model.PublicApiGetFileStorageListRequest) ([]coreRegionModel.FileStorage, error) {
	params := coreapi.GetFileStorageAdminListReq{
		GetPagedRangeRequest: db_helper.GetPagedRangeRequest{
			PageSize: 30000,
		},
		Tenant:         constant.TenantAutoDL,
		RegionSignType: req.RegionSign,
		FsType:         req.FsType,
		MinQuotaUsage:  req.MinQuotaUsage,
	}
	coreRes, err := svc.coreApi.GetFileStorageAdminList(params, nil)
	if err != nil {
		svc.log.WithField("params", params).ErrorE(err, "core api: GetFileStorageAdminList failed")
		return nil, err
	}
	if err = coreRes.GetErr(); err != nil {
		svc.log.WithField("params", params).ErrorE(err, "core api: GetFileStorageAdminList failed")
		return nil, err
	}
	return coreRes.Data.FileStorages, nil
}

func (svc *RegionService) PublicApiReformatFileStorage(req model.PublicApiReformatFileStorageRequest) error {
	params := coreapi.GetFileStorageAdminListReq{
		Tenant:         constant.TenantAutoDL,
		RegionSignType: req.RegionSign,
		UIDs:           []int{req.UID},
	}
	coreRes, err := svc.coreApi.GetFileStorageAdminList(params, nil)
	if err != nil {
		svc.log.WithField("params", params).ErrorE(err, "core api: GetFileStorageAdminList failed")
		return err
	}
	if err = coreRes.GetErr(); err != nil {
		svc.log.WithField("params", params).ErrorE(err, "core api: GetFileStorageAdminList failed")
		return err
	}

	if len(coreRes.Data.FileStorages) == 0 {
		return biz.ErrRecordNotFoundError
	}

	fs := coreRes.Data.FileStorages[0]
	if fs.UID != req.UID {
		return biz.ErrRecordNotFoundError
	}

	params2 := coreapi.UpdateFileStorageSettingReq{
		UID:            fs.UID,
		RegionSignType: fs.RegionSign,
		Settings: struct {
			ConcurrentLimit int `json:"concurrent_limit"`
			QuotaTotal      int `json:"quota_total"`
			QuotaInodeTotal int `json:"quota_inode_total"`
			MaxUploads      int `json:"max_uploads"`
			CacheSize       int `json:"cache_size"`
			BufferSize      int `json:"buffer_size"`
		}{
			ConcurrentLimit: fs.ConcurrentLimit,
			QuotaTotal:      cast.ToInt(fs.QuotaTotal / 1024 / 1024 / 1024),
			QuotaInodeTotal: cast.ToInt(fs.QuotaInodeTotal),
			MaxUploads:      fs.MaxUploads,
			CacheSize:       fs.CacheSize,
			BufferSize:      fs.BufferSize,
		},
	}
	coreRes2, err := svc.coreApi.UpdateFileStorageSetting(params2, nil)
	if err != nil {
		svc.log.WithField("params", params2).ErrorE(err, "core api: UpdateFileStorageSetting failed")
		return err
	}
	if err = coreRes2.GetErr(); err != nil {
		svc.log.WithField("params", params2).ErrorE(err, "core api: UpdateFileStorageSetting failed")
		return err
	}

	return nil
}
