package model

import (
	"encoding/json"
	"fmt"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"

	"gorm.io/datatypes"

	"gorm.io/gorm"
)

const TableNameRegion = "region"

type Region struct {
	ID        int            `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	CreatedAt time.Time      `gorm:"column:created_at;type:datetime" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`

	Sign constant.RegionSignType `gorm:"column:sign;type:varchar(255);index" json:"sign"` // 地区标识
	Name string                  `gorm:"column:name;type:varchar(255)" json:"name"`       // 地区名称
	// 数据中心名称， 相同的数据中心具有相同的此字段，表明内网是互通的
	DataCenter string `gorm:"column:data_center;type:varchar(255)" json:"data_center"`

	PortRangeMin int `gorm:"column:port_range_min" json:"port_range_min"` // 端口分配范围 min (包含)
	PortRangeMax int `gorm:"column:port_range_max" json:"port_range_max"` // 端口分配范围 max (包含)

	ExportAddr1   string `gorm:"column:export_addr_1;" json:"export_addr_1"`       // 出口地址
	ExportAddr1IP string `gorm:"column:export_addr_1_ip;" json:"export_addr_1_ip"` // 出口地址
	ExportAddr2   string `gorm:"column:export_addr_2;" json:"export_addr_2"`       // 出口地址

	ExportAddr3 string `gorm:"column:export_addr_3;" json:"export_addr_3"` // 出口地址（热备机器的地址）

	ProxyPort int `gorm:"column:proxy_port" json:"proxy_port"` // 为空默认为7000
	// frpc token. 目前默认 seetatech666, 不参与逻辑
	ProxyToken string `gorm:"type:varchar(255);column:proxy_token;" json:"proxy_token"` // default: 'seetatech666'

	// 是否开启 nfs, 决定下列字段是否有意义.
	IsNFSAvailable bool `gorm:"column:is_nfs_available;type:tinyint(1)" json:"is_nfs_available"`

	// NFS 地址, 现与出口地址相同, 且不参与逻辑 (agent 不自动挂载). create 时, 后端自动填写为与出口地址相同.
	NFSAddr string `gorm:"column:nfs_addr;type:varchar(255)" json:"nfs_addr"`

	// NFS agent 端口, 即管理网盘的 storage agent api, https
	NFSPort int `gorm:"column:nfs_port;" json:"nfs_port"`

	// NFS 健康状态, 如果填写新的 nfs, 默认为 unknown, 等待真正的状态同步.
	NFSHealth constant.NFSMachineStatus `gorm:"column:nfs_health;type:varchar(255)" json:"nfs_health"`

	// 用户配额
	DefaultUserQuotaInByte int64 `gorm:"column:default_user_quota_in_byte" json:"default_user_quota_in_byte"`

	// 排序权重
	Rank int `gorm:"column:rank" json:"rank"`

	// agent 自动挂载ADFS所需的server地址
	ADFSFilerAddr string `gorm:"column:adfs_filer_addr;type:varchar(255)" json:"adfs_filer_addr"`
	// ADFS agent 端口, 即管理网盘的 storage agent api, https
	ADFSPubAddr string `gorm:"column:adfs_pub_addr;type:varchar(255)" json:"adfs_pub_addr"`
	ADFSPubPort int    `gorm:"column:adfs_pub_port;" json:"adfs_pub_port"`

	/*
	* core内容的副本，只能由core主动更新
	 */
	// 是否开启 adfs, 决定下列字段是否有意义.
	IsADFSAvailable   bool `gorm:"column:is_adfs_available;type:tinyint(1)" json:"is_adfs_available"`
	IsAutoFsAvailable bool `gorm:"column:is_autofs_available;type:tinyint(1)" json:"is_autofs_available"`

	// 用户可见性，算力用途
	Visible             constant.RegionVisible `gorm:"column:visible;type:varchar(255)" json:"visible"`
	UsedFor             constant.RegionUsedFor `gorm:"column:used_for;type:varchar(255)" json:"used_for"`
	CustomerPortVisible constant.RegionVisible `gorm:"column:customer_port_visible;type:varchar(255);default ''" json:"customer_port_visible"`
	//SSLDomain             string `gorm:"type:varchar(255);column:ssl_domain;" json:"ssl_domain"`                               // 地区使用https+域名访问, agent容器内声明域名， 访问流程 nginx(443/ssl_domain_port_for_visit) --https2http-> frps --http声明式端口复用-> frpc
	SSLDomainPortForVisit string `gorm:"type:varchar(255);column:ssl_domain_port_for_visit;" json:"ssl_domain_port_for_visit"` // 最外层nginx监听端口，默认为443,地区无法使用443端口时，改用其他端口，不需要传递给frpc

	// 存储对前端可见性
	NFSVisibleForFrontend  bool `gorm:"column:nfs_visible_for_frontend;type:tinyint(1)" json:"nfs_visible_for_frontend"`
	ADFSVisibleForFrontend bool `gorm:"column:adfs_visible_for_frontend;type:tinyint(1)" json:"adfs_visible_for_frontend"`

	SettingJson   datatypes.JSON `gorm:"column:setting;type:json" json:"-"`
	SettingEntity RegionSetting  `gorm:"-" json:"setting"`
}

type RegionSetting struct {
	CloneInstance bool `json:"clone_instance"`
}

func (r *Region) TableName() string {
	return TableNameRegion
}

func (r *Region) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&Region{})
}

func (r *Region) AfterFind(db *gorm.DB) error {
	if len(r.SettingJson) != 0 {
		err := json.Unmarshal(r.SettingJson, &r.SettingEntity)
		if err != nil {
			return err
		}
	}

	return nil
}

func (r *Region) InspectionPass() bool {
	if len(r.Name) == 0 || r.PortRangeMin == 0 || r.PortRangeMax == 0 ||
		len(r.ExportAddr1) == 0 || r.ExportAddr1 == r.ExportAddr2 {
		return false
	}

	// 允许 [35000,35000] 只给一个的极端情况, 但不允许 [35010,35009]
	if r.PortRangeMin > r.PortRangeMax {
		return false
	}

	if r.IsNFSAvailable {
		// nfs_addr 不校验, 后端自动填写
		if r.NFSPort == 0 || r.DefaultUserQuotaInByte == 0 {
			return false
		}
	}

	return true
}

func (r *Region) FillNFSAddrIfAvailable() {
	if r.IsNFSAvailable {
		r.NFSAddr = r.ExportAddr1
	}
}

func (r *Region) NFSUrl() string {
	r.FillNFSAddrIfAvailable()
	return fmt.Sprintf("%s:%d", r.NFSAddr, r.NFSPort)
}

type RegionUsageInfo struct {
	Sign                   constant.RegionSignType        `json:"sign"`
	Name                   string                         `json:"name"`
	ExportAddr1            string                         `json:"export_addr_1"`
	ExportAddr2            string                         `json:"export_addr_2"`
	DefaultUserQuotaInByte int64                          `json:"default_user_quota_in_byte"`
	NfsStatus              constant.NetDiskInitStatusType `json:"nfs_status"`
	NFSPort                int                            `json:"nfs_port"`
	Usage                  string                         `json:"usage"`
	ExpansionInfo          []NetDiskExpansion             `json:"expansion_info"`
}

// CheckRegionIsInSameDataCenter 确定多个地区之间是否位于同一个数据中心，目前用于判断有没有内网链接
// 以启动实力迁移
// 判断方法： name相同
func CheckRegionIsInSameDataCenter(regionSignList ...constant.RegionSignType) (same bool, err error) {
	var res []Region
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &Region{},
		Filters: db_helper.QueryFilters{
			InFilters: []db_helper.In{
				{
					Key:   "sign",
					InSet: regionSignList,
				},
			},
		},
	}, &res).GetError()
	if err != nil {
		return
	}

	if len(res) == 0 {
		return
	}
	lastRegionDataCenter := res[0].DataCenter
	for _, r := range res {
		if r.DataCenter != lastRegionDataCenter {
			return
		}
	}

	same = true
	return
}

func (r *Region) RegionGet(filter *db_helper.QueryFilters) error {
	return db_helper.GetFirst(db_helper.QueryDefinition{
		DBTransactionConnection: db_helper.GlobalDBConnForRead(),
		ModelDefinition:         r,
		Filters:                 *filter,
	}, &r).GetError()
}
