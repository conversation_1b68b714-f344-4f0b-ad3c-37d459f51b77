package model

import (
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/libs"
	"time"
)

type InitUserNetDiskParams struct {
	Sign constant.RegionSignType `json:"sign"`
}

type CreateRegionParams struct {
	Sign constant.RegionSignType `form:"sign" json:"sign"` // 地区标识
	Name string                  `form:"name" json:"name"` // 地区名称

	PortRangeMin int    `form:"port_range_min" json:"port_range_min"` // 端口分配范围 min (包含)
	PortRangeMax int    `form:"port_range_max" json:"port_range_max"` // 端口分配范围 max (包含)
	ExportAddr1  string `form:"export_addr_1" json:"export_addr_1"`   // 出口地址
	ExportAddr2  string `form:"export_addr_2" json:"export_addr_2"`   // 出口地址

	NFSPort                int   `form:"nfs_port" json:"nfs_port"`
	IsNFSAvailable         bool  `form:"is_nfs_available" json:"is_nfs_available"`
	DefaultUserQuotaInByte int64 `form:"default_user_quota_in_byte" json:"default_user_quota_in_byte"` // 用户配额
}

func (r *CreateRegionParams) Check() bool {
	if len(r.Name) == 0 || r.PortRangeMin == 0 || r.PortRangeMax == 0 ||
		len(r.ExportAddr1) == 0 || r.ExportAddr1 == r.ExportAddr2 {
		return false
	}

	// 允许 [35000,35000] 只给一个的极端情况, 但不允许 [35010,35009]
	if r.PortRangeMin > r.PortRangeMax {
		return false
	}

	if r.IsNFSAvailable {
		// nfs_addr 不校验, 后端自动填写
		if r.NFSPort == 0 || r.DefaultUserQuotaInByte == 0 {
			return false
		}
	}

	return true
}

type NetDiskSetQuotaParams struct {
	Sign  constant.RegionSignType `json:"sign"`
	Phone string                  `json:"phone"`
	//UID   int                     `json:"uid"`
	Quota int64 `json:"quota"` // 单位 GB
}

type UserNetDiskQuotaParams struct {
	Phone string `form:"phone" json:"phone"`
	db_helper.GetPagedRangeRequest
	Sorts libs.SortsReq `form:"sorts[]" json:"sorts"`

	Region constant.RegionSignType `form:"region" json:"region"`
}

// 文件存储管理

type AdminFileStorageListReq struct {
	db_helper.GetPagedRangeRequest

	Sorts libs.SortsReq `json:"sorts"`

	RegionSign constant.RegionSignType `json:"region"`
	Phone      string                  `json:"phone"`
}

type AdminFileStorage struct {
	UID             int                     `json:"uid"`
	Phone           string                  `json:"phone"`
	RegionSign      constant.RegionSignType `json:"region"`
	QuotaTotal      int64                   `json:"quota_total"`
	QuotaUsage      int64                   `json:"quota_usage"`
	QuotaUsageRate  int                     `json:"quota_usage_rate"`
	QuotaInodeTotal int64                   `json:"quota_inode_total"`
	QuotaInodeUsage int64                   `json:"quota_inode_usage"`
	ConcurrentLimit int                     `gorm:"column:concurrent_limit;" json:"concurrent_limit"`
	FsType          constant.FsType         `json:"fs_type"`
	MaxUploads      int                     `json:"max_uploads"` // autoFs 并发数
	CacheSize       int                     `json:"cache_size"`
	BufferSize      int                     `json:"buffer_size"`
	CreatedAt       time.Time               `json:"created_at"`
	FsConfigVersion string                  `json:"fs_config_version"`
}

type AutoFsUpdateSettingParams struct {
	UID             int                     `json:"uid" binding:"required"`
	RegionSign      constant.RegionSignType `json:"region" binding:"required"`
	QuotaTotal      int                     `json:"quota_total"`
	QuotaInodeTotal int                     `json:"quota_inode_total"`
}

type FsMountCtrlParams struct {
	UID        int                     `json:"-"`
	RegionSign constant.RegionSignType `json:"region_sign"`
	Mount      bool                    `json:"mount"`
}

type ExclusiveNfsListParams struct {
	Phone string `json:"phone"`
	db_helper.GetPagedRangeRequest
}

type PublicApiGetFileStorageListRequest struct {
	RegionSign    constant.RegionSignType `json:"region_sign"`
	FsType        string                  `json:"fs_type"`
	MinQuotaUsage int                     `json:"min_quota_usage"`
}

type PublicApiReformatFileStorageRequest struct {
	RegionSign constant.RegionSignType `json:"region_sign"`
	UID        int                     `json:"uid"`
}

type ExclusiveNfsInfoParams struct {
	Uid                  int                           `json:"uid" binding:"required"`
	ContainerRuntimeType constant.ContainerRuntimeType `json:"container_runtime_type"`
	RegionSignType       constant.RegionSignType       `json:"region_sign_type"`
}
