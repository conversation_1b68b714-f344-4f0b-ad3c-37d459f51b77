package model

import (
	"encoding/json"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"server/pkg/constant"
	"time"
)

const TableNameUserHSFSRecord = "user_hsfs_record"

// UserHSFSRecord 用户对高速文件存储的操作记录
type UserHSFSRecord struct {
	ID        int                     `gorm:"column:id;type:int;auto_increment;primary key;"`
	CreatedAt time.Time               `gorm:"column:created_at;type:datetime;not null;"`
	UpdatedAt time.Time               `gorm:"column:updated_at;type:datetime;not null;"`
	DeletedAt gorm.DeletedAt          `gorm:"column:deleted_at;type:datetime;default:null;index:idx_deleted_at;"`
	UID       int                     `gorm:"column:uid;type:int;not null;"`
	Sign      constant.RegionSignType `gorm:"column:sign;type:varchar(255);not null;"`

	SubName       string                         `gorm:"column:sub_name;type:varchar(255);not null;default:'';"`
	Operate       constant.OperateRecordType     `gorm:"column:operate;type:varchar(255);not null;default:'';"`
	Status        constant.FileStorageStatusType `gorm:"column:status;type:varchar(255);not null;default:'';"`
	OrderUUID     string                         `gorm:"column:order_uuid;type:varchar(255);not null;default:'';"`
	ChargeSize    int64                          `gorm:"column:charge_size;type:bigint;not null;default:0;"`
	ChargeFrom    time.Time                      `gorm:"column:charge_from;type:datetime;default:null;'"`
	ChargeTo      time.Time                      `gorm:"column:charge_to;type:datetime;default:null;"`
	DetailsJson   datatypes.JSON                 `gorm:"column:details;type:json;default:null;"`
	DetailsEntity map[string]interface{}         `gorm:"-"`
}

func (model *UserHSFSRecord) TableName() string {
	return TableNameUserHSFSRecord
}

func (model *UserHSFSRecord) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&UserHSFSRecord{})
}

func (model *UserHSFSRecord) AfterFind(db *gorm.DB) error {
	if len(model.DetailsJson) != 0 {
		if err := json.Unmarshal(model.DetailsJson, &model.DetailsEntity); err != nil {
			return err
		}
	}
	return nil
}

func (model *UserHSFSRecord) BeforeCreate(db *gorm.DB) (err error) {
	model.DetailsJson, err = json.Marshal(model.DetailsEntity)
	if err != nil {
		return err
	}
	return nil
}
