package model

import (
	"server/pkg/constant"
	"time"
)

type AddRegionParams struct {
}

type RegionFilter struct {
}

type GetRegionListForIndexRes struct {
	RegionSign    constant.RegionSignType `json:"region_sign"`
	RegionName    string                  `json:"region_name"`
	DataCenter    string                  `json:"data_center"`
	Visible       constant.RegionVisible  `json:"visible"`
	UsedFor       constant.RegionUsedFor  `json:"used_for"`
	SettingEntity RegionSetting           `json:"setting"`
}

type UserNetDiskQuotaForAdminInfo struct {
	UID             int                     `json:"uid"`
	UsedQuota       int64                   `json:"used_quota"`
	RegionSign      constant.RegionSignType `json:"region_sign"`
	UserPhone       string                  `json:"user_phone"`
	TotalQuota      int64                   `json:"total_quota"`
	ExpandExpiredAt time.Time               `json:"expand_expired_at"`
	IsExpanded      bool                    `json:"is_expanded"`
}

type AutoFsGetUsageResp struct {
	MaxUsage int64 `json:"max_usage"`
	Cost     int64 `json:"cost"`
}

type SendSmsReq struct {
	SmsType    string `json:"sms_type"`    // 短信类型
	RegionName string `json:"region_name"` // 地区名称
	RegionSign string `json:"region_sign"` // 地区标识
	Phone      string `json:"phone"`
}
