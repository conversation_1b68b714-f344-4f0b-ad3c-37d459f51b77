package model

import (
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"

	"gorm.io/gorm"
)

const TableNameFileStorageUnmount = "file_storage_unmount"

type FileStorageUnmount struct {
	ID        int            `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	CreatedAt time.Time      `gorm:"column:created_at;type:datetime" json:"created_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`

	// 绑定关系
	UID        int                     `gorm:"column:uid" json:"uid"`
	RegionSign constant.RegionSignType `gorm:"column:region_sign;type:varchar(255)" json:"region_sign"`
}

func (b *FileStorageUnmount) TableName() string {
	return TableNameFileStorageUnmount
}

func (b *FileStorageUnmount) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&FileStorageUnmount{})
}

type FsUnmountForUser struct {
	RegionSign    constant.RegionSignType `json:"region_sign"`
	InitializedAt time.Time               `json:"initialized_at"`
	Mount         bool                    `json:"mount"`
}

func (b *FileStorageUnmount) FileStorageUnmountCreate() (err error) {
	return db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: b,
		InsertPayload:   b,
	}).GetError()
}

func (b *FileStorageUnmount) FileStorageUnmountGet(filter *db_helper.QueryFilters) (err error) {
	return db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: b,
		Filters:         *filter,
	}, &b).GetError()
}

func (b *FileStorageUnmount) FileStorageUnmountGetAll(filter *db_helper.QueryFilters) (regionList []constant.RegionSignType, err error) {
	regionList = []constant.RegionSignType{}
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: b,
		Filters:         *filter,
		Select:          "region_sign",
	}, &regionList).GetError()
	return
}

func (b *FileStorageUnmount) FileStorageUnmountDelete(filter *db_helper.QueryFilters) (err error) {
	return db_helper.Delete(db_helper.QueryDefinition{
		ModelDefinition: b,
		Filters:         *filter,
	}, &b).GetError()
}
