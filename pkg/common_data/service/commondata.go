package service

import (
	"gorm.io/gorm"
	"path/filepath"
	"server/pkg-agent/agent_constant"
	biz "server/pkg/businesserror"
	"server/pkg/common_data/model"
	"server/pkg/db_helper"
)

func (svc *CommonDataService) Create(params *model.CommonDataBasicRequest) (id int, err error) {
	if params == nil {
		svc.l.Error("Create common data invalid request params.")
		err = biz.ErrInvalidRequestParams
		return
	}

	commonData := &model.CommonData{
		DataName:    params.DataName,
		RelDirPath:  "",
		FileName:    params.FileName,
		Size:        params.Size,
		DataForm:    params.DataForm,
		Publisher:   params.Publisher,
		Description: params.Description,
	}

	err = svc.insertInDb(commonData)
	if err != nil {
		return
	}
	return
}

func (svc *CommonDataService) insertInDb(params *model.CommonData) (err error) {
	err = db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: &model.CommonData{},
		InsertPayload:   params,
	}).GetError()
	if err != nil {
		svc.l.WarnE(err, "Create common data in db failed.")
		err = biz.ErrDatabaseError
		return
	}
	return
}

func (svc *CommonDataService) Update(params *model.UpdateCommonDataRequest) (err error) {
	err = svc.isIdValid(params.Id)
	if err != nil {
		return
	}

	updateParams := &model.CommonData{
		DataName:    params.DataName,
		RelDirPath:  "",
		FileName:    params.FileName,
		Size:        params.Size,
		DataForm:    params.DataForm,
		Publisher:   params.Publisher,
		Description: params.Description,
	}

	err = svc.updateInDb(params.Id, updateParams)
	if err != nil {
		return
	}
	return
}

func (svc *CommonDataService) updateInDb(id int, params *model.CommonData) (err error) {
	err = db_helper.UpdateOne(db_helper.QueryDefinition{
		ModelDefinition: &model.CommonData{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{
			"id": id,
		}},
	}, params).GetError()
	if err != nil {
		svc.l.WithField("id", id).WarnE(err, "update common data in db failed.")
		return
	}
	return
}

func (svc *CommonDataService) Delete(id int) (err error) {
	err = svc.isIdValid(id)
	if err != nil {
		return
	}
	err = db_helper.Delete(db_helper.QueryDefinition{
		ModelDefinition: &model.CommonData{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"id": id,
			},
		},
	}, &model.CommonData{}).GetError()
	if err != nil {
		svc.l.WithField("id", id).WarnE(err, "Delete common data in db by id failed.")
		return
	}
	return
}

func (svc *CommonDataService) AdminList(db *gorm.DB, filter *model.CommonData, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []*model.CommonData, err error) {
	list = make([]*model.CommonData, 0)

	if db == nil {
		db = db_helper.GlobalDBConn()
	}

	db = db.Model(&model.CommonData{}).Where("deleted_at is null")
	if filter.DataName != "" {
		dataName := "%" + filter.DataName + "%"
		description := "%" + filter.Description + "%"
		db = db.Where("data_name like ?", dataName).Or("description like ?", description)
	}

	if pageReq.DateFrom != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') >= ?", pageReq.DateFrom)
	}
	if pageReq.DateTo != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') <= ?", pageReq.DateTo)
	}
	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.l.WithError(err).Warn("Count failed.")
		return
	}
	paged = db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, int(count), 0)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("id desc").
		Find(&list).
		Error
	if err != nil {
		svc.l.WithError(err).Warn("Get user list failed.")
		return
	}
	paged.List = list
	return
}

func (svc *CommonDataService) UserList(filter *model.CommonData, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []*model.UserCommonDataListReply, err error) {
	list = make([]*model.UserCommonDataListReply, 0)
	db := db_helper.GlobalDBConn().Model(&model.CommonData{}).Where("deleted_at is null")
	if filter.DataName != "" {
		dataName := "%" + filter.DataName + "%"
		description := "%" + filter.Description + "%"
		db = db.Where("data_name like ?", dataName).Or("description like ?", description)
	}

	if pageReq.DateFrom != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') >= ?", pageReq.DateFrom)
	}
	if pageReq.DateTo != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') <= ?", pageReq.DateTo)
	}
	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.l.WithError(err).Warn("Count failed.")
		return
	}
	paged = db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, int(count), 0)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("id desc").
		Find(&list).
		Error
	if err != nil {
		svc.l.WithError(err).Warn("Get user list failed.")
		return
	}
	for i := range list {
		path := filepath.Join(agent_constant.CommonDataInContainerMount, list[i].RelDirPath, list[i].FileName)
		list[i].ExistPath = path
	}

	paged.List = list
	return
}

func (svc *CommonDataService) isIdValid(id int) (err error) {
	if id == 0 {
		return biz.ErrInvalidRequestParams
	}
	return
}
