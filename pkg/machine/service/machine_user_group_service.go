package service

import (
	"gorm.io/gorm"
	"server/pkg/businesserror"
	"server/pkg/db_helper"
	"server/pkg/logger"
	"server/pkg/machine/model"
	"server/pkg/module_definition"
	userModel "server/pkg/user/model"
)

type MachineUserGroup struct {
	log            *logger.Logger
	groupInterface module_definition.GroupInterface
}

const MachineUserGroupModuleName = "machine_user_group_service"

func NewMachineUserGroupServiceProvider(groupInterface module_definition.GroupInterface) *MachineUserGroup {
	return &MachineUserGroup{log: logger.NewLogger(MachineUserGroupModuleName),
		groupInterface: groupInterface}
}

func (svc *MachineUserGroup) Create(tx *gorm.DB, params *model.CreateMachineUserGroupParam) (err error) {
	if params == nil {
		svc.log.Error("create machine user group param is bull")
		err = businesserror.ErrInvalidRequestParams
		return
	}
	for _, groupID := range params.GroupIDList {
		machineUserGroup := &model.MachineUserGroup{MachineID: params.MachineID, GroupID: groupID}
		err = db_helper.InsertOne(db_helper.QueryDefinition{ModelDefinition: &model.MachineUserGroup{},
			DBTransactionConnection: tx,
			InsertPayload:           machineUserGroup}).GetError()
		if err != nil {
			svc.log.WithField("err", err).Error("create machine_user_group failed.")
			return
		}
	}
	return
}

// GetAll最多获取100条记录
func (svc *MachineUserGroup) GetGroupNameByMachineID(machineID string) (groups []*userModel.Group, err error) {
	return svc.getGroupNameByMachineID(db_helper.GlobalDBConn(), machineID)
}

func (svc *MachineUserGroup) GetGroupNameByMachineIDFromRO(machineID string) (groups []*userModel.Group, err error) {
	return svc.getGroupNameByMachineID(db_helper.GlobalDBConnForRead(), machineID)
}

func (svc *MachineUserGroup) getGroupNameByMachineID(db *gorm.DB, machineID string) (groups []*userModel.Group, err error) {
	machineUG := make([]*model.MachineUserGroup, 0)

	if db == nil {
		db = db_helper.GlobalDBConn()
	}

	err = db.Table(model.TableMachineUserGroup).Where("deleted_at is null").
		Where("machine_id", machineID).Find(&machineUG).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			svc.log.WithField("err", err).WithField("machine_id", machineID).Info("machine_user_group record not found")
			err = nil
			return
		}
		svc.log.WithField("err", err).WithField("machine_id", machineID).Error("get machine_user_group failed")
		err = businesserror.ErrDatabaseError
		return
	}
	var groupIDs []int
	for _, m := range machineUG {
		groupIDs = append(groupIDs, m.GroupID)
	}
	groups, err = svc.groupInterface.GetGroupsByID(groupIDs)
	if err != nil {
		svc.log.WithField("err", err).WithField("machine_id", machineID).WithField("group_ids", groupIDs).Error("get group failed")
		return
	}
	return
}

func (svc *MachineUserGroup) DeleteByMachineID(tx *gorm.DB, machineID string) (err error) {
	err = db_helper.Delete(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         &model.MachineUserGroup{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{"machine_id": machineID},
		},
	}, &model.MachineUserGroup{}).GetError()
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil
		}
		svc.log.WithField("err", err).Error("delete machine_user_group failed.")
		return
	}
	return
}
