package service

import (
	log "github.com/sirupsen/logrus"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/machine/model"
	"server/plugin/queue"
	"time"
)

func (svc *McService) MsgRegister() []queue.RegisterInfo {
	return []queue.RegisterInfo{}
}

// CoreModifyMachine 用于更新machine的设置
func (svc *McService) CoreModifyMachine(payload *constant.MQCoreBusinessModifyDataPayload) (err error) {
	switch payload.DBAction {
	case constant.DBInsert:
		return svc.coreInsertMachineRecord(payload)
	case constant.DBUpdate:
		return svc.coreUpdateMachineRecord(payload)
	case constant.DBDelete:
		return svc.coreDeleteMachineRecord(payload)
	}
	svc.log.WithField("payload", payload).Error("payload.DBAction mismatch, skip it")
	return nil
}

func (svc *McService) coreInsertMachineRecord(payload *constant.MQCoreBusinessModifyDataPayload) (err error) {
	log.Infof("coreInsertMachineRecord payload.data: %+v", payload.Data)
	err = db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: &model.Machine{},
		InsertPayload:   payload.Data,
	}).GetError()
	if err != nil {
		svc.log.WithField("payload", payload).ErrorE(err, "core insert machine failed")
		return
	}
	return
}

func (svc *McService) coreUpdateMachineRecord(payload *constant.MQCoreBusinessModifyDataPayload) (err error) {
	err = db_helper.UpdateOne(db_helper.QueryDefinition{
		ModelDefinition: &model.Machine{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{
			payload.Key.String(): payload.Data[payload.Key.String()],
		}},
	}, payload.Data).GetError()
	if err != nil {
		svc.log.WithField("payload", payload).ErrorE(err, "core update machine failed")
		return
	}
	return
}

func (svc *McService) coreDeleteMachineRecord(payload *constant.MQCoreBusinessModifyDataPayload) (err error) {
	err = db_helper.Delete(db_helper.QueryDefinition{
		ModelDefinition: &model.Machine{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{
			payload.Key.String(): payload.Data[payload.Key.String()],
		}},
	}, &model.Machine{}).GetError()
	if err != nil {
		svc.log.WithField("payload", payload).ErrorE(err, "core delete machine failed")
		return
	}
	return
}

// gpu type

// CoreModifyGpuType 用于更新machine的设置
func (svc *GpuService) CoreModifyGpuType(payload *constant.MQCoreBusinessModifyDataPayload) (err error) {
	switch payload.DBAction {
	case constant.DBInsert:
		return svc.coreInsertGpuTypeRecord(payload)
	case constant.DBUpdate:
		return svc.coreUpdateGpuTypeRecord(payload)
	case constant.DBDelete:
		return svc.coreDeleteGpuTypeRecord(payload)
	}
	svc.log.WithField("payload", payload).Error("payload.DBAction mismatch, skip it")
	return nil
}

func (svc *GpuService) coreInsertGpuTypeRecord(payload *constant.MQCoreBusinessModifyDataPayload) (err error) {
	err = db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: &model.GPUType{},
		InsertPayload:   payload.Data,
	}).GetError()
	if err != nil {
		svc.log.WithField("payload", payload).ErrorE(err, "core insert GpuName failed")
		return
	}
	return
}

func (svc *GpuService) coreUpdateGpuTypeRecord(payload *constant.MQCoreBusinessModifyDataPayload) (err error) {
	payload.Data["updated_at"] = time.Now()
	err = db_helper.UpdateOne(db_helper.QueryDefinition{
		ModelDefinition: &model.GPUType{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{
			payload.Key.String(): payload.Data[payload.Key.String()],
		}},
	}, payload.Data).GetError()
	if err != nil {
		svc.log.WithField("payload", payload).ErrorE(err, "core update GpuName failed")
		return
	}
	return
}

func (svc *GpuService) coreDeleteGpuTypeRecord(payload *constant.MQCoreBusinessModifyDataPayload) (err error) {
	err = db_helper.Delete(db_helper.QueryDefinition{
		ModelDefinition: &model.GPUType{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{
			payload.Key.String(): payload.Data[payload.Key.String()],
		}},
	}, &model.GPUType{}).GetError()
	if err != nil {
		svc.log.WithField("payload", payload).ErrorE(err, "core delete GpuName failed")
		return
	}
	return
}
