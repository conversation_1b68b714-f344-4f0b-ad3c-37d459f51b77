package service

import (
	"gorm.io/gorm"
	"server/pkg/businesserror"
	"server/pkg/db_helper"
	"server/pkg/machine/model"
)

func (svc *McService) GetAllMachineTag() (tag []*model.MachineTag, err error) {
	return svc.getAllMachineTag(db_helper.GlobalDBConn())
}

func (svc *McService) GetAllMachineTagFromRO() (tag []*model.MachineTag, err error) {
	return svc.getAllMachineTag(db_helper.GlobalDBConnForRead())
}

func (svc *McService) getAllMachineTag(db *gorm.DB) (tag []*model.MachineTag, err error) {
	tag = make([]*model.MachineTag, 0)

	if db == nil {
		db = db_helper.GlobalDBConn()
	}

	err = db_helper.GetAll(db_helper.QueryDefinition{
		DBTransactionConnection: db,
		ModelDefinition:         &model.MachineTag{},
		NoLimit:                 true,
	}, &tag).GetError()
	if err != nil {
		svc.log.Error("get all machine tag failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *McService) CreateMachineRelationTag(tx *gorm.DB, params *model.CreateMachineRelationTagParam) (err error) {
	if params == nil {
		svc.log.Error("create  machine relation tag param is bull")
		err = businesserror.ErrInvalidRequestParams
		return
	}
	machineTags, err := svc.GetMachineTagByMachineID(params.MachineID)
	if err != nil {
		svc.log.WithField("machine_id", params.MachineID).Error("get machine tag failed")
		err = businesserror.ErrDatabaseError
		return
	}
	originTagList := []int{}
	originTagMap := map[int]int{}

	for _, machineTag := range machineTags {
		originTagMap[machineTag.ID] = 1
		originTagList = append(originTagList, machineTag.ID)
	}
	newTagIDList := []int{}
	newTagIDMap := map[int]int{}
	for _, newTagID := range params.TagIDList {
		newTagIDMap[newTagID] = 1
		_, ok := originTagMap[newTagID]
		if !ok {
			newTagIDList = append(newTagIDList, newTagID)
		}
	}
	deleteTagList := []int{}
	for _, d := range originTagList {
		_, ok := newTagIDMap[d]
		if !ok {
			deleteTagList = append(deleteTagList, d)
		}
	}
	for _, tagID := range newTagIDList {
		machineRelationTag := &model.MachineRelationTag{MachineID: params.MachineID, TagID: tagID}
		err = db_helper.InsertOne(db_helper.QueryDefinition{ModelDefinition: &model.MachineRelationTag{},
			DBTransactionConnection: tx,
			InsertPayload:           machineRelationTag}).GetError()
		if err != nil {
			svc.log.WithField("err", err).Error("create machine_relation_tag failed.")
			err = businesserror.ErrDatabaseError
			return
		}
	}
	if len(deleteTagList) > 0 {
		err = db_helper.Delete(db_helper.QueryDefinition{ModelDefinition: &model.MachineRelationTag{},
			DBTransactionConnection: tx,
			Filters: db_helper.QueryFilters{
				InFilters: []db_helper.In{{Key: "tag_id", InSet: deleteTagList}},
			},
		}, &model.MachineRelationTag{}).GetError()
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil
			}
			svc.log.WithField("err", err).WithField("delete_id", deleteTagList).Error("delete machine tag failed")
			err = businesserror.ErrDatabaseError
			return
		}
	}
	return
}

func (svc *McService) GetMachineTagByMachineID(machineID string) (machineTags []*model.MachineTag, err error) {
	tagIDs := []int64{}
	err = db_helper.GlobalDBConn().Table(model.TableMachineRelationTag).Where("deleted_at is null").
		Select("machine_relation_tag.tag_id").Where("machine_id = ?", machineID).Find(&tagIDs).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		svc.log.WithField("err", err).WithField("machine_id", machineID).Error("get machine relation tag failed")
		return
	}
	machineTags = make([]*model.MachineTag, 0)
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.MachineTag{},
		Filters:         db_helper.QueryFilters{InFilters: []db_helper.In{{Key: "id", InSet: tagIDs}}},
		NoLimit:         true,
	}, &machineTags).GetError()
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		svc.log.WithField("machine_id", machineID).Error("get machine tag failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}
