package model

import (
	"gorm.io/gorm"
	"time"
)

const TableMachineRelationTag = "machine_relation_tag"

type MachineRelationTag struct {
	ID        int            `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`

	MachineID string `gorm:"type:varchar(50);column:machine_id;" json:"machine_id"`
	TagID     int    `grorm:"type:int;column:tag_id;" json:"tag_id"`
}

func (m *MachineRelationTag) TableName() string {
	return TableMachineRelationTag
}

// Init 实现 db_helper 接口.
func (m *MachineRelationTag) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&MachineRelationTag{})
}

type CreateMachineRelationTagParam struct {
	MachineID string `json:"machine_id"`
	TagIDList []int  `json:"tag_id_list"`
}
