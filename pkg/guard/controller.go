package guard

import (
	"strings"

	agentConstant "server/pkg-agent/agent_constant"
	"server/pkg-core/module_definition"
	storageConstant "server/pkg-storage-agent/storage_agent_constant"
	biz "server/pkg/businesserror"
	serverConstant "server/pkg/constant"
	"server/pkg/guard_mailbox"
	h "server/pkg/http"
	"server/pkg/logger"
	"server/plugin/queue"
	redis "server/plugin/redis_plugin"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type Controller struct {
	logger           *logger.Logger
	machineService   module_definition.MachineInference
	containerService module_definition.ContainerRuntimeInterface
	regionService    module_definition.RegionInterface
	t                redis.SyncTimer
	q                *queue.Q
	cu               *redis.ContainerUsagePlugin
}

func NewController(
	machineService module_definition.MachineInference,
	containerService module_definition.ContainerRuntimeInterface,
	regionService module_definition.RegionInterface,
	t redis.SyncTimer,
	q *queue.Q,
	cu *redis.ContainerUsagePlugin,
) *Controller {
	return &Controller{
		logger:           logger.NewLogger("GuardController"),
		machineService:   machineService,
		containerService: containerService,
		regionService:    regionService,
		t:                t,
		q:                q.New(),
		cu:               cu,
	}
}

/*
 * GPU machine
 */

func (ctrl *Controller) RunReaderWriter(c *gin.Context) {
	machineID, err := ctrl.getMachineID(c)
	if err != nil {
		ctrl.logger.WarnE(err, "get machine id")
		return
	}

	ctrl.logger.Trace("machine %s connected, ready to run", machineID)

	rs, err := ctrl.getRegionSign(c)
	if err != nil {
		ctrl.logger.WithField("machine_id", machineID).WarnE(err, "get region sign failed.")
		return
	}

	g := NewGuard(
		c.Writer, c.Request,
		machineID, rs,
		ctrl.machineService,
		ctrl.containerService,
		ctrl.regionService,
		ctrl.t, 
		ctrl.q,
		ctrl.cu,
		guard_mailbox.NewMailBox(machineID),
	)

	// 阻塞在此处
	g.Run()
}

/*
 * NFS machine
 */

func (ctrl *Controller) StorageAgentRunReaderWriter(c *gin.Context) {
	rs, err := ctrl.getRegionSign(c)
	if err != nil {
		ctrl.logger.WarnE(err, "get region sign failed.")
		return
	}

	ctrl.logger.Info("region %s ip %s", rs, c.ClientIP())

	fsConfigVersion := strings.TrimSpace(ctrl.getFsConfigVersion(c))

	section := rs.String()
	if fsConfigVersion != "" {
		section = section + "_" + fsConfigVersion
	}

	g := NewGuard(
		c.Writer, c.Request,
		"", rs,
		ctrl.machineService,
		ctrl.containerService,
		ctrl.regionService,
		ctrl.t, 
		ctrl.q, ctrl.cu,
		guard_mailbox.NewMailBox(section),
	)
	g.SetRegionFlag(ctrl.getRegionFlag(c))
	g.SetFsConfigVersion(fsConfigVersion)

	// 阻塞在此处
	g.RunStorageAgent()
}

func (ctrl *Controller) StorageAgentAuthToken(c *gin.Context) {
	var req serverConstant.RegionAuthRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	// 1. check region_sign
	exist, err := ctrl.regionService.CheckRegionSignExist(req.RegionSign)
	if err != nil || !exist {
		ctrl.logger.WithField("region", req.RegionSign).Info("Check region exist failed. req: %+v", req)
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}
	ctrl.logger.Info("region sign exist:%+v", exist)

	// 2. 其他验证, 检查 token
	if err = ctrl.regionService.AuthAgent(req.AgentToken); err != nil {
		ctrl.logger.WithField("region", req.RegionSign).Info("Invalid agent token. req: %+v", req)
		h.SendError(c, err)
		return
	}

	// 3. send back uid
	user := h.GetUserInfo(c)
	uid := user.UID
	ctrl.logger.Info("region uid:%+v", uid)

	h.SendOK(c, uid)
}

// ---------------------------------------------------------------------------------------------------------------------

func (ctrl *Controller) getMachineID(c *gin.Context) (machineID string, err error) {
	machineID = c.GetHeader(agentConstant.AgentMachineIDHeaderKey)
	if len(machineID) == 0 {
		err = errors.New("a machine want to connect but failed, cause machineID in header is nil")
		return
	}
	return
}

func (ctrl *Controller) getRegionSign(c *gin.Context) (regionSign serverConstant.RegionSignType, err error) {
	region := c.GetHeader(storageConstant.AgentRegionSignHeaderKey)
	if len(region) == 0 {
		err = errors.New("a machine want to connect but failed, cause region sign in header is nil")
		return
	}

	regionSign = serverConstant.NewRegionSignType(region)
	return
}

func (ctrl *Controller) getRegionFlag(c *gin.Context) string {
	return c.GetHeader(storageConstant.AgentRegionFlagHeaderKey)
}

func (ctrl *Controller) getFsConfigVersion(c *gin.Context) string {
	return c.GetHeader(storageConstant.AgentFsConfigVersionHeaderKey)
}
