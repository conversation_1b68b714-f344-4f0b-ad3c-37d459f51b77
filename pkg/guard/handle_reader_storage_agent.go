package guard

import (
	"context"
	"encoding/json"
	"server/pkg-agent/messenger"
	storageConstant "server/pkg-storage-agent/storage_agent_constant"
	"server/pkg/businesserror"
	serverConstant "server/pkg/constant"
	"server/pkg/logger"
	"server/pkg/serializer"
	"time"

	"github.com/pkg/errors"
)

func (g *Guard) handleStorageAgentReader(ctx context.Context, panicChan chan<- error, reader <-chan messenger.Message, writer chan<- messenger.Message) {
	for {
		select {
		case <-ctx.Done():
			g.logger.Info("storage agent reader quit gracefully...")
			return
		case in, ok := <-reader:
			if !ok {
				panicChan <- errors.New("handle storage agent reader is not ok")
				return
			}

			g.mail.Confirm(in.MsgID)

			switch in.Type {
			// skip
			case messenger.DefaultSkipType:
				continue

			case messenger.RegionRegisterType:
				var request = new(storageConstant.RegionRegisterParam)
				parseErr := request.ParseFromString(in.Payload)
				if parseErr != nil {
					if parseErr != storageConstant.EchoMessageSkippedError {
						g.logger.WithField("Msg", serializer.IndentString(in)).WarnE(parseErr, "parse payload failed, skip")
					}
					g.logger.WithField("Msg", serializer.IndentString(in)).WarnE(parseErr, "parse payload failed, skip")
					continue
				}

				var result = storageConstant.RegionRegisterResult{
					IsSucceed: true,
					Message:   "",
				}

				// region service 需要支持重复确认
				err := g.regionService.RegisterAgent(serverConstant.RegionSignType(request.RegionSign), request.AgentToken)
				if err != nil {
					result.IsSucceed = false
					result.Message = err.Error()
					g.logger.WithField("region", request.RegionSign).WarnE(err, "Register storage agent failed.")
					// do not return
				}

				resultPayload, mErr := result.String()
				if mErr != nil {
					continue
				}

				writer <- messenger.Message{
					MsgID:   g.t.Now().Format(time.RFC3339Nano),
					Type:    messenger.RegionRegisterResultType,
					Payload: resultPayload,
				}

			case messenger.PingType:
				// payload is region sign
				rs := serverConstant.NewRegionSignType(in.Payload)
				g.regionService.SetStorageAgentOnlineOnce(rs)

			case messenger.NetDiskInitResultType:
				var result = new(storageConstant.NetDiskInitResult)
				parseErr := result.ParseFromString(in.Payload)
				if parseErr != nil {
					if parseErr != storageConstant.EchoMessageSkippedError {
						g.logger.WithField("Msg", serializer.IndentString(in)).WarnE(parseErr, "parse payload failed, skip")
					}
					g.logger.WithField("Msg", serializer.IndentString(in)).WarnE(parseErr, "parse payload failed, skip")
					continue
				}

				g.regionService.ConfirmRegionUserNetDiskInDB(serverConstant.RegionSignType(result.RegionSign), result.UID, result.IsSucceed, result.Msg, result.Quota)
			case messenger.NetDiskAdminSetQuotaResultType:
				var result = new(storageConstant.NetDiskInitResult)
				parseErr := result.ParseFromString(in.Payload)
				if parseErr != nil {
					if parseErr != storageConstant.EchoMessageSkippedError {
						g.logger.WithField("Msg", serializer.IndentString(in)).WarnE(parseErr, "parse payload failed, skip")
					}
					g.logger.WithField("Msg", serializer.IndentString(in)).WarnE(parseErr, "parse payload failed, skip")
					continue
				}

				g.regionService.ConfirmAdminSetQuota(serverConstant.RegionSignType(result.RegionSign), result.UID, result.IsSucceed, result.Msg, result.Quota)

			case messenger.RegionSyncUsageType:
				msg := new(serverConstant.StorageSyncUsageModel)
				err := json.Unmarshal([]byte(in.Payload), &msg)
				if err != nil {
					g.logger.WithError(err).Error("unmarshal RegionSyncUsage Msg failed")
					return
				}
				g.regionService.SyncStorageUsage(msg)

			case messenger.RegionSyncDiskAlertsType:
				msg := new(serverConstant.StorageSyncDiskAlertsModel)
				err := json.Unmarshal([]byte(in.Payload), &msg)
				if err != nil {
					g.logger.WithError(err).Error("unmarshal RegionSyncDiskAlertsType Msg failed")
					return
				}
				g.regionService.SyncDiskAlerts(msg)

			case messenger.StorageAgentFrpcRequest:
				// payload is region sign
				rs := serverConstant.NewRegionSignType(in.Payload)

				g.logger.Info("get region of %s", rs)
				proxyHost, proxyPort, proxyAPIPort, proxyToken, _, err := g.regionService.DistributeFrpcProxy(rs)
				if err != nil {
					continue
				}

				result := serverConstant.StorageAgentFrpcInfo{
					ProxyHost:          proxyHost,
					ProxyPort:          proxyPort,
					ProxyToken:         proxyToken,
					ProxyApiServerPort: proxyAPIPort,
				}

				resultPayload, mErr := result.String()
				if mErr != nil {
					continue
				}

				g.logger.WithFields(logger.Fields{
					"proxyHost":    proxyHost,
					"proxyPort":    proxyPort,
					"proxyAPIPort": proxyAPIPort,
					"proxyToken":   proxyToken,
					"result":       resultPayload,
				}).Info("region detail")

				writer <- messenger.Message{
					MsgID:   g.t.Now().Format(time.RFC3339Nano),
					Type:    messenger.StorageAgentFrpcResponse,
					Payload: resultPayload,
				}

			case messenger.ADFSStorageAgentFrpcRequest, messenger.AutoFsFrpcRequest:
				// payload is region sign
				rs := serverConstant.NewRegionSignType(in.Payload)

				g.logger.Info("get region of %s", rs)
				proxyHost, proxyPort, proxyAPIPort, proxyToken, region, err := g.regionService.DistributeFrpcProxy(rs)
				if err != nil {
					continue
				}

				if in.Type == messenger.ADFSStorageAgentFrpcRequest {
					proxyAPIPort = region.ADFSPubPort
				} else {
					proxyAPIPort = region.AutoFSPubPort

					fsConfigVersion := g.GetFsConfigVersion()

					if fsConfigVersion != "" {
						config, ok, err := g.regionService.GetFsConfig(rs, fsConfigVersion)
						if err != nil {
							g.logger.WithField("sign", rs).WithField("fsConfigVersion", fsConfigVersion).WithError(err).Error("GetFsConfig failed")
							continue
						}
						if !ok {
							err = businesserror.ErrRegionFileStorageConfigNotFound
							g.logger.WithField("sign", rs).WithField("fsConfigVersion", fsConfigVersion).WithError(err).Error("GetFsConfig failed")
							continue
						}

						proxyAPIPort = config.AutoFSPubPort
					}
				}
				result := serverConstant.StorageAgentFrpcInfo{
					ProxyHost:          proxyHost,
					ProxyPort:          proxyPort,
					ProxyToken:         proxyToken,
					ProxyApiServerPort: proxyAPIPort,
				}

				resultPayload, mErr := result.String()
				if mErr != nil {
					continue
				}

				g.logger.WithFields(logger.Fields{
					"proxyHost":    proxyHost,
					"proxyPort":    proxyPort,
					"proxyAPIPort": proxyAPIPort,
					"proxyToken":   proxyToken,
					"result":       resultPayload,
				}).Info("region detail")

				writer <- messenger.Message{
					MsgID:   g.t.Now().Format(time.RFC3339Nano),
					Type:    messenger.ADFSStorageAgentFrpcResponse,
					Payload: resultPayload,
				}
			case messenger.FileStorageInitResultType:
				var result = new(serverConstant.FSInitResult)
				parseErr := json.Unmarshal([]byte(in.Payload), result)
				if parseErr != nil {
					g.logger.WithField("Msg", serializer.IndentString(in)).WarnE(parseErr, "parse payload failed, skip")
					continue
				}

				g.regionService.InitFileStorageFinal(result)

			case messenger.AutoFsInitResultType:
				l := g.logger.WithFields(map[string]interface{}{"messageType": "AutoFsInitResultType", "msg": serializer.IndentString(in)})
				payload := serverConstant.FSInitResult{}
				err := json.Unmarshal([]byte(in.Payload), &payload)
				if err != nil {
					l.ErrorE(err, "parse payload failed")
					continue
				}

				g.regionService.InitFileStorageFinal(&payload)

			case messenger.AutoFsSyncUsageRequest:
				l := g.logger.WithFields(map[string]interface{}{"messageType": "AutoFsInitResultType", "msg": serializer.IndentString(in)})
				payload := serverConstant.AutoFsSyncUsage{}
				err := json.Unmarshal([]byte(in.Payload), &payload)
				if err != nil {
					l.ErrorE(err, "parse payload failed")
					continue
				}

				_ = g.regionService.AutoFsSyncUsageInfo(&payload)

			default:
				g.logger.WithField("Msg", serializer.IndentString(in)).Warn("no handler type match this Msg")
				continue
			}
		}
	}
}
