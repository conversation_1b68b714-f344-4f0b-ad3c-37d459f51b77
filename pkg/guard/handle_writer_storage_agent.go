package guard

import (
	"context"
	"server/pkg-agent/messenger"
	"server/plugin/queue"
	"server/plugin/queue_interface"
	"strings"
)

/*
 * NFS machine
 */

func (g *Guard) handleStorageAgentWriter(ctx context.Context, panic<PERSON>han chan<- error, writer chan<- messenger.Message) {
	// SubMsgFromRabbitMQForThisMachine(g.machineID)
	// WriteMsgToTheWriter

	var stopSubCh = make(chan bool, 1)
	defer close(stopSubCh)

	rf := g.RegionFlag()
	isADFS := strings.Contains(rf, "adfs")
	isAutoFs := strings.Contains(rf, "autofs")

	fsConfigVersion := g.GetFsConfigVersion()

	// 频道需要拼接 region sign, 为每个 adfs machine 创建一个 agent handler
	t := queue_interface.GetStorageAgentOnMachineType(g.regionSign)
	if isADFS {
		t = queue_interface.GetADFSStorageAgentOnMachineType(g.regionSign)
	} else if isAutoFs {
		t = queue_interface.GetAutoFsStorageAgentWithRegionType(g.regionSign, fsConfigVersion)
	}

	g.q.SubscribeRegister(queue.RegisterInfo{
		Type: t,
		Handler: func(payload queue.ElementPayload, consumerHandler queue.ConsumerHandlerInterface) {
			g.machineHandler(payload, consumerHandler, writer)
		},
	})

	// guard worker for region sync check
	go g.regionService.GuardCronJobRegister(ctx, g.regionSign, writer)

	// 阻塞在此处
	g.q.RunSubscribe(ctx)
}
