package guard

import (
	"context"
	"net/http"
	agentConstant "server/pkg-agent/agent_constant"
	"server/pkg-agent/messenger"
	"server/pkg-core/module_definition"
	serverConstant "server/pkg/constant"
	"server/pkg/guard_mailbox"
	"server/pkg/logger"
	"server/plugin/influxdb_plugin"
	"server/plugin/queue"
	"server/plugin/redis_plugin"
	"sync"
	"time"
)

// 封装新的 reader 和 writer. 在 server guard 端控制邮箱. reader 和 writer 通过 msg_id 确认.
type Guard struct {
	logger *logger.Logger

	// for id / sign
	machineID       string
	regionSign      serverConstant.RegionSignType
	regionFlag      string
	fsConfigVersion string
	isHSFS          bool

	// for handle ws reader/writer
	w                http.ResponseWriter
	r                *http.Request
	machineService   module_definition.MachineInference
	containerRuntime module_definition.ContainerRuntimeInterface
	regionService    module_definition.RegionInterface

	// for redis time
	t              redis_plugin.SyncTimer

	// for mq
	q *queue.Q

	// for container usage
	cu *redis_plugin.ContainerUsagePlugin

	// for restore messages
	mail guard_mailbox.MailBoxInterface

	// for concurrent handle
	gpuAgentPipelineContext *PipelineSet
	nfsAgentPipelineContext *PipelineSet // 预留
}

// influxdb bucket->writer mapping
// 由于logWriter是异步处理, 所以只需要初始化一次, 然后全局共享即可
var influxdbBucketWriterMutex = sync.Mutex{}
var influxdbBucketWriterMapping = make(map[string]*influxdb_plugin.LogWriter)

func NewGuard(
	w http.ResponseWriter,
	r *http.Request,
	machineID string,
	regionSign serverConstant.RegionSignType,
	machineService module_definition.MachineInference,
	containerService module_definition.ContainerRuntimeInterface,
	regionService module_definition.RegionInterface,
	t redis_plugin.SyncTimer,
	q *queue.Q,
	cu *redis_plugin.ContainerUsagePlugin,
	mail guard_mailbox.MailBoxInterface,
) *Guard {
	var newQ *queue.Q
	if q != nil {
		newQ = q.New()
	}

	return &Guard{
		logger:           logger.NewLogger("Guard." + machineID),
		w:                w,
		r:                r,
		machineID:        machineID,
		regionSign:       regionSign,
		machineService:   machineService,
		containerRuntime: containerService,
		regionService:    regionService,
		t:                t,
		q:                newQ,
		cu:               cu,
		mail:             mail,
	}
}

func (g *Guard) SetRegionFlag(rf string) {
	g.regionFlag = rf
}

func (g *Guard) RegionFlag() string {
	return g.regionFlag
}

func (g *Guard) SetFsConfigVersion(fsConfigVersion string) {
	g.fsConfigVersion = fsConfigVersion
}

func (g *Guard) GetFsConfigVersion() string {
	return g.fsConfigVersion
}

func (g *Guard) SetIsHSFS(isHSFS bool) {
	g.isHSFS = isHSFS
}

func (g *Guard) GetIsHSFS() bool {
	return g.isHSFS
}

/*
 * GPU machine
 */

func (g *Guard) Run() {
	conn, err := wsUpgrader.Upgrade(g.w, g.r, nil)
	if err != nil {
		g.logger.ErrorE(err, "failed to upgrade connection to ws")
		return
	}

	m, err := messenger.NewMessengerForServer(conn)
	if err != nil {
		g.logger.ErrorE(err, "can not new messenger from connection")
		return
	}

	err = g.updateMachineHealthStatus(agentConstant.Normal)
	if err != nil {
		g.logger.WithField("err", err).Error("update machine status failed")
	}
	g.logger.Info("Machine[%s] status updated:%d", g.machineID, agentConstant.Normal)

	// update region
	err = g.updateMachineRegion(g.regionSign)
	if err != nil {
		g.logger.WithField("err", err).Error("update machine region failed")
	}
	g.logger.Info("Machine[%s] region updated:%s", g.machineID, g.regionSign)

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// new pipeline here
	//g.gpuAgentPipelineContext = NewPipelineSet(ctx, g.machineID, g.handlerOfConcurrentBucket, func() {})

	panicChan := make(chan error, 10)
	reader, writer, err := m.StartHandler(ctx, panicChan)
	if err != nil {
		g.logger.ErrorE(err, "can not start messenger handler")
		return
	}

	filteredWriter := g.mail.RegisterWriterChan(ctx, writer)

	//go g.handleReaderConcurrently(ctx, panicChan, reader, filteredWriter)
	go g.handleReader(ctx, panicChan, reader, filteredWriter)
	go g.handleWriter(ctx, panicChan, filteredWriter)
	go g.machineService.MachineStatusConnect(ctx, g.machineID)
	//go g.machineService.SyncMachineIntranetIPToAgent(ctx, g.machineID)
	g.logger.Trace("machine %s is running", g.machineID)
	select {
	case err = <-panicChan:
		g.logger.Info("agent disconnected [%s]", err.Error())
		return
	}
}

/*
 * NFS machine
 */

func (g *Guard) RunStorageAgent() {
	conn, err := wsUpgrader.Upgrade(g.w, g.r, nil)
	if err != nil {
		g.logger.ErrorE(err, "failed to upgrade connection to ws")
		return
	}

	m, err := messenger.NewMessengerForServer(conn)
	if err != nil {
		g.logger.ErrorE(err, "can not new messenger from connection")
		return
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	panicChan := make(chan error, 10)
	reader, writer, err := m.StartHandler(ctx, panicChan)
	if err != nil {
		g.logger.ErrorE(err, "can not start messenger handler")
		return
	}

	filteredWriter := g.mail.RegisterWriterChan(ctx, writer)

	go g.handleStorageAgentReader(ctx, panicChan, reader, filteredWriter)
	go g.handleStorageAgentWriter(ctx, panicChan, filteredWriter)

	select {
	case err = <-panicChan:
		g.logger.Info("storage agent disconnected [%s]", err.Error())
		return
	}
}

/*
 * go Test
 */

type TestMode string

const (
	TestSimple               TestMode = "simple"
	TestSimpleUnmarshal      TestMode = "simple_unmarshal"
	TestSimpleUnmarshalSleep TestMode = "simple_unmarshal_sleep"
	TestSimpleLog            TestMode = "simple_log"
	TestConcurrentUnmarshal  TestMode = "concurrent_unmarshal"
	TestConcurrentSleep      TestMode = "concurrent_sleep"
)

func (m TestMode) NeedMarshal() bool {
	switch m {
	case TestSimpleUnmarshal, TestSimpleUnmarshalSleep, TestConcurrentUnmarshal, TestConcurrentSleep:
		return true
	}
	return false
}

func (g *Guard) TestRun(doneChan chan<- int, mode TestMode) {
	if len(mode) == 0 {
		mode = TestSimple
	}

	conn, err := wsUpgrader.Upgrade(g.w, g.r, nil)
	if err != nil {
		g.logger.ErrorE(err, "failed to upgrade connection to ws")
		return
	}

	m, err := messenger.NewMessengerForServer(conn)
	if err != nil {
		g.logger.ErrorE(err, "can not new messenger from connection")
		return
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	panicChan := make(chan error, 10)
	reader, writer, err := m.StartHandler(ctx, panicChan)
	if err != nil {
		g.logger.ErrorE(err, "can not start messenger handler")
		return
	}

	g.gpuAgentPipelineContext = NewPipelineSet(ctx, "test", g.handlerOfConcurrentBucket, func() {
		if mode == TestConcurrentSleep {
			time.Sleep(10 * time.Millisecond)
		}
		doneChan <- 1
	})

	// no mailbox in speed test
	go g.handleTestAgentReader(ctx, panicChan, reader, writer, doneChan, mode)
	go g.handleTestAgentWriter(ctx, panicChan, writer)

	select {
	case err = <-panicChan:
		g.logger.Info("storage agent disconnected [%s]", err.Error())
		return
	}
}

// ---------------------------------------------------------------------------------------------------------------------

func (g *Guard) updateMachineHealthStatus(healthStatus agentConstant.MachineHealthStatus) (err error) {
	payload := &agentConstant.MachineStatusParam{}
	payload.MachineID = g.machineID
	payload.HealthStatus = healthStatus
	return g.machineService.SetMachineStatus(payload)
}

// 连接时更新 agent 的地区信息. 此信息取自 agent 的配置文件 -r, 不应当频繁更新. 不应当由后端管理员手动修改.
func (g *Guard) updateMachineRegion(region serverConstant.RegionSignType) (err error) {
	return g.machineService.SetMachineRegion(&agentConstant.MachineRegionParam{
		MachineID:  g.machineID,
		RegionSign: region.String(),
	})
}
