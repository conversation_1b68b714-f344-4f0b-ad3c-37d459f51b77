package model

import (
	"gorm.io/gorm"
	"server/pkg/constant"
	"time"
)

const TableNamePort string = "port"

/**
	* 1: port 与实例进行绑定, 与实例之间是多对一的关系
	* 2: port 有可用范围规范 1024-65535, 1023以下的端口被分配给了一些常用的应用程序
	* 3: port 需要进行分发释放等操作
    * NOTE: 默认只有被绑定的端口才存在数据库中. 简化端口字段.
 **/

type Port struct {
	ID        int            `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"` // 即 BindAt
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"` // 即 ReleasedAt

	// 端口号, 不允许重复
	PortNumber int `gorm:"column:port_number;" json:"port_number"`

	// 端口类型, 用途, SSH/JupyterPort/TensorboardPort...
	PortType PortType `gorm:"type:varchar(255);column:port_type;" json:"port_type"`

	// 实例和闲时任务绑定的都是 container_runtime_uuid, 建议改为 entity_uuid
	ProductUUID string `gorm:"type:varchar(255);column:product_uuid;" json:"product_uuid"`

	// 区分正常绑定和 telnet 发现的. 注意老数据此项为空. 目前仅用于观测.
	EntityType PortEntityType `gorm:"type:varchar(255);column:entity_type;" json:"entity_type"`

	// 端口所在地区
	PortRegion constant.RegionSignType `gorm:"type:varchar(255);column:port_region;default:'';" json:"port_region"`
}

func (p *Port) TableName() string {
	return TableNamePort
}

// Init 实现 db_helper 接口.
func (p *Port) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&Port{})
}
