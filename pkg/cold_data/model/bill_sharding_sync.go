package model

import (
	bcm "server/pkg/billing_center/model"
	"time"
)

type BillShardingSync struct {
	bcm.BillShardingSync `gorm:"embedded"`
}

func (b *BillShardingSync) retentionTime() time.Time {
	return time.Now().AddDate(0, -6, 0)
}

func (b *BillShardingSync) GetModel() JunkModel {
	return &BillShardingSync{}
}

func (b *BillShardingSync) CanDelete() bool {
	// 防止异常情况
	if b.ID == 0 {
		return false
	}

	if b.CreatedAt.After(b.retentionTime()) {
		return false
	}

	return true
}
