package cold_data

import (
	"context"
	"gorm.io/gorm"
	"server/pkg/cold_data/model"
	"server/pkg/logger"
)

type CodeDataArchive struct {
	serverDB   *gorm.DB
	coreDB     *gorm.DB
	codeDataDB *gorm.DB
	l          *logger.Logger
	debug      bool
}

func NewCodeDataArchive(serverDB, coreDB, codeDataDB *gorm.DB, debug bool) *CodeDataArchive {
	return &CodeDataArchive{
		serverDB:   serverDB,
		coreDB:     coreDB,
		codeDataDB: codeDataDB,
		l:          logger.NewLogger("code_data_migrate"),
		debug:      debug,
	}
}

func (cdm *CodeDataArchive) Run(ctx context.Context, panicChan chan<- error) {
	for _, v := range ModelSet {
		err := v.Init(cdm.codeDataDB)
		if err != nil {
			panicChan <- err
			return
		}

		cdm.l.Debug("table [%s] AutoMigrate Success", v.<PERSON>())
	}

	// cold data backup..........

	// instance
	go cdm.commonArchive(ctx, panicChan, cdm.serverDB, &model.Instance{})

	// deployment DeploymentSnapshot DeploymentStatusHistory
	go cdm.commonArchive(ctx, panicChan, cdm.serverDB, &model.Deployment{})

	// DeploymentContainer DeploymentContainerEvent
	go cdm.commonArchive(ctx, panicChan, cdm.serverDB, &model.DeploymentContainer{})

	// charging
	go cdm.commonArchive(ctx, panicChan, cdm.serverDB, &model.Charging{})

	// NotifyRecord
	go cdm.commonArchive(ctx, panicChan, cdm.serverDB, &model.NotifyRecord{})

	// PrivateImage
	go cdm.commonArchive(ctx, panicChan, cdm.serverDB, &model.PrivateImage{})

	// Container OperateHistory StatusHistory
	go cdm.commonArchive(ctx, panicChan, cdm.coreDB, &model.Container{})

	// port
	go cdm.commonArchive(ctx, panicChan, cdm.coreDB, &model.Port{})

	// dec迁移
	//go cdm.dceArchive(ctx)

	// junk data clean up.........

	// server message
	go cdm.commonJunkDataDelete(ctx, cdm.serverDB, &model.Message{})

	// core message
	go cdm.commonJunkDataDelete(ctx, cdm.coreDB, &model.Message{})

	// server bill_sharding_sync
	go cdm.commonJunkDataDelete(ctx, cdm.serverDB, &model.BillShardingSync{})

}
