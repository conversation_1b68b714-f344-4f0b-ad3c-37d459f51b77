package http

import (
	"net/http"
	"server/pkg/businesserror"
	"server/pkg/businesserror/codes"
	"server/pkg/threadlocal"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	log "github.com/sirupsen/logrus"
)

// TODO: 优化错误码模式

// CommonResponse for swagger
type CommonResponse struct {
	Code string      `json:"code"`
	Data interface{} `json:"data"`
	Msg  string      `json:"msg"`
}

// CommonNullRequest for swagger
type CommonNullRequest struct {
}

func SendOK(c *gin.Context, data interface{}) {
	c.JSON(200, gin.H{
		"code":       "Success",
		"data":       data,
		"msg":        "",
		"request_id": threadlocal.GetRequestID(),
	})
}

func SendPVError(c *gin.Context, pv int, err error) {
	sendError(c, pv, err)
}

func SendError(c *gin.Context, err error) {
	sendError(c, nil, err)
}

func SendMsg(c *gin.Context, data interface{}, msg string) {
	c.JSON(200, gin.H{
		"code":       "Success",
		"data":       data,
		"msg":        msg,
		"request_id": threadlocal.GetRequestID(),
	})
}

func SendHtml(c *gin.Context, data string) {
	c.Header("Content-Type", "text/html; charset=utf-8")
	c.String(http.StatusOK, data)
}

// AbortAndSendAuthErr 用以替换 middleware 中重复的 abort. 不知道为什么此方法无法用于 defer...
func AbortAndSendAuthErr(c *gin.Context, abortErr error) {
	var finalErr = businesserror.ErrAuthorizeFailed

	// 如果错误不同, 则进行拼接补充.
	if !errors.Is(abortErr, businesserror.ErrAuthorizeFailed) && abortErr != nil {
		finalErr = finalErr.New().Append(abortErr)
	}

	// 发送并中断连接.
	SendError(c, finalErr)
	c.Abort()

	log.WithError(finalErr).Info("Abort a connection by error.")
	return
}

func AbortAndSendErr(c *gin.Context, abortErr error) {
	// 发送并中断连接.
	SendError(c, abortErr)
	c.Abort()

	//log.WithError(abortErr).Info("Abort a connection by error.")
	return
}

// SendHttpCode storage 上传文件，超过上限。由于前端组件只能处理http code，故约定使用501作为超过网盘容量的报错信息
type Code int

const (
	HTTPQuotaLimit   Code = 501
	HTTPNfsDiskLimit Code = 403
)

func SendHttpCode(c *gin.Context, code Code) {
	c.JSON(int(code), gin.H{
		"request_id": threadlocal.GetRequestID(),
	})
	c.Abort()
}

// ///////////////////////////////////////////////////////////////////

func sendError(c *gin.Context, data interface{}, err error) {
	requestID := threadlocal.GetRequestID()

	switch err := err.(type) {
	case *businesserror.Error: // 以此 case 为主.
		lang := GetLanguage(c)
		responseErr := err.New() // 注意对标准错误的修改都要 new 一个.
		_ = responseErr.SetLanguage(lang)
		_ = responseErr.SetData(data)
		// 通过其自带的 marshal json 方法, 结合配置文件, 控制原始 error 的显示.
		c.JSON(200, gin.H{
			"code":       responseErr.GetCode(),
			"data":       data,
			"msg":        responseErr.Error(),
			"request_id": requestID,
		})
	case codes.Error:
		c.JSON(200, gin.H{
			"code":       err.ResponseCode(),
			"data":       data,
			"msg":        err.Text(),
			"request_id": requestID,
		})
	case *Error:
		c.JSON(200, gin.H{
			"code":       err.Code.Error(),
			"data":       data,
			"msg":        err.Message,
			"request_id": requestID,
		})
	case error:
		c.JSON(200, gin.H{
			"code":       businesserror.CodeInternalError,
			"data":       data,
			"msg":        businesserror.ErrInternalError.Error(),
			"request_id": requestID,
		})
	default:
		panic("invalid SendError err type")
	}
}

// SendOKResponse swagger使用
type SendOKResponse struct {
	Code      string      `json:"code"`
	Data      interface{} `json:"data"`
	Msg       string      `json:"msg"`
	RequestID string      `json:"request_id"`
}
