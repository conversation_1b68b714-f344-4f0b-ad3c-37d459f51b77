package http

import (
	"net/http"
	"server/pkg/libs"
	"server/pkg/logger"

	"github.com/gin-gonic/gin"
)

func GetGinPanicHandler(l *logger.Logger) gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, err interface{}) {
		l.<PERSON>(logger.Fields{
			"header": c.Request.Header,
			"length": c.Request.ContentLength,
			"router": c.Request.URL.String(),
			"err":    err,
		}).Error("router is panic, need repair, stack: %s", libs.Stack(3))
		c.<PERSON>bort<PERSON>ith<PERSON>tat<PERSON>(http.StatusBadRequest)
	})
}
