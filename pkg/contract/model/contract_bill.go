package model

import (
	"database/sql"
	"gorm.io/gorm"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"
)

const TableNameContractBill = "contract_bill"

type ContractBill struct { // 合同账单
	ID                     int                         `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	CreatedAt              time.Time                   `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt              time.Time                   `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt              gorm.DeletedAt              `gorm:"type:datetime;column:deleted_at;index" json:"-"`
	ContractUUID           string                      `gorm:"column:contract_uuid;type:varchar(255);" json:"contract_uuid"`
	UID                    int                         `gorm:"type:int;column:uid" json:"uid"`                                                            // user_id
	Status                 constant.ContractBillStatus `gorm:"type:varchar(255);column:status;default:''" json:"status"`                                  // 状态
	Amount                 int64                       `gorm:"type:int;column:amount" json:"amount"`                                                      // 账单金额
	StartTime              sql.NullTime                `gorm:"type:datetime;column:start_time" json:"start_time"`                                         // 账单开始时间
	EndTime                sql.NullTime                `gorm:"type:datetime;column:end_time" json:"end_time"`                                             // 账单结束时间
	IsSkipCustomerConfirm  bool                        `gorm:"type:tinyint(1);column:is_skip_customer_confirm;default:0" json:"is_skip_customer_confirm"` // 是否跳过客户确认账单
	PaymentDeadlineTime    sql.NullTime                `gorm:"type:datetime;column:payment_deadline_time" json:"payment_deadline_time"`                   // 本期付款截至时间
	NextBillInitiationTime sql.NullTime                `gorm:"type:datetime;column:next_bill_initiation_time" json:"next_bill_initiation_time"`           // 下期账单发起时间
	ContractBillFileEntity FileInfo                    `gorm:"serializer:json;type:json;column:contract_bill_file" json:"contract_bill_file"`             // 账单文件
	InvoiceFileEntity      FileInfo                    `gorm:"serializer:json;type:json;column:invoice_file" json:"invoice_file"`                         // 发票文件
	PayNote                string                      `gorm:"type:text;column:pay_note;" json:"pay_note"`                                                // 支付备注
}

func (c *ContractBill) TableName() string {
	return TableNameContractBill
}

func (c *ContractBill) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&ContractBill{})
}

func (c *ContractBill) ContractBillCreate(tx *gorm.DB) error {
	return db_helper.InsertOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         c,
		InsertPayload:           c,
	}).GetError()
}

func (c *ContractBill) ContractBillUpdate(tx *gorm.DB, filter *db_helper.QueryFilters, um map[string]interface{}) (err error) {
	um["updated_at"] = time.Now()
	return db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         c,
		Filters:                 *filter,
	}, um).GetError()
}

func (c *ContractBill) ContractBillGetAll(filter db_helper.QueryFilters) (list []ContractBill, err error) {
	list = []ContractBill{}
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: c,
		Filters:         filter,
	}, &list).GetError()
	return
}

func (c *ContractBill) ContractBillGetAllWithSelect(s string, filters db_helper.QueryFilters, result interface{}) (err error) {
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: c,
		Select:          s,
		Filters:         filters,
		NoLimit:         true,
	}, result).GetError()
	return
}

func (c *ContractBill) ContractBillGetFirst(filter *db_helper.QueryFilters) (err error) {
	err = db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: c,
		Filters:         *filter,
	}, &c).GetError()
	return
}
