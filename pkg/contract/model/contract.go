package model

import (
	"database/sql"
	"gorm.io/gorm"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"
)

const TableNameContract = "contract"

type Contract struct { // 合同
	ID                     int                         `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	CreatedAt              time.Time                   `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt              time.Time                   `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt              gorm.DeletedAt              `gorm:"type:datetime;column:deleted_at;index" json:"-"`
	UUID                   string                      `gorm:"column:uuid;type:varchar(255);" json:"uuid"`
	UserPhone              string                      `gorm:"type:varchar(255);column:user_phone;default:''" json:"user_phone"`
	UID                    int                         `gorm:"type:int;column:uid" json:"uid"`                                                  // user_id
	Name                   string                      `gorm:"type:varchar(255);column:name;default:''" json:"name"`                            // 合同名称
	AEntityName            string                      `gorm:"type:varchar(255);column:a_entity_name;default:''" json:"a_entity_name"`          // 甲方主体名称
	ContractType           constant.ContractType       `gorm:"type:varchar(255);column:contract_type;default:''" json:"contract_type"`          // 合同类型
	StartTime              sql.NullTime                `gorm:"type:datetime;column:start_time" json:"start_time"`                               // 合同开始时间
	EndTime                sql.NullTime                `gorm:"type:datetime;column:end_time" json:"end_time"`                                   // 合同结束时间
	BusinessType           constant.BusinessType       `gorm:"type:varchar(255);column:business_type;default:''" json:"business_type"`          // 业务类型
	PaymentMethod          string                      `gorm:"type:text;column:payment_method;" json:"payment_method"`                          // 付款方式
	TotalAmount            int64                       `gorm:"type:int;column:total_amount" json:"total_amount"`                                // 合同总金额
	PerPeriodAmount        int64                       `gorm:"type:varchar(255);column:per_period_amount" json:"per_period_amount"`             // 每期账单金额
	Note                   string                      `gorm:"type:text;column:note;" json:"note"`                                              // 备注
	NextBillInitiationTime sql.NullTime                `gorm:"type:datetime;column:next_bill_initiation_time" json:"next_bill_initiation_time"` // 下期账单发起时间
	BillPaidTime           sql.NullTime                `gorm:"type:datetime;column:bill_paid_time" json:"bill_paid_time"`                       // 账单已支付到（最新的时间）
	Status                 constant.ContractStatus     `gorm:"type:varchar(255);column:status" json:"status"`                                   // 合同状态
	ContractFileEntity     FileInfo                    `gorm:"serializer:json;type:json;column:contract_file" json:"contract_file"`             // 合同文件
	ProgressBillStatus     constant.ProgressBillStatus `gorm:"type:varchar(255);column:progress_bill_status" json:"progress_bill_status"`       // 进行中账单状态
}

func (c *Contract) TableName() string {
	return TableNameContract
}

func (c *Contract) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&Contract{})
}

func (c *Contract) ContractCreate(tx *gorm.DB) error {
	return db_helper.InsertOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         c,
		InsertPayload:           c,
	}).GetError()
}

func (c *Contract) ContractUpdate(tx *gorm.DB, filter *db_helper.QueryFilters, um map[string]interface{}) (err error) {
	um["updated_at"] = time.Now()
	return db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         c,
		Filters:                 *filter,
	}, um).GetError()
}

func (c *Contract) ContractGetAll(filter db_helper.QueryFilters) (list []Contract, err error) {
	list = []Contract{}
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: c,
		Filters:         filter,
	}, &list).GetError()
	return
}

func (c *Contract) ContractGetAllWithSelect(s string, filters db_helper.QueryFilters, result interface{}) (err error) {
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: c,
		Select:          s,
		Filters:         filters,
		NoLimit:         true,
	}, result).GetError()
	return
}

func (c *Contract) ContractGetFirst(filter *db_helper.QueryFilters) (err error) {
	err = db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: c,
		Filters:         *filter,
	}, &c).GetError()
	return
}
