package model

import (
	"gorm.io/gorm"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"
)

const TableNameDeploymentContainerEvent = "deployment_container_event"

type DeploymentContainerEvent struct {
	ID                      int               `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	UID                     int               `gorm:"column:uid;type:int" json:"uid"` // 创建人, 详细信息从 /pkg/user 模块获取.
	MachineID               string            `gorm:"type:varchar(255);column:machine_id;default ''" json:"machine_id"`
	DeploymentUUID          string            `gorm:"column:deployment_uuid;type:varchar(255);index" json:"deployment_uuid"`
	DeploymentContainerUUID string            `gorm:"column:deployment_container_uuid;type:varchar(255)" json:"deployment_container_uuid"`
	Status                  constant.DCStatus `gorm:"column:status;type:varchar(255)" json:"status"`
	StartError              bool              `gorm:"column:start_error;type:tinyint(1)" json:"start_error"`
	Msg                     string            `gorm:"column:msg;type:varchar(255)" json:"msg"`

	CreatedAt time.Time `gorm:"type:datetime;column:created_at;" json:"created_at"`
}

func (i *DeploymentContainerEvent) TableName() string {
	return TableNameDeploymentContainerEvent
}

// Init 实现 db_helper 接口.
func (i *DeploymentContainerEvent) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&DeploymentContainerEvent{})
}

func (i *DeploymentContainerEvent) DCEventCreate(tx *gorm.DB) (err error) {
	err = db_helper.InsertOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         i,
		InsertPayload:           i,
	}).GetError()
	return
}

func (i *DeploymentContainerEvent) CountStartErrorLastFiveMinute(deploymentUUID string) (count int64, err error) {
	//err = db_helper.GlobalDBConn().Table(TableNameDeploymentContainerEvent).
	//	Select("count(*)").
	//	Where("deployment_uuid = ?", deploymentUUID).
	//	Where("start_error = ?", true).
	//	Where("created_at > ?", time.Now().Add(-5*time.Minute)).
	//	Find(&count).Error
	return 0, nil
}

func (i *DeploymentContainerEvent) CountMachineException(machineID string) (last2Count, last10Count int64, err error) {
	//db := db_helper.GlobalDBConn().Table(TableNameDeploymentContainerEvent).
	//	Select("count(*)").
	//	Where("machine_id = ?", machineID).
	//	Where("status in (?)", []constant.DCStatus{constant.DCShutdownByStartingError})
	//err = db.Where("created_at > ?", time.Now().Add(-2*time.Minute)).
	//	Find(&last2Count).Error
	//if err != nil {
	//	return
	//}
	//err = db.Where("created_at > ?", time.Now().Add(-10*time.Minute)).
	//	Find(&last10Count).Error
	return 0, 0, nil
}

func (i *DeploymentContainerEvent) DCEventGetLast(f *db_helper.QueryFilters) error {
	return db_helper.GetLast(db_helper.QueryDefinition{
		ModelDefinition: i,
		Filters:         *f,
	}, &i).GetError()
}
