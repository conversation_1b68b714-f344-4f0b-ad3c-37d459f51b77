package model

import (
	"gorm.io/gorm"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"strings"
	"time"
)

const TableNameDeploymentDurationPkg = "deployment_duration_pkg"

type DeploymentDurationPkg struct {
	ID             int    `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	UID            int    `gorm:"column:uid;type:int" json:"uid"` // 创建人, 详细信息从 /pkg/user 模块获取.
	DeploymentUUID string `gorm:"column:deployment_uuid;type:varchar(255)" json:"deployment_uuid"`
	OrderUUID      string `gorm:"column:order_uuid;type:varchar(255);uniqueIndex" json:"order_uuid"`
	GpuType        string `gorm:"column:gpu_type;type:varchar(255)" json:"gpu_type"`
	//RegionSignList   string             `gorm:"column:region_sign_list;type:varchar(255)" json:"region_sign_list"`
	DCList           string             `gorm:"column:dc_list;type:varchar(255)" json:"dc_list"`
	PaygPrice        int64              `gorm:"column:payg_price" json:"payg_price"`
	PaygPriceVoucher int64              `gorm:"column:payg_price_voucher" json:"payg_price_voucher"`
	Total            int64              `gorm:"column:total" json:"total"`     // 总时长, 单位秒
	Balance          int64              `gorm:"column:balance" json:"balance"` // 剩余时长
	Status           constant.DDPStatus `gorm:"column:status;type:varchar(255)" json:"status"`
	SubName          string             `gorm:"column:sub_name;type:varchar(255);default ''" json:"sub_name"`

	// time
	CreatedAt time.Time `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
}

func (i *DeploymentDurationPkg) TableName() string {
	return TableNameDeploymentDurationPkg
}

// Init 实现 db_helper 接口.
func (i *DeploymentDurationPkg) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&DeploymentDurationPkg{})
}

func (i *DeploymentDurationPkg) DDPCreate(tx *gorm.DB) error {
	return db_helper.InsertOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         i,
		InsertPayload:           i,
	}).GetError()
}

func (i *DeploymentDurationPkg) DDPUpdate(tx *gorm.DB, filter db_helper.QueryFilters, um map[string]interface{}) error {
	um["updated_at"] = time.Now()
	return db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         i,
		Filters:                 filter,
	}, um).GetError()
}

func (i *DeploymentDurationPkg) DDPCount(filter db_helper.QueryFilters) (count int64, err error) {
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: i,
		Filters:         filter,
	}, &count).GetError()
	return
}

func (i *DeploymentDurationPkg) DDPGet(tx *gorm.DB, filter db_helper.QueryFilters) error {
	return db_helper.GetFirst(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         i,
		Filters:                 filter,
	}, &i).GetError()
}

func (i *DeploymentDurationPkg) DDPGetAllWithSelect(s string, filter db_helper.QueryFilters, dst interface{}) error {
	return db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: i,
		Select:          s,
		Filters:         filter,
		NoLimit:         true,
	}, dst).GetError()
}

func (i *DeploymentDurationPkg) DDPList(tx *gorm.DB, filter db_helper.QueryFilters) ([]DeploymentDurationPkg, error) {
	list := make([]DeploymentDurationPkg, 0)
	err := db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition:         i,
		DBTransactionConnection: tx,
		Filters:                 filter,
	}, &list).GetError()

	return list, err
}

func (i *DeploymentDurationPkg) DDPGetAllForBCCronCharge() (map[string][]int, map[int]int64, error) {
	list := make([]struct {
		ID             int    `gorm:"column:id" json:"id"`
		DeploymentUUID string `gorm:"column:deployment_uuid" json:"deployment_uuid"`
		GpuType        string `gorm:"column:gpu_type" json:"gpu_type"`
		DCList         string `gorm:"column:dc_list;" json:"dc_list"`
		Balance        int64  `gorm:"column:balance" json:"balance"`
	}, 0)
	resp1 := make(map[string][]int)
	resp2 := make(map[int]int64)
	err := i.DDPGetAllWithSelect("id, deployment_uuid, gpu_type, balance, dc_list",
		db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"status": constant.DDPInEffect,
			},
			//Groups: []string{"deployment_uuid", "gpu_type"},
		}, &list)
	if err != nil {
		return resp1, resp2, err
	}

	for _, v := range list {
		for _, vv := range strings.Split(v.DCList, ",") {
			key1 := v.DeploymentUUID + v.GpuType + vv
			if _, ok := resp1[key1]; !ok {
				resp1[key1] = make([]int, 0)
			}
			resp1[key1] = append(resp1[key1], v.ID)
			resp2[v.ID] = v.Balance
		}
	}

	return resp1, resp2, nil
}
