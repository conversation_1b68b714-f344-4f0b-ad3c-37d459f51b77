package service

import (
	"server/pkg/logger"
	"server/pkg/module_definition"
	"server/plugin/queue"
	redis "server/plugin/redis_plugin"
	"sync"
)

const ModuleName = "deployment_service"

type DeploymentService struct {
	log                 *logger.Logger
	region              module_definition.RegionInterface
	machine             module_definition.MachineInference
	gpuStock            module_definition.GpuStockInterface
	container           module_definition.ContainerRuntimeInterface
	user                module_definition.UserInterface
	gpuType             module_definition.GpuTypeInference
	bc                  module_definition.BCInterface
	bcServer            module_definition.BCServerInterface
	image               module_definition.ImageInference
	privateImage        module_definition.PrivateImageInterface
	q                   *queue.Q
	mutex               *redis.MutexRedis
	schedulingLimitChan chan struct{}
	schedulingMap       sync.Map
	adjustingMap        sync.Map
	versionCtrl         *redis.DeploymentVersion
	deploymentLock      *redis.DeploymentLockPlugin
	schedulerCache
	stopRedis *redis.DeploymentStop
}

func NewDeploymentServiceProvider(
	self *DeploymentService,
	region module_definition.RegionInterface,
	machineInterface module_definition.MachineInference,
	gpuStockInterface module_definition.GpuStockInterface,
	containerInterface module_definition.ContainerRuntimeInterface,
	user module_definition.UserInterface,
	gpuType module_definition.GpuTypeInference,
	bc module_definition.BCInterface,
	bcServer module_definition.BCServerInterface,
	image module_definition.ImageInference,
	privateImage module_definition.PrivateImageInterface,
	q *queue.Q,
	mutex *redis.MutexRedis,
	deploymentLock *redis.DeploymentLockPlugin,
	versionCtrl *redis.DeploymentVersion,
	stopRedis *redis.DeploymentStop,
) {
	self.region = region
	self.log = logger.NewLogger(ModuleName)
	self.machine = machineInterface
	self.gpuStock = gpuStockInterface
	self.container = containerInterface
	self.user = user
	self.gpuType = gpuType
	self.bc = bc
	self.bcServer = bcServer
	self.image = image
	self.privateImage = privateImage
	self.q = q
	self.mutex = mutex
	self.deploymentLock = deploymentLock
	self.versionCtrl = versionCtrl
	self.schedulingLimitChan = make(chan struct{}, 2) // 调度并发
	self.schedulingMap = sync.Map{}
	self.adjustingMap = sync.Map{}
	self.schedulerCache = schedulerCache{
		loopDeploymentIndex:                 0,
		loopMachineAccordingDeploymentIndex: map[string]int{},
	}
	self.stopRedis = stopRedis
}
