package controller

import (
	"server/pkg/deployment/service"
	service2 "server/pkg/image/service"
	"server/pkg/logger"
	"server/pkg/module_definition"
	"server/plugin/redis_plugin"
)

type DeploymentController struct {
	d            module_definition.DeploymentInterface
	deployment   module_definition.DeploymentInterface
	privateImage module_definition.PrivateImageInterface
	user         module_definition.UserInterface
	bc           module_definition.BCInterface
	log          *logger.Logger
	redisStop    *redis_plugin.DeploymentStop
}

const ModuleName = "deployment_ctrl"

func NewDeploymentProvider(user module_definition.UserInterface, bc module_definition.BCInterface, deployment *service.DeploymentService, privateImage *service2.PrivateImageService,
	redisStop *redis_plugin.DeploymentStop) *DeploymentController {
	return &DeploymentController{
		user:         user,
		bc:           bc,
		deployment:   deployment,
		privateImage: privateImage,
		log:          logger.NewLogger(ModuleName),
		redisStop:    redisStop,
	}
}
