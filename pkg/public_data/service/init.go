package service

import (
	"server/pkg/logger"
	"server/pkg/module_definition"
	"server/plugin/redis_plugin"
)

type PDService struct {
	mutex *redis_plugin.MutexRedis
	redis *redis_plugin.PublicDataPlugin
	user  module_definition.UserInterface
	log   *logger.Logger
}

func NewPublicDataServiceProvider(
	self *PDService,
	mutex *redis_plugin.MutexRedis,
	redis *redis_plugin.PublicDataPlugin,
	user module_definition.UserInterface,
) {
	self.mutex = mutex
	self.redis = redis
	self.user = user
	self.log = logger.NewLogger("public_data_service")
}
