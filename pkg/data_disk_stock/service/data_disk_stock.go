package service

import (
	log "github.com/sirupsen/logrus"
	"server/pkg-core/api/coreapi"
	dataDiskModel "server/pkg-core/data_disk_stock/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/data_disk_stock/model"
	"server/pkg/db_helper"
	"time"

	"github.com/pkg/errors"
	"gorm.io/gorm"
)

// Create 创建数据盘记录，在机器注册的时候.防止重复创建，复用get方法
func (svc *DataDiskService) Create(tx *gorm.DB, machineID string) (err error) {
	_, err = svc.Get(tx, machineID)
	return
}

// Update 用于更新数据盘价格以及一些配置信息。
func (svc *DataDiskService) Update(tx *gorm.DB, params *model.UpdateDDSParams) (err error) {
	if params == nil {
		return businesserror.ErrInternalError
	}

	var (
		updateMap = make(map[string]interface{})
		dds       *model.DataDiskStock
	)

	dds, err = svc.Get(nil, params.MachineID)
	if err != nil {
		svc.log.WithField("machine_id", params.MachineID).Error("update dds: get dds failed")
		return err
	}

	updateMap["stock_lock"] = dds.StockLock + 1
	if params.ChargeInfo != nil {
		if dds.DailyPrice != params.ChargeInfo.DailyPrice ||
			dds.WeeklyPrice != params.ChargeInfo.WeeklyPrice ||
			dds.MonthlyPrice != params.ChargeInfo.MonthlyPrice ||
			dds.YearlyPrice != params.ChargeInfo.YearlyPrice {
			updateMap["daily_price"] = params.ChargeInfo.DailyPrice
			updateMap["weekly_price"] = params.ChargeInfo.WeeklyPrice
			updateMap["monthly_price"] = params.ChargeInfo.MonthlyPrice
			updateMap["yearly_price"] = params.ChargeInfo.YearlyPrice
		}
	}
	// 业务层只会修改上面的计费信息等字段，不会修改其它的，所以这里删掉其它的更新逻辑

	if len(updateMap) <= 1 {
		return nil
	}
	rows, errDB := db_helper.UpdateOneAffectRows(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         &model.DataDiskStock{},
		Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"machine_id": params.MachineID, "stock_lock": dds.StockLock}},
	}, updateMap)
	if errDB.IsNotNil() {
		svc.log.WithError(errDB.GetError()).WithField("machine_id", params.MachineID).Error("update dds failed")
		err = businesserror.ErrDatabaseError
		return err
	}
	if rows == 0 {
		svc.log.Error("update dds: affect rows is 0")
		err = businesserror.ErrServerBusy
		return err
	}

	return
}

// Get 支持查询到空记录时自动创建
func (svc *DataDiskService) Get(tx *gorm.DB, machineID string) (res *model.DataDiskStock, err error) {
	err = db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: &model.DataDiskStock{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"machine_id": machineID}},
	}, &res).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			res = &model.DataDiskStock{
				MachineID: machineID,
			}
			err = db_helper.InsertOne(db_helper.QueryDefinition{
				DBTransactionConnection: tx,
				ModelDefinition:         &model.DataDiskStock{},
				InsertPayload:           res,
			}).GetError()
			if err != nil {
				svc.log.WithField("machine_id", machineID).Error("insert machine data disk record failed.")
				err = businesserror.ErrDatabaseError
			}
			return
		}
		svc.log.WithError(err).WithField("machine_id", machineID).Error("get data disk record failed")
		err = businesserror.ErrDatabaseError
	}

	return
}

func (svc *DataDiskService) GetByMachineIdsFromCore(machineIds []string) (res map[string]dataDiskModel.DataDiskStock, err error) {
	req := coreapi.GetDataDiskByMachineIdsReq{
		MachineIds: machineIds,
	}
	coreRes, err := svc.coreApi.GetDataDiskByMachineIds(req, nil)
	if err != nil {
		svc.log.WithField("machine_ids", machineIds).ErrorE(err, "core api: GetDataDiskByMachineIds failed")
		return
	}

	err = coreRes.GetErr()
	if err != nil {
		return
	}

	res = make(map[string]dataDiskModel.DataDiskStock)

	for _, dds := range coreRes.Data {
		res[dds.MachineID] = dds
	}

	return
}

// GetList 获取列表
func (svc *DataDiskService) GetList(machineIDList []string) (resSlice []model.DataDiskStock, resMap map[string]model.DataDiskStock, err error) {
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.DataDiskStock{},
		Filters:         db_helper.QueryFilters{InFilters: []db_helper.In{{Key: "machine_id", InSet: machineIDList}}},
		NoLimit:         true,
	}, &resSlice).GetError()
	if err != nil {
		svc.log.WithError(err).Error("get dds by machine id list failed")
		err = businesserror.ErrDatabaseError
		return
	}

	resMap = make(map[string]model.DataDiskStock)
	for k := range resSlice {
		resMap[resSlice[k].MachineID] = resSlice[k]
	}
	return
}

// Reserve 占用库存, 第一次占用，变更占用大小使用changeSize
func (svc *DataDiskService) Reserve(params *model.ExpandParams) (txUUID string, err error) {
	var (
		dds *model.DataDiskStock
		dda *model.DataDiskAllocate
	)

	// core 占用库存时会根据机器 ID 上锁，但是 core 不知道占用操作是否是重复的，所以需要业务层去防止重复操作。
	// 业务层上锁后，重复的操作会获取到 dda，认为是重复操作不会再调用 core 了。
	lock, err := svc.setLockForUpdateDDSStock(params.MachineID)
	if err != nil {
		svc.log.WithError(err).Warn("setLockForUpdateDDSStock: set lock failed.")
		return "", err
	}
	defer func() {
		_ = lock.UnLock()
	}()

	// 校验
	dda, err = svc.GetDDA(params.MachineID, params.ProductUUID)
	if err != nil {
		return
	}
	// 只要有占用记录就认为是重复操作
	if dda != nil {
		return "", nil
	}

	dds, err = svc.Get(nil, params.MachineID)
	if err != nil {
		svc.log.WithError(err).WithField("machine_id", params.MachineID).Error("get dds failed")
		return
	}

	if params.ExpandDiskSize > dds.GetAvailable() {
		return "", businesserror.ErrDataDiskReserveInsufficientDisk
	}

	// 先占用
	req := &dataDiskModel.ExpandParams{
		MachineID:      params.MachineID,
		ProductUUID:    params.ProductUUID,
		ExpandDiskSize: params.ExpandDiskSize,
	}
	coreRes, err := svc.coreApi.ReserveDataDisk(req, nil)
	if err != nil {
		svc.log.WithField("params", params).ErrorE(err, "core api: ReserveDataDisk failed")
		return
	}

	err = coreRes.GetErr()
	if err != nil {
		return
	}
	txUUID = coreRes.Data.TxUUID

	return coreRes.Data.TxUUID, nil
}

// ChangeSize 扩容/缩容
func (svc *DataDiskService) ChangeSize(params *model.ChangeSizeParams) (txUUID string, err error) {
	if params == nil {
		return "", businesserror.ErrInternalError
	}

	var (
		log = svc.log.WithField("params", params)
		dds *model.DataDiskStock
		dda *model.DataDiskAllocate
	)

	// 校验
	dda, err = svc.GetDDA(params.MachineID, params.ProductUUID)
	if err != nil {
		log.WithError(err).Error("changeSize: get dda failed")
		return
	}
	if dda == nil {
		return svc.Reserve(&model.ExpandParams{
			MachineID:      params.MachineID,
			ProductUUID:    params.ProductUUID,
			ExpandDiskSize: params.FinallySize,
		})
	}

	dds, err = svc.Get(nil, params.MachineID)
	if err != nil {
		svc.log.WithError(err).WithField("machine_id", params.MachineID).Error("get dds failed")
		return
	}

	switch params.OptType {
	case constant.OrderTypeDataDiskExpand, constant.OrderTypeDataDiskExpandOfPrepay:
		if dda.DiskAllocate+params.ChangeSize != params.FinallySize {
			err = businesserror.ErrInternalError
			log.WithField("dda.allocate", dda.DiskAllocate).Error("change size: expand, size error")
			return
		}

		if params.ChangeSize > dds.GetAvailable() {
			err = businesserror.ErrDataDiskMaxExpandSizeLimit
			return
		}
		// 预先占用
		var coreRes coreapi.ReserveDataDiskRes
		req := &dataDiskModel.ExpandParams{
			MachineID:      params.MachineID,
			ProductUUID:    params.ProductUUID,
			ExpandDiskSize: params.ChangeSize,
		}

		coreRes, err = svc.coreApi.ReserveDataDisk(req, nil)
		if err != nil {
			svc.log.WithField("params", params).ErrorE(err, "core api: ReserveDataDisk failed")
			return
		}

		err = coreRes.GetErr()
		if err != nil {
			return
		}
		txUUID = coreRes.Data.TxUUID
	case constant.OrderTypeDataDiskReduce:
		if dda.DiskAllocate-params.ChangeSize != params.FinallySize {
			log.WithField("dda.allocate", dda.DiskAllocate).Error("change size: reduce, size error")
			return "", businesserror.ErrInternalError
		}
		// reduce不是全部release，所以这里不能调dds.release方法，而是直接调用coreapi.ReleaseDataDisk方法
		var coreRes coreapi.ReleaseDataDiskRes
		req := &dataDiskModel.ReleaseParams{
			MachineID:        params.MachineID,
			ProductUUID:      params.ProductUUID,
			DiskAllocateSize: params.ChangeSize,
		}
		coreRes, err = svc.coreApi.ReleaseDataDisk(req, nil)
		if err != nil {
			svc.log.WithField("params", params).ErrorE(err, "core api: ReleaseDataDisk failed")
			return
		}

		err = coreRes.GetErr()
		if err != nil {
			return
		}
		txUUID = coreRes.Data.TxUUID
	default:
		log.Error("change size: opt type error.")
		return "", businesserror.ErrInternalError
	}

	return
}

// Release 释放库存
func (svc *DataDiskService) Release(tx *gorm.DB, params *model.ReleaseParams) (txUUID string, err error) {
	if params == nil {
		return "", businesserror.ErrInternalError
	}

	var (
		log         = svc.log.WithField("params", params)
		now         = time.Now()
		dda         *model.DataDiskAllocate
		coreRelease = true
	)

	// core 释放库存时会根据机器 ID 上锁，但是 core 不知道释放操作是否是重复的，所以需要业务层去防止重复操作。
	// 业务层上锁后，重复的操作获取不到 dda，coreRelease 为 false，不会再调用 core 了。
	lock, err := svc.setLockForUpdateDDSStock(params.MachineID)
	if err != nil {
		svc.log.WithError(err).Warn("setLockForUpdateDDSStock: set lock failed.")
		return "", err
	}
	defer func() {
		_ = lock.UnLock()
	}()

	// 校验
	if params.ReleaseDDA {
		dda, err = svc.GetDDAByProductUUID(params.ProductUUID)
		if err != nil {
			return
		}
		if dda == nil {
			coreRelease = false
		} else {
			params.ReleaseSize = dda.DiskAllocate
		}
	}

	if coreRelease {
		req := &dataDiskModel.ReleaseParams{
			MachineID:        params.MachineID,
			ProductUUID:      params.ProductUUID,
			DiskAllocateSize: params.ReleaseSize,
		}
		coreRes, err := svc.coreApi.ReleaseDataDisk(req, nil)
		if err != nil {
			svc.log.WithField("params", params).ErrorE(err, "core api: ReleaseDataDisk failed")
			return "", err
		}

		err = coreRes.GetErr()
		if err != nil {
			return "", err
		}
		txUUID = coreRes.Data.TxUUID
	}

	if params.ReleaseDDA {
		// 构建map
		err = db_helper.UpdateOne(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.DataDiskAllocate{},
			Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{
				"product_uuid": params.ProductUUID,
				"using":        true,
			}},
		}, map[string]interface{}{
			"using":      false,
			"updated_at": now,
		}).GetError()
		if err != nil {
			log.WithField("params", params).ErrorE(err, "update dda failed")
			err = businesserror.ErrDatabaseError
			return
		}
	}

	return
}

// GetDDA 获取实例的占用记录
func (svc *DataDiskService) GetDDA(machineID, productUUID string) (dda *model.DataDiskAllocate, err error) {
	err = db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: &model.DataDiskAllocate{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{
			"machine_id":   machineID,
			"product_uuid": productUUID,
			"using":        true,
		}},
	}, &dda).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}

		svc.log.WithError(err).Error("get dda failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

// GetDDAByProductUUID 获取实例的占用记录
func (svc *DataDiskService) GetDDAByProductUUID(productUUID string) (dda *model.DataDiskAllocate, err error) {
	err = db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: &model.DataDiskAllocate{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{
			"product_uuid": productUUID,
			"using":        true,
		}},
	}, &dda).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}

		svc.log.WithError(err).Error("get dda failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

// ExistDDA 判断是否存在扩容
func (svc *DataDiskService) ExistDDA(machineID, productUUID string) (exist bool, err error) {
	var row int64
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: &model.DataDiskAllocate{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{
			"machine_id":   machineID,
			"product_uuid": productUUID,
			"using":        true,
		}},
	}, &row).GetError()
	if err != nil {
		svc.log.WithError(err).Error("get dda failed")
		err = businesserror.ErrDatabaseError
		return
	}

	if row != 0 {
		return true, nil
	}
	return false, nil
}

// UpdateDDA 更新dda
func (svc *DataDiskService) UpdateDDA(tx *gorm.DB, params *model.UpdateDDAParams) (err error) {
	now := time.Now()

	// 根据情况选择, 此处要保证, 如果是预付费扩容,不能同时有多个未付款订单,在创建订单出加校验
	exist, err := svc.ExistDDA(params.MachineID, params.ProductUUID)
	if err != nil {
		return
	}

	if !exist {
		// 构建实体
		dda := &model.DataDiskAllocate{
			MachineID:    params.MachineID,
			ProductUUID:  params.ProductUUID,
			DiskAllocate: params.FinallySize,
			Using:        true,
			CreatedAt:    now,
			UpdatedAt:    now,
		}

		// operate
		err = db_helper.InsertOne(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.DataDiskAllocate{},
			InsertPayload:           dda}).GetError()
		if err != nil {
			svc.log.WithError(err).Error("insert into data disk allocate failed")
			return businesserror.ErrDatabaseError
		}
		return nil
	}

	// allocate
	affectRows, errDB := db_helper.UpdateOneAffectRows(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         &model.DataDiskAllocate{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{
			"machine_id":   params.MachineID,
			"product_uuid": params.ProductUUID,
			"using":        true,
		}},
	}, map[string]interface{}{
		"disk_allocation": params.FinallySize,
		"updated_at":      now,
	})
	if errDB.IsNotNil() {
		log.WithError(errDB.GetError()).Error("update dda failed")
		err = businesserror.ErrDatabaseError
		return
	}
	if affectRows == 0 {
		log.Error("update dda: server busy")
		err = businesserror.ErrServerBusy
		return
	}
	return
}

// GetDDAList 获取列表
func (svc *DataDiskService) GetDDAList(productUUIDList []string) (resSlice []model.DataDiskAllocate, resMap map[string]model.DataDiskAllocate, err error) {
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.DataDiskAllocate{},
		Filters: db_helper.QueryFilters{InFilters: []db_helper.In{{
			Key: "product_uuid", InSet: productUUIDList,
		}}},
	}, &resSlice).GetError()
	if err != nil {
		svc.log.WithError(err).Error("get dda by product uuid list failed")
		err = businesserror.ErrDatabaseError
		return
	}

	resMap = make(map[string]model.DataDiskAllocate)
	for k := range resSlice {
		resMap[resSlice[k].ProductUUID] = resSlice[k]
	}
	return
}

// GetDDAListInUse 获取列表
func (svc *DataDiskService) GetDDAListInUse(productUUIDList []string) (resSlice []model.DataDiskAllocate, resMap map[string]model.DataDiskAllocate, err error) {
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.DataDiskAllocate{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"using": true,
			},
			InFilters: []db_helper.In{{
				Key: "product_uuid", InSet: productUUIDList,
			}}},
	}, &resSlice).GetError()
	if err != nil {
		svc.log.WithError(err).Error("get dda by product uuid list failed")
		err = businesserror.ErrDatabaseError
		return
	}

	resMap = make(map[string]model.DataDiskAllocate)
	for k := range resSlice {
		resMap[resSlice[k].ProductUUID] = resSlice[k]
	}
	return
}

func (svc *DataDiskService) TxCommit(txUUID string) (err error) {
	req := coreapi.DataDiskTxCommitReq{
		TxUUID: txUUID,
	}
	coreRes, err := svc.coreApi.DataDiskTxCommit(req, nil)
	if err != nil {
		svc.log.WithField("txUUID", txUUID).ErrorE(err, "core api: DataDiskTxCommit failed")
		return
	}
	err = coreRes.GetErr()
	if err != nil {
		return
	}
	return nil
}

func (svc *DataDiskService) TxRollback(txUUID string) (err error) {
	req := coreapi.DataDiskTxRollbackReq{
		TxUUID: txUUID,
	}
	coreRes, err := svc.coreApi.DataDiskTxRollback(req, nil)
	if err != nil {
		svc.log.WithField("txUUID", txUUID).ErrorE(err, "core api: DataDiskTxRollback failed")
		return
	}
	err = coreRes.GetErr()
	if err != nil {
		return
	}
	return nil
}
