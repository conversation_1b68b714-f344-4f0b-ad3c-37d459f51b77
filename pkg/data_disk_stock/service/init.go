package service

import (
	"server/pkg-core/api/coreapi"
	"server/pkg/logger"
	"server/pkg/module_definition"
	redis "server/plugin/redis_plugin"
)

const DDSModuleName = "data_disk_stock"

type DataDiskService struct {
	log     *logger.Logger
	mutex   *redis.MutexRedis
	coreApi *coreapi.Api
	machine module_definition.MachineInference
}

func NewDataDiskServiceProvider(mutex *redis.MutexRedis, coreApi *coreapi.Api, machine module_definition.MachineInference) *DataDiskService {
	return &DataDiskService{
		log:     logger.NewLogger(DDSModuleName),
		mutex:   mutex,
		coreApi: coreApi,
		machine: machine,
	}
}
