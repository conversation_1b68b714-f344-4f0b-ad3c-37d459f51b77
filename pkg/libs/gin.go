package libs

import (
	"fmt"
	"server/pkg/constant"
	"server/pkg/threadlocal"
	"time"

	"github.com/gin-gonic/gin"
)

var LogFormatter = func(param gin.LogFormatterParams) string {
	var statusColor, methodColor, resetColor string
	if param.IsOutputColor() {
		statusColor = param.StatusCodeColor()
		methodColor = param.MethodColor()
		resetColor = param.ResetColor()
	}

	if param.Latency > time.Minute {
		// Truncate in a golang < 1.8 safe way
		param.Latency = param.Latency - param.Latency%time.Second
	}

	appversion := param.Request.Header.Get("appversion")
	if len(appversion) > 0 {
		appversion = fmt.Sprintf("| %s ", appversion)
	}

	var uid int
	uidContent, ok := param.Keys[constant.GinKeyUserID]
	if ok && uidContent != nil {
		uid = uidContent.(int)
	}
	requestID := param.Request.Header.Get(threadlocal.HttpHeaderXRequestIDKey)
	// gin 框架日志输出格式
	return fmt.Sprintf("[GIN] %v |%s %3d %s| %13v | %15s %s|%s|%d|%s %-7s %s %#v\n%s",
		param.TimeStamp.Format("2006/01/02-15:04:05"),
		statusColor, param.StatusCode, resetColor,
		param.Latency,
		param.ClientIP,
		appversion,
		requestID,
		uid,
		methodColor, param.Method, resetColor,
		param.Path,
		param.ErrorMessage,
	)
}

func LoggerWithConfig(addList ...string) gin.HandlerFunc {
	formatter := LogFormatter

	out := gin.DefaultWriter

	notlogged := []string{
		"/health",
		"/api/v1/version",
		"/api/v1/public_image",
	}
	notlogged = append(notlogged, addList...)

	var skip map[string]struct{}

	if length := len(notlogged); length > 0 {
		skip = make(map[string]struct{}, length)

		for _, path := range notlogged {
			skip[path] = struct{}{}
		}
	}

	return func(c *gin.Context) {
		// Start timer
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// Process request
		c.Next()

		// Log only when path is not being skipped
		if _, ok := skip[path]; !ok {
			param := gin.LogFormatterParams{
				Request: c.Request,
				Keys:    c.Keys,
			}

			// Stop timer
			param.TimeStamp = time.Now()
			param.Latency = param.TimeStamp.Sub(start)

			param.ClientIP = c.ClientIP()
			param.Method = c.Request.Method
			param.StatusCode = c.Writer.Status()
			param.ErrorMessage = c.Errors.ByType(gin.ErrorTypePrivate).String()

			param.BodySize = c.Writer.Size()

			if raw != "" {
				path = path + "?" + raw
			}

			param.Path = path

			fmt.Fprint(out, formatter(param))
		}
	}
}
