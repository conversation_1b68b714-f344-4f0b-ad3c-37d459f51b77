package libs

import (
	"encoding/json"
	"fmt"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/auth/credentials"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"net"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"testing"
)

func TestValidateURL(t *testing.T) {
	signature := "3510803d54320b4b4ffbc190e9377b2f46d358b1"
	// echostr := "2005551044041373351"
	timestamp := "1649325699"
	nonce := "1725172200"
	valid := ValidateURL(constant.Token, timestamp, nonce, signature)
	fmt.Println("valid: ", valid)
}

func TestRealNameAuth(t *testing.T) {
	resp, err := RealNameAuth("zhotao11", "165558452548526514")
	if err != nil {
		t.Error(err)
		return
	}

	res := struct {
		Code    string `json:"code"`
		Message string `json:"message"`
		Result  struct {
			Res string `json:"res"`
		} `json:"result"`
	}{}
	t.Log(resp.StatusCode)
	t.Log(resp.RawResponse.Body)

	err = resp.JSON(&res)
	if err != nil {
		t.Log(err)
		return
	}
	if res.Code != "0" {
		t.Log(res.Message)
		err = businesserror.ErrEmptyBox.New().Format(res.Message)
		return
	}

	t.Log(res)
}

func TestSendEmail(t *testing.T) {
	code, err := SendEmail("12345", "<EMAIL>")
	if err != nil {
		t.Log(err)
		return
	}

	t.Log(code)

}

func TestEmailDomain(t *testing.T) {
	domain := "wystu.ahu.edu.cn"
	r, err := net.LookupMX(domain)
	if err != nil {
		t.Log(err)
		return
	}

	if len(r) <= 0 {
		t.Log("domian not found")
		return
	}

	t.Log("domain ok")

}

func TestQueryInvalidEmailAddress(t *testing.T) {
	resp, err := QueryInvalidEmailAddress()
	if err != nil {
		t.Log(err)
		return
	}

	t.Log(IndentString(resp))
}

func TestEmailHtml(t *testing.T) {
	a := emailHtml1 + "12345" + emailHtml2
	t.Log(a)

}

func TestOverseasSMS(t *testing.T) {
	accessKeyID := "LTAIBCFQlWz0EuyK"
	accessSecret := "L2Z1Gu0fTdVsFnmH01Q0Y2AJa3QWOb"

	config := sdk.NewConfig()

	credential := credentials.NewAccessKeyCredential(accessKeyID, accessSecret)
	client, err := sdk.NewClientWithOptions("cn-hangzhou", config, credential)
	if err != nil {
		panic(err)
	}

	request := requests.NewCommonRequest()
	request.Method = "POST"
	request.Scheme = "https"
	request.Domain = "dysmsapi.aliyuncs.com"
	request.Version = "2017-05-25"
	request.ApiName = "SendMessageToGlobe"
	request.QueryParams["To"] = "15105452002"
	request.QueryParams["From"] = "18667991213"
	request.QueryParams["Message"] = "have a test."
	request.QueryParams["Type"] = "OTP"
	response, err := client.ProcessCommonRequest(request)
	if err != nil {
		panic(err)
	}

	responseContent := response.GetHttpContentString()

	fmt.Print(responseContent)

	var responseData OverseasSendSmsReply
	err = json.Unmarshal([]byte(responseContent), &responseData)
	if err != nil {
		panic(err)
	}
	if responseData.Code == "OK" {
		fmt.Println("Success")
	}
}

func call1() {
	call2()
}

func call2() {
	call3()
}

func call3() {
	fmt.Println(GetFuncCaller())

}

func TestCaller(t *testing.T) {
	call1()
}
func TestTemplate(t *testing.T) {
	fmt.Printf(fmt.Sprintf(`{"instance_id":"%s","time":"%s"}`, "24fb4ca36a-9f4a231b", "3点钱"))
}
