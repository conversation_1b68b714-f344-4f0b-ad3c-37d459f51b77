package libs

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	emoji "github.com/Andrew-M-C/go.emoji"
	"github.com/pkg/errors"
	"io"
	"math/rand"
	"net/http"
	"net/url"
	"regexp"
	"server/conf"
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/aliyun/alibaba-cloud-sdk-go/sdk"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/auth/credentials"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"

	"github.com/levigross/grequests"

	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
	gocrypt "github.com/vonwenm/go-crypt"
)

// GenerateTicket generate ticket for user to exchange token
func GenerateTicket(uid int) string {
	return generateTicket(strconv.Itoa(uid))
}

func GenerateTicketSubUser(subName string) string {
	return generateTicket(subName)
}

func generateTicket(data string) (ticket string) {
	s := sha1.New()
	_, _ = s.Write([]byte(ticket))
	_, _ = s.Write([]byte(data))
	ticket = base64.URLEncoding.EncodeToString(s.Sum(nil))
	return
}

// crypt wraps C library crypt_r
func crypt(key, salt string) string {
	out := gocrypt.Crypt(key, salt)
	return out
}

// SaltPassword add salt for password
func SaltPassword(password string) string {
	salt := "4f"
	return crypt(password, salt)
}

// Sha1 password sha1 encode
func Sha1(password string) string {
	o := sha1.New()
	o.Write([]byte(password))
	return hex.EncodeToString(o.Sum(nil))
}

const (
	accessKeyID  = "LTAIBCFQlWz0EuyK"
	accessSecret = "L2Z1Gu0fTdVsFnmH01Q0Y2AJa3QWOb"
	signName     = "AutoDL"
	accountName  = "<EMAIL>"
	subject      = "AutoDL学生认证"
)

// SendSmsReply
type SendSmsReply struct {
	Code     string `json:"Code,omitempty"`
	Message  string `json:"StdMessage,omitempty"`
	EMessage string `json:"Message,omitempty"`
	BizId    string `json:"BizId"`
}

func BuildSMSTemplateParamCode(code string) string {
	return fmt.Sprintf(`{"code":"%s"}`, code)
}

func BuildSMSTemplateParamInstanceReleaseWarning(instance string, time string) string {
	return fmt.Sprintf(`{"instance_id":"%s","time":"%s"}`, instance, time)
}

func BuildSMSTemplateParamInstanceExpireWarning(instance string, time string) string {
	return fmt.Sprintf(`{"instance_id":"%s","time":"%s"}`, instance, time)
}

func BuildSMSTemplateParamBalanceWarning(money string) string {
	return fmt.Sprintf(`{"money":"%s"}`, money)
}

func BuildSMSInstanceRunningNotify(instanceUuid string) string {
	return fmt.Sprintf(`{"instance_id":"%s"}`, instanceUuid)
}

func BuildSMSTemplateParamMachineStoppedErr(instanceUuid string, time int) string {
	// 您的实例：{instance_uuid} 所在主机出现异常导致实例宕机，我们正在快速修复，预计修复时间为{n_hour}小时，烦请耐心等待。
	return fmt.Sprintf(`{"instance_id":"%s","time":"%d"}`, instanceUuid, time)
}

func BuildSMSTemplateParamRegionRelease(regionName string) string {
	// 您好，${region_name}即将下线，该地区文件存储数据将于72小时后进行清理，如需迁移请在此时间前联系微信客服，给您带来的不便敬请谅解！
	return fmt.Sprintf(`{"region_name":"%s"}`, regionName)
}

func BuildSMSTemplateScheduleInstanceSuccess(comment string) string {
	return fmt.Sprintf(`{"comment":"%s"}`, comment)
}

type NumberDetail struct {
	Country string `json:"Country"`
	Region  string `json:"Region"`
	Carrier string `json:"Carrier"`
}

type OverseasSendSmsReply struct {
	NumberDetail NumberDetail `json:"NumberDetail"`
	RequestId    string       `json:"RequestId"`
	Segments     string       `json:"Segments"`
	From         string       `json:"From"`
	To           string       `json:"To"`
	Code         string       `json:"Code"`
	MessageId    string       `json:"MessageId"`
}

func SendSMS(phoneArea string, phoneNumbers, templateParam, SMSType, smsCode string) (string, error) {
	areaCode := PhoneAreaCodeCorrect(phoneArea)
	log.WithFields(map[string]interface{}{
		"phoneArea":     phoneArea,
		"phoneNumbers":  phoneNumbers,
		"templateParam": templateParam,
		"SMSType":       SMSType,
	}).Info("SendSMS")

	// 美国、加拿大
	if areaCode == "+1" {
		var msg string
		switch SMSType {
		case constant.ResetPassword:
			msg = ResetPasswordMsg(templateParam)
		case constant.Login:
			msg = LoginMsg(templateParam)
		case constant.Register:
			msg = RegisterMsg(templateParam)
		default:
			msg = GenericMsg(templateParam)
		}

		accessKeyID := "LTAIBCFQlWz0EuyK"
		accessSecret := "L2Z1Gu0fTdVsFnmH01Q0Y2AJa3QWOb"

		config := sdk.NewConfig()

		credential := credentials.NewAccessKeyCredential(accessKeyID, accessSecret)
		client, err := sdk.NewClientWithOptions("cn-hangzhou", config, credential)
		if err != nil {
			return "", err
		}

		request := requests.NewCommonRequest()
		request.Method = "POST"
		request.Scheme = "https"
		request.Domain = "dysmsapi.aliyuncs.com"
		request.Version = "2017-05-25"
		request.ApiName = "SendMessageToGlobe"
		request.QueryParams["To"] = phoneNumbers
		request.QueryParams["From"] = "18667991213"
		request.QueryParams["Message"] = msg
		request.QueryParams["Type"] = "OTP"
		response, err := client.ProcessCommonRequest(request)
		if err != nil {
			return "", err
		}
		responseContent := response.GetHttpContentString()

		overseasSendSmsReply := &OverseasSendSmsReply{}
		err = json.Unmarshal([]byte(responseContent), &overseasSendSmsReply)
		if err != nil {
			return "", err
		}

		if overseasSendSmsReply.Code != "OK" {
			return "", errors.New(overseasSendSmsReply.Code)
		}

	} else {
		var templateCode string
		switch SMSType {
		case constant.Register:
			templateCode = "SMS_482880469"
		case constant.ResetPassword:
			templateCode = "SMS_483325026"
		case constant.ChangePhone:
			templateCode = "SMS_483280025" // 通用模板
		case constant.Abroad:
			templateCode = "SMS_463225921"
		case constant.InstanceReleaseWarning:
			templateCode = "SMS_483305035"
		case constant.InstanceExpireWarning:
			templateCode = "SMS_482895408"
		case constant.BalanceWarning, constant.BalanceNegative:
			templateCode = "SMS_483345027"
		case constant.BankRemitAccept:
			templateCode = "SMS_483215134"
		case constant.InstanceCreateRunning:
			templateCode = "SMS_482955492"
		case constant.MachineStoppedErr:
			templateCode = "SMS_483170125"
		case constant.SubUserUpdatePassword:
			templateCode = "SMS_483335067"
		case constant.SubUserBind:
			templateCode = "SMS_483285046"
		case constant.RegionRelease:
			templateCode = "SMS_482820471"
		case constant.InstanceScheduleSuccess:
			templateCode = "SMS_483205158"
		default:
			templateCode = "SMS_483220098"
		}

		if smsCode != "" {
			templateCode = smsCode
		}

		paras := map[string]string{
			"SignatureMethod":  "HMAC-SHA1",
			"SignatureNonce":   uuid.NewV4().String(),
			"AccessKeyId":      accessKeyID,
			"SignatureVersion": "1.0",
			"Timestamp":        time.Now().UTC().Format("2006-01-02T15:04:05Z"),
			"Format":           "JSON",

			"Action":        "SendSms",
			"Version":       "2017-05-25",
			"RegionId":      "cn-hangzhou",
			"PhoneNumbers":  phoneNumbers,
			"SignName":      signName,
			"TemplateParam": templateParam,
			"TemplateCode":  templateCode,
		}

		var keys []string

		for k := range paras {
			keys = append(keys, k)
		}

		sort.Strings(keys)

		var sortQueryString string

		for _, v := range keys {
			sortQueryString = fmt.Sprintf("%s&%s=%s", sortQueryString, replace(v), replace(paras[v]))
		}

		stringToSign := fmt.Sprintf("GET&%s&%s", replace("/"), replace(sortQueryString[1:]))

		mac := hmac.New(sha1.New, []byte(fmt.Sprintf("%s&", accessSecret)))
		_, err := mac.Write([]byte(stringToSign))
		if err != nil {
			return "", err
		}
		sign := replace(base64.StdEncoding.EncodeToString(mac.Sum(nil)))

		str := fmt.Sprintf("http://dysmsapi.aliyuncs.com/?Signature=%s%s", sign, sortQueryString)

		resp, err := http.Get(str)
		if err != nil {
			return "", err
		}
		defer resp.Body.Close()
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return "", err
		}

		ssr := &SendSmsReply{}

		if err := json.Unmarshal(body, ssr); err != nil {
			return "", err
		}

		if ssr.Code != "OK" {
			errStr := ssr.Code + ":" + ssr.EMessage
			return "", biz.ErrEmptyBox.New().Format(errStr)
		} else {
			return ssr.BizId, nil
		}
	}

	return "", nil
}

type DetectSensitiveResp struct {
	Result      string       `json:"result"`                // 审核结果类型，1:合规，2:不合规，3:疑似，4:审核失败
	ResultMsg   string       `json:"resultMsg"`             // 结果描述
	ResultItems []ResultItem `json:"resultItems,omitempty"` // 不合规项列表
}

type ResultItem struct {
	NonComplianceType int    `json:"nonComplianceType"` // 不合规类型
	Msg               string `json:"msg"`               // 不合规描述
	Hits              []Hit  `json:"hits"`              // 命中的敏感词
}

type Hit struct {
	Probability       float64           `json:"probability"`       // 置信度，范围0至1
	Words             []string          `json:"words"`             // 送检文本命中词库的关键词
	WordHitPositions  []WordHitPosition `json:"wordHitPositions"`  // 送检文本命中词库的位置信息
	ModelHitPositions [][]float64       `json:"modelHitPositions"` // 送检文本命中模型的位置信息及置信度
}

type WordHitPosition struct {
	SubLabel     string  `json:"subLabel"`
	SubLabelDesc string  `json:"subLabelDesc"`
	Positions    [][]int `json:"positions"`
	LabelDesc    string  `json:"labelDesc"`
	Label        string  `json:"label"`
	Keyword      string  `json:"keyword"`
}

// DetectSensitiveStr 敏感词检测
func DetectSensitiveStr(str string) error {
	headers := map[string]string{
		"Authorization": "APPCODE " + conf.GetGlobalGsConfig().DetectSensitiveText.AppCode,
		"Content-Type":  "application/x-www-form-urlencoded; charset=UTF-8",
	}
	data := map[string]string{
		"text": str,
	}

	postResp, err := grequests.Post(constant.DetectSensitiveUrl, &grequests.RequestOptions{
		Headers: headers,
		Data:    data,
	})
	if err != nil {
		log.WithField("text", str).Error(err, "post detect sensitive failed")
		return err
	}

	var res struct {
		Code int `json:"code"`
		Data struct {
			ResultMsg   string       `json:"resultMsg"`
			Result      string       `json:"result"`
			ResultItems []ResultItem `json:"resultItems"`
		} `json:"data"`
		Msg    string `json:"msg"`
		TaskNo string `json:"taskNo"`
	}

	err = postResp.JSON(&res)
	if err != nil {
		log.WithField("resp", postResp.RawResponse).Error(err, "unmarshal resp failed")
		return err
	}

	if res.Code != 200 {
		return errors.Errorf("grequests failed, status code: [%d], body: [%s]", res.Code, postResp.String())
	}

	if res.Data.Result != "1" { //1：合规，2：不合规，3：疑似，4：审核失败
		var builder strings.Builder
		for _, errItem := range res.Data.ResultItems {
			builder.WriteString(errItem.Msg)
			builder.WriteString(";")
		}

		// 去掉末尾多余的分号
		errStr := strings.TrimRight(builder.String(), ";")
		err = biz.ErrEmptyBox.New().Format(errStr)
		return err
	}

	return nil
}

// 特殊字符（emoji）检测
func CheckForEmojis(s string) string {
	var unicodeCodes []string

	result := emoji.ReplaceAllEmojiFunc(s, func(emojiStr string) string {
		for _, item := range []int32(emojiStr) {
			hex := strconv.FormatInt(int64(item), 16)
			unicodeCodes = append(unicodeCodes, fmt.Sprintf("U+%s", hex))
		}
		return ""
	})
	return result
}

func InstanceNameDetectAndProcess(instanceUUID, name string) (instanceInfo string, isRetry bool, err error) {
	instanceInfo = instanceUUID
	if name != "" {
		cleanedEmojisStr := CheckForEmojis(name)
		if strings.TrimSpace(cleanedEmojisStr) == "" {
			log.WithField("cleanedEmojisStr", cleanedEmojisStr).Warn("after emoji check,this str is a space")
		} else {
			err = DetectSensitiveStr(cleanedEmojisStr)
			if err != nil {
				log.WithField("text", instanceInfo).WithError(err).Error("detect sensitive words failed")
				return instanceInfo, isRetry, err
			} else {
				instanceInfo = instanceUUID + "(" + cleanedEmojisStr + ")"
				isRetry = true
			}
		}
	}
	return instanceInfo, isRetry, nil
}

func replace(in string) string {
	rep := strings.NewReplacer("+", "%20", "*", "%2A", "%7E", "~")
	return rep.Replace(url.QueryEscape(in))
}

func UrlEncode(in string) string {
	r := strings.NewReplacer("+", "%20", "*", "%2A", "%7E", "~")
	return r.Replace(url.QueryEscape(in))
}

func Sign(stringToSign string) string {
	h := hmac.New(sha1.New, []byte(fmt.Sprintf("%s&", accessSecret)))
	h.Write([]byte(stringToSign))
	return UrlEncode(base64.StdEncoding.EncodeToString(h.Sum(nil)))
}

type EmailResp struct {
	EnvId     string `json:"EnvId"`
	RequestId string `json:"RequestId"` // "8906582E-6722-409A-A6C4-0E7863B733A5",
	HostId    string `json:"HostId"`    // "dm.aliyuncs.com",
	Code      string `json:"Code"`      // "InvalidTemplate.NotFound",
	Message   string `json:"Message"`   // "The specified template does not found."
}

var emailHtml1 = `<html>
 <head></head>
 <body>   
  <table width="100%" cellspacing="0" cellpadding="0" border="0"> 
   <tbody> 
    <tr> 
     <td align="left"> <img src="http://autodl-public.ks3-cn-beijing.ksyun.com/docs/logo-b.png" style="display: block; width: 128px; height: 32px; padding-bottom:5px;" width="128" height="32" /> </td> 
    </tr> 
   </tbody> 
  </table>      
  <table style="min-width: 332px; max-width: 600px; border: 1px solid #409EFF; border-bottom: 0; border-top-left-radius: 3px; border-top-right-radius: 3px;" width="100%" cellspacing="0" cellpadding="0" border="0" bgcolor="#409EFF"> 
   <tbody> 
    <tr> 
     <td colspan="3" height="72px"></td> 
    </tr> 
    <tr> 
     <td width="32px"></td> 
     <td style="font-family: Roboto-Regular,Helvetica,Arial,sans-serif; font-size: 24px; color: #FFFFFF; line-height: 1.25;"> AutoDL 验证码 </td> 
     <td width="32px"></td> 
    </tr> 
    <tr> 
     <td colspan="3" height="18px"></td> 
    </tr> 
   </tbody> 
  </table>     
  <table style="min-width: 332px; max-width: 600px; border: 1px solid #F0F0F0; border-bottom: 1px solid #C0C0C0; border-top: 0; border-bottom-left-radius: 3px; border-bottom-right-radius: 3px;" width="100%" cellspacing="0" cellpadding="0" border="0" bgcolor="#FAFAFA"> 
   <tbody> 
    <tr height="16px"> 
     <td rowspan="3" width="32px"></td> 
     <td></td> 
     <td rowspan="3" width="32px"></td> 
    </tr> 
    <tr> 
     <td><p>尊敬的 AutoDL 炼丹师：</p> <p>您正在进行学生认证，您的验证码为：</p> 
      <div style="text-align: center;"> 
       <p dir="ltr"> <strong style="text-align: center; font-size: 24px; font-weight: bold;">`

var emailHtml2 = `</strong> </p> 
      </div> <p>如果您并未请求此验证码，则可能是他人正在使用您的邮箱进行认证。<strong>请勿将此验证码转发给或提供给任何人。</strong> </p> <p>此致</p> <p>AutoDL 团队敬上</p></td> 
    </tr> 
    <tr height="32px"></tr> 
   </tbody> 
  </table>      
  <table> 
   <tbody> 
    <tr> 
     <td>此电子邮件地址无法接收回复。如需更多信息，请微信联系客服: <br /> 
      <table style="font-family: Roboto-Regular,Helvetica,Arial,sans-serif; font-size: 10px; color: #666666; line-height: 18px; padding-bottom: 10px"></table> </td> 
    </tr> 
    <tr> 
     <td align="left"> <img src="http://autodl-public.ks3-cn-beijing.ksyun.com/docs/cs_qrcode_167.png" style="display: block; width: 92px; height: 92px;" width="92" height="32" /> </td> 
    </tr> 
   </tbody> 
  </table>   
 </body>
</html>`

// SendEmail send email to specified email address
func SendEmail(code, email string) (int, error) {

	html := emailHtml1 + code + emailHtml2
	rand.Seed(time.Now().UnixNano())

	data := map[string]string{
		"SignatureMethod":  "HMAC-SHA1",
		"SignatureNonce":   uuid.NewV4().String(),
		"AccessKeyId":      accessKeyID,
		"SignatureVersion": "1.0",
		"Timestamp":        time.Now().UTC().Format("2006-01-02T15:04:05Z"),
		"Format":           "JSON",

		"Action":         "SingleSendMail",
		"Version":        "2015-11-23",
		"AccountName":    accountName,
		"ReplyToAddress": "true",
		"AddressType":    "1",
		"ToAddress":      email,
		"Subject":        subject,
		"HtmlBody":       html,
	}

	sortQueryString := SortedString(data)
	stringToSign := fmt.Sprintf("GET&%s&%s", UrlEncode("/"), UrlEncode(sortQueryString[1:]))
	url := fmt.Sprintf("http://dm.aliyuncs.com/?Signature=%s%s", Sign(stringToSign), sortQueryString)

	r, err := http.Get(url)
	if err != nil {
		return 0, err
	}
	defer r.Body.Close()

	time.Sleep(time.Second * 2)
	invalidList, err := QueryInvalidEmailAddress()
	if err != nil {
		return 0, nil
	}

	for _, v := range invalidList.Data.MailDetail {
		if v.ToAddress == email {
			return 404, nil
		}
	}

	return 0, nil
}

type QueryInvalidEmailAddressResp struct {
	TotalCount int `json:"TotalCount"`
	Data       struct {
		MailDetail []struct {
			ToAddress         string `json:"ToAddress"`
			LastUpdateTime    string `json:"LastUpdateTime"`
			UtcLastUpdateTime int64  `json:"UtcLastUpdateTime"`
		} `json:"mailDetail"`
	} `json:"data"`
	NextStart string `json:"NextStart"`
}

func QueryInvalidEmailAddress() (*QueryInvalidEmailAddressResp, error) {
	rand.Seed(time.Now().UnixNano())

	data := map[string]string{
		"SignatureMethod":  "HMAC-SHA1",
		"SignatureNonce":   uuid.NewV4().String(),
		"AccessKeyId":      accessKeyID,
		"SignatureVersion": "1.0",
		"Timestamp":        time.Now().UTC().Format("2006-01-02T15:04:05Z"),
		"Format":           "JSON",

		"Action":         "QueryInvalidAddress",
		"Version":        "2015-11-23",
		"AccountName":    accountName,
		"ReplyToAddress": "true",
		"Subject":        subject,
	}

	sortQueryString := SortedString(data)
	stringToSign := fmt.Sprintf("GET&%s&%s", UrlEncode("/"), UrlEncode(sortQueryString[1:]))
	url := fmt.Sprintf("http://dm.aliyuncs.com/?Signature=%s%s", Sign(stringToSign), sortQueryString)

	r, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer r.Body.Close()

	resp := &QueryInvalidEmailAddressResp{}
	body, _ := io.ReadAll(r.Body)
	_ = json.Unmarshal(body, &resp)

	return resp, nil
}

func Keys(data map[string]string) []string {
	var keys []string
	for k := range data {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	return keys
}

func SortedString(data map[string]string) string {
	var sortQueryString string
	for _, v := range Keys(data) {
		sortQueryString = fmt.Sprintf("%s&%s=%s", sortQueryString, v, replace(data[v]))
	}
	return sortQueryString
}

// ValidateURL 验证微信服务器url是否正确
func ValidateURL(token, timestamp, nonce, signature string) bool {
	tmpArr := []string{token, timestamp, nonce}
	sort.Strings(tmpArr)
	tmpStr := strings.Join(tmpArr, "")
	actual := fmt.Sprintf("%x", sha1.Sum([]byte(tmpStr)))
	return actual == signature
}

// Signature 对加密的报文计算签名
func Signature(token, timestamp, nonce, encrypt string) string {
	tmpArr := []string{token, timestamp, nonce, encrypt}
	sort.Strings(tmpArr)

	tmpStr := strings.Join(tmpArr, "")
	actual := fmt.Sprintf("%x", sha1.Sum([]byte(tmpStr)))
	return actual
}

// CheckSignature 验证加密的报文的签名
func CheckSignature(token, timestamp, nonce, encrypt, sign string) bool {
	return Signature(token, timestamp, nonce, encrypt) == sign
}

// TestRegexpPassword 测试密码是否符合规范(大小写加数字,8-16位,且三种至少各一位)
func TestRegexpPassword(password string) (ok bool) {
	var upper bool
	var lower bool
	var number bool
	var spec bool

	defer func() {
		if upper && lower && number && !spec {
			ok = true
		} else {
			ok = false
		}
	}()

	if len(password) < 8 || len(password) > 16 {
		return false
	}

	for i := range password {
		switch {
		case 64 < password[i] && password[i] < 91:
			upper = true
		case 96 < password[i] && password[i] < 123:
			lower = true
		case 47 < password[i] && password[i] < 58:
			number = true
		default:
			spec = true
			break
		}

		if upper && lower && number {
			break
		}
	}
	return ok
}

// TestRegexpPhone 测试手机号是否符合规范
func TestRegexpPhone(phone string) (ok bool) {
	ok = true
	regexpPhone, err := regexp.Compile("^[1][3456789][0-9]{9}$")
	if err != nil {
		err = biz.ErrInvalidRequestParams.New().Append(biz.ErrRegexpFormatFailed)
		log.WithField("regexpPhone", regexpPhone).WithError(err).Info("Phone regexp format failed.")
		ok = false
		return
	}
	if !regexpPhone.MatchString(phone) {
		err = biz.ErrInternalError.New().Append(biz.ErrPhoneWrongFormat)
		log.WithField("phone", phone).WithError(err).Info("Input phone format failed.")
		ok = false
		return
	}
	return
}

// StringCombination 字符串组合
func StringCombination(phone, key string) (str string) {
	var build strings.Builder
	build.WriteString(phone)
	build.WriteString(key)
	str = build.String()
	return
}

func UseTicketGetQrcodeImg(ticket string) string {
	return fmt.Sprintf(constant.AccountGetQRCodeImgAddrURL, url.QueryEscape(ticket))
}

// AESCBCEncrypt 采用 CBC 模式的 AES 加密
func AESCBCEncrypt(src, key, iv []byte) (enc []byte, err error) {
	log.Tracef("src: %s", src)
	src = PKCS7Padding(src, len(key))

	block, err := aes.NewCipher(key)
	if err != nil {
		log.WithField("key", key).Info("New cipher key failed")
		return nil, err
	}

	mode := cipher.NewCBCEncrypter(block, iv)

	mode.CryptBlocks(src, src)
	enc = src

	return enc, nil
}

// AESCBCDecrypt 采用 CBC 模式的 AES 解密
func AESCBCDecrypt(enc, key, iv []byte) (src []byte, err error) {
	log.Tracef("enc: % x", enc)
	if len(enc) < len(key) {
		return nil, fmt.Errorf("the length of encrypted message too short: %d", len(enc))
	}
	if len(enc)&(len(key)-1) != 0 { // or len(enc)%len(key) != 0
		return nil, fmt.Errorf("encrypted message is not a multiple of the key size(%d), the length is %d", len(key), len(enc))
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	mode := cipher.NewCBCDecrypter(block, iv)

	mode.CryptBlocks(enc, enc)
	src = PKCS7UnPadding(enc)

	log.Tracef("src: %s", src)
	return src, nil
}

// PKCS7Padding PKCS#7填充，Buf需要被填充为K的整数倍，
// 在buf的尾部填充(K-N%K)个字节，每个字节的内容是(K- N%K)
func PKCS7Padding(src []byte, k int) (padded []byte) {
	padLen := k - len(src)%k
	padding := bytes.Repeat([]byte{byte(padLen)}, padLen)
	return append(src, padding...)
}

// PKCS7UnPadding 去掉PKCS#7填充，Buf需要被填充为K的整数倍，
// 在buf的尾部填充(K-N%K)个字节，每个字节的内容是(K- N%K)
func PKCS7UnPadding(src []byte) (padded []byte) {
	padLen := int(src[len(src)-1])
	return src[:len(src)-padLen]
}

// EncryptMsg 加密报文
func EncryptMsg(msg []byte, aesKey []byte, appId string) (b64Enc string, err error) {
	// 拼接完整报文
	src := SpliceFullMsg(msg, appId)

	// AES CBC 加密报文
	dst, err := AESCBCEncrypt(src, aesKey, aesKey[:aes.BlockSize])
	if err != nil {
		log.Info("aes cbc encrypt failed.")
		return "", err
	}

	return base64.StdEncoding.EncodeToString(dst), nil
}

// DecryptMsg 解密报文
func DecryptMsg(b64Enc string, aesKey []byte, appId string) (msg []byte, err error) {
	// log.Tracef("b64Enc: %s", b64Enc)
	enc, err := base64.StdEncoding.DecodeString(b64Enc)
	if err != nil {
		return nil, err
	}

	// AES CBC 解密报文
	src, err := AESCBCDecrypt(enc, aesKey, aesKey[:aes.BlockSize])
	if err != nil {
		log.WithField("aeskey", aesKey).Info("aes cbc decrypt failed.")
		return nil, err
	}

	_, _, msg, appId2 := ParseFullMsg(src)
	if appId2 != appId {
		return nil, fmt.Errorf("expected appId %s, but %s", appId, appId2)
	}

	return msg, nil
}

// SpliceFullMsg 拼接完整报文，
// AES加密的buf由16个字节的随机字符串、4个字节的msg_len(网络字节序)、msg和$AppId组成，
// 其中msg_len为msg的长度，$AppId为公众帐号的AppId
func SpliceFullMsg(msg []byte, appId string) (fullMsg []byte) {
	// 16个字节的随机字符串
	randBytes := RandBytes(16)

	// 4个字节的msg_len(网络字节序)
	msgLen := len(msg)
	lenBytes := []byte{
		byte(msgLen >> 24 & 0xFF),
		byte(msgLen >> 16 & 0xFF),
		byte(msgLen >> 8 & 0xFF),
		byte(msgLen & 0xFF),
	}

	return bytes.Join([][]byte{randBytes, lenBytes, msg, []byte(appId)}, nil)
}

// ParseFullMsg 从完整报文中解析出消息内容，
// AES加密的buf由16个字节的随机字符串、4个字节的msg_len(网络字节序)、msg和$AppId组成，
// 其中msg_len为msg的长度，$AppId为公众帐号的AppId
func ParseFullMsg(fullMsg []byte) (randBytes []byte, msgLen int, msg []byte, appId string) {
	randBytes = fullMsg[:16]

	msgLen = (int(fullMsg[16]) << 24) |
		(int(fullMsg[17]) << 16) |
		(int(fullMsg[18]) << 8) |
		int(fullMsg[19])
	// log.Tracef("msgLen=[% x]=(%d %d %d %d)=%d", fullMsg[16:20], (int(fullMsg[16]) << 24),
	// 	(int(fullMsg[17]) << 16), (int(fullMsg[18]) << 8), int(fullMsg[19]), msgLen)

	msg = fullMsg[20 : 20+msgLen]

	appId = string(fullMsg[20+msgLen:])

	return
}

// RandBytes 产生 size 个长度的随机字节
func RandBytes(size int) (r []byte) {
	r = make([]byte, size)
	_, err := rand.Read(r)
	if err != nil {
		// 忽略错误，不影响其他逻辑，仅仅打印日志
		log.Warnf("rand read error: %s", err)
	}
	return r
}

func EncodingAESKey2AESKey(encodingKey string) []byte {
	data, _ := base64.StdEncoding.DecodeString(encodingKey + "=")
	return data
}

// 将前端传过来的时间进行解析
func AddTimeInfoToTheTransTime(deadline string) (res *time.Time, err error) {
	if deadline == "" {
		err = biz.ErrInvalidRequestParams
		return
	}
	nowHour, nowMinute, nowSeconds := time.Now().Clock()
	t, err := time.ParseInLocation(constant.FormatDateString, deadline, time.Local)
	if err != nil {
		log.Warn("Parse time in location failed.")
		err = biz.ErrInvalidRequestParams
		return
	}
	t = t.Add(time.Hour*time.Duration(nowHour) + time.Minute*time.Duration(nowMinute) + time.Second*time.Duration(nowSeconds))
	return &t, nil
}

func ExpiredTokenTime() (seconds time.Duration) {
	nowHour, nowMinute, nowSeconds := time.Now().Clock()
	seconds = time.Duration((23-nowHour)*60*60+(59-nowMinute)*60+60-nowSeconds) * time.Second
	return
}

func MemberShipDeadline() (tst time.Time) {
	tt := time.Now().Unix()
	ts := time.Unix(tt, 0)
	tst = ts.AddDate(0, 0, 30)
	return
}

func StudentMemberShipDeadline() (tst time.Time) {
	timeNow := time.Now()
	tst = time.Date(timeNow.Year()+1, timeNow.Month(), timeNow.Day(), timeNow.Hour(), timeNow.Minute(), timeNow.Second(), timeNow.Nanosecond(), time.Local)
	return
}

func TransRechargeMoneyToCorrespondingGrowth(asset int64) (growth int64) {
	tranAssets := asset / 1000

	switch {
	case tranAssets < 100:
		growth = tranAssets
	case tranAssets < 500:
		growth = tranAssets + 10
	case tranAssets < 1000:
		growth = tranAssets + 80
	case tranAssets < 2000:
		growth = tranAssets + 200
	default:
		growth = tranAssets + 500
	}
	return
}

func TransUserGrowthValueToCorrespondingLevel(acqMode constant.MemberGainWays, growth int64) (level constant.MemberLevelName) {
	if acqMode == constant.VIPUserAcquire {
		if growth >= 500 {
			return constant.MemberUser
		}
		return constant.NormalUser
	}
	return constant.MemberUser
}

type UserMemberLevelList struct {
	LevelInfo []LevelInfo `json:"correspond"`
}

type LevelInfo struct {
	Name  string `json:"name"`
	Range string `json:"range"`
}

func UserMemberLevelInfoList() (data UserMemberLevelList) {
	data.LevelInfo = append(data.LevelInfo, LevelInfo{
		Name:  "炼丹会员Ⅰ",
		Range: "100-1499",
	})
	data.LevelInfo = append(data.LevelInfo, LevelInfo{
		Name:  "炼丹会员Ⅱ",
		Range: "1500-9999",
	})
	data.LevelInfo = append(data.LevelInfo, LevelInfo{
		Name:  "炼丹会员Ⅲ",
		Range: "10000以上",
	})
	return
}

func GeneratePersonalToken() (token string) {
	s := uuid.NewV4().String()
	s1 := strings.Split(s, "-")
	return s1[4]
}

func GetHeader(contentType constant.HeaderContent) (res map[string]string) {
	var value constant.ContentTypeValue
	if contentType == constant.TokenHeaderContent {
		value = constant.TokenContentType
	} else {
		value = constant.JsonContentType
	}
	return map[string]string{
		constant.ContentType: String(value),
	}
}

func PhoneAreaCodeCorrect(areaCode string) (code string) {
	areaCode = strings.ReplaceAll(areaCode, " ", "")
	if areaCode == "" {
		return "+86"
	}
	if !strings.HasPrefix(areaCode, "+") {
		areaCode = "+" + areaCode
	}
	return areaCode
}

func PhoneBuildWithArea(areaCode, phone string) string {
	areaCode = PhoneAreaCodeCorrect(areaCode)
	return areaCode + PhoneCorrect(areaCode, phone)
}

// PhoneBuildWithOverseasArea area不带+
func PhoneBuildWithOverseasArea(areaCode, phone string) string {
	areaCode = PhoneAreaCodeCorrect(areaCode)
	trimPrefixAreaCode := strings.TrimPrefix(areaCode, "+")
	return trimPrefixAreaCode + phone
}

func PhoneCorrect(areaCode, phone string) string {
	if strings.HasPrefix(phone, areaCode) {
		return strings.TrimPrefix(phone, areaCode)
	}
	return phone
}

func RealNameAuth(name, idCard string) (resp *grequests.Response, err error) {
	// https://market.aliyun.com/products/57000002/cmapi026109.html?spm=5176.730005.result.2.27163524ccFeH6&innerSource=search_%E8%BA%AB%E4%BB%BD%E8%AF%81%E5%AE%9E%E5%90%8D#sku=yuncode2010900007

	url := fmt.Sprintf("https://eid.shumaidata.com/eid/check?idcard=%s&name=%s", idCard, name)
	resp, err = grequests.Post(url, &grequests.RequestOptions{
		Headers: map[string]string{
			"Authorization": "APPCODE 0675993862c245eeaef326388b4097c2",
		}},
	)
	return
}

func UserAgreementExist(agreementID string) bool {
	switch agreementID {
	case "agreements", "nas_agreements", "service_agreement":
		return true
	}
	return false
}

func GenericMsg(code string) string {
	return fmt.Sprintf("您的验证码为：%s，该验证码5分钟内有效，请勿泄露于他人。", code)
}

func ResetPasswordMsg(code string) string {
	return fmt.Sprintf("验证码：%s，您正在重置AutoDL账号密码，有效期为10分钟，请勿向他人泄露。", code)
}

func LoginMsg(code string) string {
	return fmt.Sprintf("验证码：%s，您正在登录AutoDL账号，有效期为10分钟，请勿向他人泄露。", code)
}

func RegisterMsg(code string) string {
	return fmt.Sprintf("验证码：%s，您正在注册AutoDL账号，有效期为10分钟，请勿向他人泄露。", code)
}
