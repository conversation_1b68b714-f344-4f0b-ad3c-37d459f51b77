package libs

import (
	"context"
	"net"
	"strings"
	"time"
)

var emailPool = []string{
	".edu",    // 美国
	".ac.cn",  // 中科院
	".ac.uk",  // 英国
	".ac.tw",  // 台湾
	".ac.jp",  // 日本
	".edu.hk", // 香港
	"student", // 包含 student
	"stu",     // 包含stu

	// 其他无明显规律的高校
	"hku.hk",              // 香港大学
	"ust.hk",              // 香港科技大学
	"connect.polyu.hk",    // 香港理工
	"student.gsu.edu",     // 佐治亚州立
	"student.must.edu.mo", // 澳门科技大学
	"UBishops.ca",         // 加拿大 Bishop's University
	"my.cityu.edu.hk",     // 香港城市大学
	"tum.de",              // 慕尼黑大学
	"ruri.waseda.jp",      // 早稻田大学
	"waseda.jp",           // 早稻田大学
	"bristol.ac.uk",       // 布里斯托大学
	"dtu.dk",              // 丹麦技术大学
	"stu.xujc.com",        // 厦门大学嘉庚学院
	"cityu.mo",            // 澳门城市大学
	"bupt.cn",             // 北京邮电大学
	"caas.cn",             // 中国农业科学院
	"gdpu.com.cn",         // 广东药科大学
}

// 当前有一些特定的邮箱域名，无法通过 LookupMX 验证
// （测试环境没问题，本地测试用例也没问题，线上不行）故收集排除
var excludeEmailDomainMx = map[string]struct{}{
	"stu.kust.edu.cn":    {},
	"vip.henu.edu.cn":    {},
	"stu.sju.edu.cn":     {},
	"stu.xijing.edu.cn":  {},
	"mails.swust.edu.cn": {},
	"stumail.taru.edu":   {},
	"stu.ouc.edu.cn":     {},
	"bistu.edu.cn":       {},
	"stu.cwnu.edu.cn":    {},

	// 对于emailPool中部分已知的大学邮箱域名, 填在这里快速通过
	"hku.hk":              {}, // 香港大学
	"ust.hk":              {}, // 香港科技大学
	"connect.polyu.hk":    {}, // 香港理工
	"student.gsu.edu":     {}, // 佐治亚州立
	"student.must.edu.mo": {}, // 澳门科技大学
	"UBishops.ca":         {}, // 加拿大 Bishop's University
	"my.cityu.edu.hk":     {}, // 香港城市大学
	"tum.de":              {}, // 慕尼黑大学
	"ruri.waseda.jp":      {}, // 早稻田大学
	"waseda.jp":           {}, // 早稻田大学
	"bristol.ac.uk":       {}, // 布里斯托大学
	"dtu.dk":              {}, // 丹麦技术大学
	"stu.xujc.com":        {}, // 厦门大学嘉庚学院
	"cityu.mo":            {}, // 澳门城市大学
	"bupt.cn":             {}, // 北京邮电大学
	"caas.cn":             {}, // 中国农业科学院
	"gdpu.com.cn":         {}, // 广东药科大学
}

// TestRegexpEmail 测试邮箱是否符合规范
func TestRegexpEmail(email string) (ok bool) {
	if !strings.Contains(email, "@") {
		return false
	}
	s := strings.Split(email, "@")
	domain := s[len(s)-1]

	// 快速通过
	if _, exist := excludeEmailDomainMx[domain]; exist {
		return true
	}

	// 规则匹配
	for _, value := range emailPool {
		ok = strings.Contains(domain, value)
		if ok {
			break
		}
	}
	if !ok {
		return false
	}

	//
	for _, resolver := range getDnsResolverList() {
		mxRecords, err := resolver.LookupMX(context.Background(), domain)
		if err != nil {
			return false
		}
		if len(mxRecords) > 0 {
			return true
		}
	}

	return false
}

var dnsResolverList []*net.Resolver

func init() {
	initDnsResolverList()
}

func initDnsResolverList() {
	dnsResolverList = []*net.Resolver{
		net.DefaultResolver,
	}

	dnsList := []string{"***************:53", "*******:53"}

	for _, dns := range dnsList {
		dnsResolverList = append(dnsResolverList, &net.Resolver{
			PreferGo: true, // 必须启用 Go 实现的解析器
			Dial: func(ctx context.Context, network, address string) (net.Conn, error) {
				dialer := &net.Dialer{
					Timeout: 5 * time.Second, // 设置超时
				}
				// 强制使用指定 DNS 服务器
				return dialer.DialContext(ctx, "udp", dns)
			},
		})
	}
}

func getDnsResolverList() []*net.Resolver {
	if dnsResolverList == nil || len(dnsResolverList) == 0 {
		initDnsResolverList()
	}
	return dnsResolverList
}
