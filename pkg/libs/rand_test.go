package libs_test

import (
	"crypto/sha1"
	"encoding/hex"
	"fmt"
	"mime"
	"net"
	"net/smtp"
	"path/filepath"
	"regexp"
	"server/pkg/constant"
	"server/pkg/libs"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/jordan-wright/email"
	log "github.com/sirupsen/logrus"
)

/**
 * 此包用于临时测试.
 */

// PortConfirm 返回此次获取的端口中不能用作分配的端口(也即本机上该端口已经被占用)
func portConfirm(ip string, ports ...int) (disablePorts []int) {
	for _, port := range ports {
		address := net.JoinHostPort(ip, strconv.Itoa(port))
		_, err := net.DialTimeout("tcp", address, time.Second)
		if err != nil {
			disablePorts = append(disablePorts, port)
		}
	}
	fmt.Println(disablePorts)
	return
}

func StringCombination(phone, key string) (str string) {
	var build strings.Builder
	build.WriteString(phone)
	build.WriteString(key)
	str = build.String()
	fmt.Println(str)
	return
}

func TestSomething(t *testing.T) {
	for i := 0; i < 10; i++ {
		fmt.Println(libs.RandNum(10))
	}
}

func TestSendVCode(t *testing.T) {
	str := "15555555555"
	str2 := "+8615555555555"
	r, err := regexp.Compile("^((\\+86)|(86))?[1][3456789][0-9]{9}$")
	if err != nil {
		return
	}
	if !r.MatchString(str) {
		log.Error("normal wrong")
		fmt.Println(r, err)
	}
	if !r.MatchString(str2) {
		log.Error("+86 wrong")
		fmt.Println(r, err)
	}
}

func TestEmail(t *testing.T) {
	email1 := "9999999qq.edu.com"
	email2 := "9999@<EMAIL>"
	email3 := "<EMAIL>"
	email4 := "2035844964st.usst.edu.cn.cc"
	email5 := "<EMAIL>"
	email6 := "<EMAIL>"
	email7 := "<EMAIL>"
	email8 := "<EMAIL>"
	var emails []string
	emails = append(emails, email1, email2, email3, email4, email5, email6, email7, email8)
	for i := range emails {
		fmt.Println(libs.TestRegexpEmail(emails[i]))
	}
}

// 定时结算, 每整十分钟一次, 如 10:10, 10:20, 10:30

func TestTicker(ttt *testing.T) {
	_ = getNextTenSecondsTimerForTest() // 运行并对照手表观察时间
}

// 测试用的, 隔十秒钟
func getNextTenSecondsTimerForTest() *time.Timer {
	now := time.Now()
	nextPoint := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), (now.Second()/10+1)*10, 0, now.Location())
	timer := time.NewTimer(nextPoint.Sub(now))

	fmt.Println(nextPoint.Local())

	return timer
}

// 隔十分钟的使用此函数
func getNextTenMinutesTimer() *time.Timer {
	now := time.Now()
	nextPoint := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), (now.Minute()/10+1)*10, 0, 0, now.Location())
	timer := time.NewTimer(nextPoint.Sub(now))

	return timer
}

func TestSendEmail(t *testing.T) {
	go sendEmail("测试第三方email库", "<EMAIL>")
}

func sendEmail(subject string, tos ...string) error {
	e := email.NewEmail()
	// 发件人邮箱
	smtpUserName := "<EMAIL>"
	e.From = mime.QEncoding.Encode("UTF-8", "Go 测试邮件") + "<<EMAIL>>"
	e.To = tos
	e.Subject = subject
	e.HTML = []byte("<h1> HTML 正文 </ĥ1>")

	auth := smtp.PlainAuth("", smtpUserName, "授权码", "smtp.qq.com")
	err := e.Send("smtp.qq.com:25", auth)
	if err != nil {
		log.Println("Send mail to", strings.Join(tos, ","), "error:", err)
		return err
	}
	log.Println("Send mail to", strings.Join(tos, ","), "Successfully")
	return nil
}

func TestTrimContainerID(t *testing.T) {
	containerID := "/instance_for_debug_gkzfe"
	println(filepath.Base(containerID))
	println(strings.TrimPrefix(containerID, "/"))
}

func TestRandStr(t *testing.T) {
	println(-1, "---", libs.GenRandomStrByUUID(-1))
	println(0, "---", libs.GenRandomStrByUUID(0))
	println(10, "---", libs.GenRandomStrByUUID(10))
	println(20, "---", libs.GenRandomStrByUUID(20))
	println(32, "---", libs.GenRandomStrByUUID(32))
	println(44, "---", libs.GenRandomStrByUUID(44))
	println(64, "---", libs.GenRandomStrByUUID(64))
	println(65, "---", libs.GenRandomStrByUUID(65))

	println("Jupyter token: ", libs.JupyterToken("container-6ff511900c-f63d5706"))
}

func TestRandomStringNumbers(t *testing.T) {
	fmt.Println(GetUserName("16666666666"))
	fmt.Println(GetUserName("15555555555"))
}

func GetUserName(phone string) string {
	r := fmt.Sprintf("%s%s", "用户", phone)
	return r
}

func TestSha1(t *testing.T) {
	s := "Admin123"
	h := sha1.New()
	h.Write([]byte(s))
	x := hex.EncodeToString(h.Sum(nil))
	fmt.Println(x)
	fmt.Println(hex.DecodeString(x))
	str := string(x)
	fmt.Println(str)
}

func TestTime(t *testing.T) {
	timeParse := "2021-09-24"
	ti, err := libs.AddTimeInfoToTheTransTime(timeParse)
	if err != nil {
		return
	}
	fmt.Println(ti)
	fmt.Println(time.Now())
	tm1 := time.Unix(1629342844, 0)
	fmt.Println(tm1)
	fmt.Println(tm1.Format("2006-01-02 15:04:05"))
	tm2 := time.Unix(1629342975, 0)
	fmt.Println(tm2.Format("2006-01-02 15:04:05"))
	timeNow := time.Now().Unix()
	ts := time.Unix(timeNow, 0)
	tst := ts.AddDate(0, 1, 0)
	tst2 := ts.AddDate(0, -1, 0)
	fmt.Println(ts)
	fmt.Println(tst)
	fmt.Println(tst2)
	fmt.Println(time.Now().Local())
	fmt.Println(time.Now().Format(constant.FormatDateString))
}

func TestRandNumCapitalization(t *testing.T) {
	println(libs.RandNumCapitalization(10))
}

func TestWxCallBackResult(t *testing.T) {
	str1 := "Bind_1_cy"
	str2 := "Exist_Bind_1_cy"
	str3 := "Exist_cy"
	str4 := "cy"
	str5 := "4864063f-8146-4df6-8e54-ef415b2357d6"
	str6 := fmt.Sprintf("%s**%s**%s", str4, str4, str5)
	strSplit := strings.Split(str6, "**")
	fmt.Println(strSplit)
	fmt.Println(strSplit[0])
	fmt.Println(str6)
	var str []string
	str = []string{str1, str2, str3, str4, str5, str6}
	exist := strings.Contains(str6, "cy**")
	fmt.Println(exist)
	fmt.Println(str)
	for i := 0; i < len(str); i++ {
		strSplit := strings.Split(str[i], "**")
		fmt.Println(strSplit)
		fmt.Println(len(strSplit))
	}
}

func TestParseTime(t *testing.T) {
	FormatTimeStringDate := "2006-01-02"
	timeString := "2021-09-23"
	changeTime, err := time.ParseInLocation(FormatTimeStringDate, timeString, time.Local)
	if err != nil {
		fmt.Println(err)
		return
	}
	changeTime = changeTime.Add(time.Hour*23 + time.Minute*59 + time.Second*59)
	fmt.Println(changeTime)
	nums := []int{1, 2, 3, 6, 7, 4, 5}
	fmt.Println(BubbleSort(nums))
}

func BubbleSort(a []int) []int {
	if len(a) <= 1 {
		return a
	}
	for i := 0; i < len(a); i++ {
		// 提前退出标志
		flag := false
		for j := 0; j < len(a)-i-1; j++ {
			if a[j] > a[j+1] {
				a[j], a[j+1] = a[j+1], a[j]
				// 此次冒泡有数据交换
				flag = true
			}
		}
		// 如果没有交换数据，提前退出
		if !flag {
			break
		}
	}
	return a
}
