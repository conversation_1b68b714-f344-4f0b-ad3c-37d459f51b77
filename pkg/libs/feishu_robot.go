package libs

import (
	"fmt"
	"github.com/levigross/grequests"
	"os"
)

const (
	GpuOccupyAlert = "https://open.feishu.cn/open-apis/bot/v2/hook/0d84e787-d21e-4b3c-8821-5f44e86b9cbb"

	FSContainerAlert     = "https://open.feishu.cn/open-apis/bot/v2/hook/67d58ead-89f4-4ae7-80e1-b581d1e8bd3b"
	FSContainerAlertTest = "https://open.feishu.cn/open-apis/bot/v2/hook/f34bf4a4-bfcb-43d7-9eab-8eaa1f1d0c2d"

	//NfsDisconnectedAlert = "https://open.feishu.cn/open-apis/bot/v2/hook/e6099300-774e-4653-b8e9-7a24e01bada2"
	NfsDisconnectedAlert = "https://open.feishu.cn/open-apis/bot/v2/hook/6f2748dc-d3e9-46d6-b8b5-654d8670f4ee"

	// AutodlServiceAlert AutoDL服务告警
	AutodlServiceAlert = "https://open.feishu.cn/open-apis/bot/v2/hook/677c1c55-ce66-4ff1-8984-52e8644a534f"
)

func FeiShuRobotSendMSg(webHookAddr, msg string) error {
	resp, err := grequests.Post(webHookAddr, &grequests.RequestOptions{
		JSON: map[string]interface{}{
			"msg_type": "text",
			"content": map[string]string{
				"text": msg,
			},
		},
	})
	if err != nil {
		return fmt.Errorf("robot send msg err:%v,resp:%s", err, resp.String())
	}

	return nil
}

func AlertPanic(alertURL, message string) error {
	if alertURL == "" {
		return nil
	}

	name, _ := os.Hostname()

	body := fmt.Sprintf(`【AutoDL 服务异常】🚨🚨🚨🚨
hostname: %s
%s
`,
		name,
		message,
	)

	return FeiShuRobotSendMSg(alertURL, body)
}
