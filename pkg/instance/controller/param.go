package controller

import (
	bcm "server/pkg/billing_center/model"
	"server/pkg/constant"
	"server/pkg/db_helper"
	instanceModel "server/pkg/instance/model"
)

// 请求定义 + 文档 @

type InstanceOptRequest struct {
	InstanceUUID constant.InstanceUUIDType `form:"instance_uuid" json:"instance_uuid" binding:"required"` // 操作的实例
	/*
	 * 可能需要的配置; 如当访问开机接口时, 此 payload 指启动模式.
	 */
	Payload string `form:"payload" json:"payload"`
}

type InstanceForceStopReq struct {
	InstanceUuid string `json:"instance_uuid"`
	IsStop       bool   `json:"is_stop"`
}

type InstanceForceStopRes struct {
	Status constant.InstanceStatusType `json:"status"`
}

type InstancePagedRequest struct {
	instanceModel.InstanceFilter // filter

	db_helper.GetPagedRangeRequest // 分页信息

	TagId int `form:"tag_id" json:"tag_id"`
}

type InstanceUpdateNameRequest struct {
	InstanceOptRequest
	InstanceName string `json:"instance_name"`
}

type InstanceUpdateDescriptionRequest struct {
	InstanceOptRequest
	Description string `json:"description"`
}

type InstanceSaveImageOptRequest struct {
	InstanceUUID constant.InstanceUUIDType `form:"instance_uuid" json:"instance_uuid" binding:"required"` // 操作的实例
	ImageName    string                    `form:"image_name" json:"image_name"`
	TryAgain     bool                      `form:"try_again" json:"try_again"`
}

type InstanceChangeImageOptRequest struct {
	InstanceUUID     constant.InstanceUUIDType `form:"instance_uuid" json:"instance_uuid" binding:"required"` // 操作的实例
	ImageName        string                    `form:"image_name" json:"image_name"`
	PrivateImageUUID string                    `form:"private_image_uuid" json:"private_image_uuid"`
	ReproductionUUID string                    `form:"reproduction_uuid" json:"reproduction_uuid"`
	ReproductionID   int                       `form:"reproduction_id" json:"reproduction_id"`
}

type InstanceUpdateConfigRequest struct {
	InstanceOptRequest
	GpuNumber int `form:"gpu_number" json:"gpu_number"`
}

type InstanceScheduledShutdownRequest struct {
	InstanceOptRequest
	ShutdownAt *string `json:"shutdown_at"` // 前端传 null 表示取消关机. 此字符串手动解析为分钟
}

type InstanceUpdateSettingsRequest struct {
	InstanceOptRequest
	constant.ContainerUpdateSettingsByAdminReq
}

type InstanceOperateHistoryRequest struct {
	UserPhone    string                    `json:"user_phone"`
	InstanceUUID constant.InstanceUUIDType `json:"instance_uuid"`
	db_helper.GetPagedRangeRequest
}

type InstanceMigratePagedRequest struct {
	Uid int `form:"uid" json:"uid"`
	db_helper.GetPagedRangeRequest
}

type CountInstanceMigrateRequest struct {
	UID int `form:"uid" json:"uid"`
}

type CreateOrderForInstanceMigrateRequest struct {
	InstanceUUID string `json:"instance_uuid"`
	bcm.CreateInstanceRequest
}

type GetMachineErrInstanceListReq struct {
	MachineId string `form:"machine_id"json:"machine_id"`
}
