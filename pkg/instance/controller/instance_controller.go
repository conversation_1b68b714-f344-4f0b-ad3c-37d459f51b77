package controller

import (
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"server/pkg-agent/agent_constant"
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	_ "server/pkg/db_helper"
	h "server/pkg/http"
	"server/pkg/instance/model"
	_ "server/pkg/instance/model"
	"server/pkg/libs"
	"server/pkg/logger"
	"server/pkg/module_definition"
	regionModel "server/pkg/region/model"
	userModel "server/pkg/user/model"
	"server/plugin/kv_plugin"
	"server/plugin/redis_plugin"
	"strings"
	"time"
	"unicode/utf8"
)

type InstanceController struct {
	instanceService     module_definition.InstanceInterface
	bc                  module_definition.BCInterface
	bcs                 module_definition.BCServerInterface
	user                module_definition.UserInterface
	machine             module_definition.MachineInference
	privateImage        module_definition.PrivateImageInterface
	kv                  *kv_plugin.KVPlugin
	l                   *logger.Logger
	instancePowerOnTime *redis_plugin.InstanceLastPowerOnTimePlugin
}

const ModuleName = "instance_controller"

func NewInstanceControllerProvider(instanceService module_definition.InstanceInterface,
	user module_definition.UserInterface,
	machine module_definition.MachineInference,
	kv *kv_plugin.KVPlugin,
	bc module_definition.BCInterface,
	bcs module_definition.BCServerInterface,
	privateImage module_definition.PrivateImageInterface, instancePowerOnTime *redis_plugin.InstanceLastPowerOnTimePlugin) *InstanceController {
	return &InstanceController{
		instanceService:     instanceService,
		bc:                  bc,
		bcs:                 bcs,
		user:                user,
		machine:             machine,
		kv:                  kv,
		privateImage:        privateImage,
		l:                   logger.NewLogger(ModuleName),
		instancePowerOnTime: instancePowerOnTime,
	}
}

// GetPaged
// ShowAccount godoc
// @Summary 获取实例列表, 支持分页和筛选名字状态
// @Description author: ytb
// @Tags instance
// @Accept  json
// @Produce  json
// @Param req query InstancePagedRequest true "请求体"
// @Success 200 {object} h.SendOKResponse{data=db_helper.PagedData{List=[]model.InstanceFrontendInfo}} "Success"
// @Router /api/v1/instance [get]
func (ctrl *InstanceController) GetPaged(c *gin.Context) {
	var req InstancePagedRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	i := h.GetUserInfo(c)
	req.AutoChargeType()

	// 兼容前端小程序
	if req.PageIndex == 0 && req.PageSize == 0 {
		req.PageIndex, req.PageSize = 1, 100
	}
	if req.TagId != 0 {
		// 获取 instance uuids
		instanceUuids, err := ctrl.instanceService.SearchInstanceUuidsByTag(req.TagId, i.UID)
		if err != nil {
			ctrl.l.ErrorE(err, "get instance uuids by tag_id:[%d] and  uid:[%d] failed", req.TagId, i.UID)
			return
		}
		req.InstanceFilter.InstanceUuids = instanceUuids
	}

	paged, _, err := ctrl.instanceService.ListInstanceForFrontend(i.UID, req.InstanceFilter, req.GetPagedRangeRequest)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, paged)
}

// CheckInstanceExist
// ShowAccount godoc
// @Summary 获取实例列表, 支持分页和筛选名字状态
// @Description author: ytb
// @Tags instance
// @Accept  json
// @Produce  json
// @Param req query InstancePagedRequest true "请求体"
// @Success 200 {object} h.SendOKResponse{data=bool} "Success"
// @Router /api/v1/instance/exist [get]
func (ctrl *InstanceController) CheckInstanceExist(c *gin.Context) {
	var req InstanceOptRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	exist := ctrl.instanceService.CheckInstanceUUIDExist(req.InstanceUUID)

	h.SendOK(c, exist)
}

// GetInstanceSnapshot
// ShowAccount godoc
// @Summary snapshot 获取实例详情弹出框用, 包括 machine 和 gpu 等.
// @Tags instance
// @Accept  json
// @Produce  json
// @Param req query InstanceOptRequest true "请求体"
// @Success 200 {object} h.CommonResponse{data=model.InstanceSnapshot{}}} "Success"
// @Router /api/v1/instance/snapshot [get]
func (ctrl *InstanceController) GetInstanceSnapshot(c *gin.Context) {
	var req InstanceOptRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	i := h.GetUserInfo(c)

	err := ctrl.instanceService.Auth(i.UID, req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	snapshot, err := ctrl.instanceService.SnapshotWithRightPaygPriceForFrontend(req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, snapshot)
}

// GetInstanceDetail
// ShowAccount godoc
// @Summary detail 获取实例详情弹出框用, 最新 machine 和 gpu 等.
// @Tags instance
// @Accept  json
// @Produce  json
// @Param req query InstanceOptRequest true "请求体"
// @Success 200 {object} h.CommonResponse{data=model.InstanceLatestDetail{}}} "Success"
// @Router /api/v1/instance/detail [get]
func (ctrl *InstanceController) GetInstanceDetail(c *gin.Context) {
	var req InstanceOptRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	i := h.GetUserInfo(c)

	if !i.IsAdmin {
		err := ctrl.instanceService.Auth(i.UID, req.InstanceUUID)
		if err != nil {
			h.SendError(c, err)
			return
		}
	}

	detail, err := ctrl.instanceService.DetailWithInstanceForFrontend(req.InstanceUUID)
	if err != nil {
		ctrl.l.WarnE(err, "Get instance detail for frontend failed.")
		h.SendError(c, err)
		return
	}

	h.SendOK(c, detail)
}

// PowerOnInstance
// ShowAccount godoc
// @Summary 实例开机, docker start
// @Description author: ytb
// @Tags instance
// @Accept  json
// @Produce  json
// @Param req body InstanceOptRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/instance/power_on [post]
func (ctrl *InstanceController) PowerOnInstance(c *gin.Context) {
	var req InstanceOptRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	// 判断redis中是否存在该实例的uuid
	_, err := ctrl.instancePowerOnTime.Get(string(req.InstanceUUID))
	if err != nil {
		if errors.Is(err, redis.Nil) {
			err = ctrl.instancePowerOnTime.Set(string(req.InstanceUUID), time.Now().Format(constant.FormatTimeString))
			if err != nil {
				ctrl.l.ErrorE(err, "set instance power on time in redis failed")
				h.SendError(c, err)
				return
			}
		} else {
			ctrl.l.ErrorE(err, "get instance power on time from redis failed")
			h.SendError(c, err)
			return
		}
	} else {
		h.SendOK(c, nil)
		return
	}

	defer func() {
		if err != nil {
			errE := ctrl.instancePowerOnTime.Del(string(req.InstanceUUID))
			if errE != nil {
				ctrl.l.WithField("instanceUUID", req.InstanceUUID).WithError(errE).Error("del instance power on time in redis failed.")
			}
		}
	}()

	i := h.GetUserInfo(c)
	err = ctrl.instanceService.Auth(i.UID, req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	startMode := constant.NewContainerStartMode(req.Payload)
	if !startMode.Available() { // 由于此值从前端直接获取. 需要防止被传入其他 mode
		ctrl.l.WithField("runtime_uuid", req.InstanceUUID).Error("Start mode[%s] not available! Please check http request.", startMode)
		err = biz.ErrInstanceWrongStartMode
		h.SendError(c, err)
		return
	}

	online := ctrl.instanceService.CheckMachineBusinessHealthBeforeOperate(req.InstanceUUID)
	if !online {
		err = biz.ErrMachineUnhealthy
		h.SendError(c, err)
		return
	}
	if startMode.IsGPU() {
		err = ctrl.instanceService.CheckOptMachineOnline(constant.InstanceStartOpt, req.InstanceUUID)
		if err != nil {
			h.SendError(c, err)
			return
		}
	}

	instance, err := ctrl.instanceService.GetInstance(req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	err = instance.Status.CanOpt(constant.InstanceStartOpt)
	if err != nil {
		h.SendError(c, err)
		return
	}

	// 先判断实例是否在保存镜像，若是正在saving，则返回错误
	ok, err := ctrl.instanceService.InstanceCheckImageIsSaving(req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
	}
	if ok {
		err = biz.ErrPrivateImageStatusIsSaving
		h.SendError(c, err)
		return
	}

	opt := constant.OptInstanceReq{
		InstanceUUID: req.InstanceUUID.String(),
		OptType:      constant.InstanceStartOpt,
		Caller:       constant.NewOptCallerUser(i.UID),
		Payload:      req.Payload,
	}

	err = ctrl.instanceService.OperateInstance(opt)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)

	ro := userModel.OperateRecord{
		UID:           i.UID,
		Operate:       constant.OperateInstancePowerOn,
		EntityUUID:    req.InstanceUUID.String(),
		PayloadEntity: &req,
	}
	err = ro.OperateRecordCreate(c)
	if err != nil {
		ctrl.l.WarnE(err, "insert operate record failed")
	}
}

// RestartInstance
// ShowAccount godoc
// @Summary 实例重启
// @Description author: zt
// @Tags instance
// @Accept  json
// @Produce  json
// @Param req body InstanceOptRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/instance/restart [post]
func (ctrl *InstanceController) RestartInstance(c *gin.Context) {
	var req InstanceOptRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	i := h.GetUserInfo(c)

	err := ctrl.instanceService.Auth(i.UID, req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	startMode := constant.NewContainerStartMode(req.Payload)
	if !startMode.Available() { // 由于此值从前端直接获取. 需要防止被传入其他 mode
		ctrl.l.WithField("runtime_uuid", req.InstanceUUID).Error("Start mode[%s] not available! Please check http request.", startMode)
		err = biz.ErrInstanceWrongStartMode
		h.SendError(c, err)
		return
	}

	online := ctrl.instanceService.CheckMachineBusinessHealthBeforeOperate(req.InstanceUUID)
	if !online {
		err = biz.ErrMachineUnhealthy
		h.SendError(c, err)
		return
	}
	if startMode.IsGPU() {
		err = ctrl.instanceService.CheckOptMachineOnline(constant.InstanceStartOpt, req.InstanceUUID)
		if err != nil {
			h.SendError(c, err)
			return
		}
	}

	// 先判断实例是否在保存镜像，若是正在saving，则返回错误
	ok, err := ctrl.instanceService.InstanceCheckImageIsSaving(req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
	}
	if ok {
		err = biz.ErrPrivateImageStatusIsSaving
		h.SendError(c, err)
		return
	}

	opt := constant.OptInstanceReq{
		InstanceUUID: req.InstanceUUID.String(),
		OptType:      constant.InstanceRestartOpt,
		Caller:       constant.NewOptCallerUser(i.UID),
		Payload:      req.Payload,
	}

	err = ctrl.instanceService.OperateInstance(opt)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)

	ro := userModel.OperateRecord{
		UID:           i.UID,
		Operate:       constant.OperateInstanceRestart,
		EntityUUID:    req.InstanceUUID.String(),
		PayloadEntity: &req,
		SubName:       i.SubName,
	}
	err = ro.OperateRecordCreate(c)
	if err != nil {
		ctrl.l.WarnE(err, "insert operate record failed")
	}
}

func (ctrl *InstanceController) CanPowerOnByNonGPUMode(c *gin.Context) {
	i := h.GetUserInfo(c)
	can, err := ctrl.instanceService.CanPowerOnByNonGPUMode(i.UID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	h.SendOK(c, can)
}

// PowerOffInstance
// ShowAccount godoc
// @Summary 实例关机, docker stop
// @Description author: ytb
// @Tags instance
// @Accept  json
// @Produce  json
// @Param req body InstanceOptRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/instance/power_off [post]
func (ctrl *InstanceController) PowerOffInstance(c *gin.Context) {
	var req InstanceOptRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	i := h.GetUserInfo(c)

	err := ctrl.instanceService.Auth(i.UID, req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	instance, err := ctrl.instanceService.GetInstance(req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	err = instance.Status.CanOpt(constant.InstanceStopOpt)
	if err != nil {
		h.SendError(c, err)
		return
	}

	opt := constant.OptInstanceReq{
		InstanceUUID: req.InstanceUUID.String(),
		OptType:      constant.InstanceStopOpt,
		Caller:       constant.NewOptCallerUser(i.UID),
	}

	err = ctrl.instanceService.OperateInstance(opt)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)

	ro := userModel.OperateRecord{
		UID:           i.UID,
		Operate:       constant.OperateInstancePowerOff,
		EntityUUID:    req.InstanceUUID.String(),
		PayloadEntity: &req,
	}
	err = ro.OperateRecordCreate(c)
	if err != nil {
		ctrl.l.WarnE(err, "insert operate record failed")
	}
}

// ReleaseInstance
// ShowAccount godoc
// @Summary 实例释放, 不可逆
// @Description author: ytb
// @Tags instance
// @Accept  json
// @Produce  json
// @Param req body InstanceOptRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/instance/release [post]
func (ctrl *InstanceController) ReleaseInstance(c *gin.Context) {
	var req InstanceOptRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	exist, err := ctrl.bc.ExistUnpaidChangeTypeOrder(req.InstanceUUID.String())
	if err != nil {
		h.SendError(c, err)
		return
	}

	if exist {
		h.SendError(c, biz.ErrOrderCannotReleaseInstanceByExistUnpaid)
		return
	}

	i := h.GetUserInfo(c)

	ins, err := ctrl.instanceService.GetInstance(req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	if ins.UID != i.UID {
		h.SendError(c, biz.ErrInstanceNotFound)
		return
	}

	if !ins.Status.IsDown() {
		h.SendError(c, biz.ErrInstanceReleaseNotAllow)
		return
	}

	// 先判断实例是否在保存镜像，若是正在saving，则返回错误
	ok, err := ctrl.instanceService.InstanceCheckImageIsSaving(req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
	}
	if ok {
		err = biz.ErrPrivateImageStatusIsSaving
		h.SendError(c, err)
		return
	}

	opt := constant.OptInstanceReq{
		InstanceUUID: req.InstanceUUID.String(),
		OptType:      constant.InstanceRemoveOpt,
		Caller:       constant.NewOptCallerUser(i.UID),
	}

	err = ctrl.instanceService.OperateInstance(opt)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)

	ro := userModel.OperateRecord{
		UID:           i.UID,
		Operate:       constant.OperateInstanceRelease,
		EntityUUID:    req.InstanceUUID.String(),
		PayloadEntity: &req,
	}
	err = ro.OperateRecordCreate(c)
	if err != nil {
		ctrl.l.WarnE(err, "insert operate record failed")
	}
}

// InitInstance
// ShowAccount godoc
// @Summary 实例初始化
// @Description author: ytb
// @Tags instance
// @Accept  json
// @Produce  json
// @Param req body InstanceOptRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/instance/init [post]
func (ctrl *InstanceController) InitInstance(c *gin.Context) {
	var req InstanceOptRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	i := h.GetUserInfo(c)

	err := ctrl.instanceService.Auth(i.UID, req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	online := ctrl.instanceService.CheckMachineBusinessHealthBeforeOperate(req.InstanceUUID)
	if !online {
		err = biz.ErrMachineUnhealthy
		h.SendError(c, err)
		return
	}
	err = ctrl.instanceService.CheckOptMachineOnline(constant.InstanceReInitOpt, req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	// 先判断实例是否在保存镜像，若是正在saving，则返回错误
	ok, err := ctrl.instanceService.InstanceCheckImageIsSaving(req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
	}
	if ok {
		err = biz.ErrPrivateImageStatusIsSaving
		h.SendError(c, err)
		return
	}

	// 判断使用的镜像是否存在
	ins, err := ctrl.instanceService.GetInstance(req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	if ins.PrivateImageUUID != "" {
		_, err = ctrl.privateImage.GetImageByUUID(ins.PrivateImageUUID)
		if err != nil {
			h.SendError(c, biz.ErrInitInstanceNotFountPrivateImage)
			return
		}
	}

	opt := constant.OptInstanceReq{
		InstanceUUID: req.InstanceUUID.String(),
		OptType:      constant.InstanceReInitOpt,
		Caller:       constant.NewOptCallerUser(i.UID),
	}

	err = ctrl.instanceService.OperateInstance(opt)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)

	ro := userModel.OperateRecord{
		UID:           i.UID,
		Operate:       constant.OperateInstanceInit,
		EntityUUID:    req.InstanceUUID.String(),
		PayloadEntity: &req,
	}
	err = ro.OperateRecordCreate(c)
	if err != nil {
		ctrl.l.WarnE(err, "insert operate record failed")
	}
}

// UpdateInstanceName
// ShowAccount godoc
// @Summary 实例更改用户自定义的名称
// @Description author: ytb
// @Tags instance
// @Accept  json
// @Produce  json
// @Param req body InstanceUpdateNameRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/instance/name [put]
func (ctrl *InstanceController) UpdateInstanceName(c *gin.Context) {
	var req InstanceUpdateNameRequest

	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	i := h.GetUserInfo(c)

	err := ctrl.instanceService.Auth(i.UID, req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	err = ctrl.instanceService.UpdateInstanceName(i.UID, req.InstanceUUID, req.InstanceName)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)
}

// UpdateInstanceDescription
// ShowAccount godoc
// @Summary 实例更改用户自定义的名称
// @Description author: ytb
// @Tags instance
// @Accept  json
// @Produce  json
// @Param req body InstanceUpdateDescriptionRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/instance/description [put]
func (ctrl *InstanceController) UpdateInstanceDescription(c *gin.Context) {
	var req InstanceUpdateDescriptionRequest

	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	i := h.GetUserInfo(c)

	err := ctrl.instanceService.Auth(i.UID, req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	err = ctrl.instanceService.UpdateInstanceDescription(i.UID, req.InstanceUUID, req.Description)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)
}

// ChangeImage
// ShowAccount godoc
// @Summary 实例更换镜像, payload 中是镜像名. 必须是合法镜像.
// @Description author: ytb
// @Tags instance
// @Accept  json
// @Produce  json
// @Param req body InstanceOptRequest true "请求体"
// @Success 200 {object} h.CommonResponse "Success"
// @Router /api/v1/instance/change_image [post]
func (ctrl *InstanceController) ChangeImage(c *gin.Context) {
	var req InstanceChangeImageOptRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	i := h.GetUserInfo(c)

	instance, err := ctrl.instanceService.AuthAndGet(i.UID, req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	err = instance.Status.CanOpt(constant.InstanceChangeImageOpt)
	if err != nil {
		h.SendError(c, err)
		return
	}

	err = ctrl.instanceService.CheckOptMachineOnline(constant.InstanceChangeImageOpt, req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	online := ctrl.instanceService.CheckMachineBusinessHealthBeforeOperate(req.InstanceUUID)
	if !online {
		err = biz.ErrMachineUnhealthy
		h.SendError(c, err)
		return
	}
	// 先判断实例是否在保存镜像，若是正在saving，则返回错误
	ok, err := ctrl.instanceService.InstanceCheckImageIsSaving(req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
	}
	if ok {
		err = biz.ErrPrivateImageStatusIsSaving
		h.SendError(c, err)
		return
	}
	exist, err := ctrl.instanceService.InstanceChangeImageOpt(i.UID, req.ImageName, req.PrivateImageUUID, req.ReproductionUUID, req.ReproductionID)
	if err != nil {
		ctrl.l.WithField("err", err).Info("check image exist failed.")
		err = biz.ErrInternalError
		h.SendError(c, err)
		return
	}
	if !exist {
		err = biz.ErrInstanceInvalidCustomImage
		h.SendError(c, err)
		return

	}

	if req.PrivateImageUUID != "" {
		privateImage, err := ctrl.privateImage.GetImageByUUID(req.PrivateImageUUID)
		if err != nil {
			return
		}

		if privateImage.UID != i.UID {
			err = biz.ErrPrivateImageAuthFailed
			h.SendError(c, err)
			return
		}
	}

	imageInfo := constant.ImageInfo{
		ImageName:        req.ImageName,
		PrivateImageUUID: req.PrivateImageUUID,
		ReproductionUUID: req.ReproductionUUID,
		ReproductionID:   req.ReproductionID,
	}
	opt := constant.OptInstanceReq{
		InstanceUUID: req.InstanceUUID.String(),
		OptType:      constant.InstanceChangeImageOpt,
		Caller:       constant.NewOptCallerUser(i.UID),
		Payload:      imageInfo.ToString(),
	}

	err = ctrl.instanceService.OperateInstance(opt)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)

	ro := userModel.OperateRecord{
		UID:           i.UID,
		Operate:       constant.OperateInstanceChangeImage,
		EntityUUID:    req.InstanceUUID.String(),
		PayloadEntity: &req,
	}
	err = ro.OperateRecordCreate(c)
	if err != nil {
		ctrl.l.WarnE(err, "insert operate record failed")
	}
}

// SaveImage
// ShowAccount godoc
// @Summary SaveImage
// @Description author: ytb
// @Tags instance
// @Accept  json
// @Produce  json
// @Param req body InstanceSaveImageOptRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/instance/image/save [post]
func (ctrl *InstanceController) SaveImage(c *gin.Context) {
	var req InstanceSaveImageOptRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Warn("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	i := h.GetUserInfo(c)

	err := ctrl.instanceService.Auth(i.UID, req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	instance, err := ctrl.instanceService.GetInstance(req.InstanceUUID)
	if err != nil {
		ctrl.l.WithError(err).Warn("get instance failed.")
		err = biz.ErrDatabaseError
		h.SendError(c, err)
		return
	}
	if instance.Status != constant.InstanceShutdown {
		err = biz.ErrInstanceStatusNotShutdown
		h.SendError(c, err)
		return
	}

	machineStatus, err := ctrl.machine.GetMachineStatus(instance.MachineID)
	if err != nil {
		ctrl.l.WithField("machine_id", instance.MachineID).Error("get machine status from redis failed")
		h.SendError(c, err)
		return
	}
	if machineStatus.HealthStatus == agent_constant.NetworkConnectionException {
		h.SendError(c, biz.ErrMachineNetworkException)
		return
	}
	online, err := ctrl.machine.GetMachineOnline(instance.MachineID)
	if err != nil {
		ctrl.l.WithField("machine_id", instance.MachineID).Error("get machine online failed")
		h.SendError(c, err)
		return
	}
	if !online.IsOnLine() {
		h.SendError(c, biz.ErrMachineOffline)
		return
	}

	image, err := ctrl.instanceService.InstanceSaveImageOpt(req.ImageName, "", i.UID, req.TryAgain, req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	opt := constant.OptInstanceReq{
		InstanceUUID: req.InstanceUUID.String(),
		OptType:      constant.InstanceSaveImageOpt,
		Caller:       constant.NewOptCallerUser(i.UID),
		Payload:      image.ImageUUID,
	}

	err = ctrl.instanceService.OperateInstance(opt)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)
}

// CountInstance godoc
// @Summary 统计用户的实例数量
// @Description author: yff
// @Tags instance
// @Produce  json
// @Param req body h.CommonNullRequest true "请求体"
// @Success 200 {object} h.SendOKResponse "Success"
// @Router /api/v1/instance/count [get]
func (ctrl *InstanceController) CountInstance(c *gin.Context) {
	u := h.GetUserInfo(c)
	count, err := ctrl.instanceService.CountInstance(u.UID)
	if err != nil {
		ctrl.l.WithField("userId", u.UID).WarnE(err, "Count user instance failed.")
		h.SendError(c, err)
	}
	h.SendOK(c, count)
}

func (ctrl *InstanceController) CountUserInstanceInstance(c *gin.Context) {
	u := h.GetUserInfo(c)
	count, err := ctrl.instanceService.CountUserExistInstances(u.UID)
	if err != nil {
		ctrl.l.WithField("userId", u.UID).WarnE(err, "Count user exist instance failed.")
		h.SendError(c, err)
	}
	h.SendOK(c, count)
}

// ScheduledShutdown
// ShowAccount godoc
// @Summary 实例定时关机
// @Description author: ytb
// @Tags instance
// @Accept  json
// @Produce  json
// @Param req body InstanceScheduledShutdownRequest true "请求体"
// @Success 200 {object} h.CommonResponse "Success"
// @Router /api/v1/instance/timed/shutdown [post]
func (ctrl *InstanceController) ScheduledShutdown(c *gin.Context) {
	var req InstanceScheduledShutdownRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	shutdownAt, err := constant.ParseTimeByMinute(req.ShutdownAt)
	if err != nil {
		ctrl.l.WithError(err).Info("Parse time failed. Time string from frontend: %v", req.ShutdownAt)
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	i := h.GetUserInfo(c)
	err = ctrl.instanceService.SetInstanceScheduledShutdownAt(i.UID, req.InstanceUUID, shutdownAt)
	if err != nil {
		h.SendError(c, err)
		return
	}

	// 保存操作记录
	ro := userModel.OperateRecord{
		UID:           i.UID,
		Operate:       constant.OperateInstanceTimedShutDown,
		EntityUUID:    string(req.InstanceUUID),
		PayloadEntity: &req,
	}
	err = ro.OperateRecordCreate(c)
	if err != nil {
		ctrl.l.WarnE(err, "insert operate record failed")
	}

	h.SendOK(c, nil)
}

// InstanceMonitor
// ShowAccount godoc
// @Summary 实例监控
// @Description author: yff
// @Tags instance
// @Accept  json
// @Produce  json
// @Param req body InstanceOptRequest true "请求体"
// @Success 200 {object} h.CommonResponse "Success"
// @Router /api/v1/instance/monitor [get]
func (ctrl *InstanceController) InstanceMonitor(c *gin.Context) {
	var req InstanceOptRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Instance monitor invalid request params.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	i := h.GetUserInfo(c)
	instance, err := ctrl.instanceService.GetInstance(req.InstanceUUID)
	if err != nil {
		ctrl.l.WithField("instanceUUID", req.InstanceUUID).WarnE(err, "Controller.InstanceUsageInfo method get instance failed.")
		h.SendError(c, err)
		return
	}
	if instance.UID != i.UID {
		h.SendError(c, biz.ErrResourceAccessAuthFailed)
		return
	}

	detail, err := ctrl.instanceService.InstanceMonitorInfo(req.InstanceUUID.String())
	if err != nil {
		ctrl.l.WithField("instanceUUID", req.InstanceUUID).WarnE(err, "Controller.InstanceUsageInfo method failed.")
		h.SendError(c, err)
		return
	}

	h.SendOK(c, detail)
}

// InstanceAllocate
// ShowAccount godoc
// @Summary 给子帐号分配实例
// @Description author: zt,只上传有改动的内容，没有改动的不用上传
// @Tags instance
// @Accept  json
// @Produce  json
// @Param req body []model.InstanceSubUserAllocateParams true "请求体"
// @Success 200 {object} h.CommonResponse "Success"
// @Router /api/v1/instance/allocate [post]
func (ctrl *InstanceController) InstanceAllocate(c *gin.Context) {
	var req []model.InstanceSubUserAllocateParams
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	u := h.GetUserInfo(c)
	err := ctrl.instanceService.InstanceSubUserAllocate(u.UID, req)
	if err != nil {
		h.SendError(c, err)
		return
	}
	h.SendOK(c, nil)
}

// InstanceGetListForAllocate
// ShowAccount godoc
// @Summary 分配实例，获取所有实例
// @Description author: zt, 因为获取内容比较多，返回部分数据
// @Tags instance
// @Accept  json
// @Produce  json
// @Param req body []model.InstanceSubUserAllocateParams true "请求体"
// @Success 200 {object} h.CommonResponse "Success"
// @Router /api/v1/instance/allocate [post]
func (ctrl *InstanceController) InstanceGetListForAllocate(c *gin.Context) {
	var req struct {
		UUID string `form:"uuid" json:"uuid"`
	}
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	u := h.GetUserInfo(c)
	list, err := ctrl.instanceService.InstanceGetListForAllocate(u.UID, req.UUID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	h.SendOK(c, list)
}

// FileTransfer
// ShowAccount godoc
// @Summary 实例迁移数据，源实例必须关机，目标实例无要求.
// @Description author: ningfd
// @Tags instance
// @Accept  json
// @Produce  json
// @Param req body constant.InstanceFileTransferOptRequest true "请求体"
// @Success 200 {string} string "taskID, 用于/api/v1/internal/kv/key/:key [get]"
// @Router /api/v1/instance/file_transfer [post]
func (ctrl *InstanceController) FileTransfer(c *gin.Context) {
	var req constant.InstanceFileTransferOptRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}
	if len(req.SrcPath) == 0 {
		req.SrcPath = "*"
	}
	if len(req.DstPath) == 0 {
		req.DstPath = "/"
	}

	if !req.ValidatePath() {
		h.SendError(c, biz.ErrCloneSrcPathInvalid)
		return
	}

	i := h.GetUserInfo(c)
	user, err := ctrl.user.FindByUserId(i.UID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	srcInstance, err := ctrl.instanceService.GetInstance(req.SrcInstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	if srcInstance.UID != i.UID {
		h.SendError(c, biz.ErrInstanceNotFound)
		return
	}

	dstInstance, err := ctrl.instanceService.GetInstance(req.DstInstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	if dstInstance.UID != i.UID {
		h.SendError(c, biz.ErrInstanceNotFound)
		return
	}

	if i.SubName != "" {
		err = ctrl.instanceService.InstanceSubUserAuth(i.SubName, req.SrcInstanceUUID.String())
		if err != nil {
			h.SendError(c, err)
			return
		}
		err = ctrl.instanceService.InstanceSubUserAuth(i.SubName, req.DstInstanceUUID.String())
		if err != nil {
			h.SendError(c, err)
			return
		}
	}

	srcMachine, err := ctrl.machine.Get(srcInstance.MachineID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	if !srcMachine.IsIntranetOK() {
		h.SendError(c, biz.ErrMachinePrivateNetworkDisabled.AppendStr("[src]"))
		return
	}

	dstMachine, err := ctrl.machine.Get(dstInstance.MachineID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	if !dstMachine.IsIntranetOK() {
		h.SendError(c, biz.ErrMachinePrivateNetworkDisabled.AppendStr("[dst]"))
		return
	}

	same, err := regionModel.CheckRegionIsInSameDataCenter(srcMachine.RegionSign, dstMachine.RegionSign)
	if err != nil {
		ctrl.l.ErrorE(err, "CheckRegionIsInSameDataCenter failed")
		h.SendError(c, biz.ErrServerBusy)
		return
	}
	if !same {
		h.SendError(c, biz.ErrInstanceNotMeetCloneCondition.AppendStr("different data center"))
		return
	}

	err = ctrl.instanceService.CheckOptMachineOnline(constant.InstanceChangeImageOpt, req.SrcInstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	err = ctrl.instanceService.CheckOptMachineOnline(constant.InstanceChangeImageOpt, req.DstInstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	online := ctrl.instanceService.CheckMachineBusinessHealthBeforeOperate(req.DstInstanceUUID)
	if !online {
		err = biz.ErrMachineUnhealthy
		h.SendError(c, err)
		return
	}

	taskID, opt, err := ctrl.instanceService.GenFTInstanceOptReq(&model.GenFTInstanceOptReqParams{
		UID:                    user.ID,
		UserMaxFTPerDay:        user.GetMaxFileTransferPerDay(),
		UserUUID:               user.UUID,
		SrcInstanceUUID:        srcInstance.InstanceUUID,
		DstInstanceUUID:        dstInstance.InstanceUUID,
		SrcRuntimeUUID:         srcInstance.RuntimeUUID,
		DstRuntimeUUID:         dstInstance.RuntimeUUID,
		SrcPath:                req.SrcPath,
		DstPath:                req.DstPath,
		DstMachinePrivateNetIP: dstMachine.PrivateNetIP,
		IsTransferDiff:         req.IsTransferDiff,
	})
	if err != nil {
		h.SendError(c, err)
		return
	}

	err = ctrl.instanceService.OperateInstance(opt)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, taskID)
}

// FileTransferCancel
// ShowAccount godoc
// @Summary 取消实例迁移数据
// @Description author: ningfd
// @Tags instance
// @Accept  json
// @Produce  json
// @Param req body constant.InstanceFileTransferOptRequest true "请求体"
// @Success 200 {string} string ""
// @Router /api/v1/instance/file_transfer/cancel [post]
func (ctrl *InstanceController) FileTransferCancel(c *gin.Context) {
	var req constant.InstanceFileTransferOptRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	i := h.GetUserInfo(c)
	_, err := ctrl.user.FindByUserId(i.UID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	srcInstance, err := ctrl.instanceService.GetInstance(req.SrcInstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	if srcInstance.UID != i.UID {
		h.SendError(c, biz.ErrInstanceNotFound)
		return
	}

	dstInstance, err := ctrl.instanceService.GetInstance(req.DstInstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	if dstInstance.UID != i.UID {
		h.SendError(c, biz.ErrInstanceNotFound)
		return
	}

	if i.SubName != "" {
		err = ctrl.instanceService.InstanceSubUserAuth(i.SubName, req.SrcInstanceUUID.String())
		if err != nil {
			h.SendError(c, err)
			return
		}
		err = ctrl.instanceService.InstanceSubUserAuth(i.SubName, req.DstInstanceUUID.String())
		if err != nil {
			h.SendError(c, err)
			return
		}
	}

	srcMachine, err := ctrl.machine.Get(srcInstance.MachineID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	if !srcMachine.PrivateNetAccess || len(srcMachine.PrivateNetIP) == 0 {
		h.SendError(c, biz.ErrMachinePrivateNetworkDisabled.AppendStr("[src]"))
		return
	}

	dstMachine, err := ctrl.machine.Get(dstInstance.MachineID)
	if err != nil {
		h.SendError(c, err)
		return
	}

	if !dstMachine.PrivateNetAccess || len(dstMachine.PrivateNetIP) == 0 {
		h.SendError(c, biz.ErrMachinePrivateNetworkDisabled.AppendStr("[dst]"))
		return
	}

	same, err := regionModel.CheckRegionIsInSameDataCenter(srcMachine.RegionSign, dstMachine.RegionSign)
	if err != nil {
		ctrl.l.ErrorE(err, "CheckRegionIsInSameDataCenter failed")
		h.SendError(c, biz.ErrServerBusy)
		return
	}
	if !same {
		h.SendError(c, biz.ErrInstanceNotMeetCloneCondition.AppendStr("different data center"))
		return
	}

	err = ctrl.instanceService.CheckOptMachineOnline(constant.InstanceChangeImageOpt, req.SrcInstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	online := ctrl.instanceService.CheckMachineBusinessHealthBeforeOperate(req.SrcInstanceUUID)
	if !online {
		err = biz.ErrMachineUnhealthy
		h.SendError(c, err)
		return
	}

	err = ctrl.instanceService.CheckOptMachineOnline(constant.InstanceChangeImageOpt, req.DstInstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	online = ctrl.instanceService.CheckMachineBusinessHealthBeforeOperate(req.DstInstanceUUID)
	if !online {
		err = biz.ErrMachineUnhealthy
		h.SendError(c, err)
		return
	}

	taskID := kv_plugin.FileTransferStatusGenerateUUID(req.SrcInstanceUUID, req.DstInstanceUUID)
	opt := constant.OptInstanceReq{
		InstanceUUID: req.SrcInstanceUUID.String(),
		OptType:      constant.InstanceDataTransferCancelOpt,
		Caller:       constant.NewOptCallerUser(i.UID),
		Payload:      taskID,
	}

	err = ctrl.instanceService.OperateInstance(opt)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)
}

// TransferInstanceList
// ShowAccount godoc
// @Summary 实例迁移数据，目标机器列表，默认筛选运行中或已关机实例
// @Description author: zt
// @Tags instance
// @Accept  json
// @Produce  json
// @Param req body model.TransferInstanceListParams true "请求体"
// @Success 200 {object} model.TransferInstanceListReply
// @Router /api/v1/instance/file_transfer/list [post]
func (ctrl *InstanceController) TransferInstanceList(c *gin.Context) {
	var req model.TransferInstanceListParams
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	u := h.GetUserInfo(c)

	req.UID = u.UID
	req.SubName = u.SubName
	list, err := ctrl.instanceService.TransferInstanceList(&req)
	if err != nil {
		h.SendError(c, err)
		return
	}
	h.SendOK(c, list)
}

// GetDashboardInstance
// ShowAccount godoc
// @Summary 控制台首页面板中的实例数据
// @Description author: liangjunmo
// @Tags dashboard
// @Accept  json
// @Produce  json
// @Success 200 {object} h.SendOKResponse{data=model.DashboardInstance{}} "Success"
// @Router /api/v1/dashboard/instance [get]
func (ctrl *InstanceController) GetDashboardInstance(c *gin.Context) {
	u := h.GetUserInfo(c)
	dashboard, err := ctrl.instanceService.GetDashboardInstance(u.UID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	h.SendOK(c, dashboard)
}

// GetDashboardStorage
// ShowAccount godoc
// @Summary 控制台首页面板中的数据管理
// @Description author: liangjunmo
// @Tags dashboard
// @Accept  json
// @Produce  json
// @Success 200 {object} h.SendOKResponse{data=model.DashboardStorage{}} "Success"
// @Router /api/v1/dashboard/storage [get]
func (ctrl *InstanceController) GetDashboardStorage(c *gin.Context) {
	u := h.GetUserInfo(c)
	dashboard, err := ctrl.instanceService.GetDashboardStorage(u.UID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	h.SendOK(c, dashboard)
}

// GetNotifyWarningRecords godoc
// @Summary 获取通知实例预警记录
// @Description author: liangjunmo
// @Tags instance
// @Accept  json
// @Produce  json
// @Param req query db_helper.GetPagedRangeRequest true "请求体"
// @Success 200 {object} h.SendOKResponse{data=db_helper.PagedData{List=[]model.NotifyRecord}} "Success"
// @Router /api/v1/instance/notify/records [get]
func (ctrl *InstanceController) GetNotifyWarningRecords(c *gin.Context) {
	var req db_helper.GetPagedRangeRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("bind request failed")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	u := h.GetUserInfo(c)
	paged, _, err := ctrl.instanceService.GetNotifyWarningRecords(u.UID, req)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, paged)
}

func (ctrl *InstanceController) UpdateSetting(c *gin.Context) {
	var req model.UpdateSettingRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("bind request failed")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	u := h.GetUserInfo(c)
	err := ctrl.instanceService.UpdateSetting(u.UID, req)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)
}

func (ctrl *InstanceController) SSHPwdUpdate(c *gin.Context) {
	var req model.SSHPwdUpdateParams
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("bind request failed")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	u := h.GetUserInfo(c)
	err := ctrl.instanceService.SSHPwdUpdate(u.UID, &req)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)
}

func (ctrl *InstanceController) CloneRetry(c *gin.Context) {
	var req InstanceOptRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("bind request failed")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	u := h.GetUserInfo(c)
	opt := constant.OptInstanceReq{
		InstanceUUID: req.InstanceUUID.String(),
		OptType:      constant.InstanceCloneRetryOpt,
		Caller:       constant.NewOptCallerUser(u.UID),
	}

	dstInstance, err := ctrl.instanceService.GetInstance(req.InstanceUUID)
	if err != nil {
		h.SendError(c, err)
		return
	}
	if dstInstance.AdditionalEntity.SrcInstanceUUID == "" {
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	err = ctrl.bcs.CloneInstanceCheck(dstInstance.AdditionalEntity.SrcInstanceUUID, dstInstance.MachineID, false)
	if err != nil {
		h.SendError(c, err)
		return
	}

	err = ctrl.instanceService.OperateInstance(opt)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)
	ro := userModel.OperateRecord{
		UID:           u.UID,
		Operate:       constant.OperateInstanceCloneRetry,
		EntityUUID:    req.InstanceUUID.String(),
		PayloadEntity: &req,
		SubName:       u.SubName,
	}
	err = ro.OperateRecordCreate(c)
	if err != nil {
		ctrl.l.WarnE(err, "insert operate record failed")
	}
}

func (ctrl *InstanceController) CloneFileTransferDone(c *gin.Context) {
	//var req struct {
	//	InstanceUUID constant.InstanceUUIDType `form:"instance_uuid" json:"instance_uuid"`
	//}
	//if err := c.ShouldBind(&req); err != nil {
	//	ctrl.l.WithError(err).Info("bind request failed")
	//	h.SendError(c, biz.ErrInvalidRequestParams)
	//	return
	//}
	//
	//instance, err := ctrl.instanceService.GetInstance(req.InstanceUUID)
	//if err != nil {
	//	ctrl.l.WithField("instanceUUID", req.InstanceUUID).ErrorE(err, "CloneFileTransferDone get instance failed")
	//	h.SendOK(c, nil)
	//	return
	//}
	//
	//instance.AdditionalEntity.CopyDataDiskAfterCloneRunning = false
	//err = ctrl.instanceService.InstanceUpdateAdditional(nil, req.InstanceUUID.String(), &instance.AdditionalEntity)
	//if err != nil {
	//	ctrl.l.WithField("instanceUUID", req.InstanceUUID).ErrorE(err, "CloneFileTransferDone update instance additional failed")
	//}
	//h.SendOK(c, nil)
	return
}

func (ctrl *InstanceController) GetUnExpireList(c *gin.Context) {
	var req model.GetUnExpireInstanceListReq

	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("bind request failed")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}
	u := h.GetUserInfo(c)
	paged, _, err := ctrl.instanceService.GetUnExpireInstanceList(&model.GetUnExpireInstanceListReq{
		IsDescRank: req.IsDescRank,
		ExpireTime: req.ExpireTime,
		Uid:        u.UID,
	}, &req.GetPagedRangeRequest)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, paged)

}

func (ctrl *InstanceController) CreateInstanceSchedule(c *gin.Context) {
	var req model.CreateInstanceScheduleReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("bind request failed")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	// 判断通行码是否正确
	isPass := libs.ConfuseCodeAesDecrypt(req, req.Passcode)
	if !isPass {
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	u := h.GetUserInfo(c)
	err := ctrl.instanceService.CreateInstanceSchedule(u.UID, &req)
	if err != nil {
		h.SendError(c, err)
		return
	}

	h.SendOK(c, nil)
}

func (ctrl *InstanceController) StopInstanceSchedule(c *gin.Context) {
	var req model.StopInstanceScheduleReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	u := h.GetUserInfo(c)

	queue, err := ctrl.instanceService.GetInstanceScheduleByID(req.Id)
	if err != nil {
		ctrl.l.WithError(err).Error("GetInstanceQueue failed")
		h.SendError(c, err)
		return
	}
	if queue == nil {
		h.SendError(c, biz.ErrScheduleInstanceNotExist)
		return
	}
	if queue.Uid != u.UID {
		h.SendError(c, biz.ErrScheduleInstanceAuthFailed)
		return
	}

	if queue.Status == constant.InstanceQueueStatusSucceeded {
		ctrl.l.WithFields(map[string]interface{}{
			"uid":                  u.UID,
			"schedule_instance_id": req.Id,
		}).Error("instance schedule success")
		h.SendError(c, biz.ErrScheduleInstanceSuccess)
		return
	}

	err = ctrl.instanceService.StopInstanceSchedule(queue.Id)
	if err != nil {
		ctrl.l.WithError(err).Error("StopInstanceSchedule failed")
		h.SendError(c, err)
		return
	}
	h.SendOK(c, nil)
}

func (ctrl *InstanceController) UpdateInstanceIsSendMsgStatus(c *gin.Context) {
	var req model.UpdateInstanceIsSendMsgStatusReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	u := h.GetUserInfo(c)

	queue, err := ctrl.instanceService.GetInstanceScheduleByID(req.Id)
	if err != nil {
		ctrl.l.WithError(err).Error("GetInstanceQueue failed")
		h.SendError(c, err)
		return
	}
	if queue == nil {
		h.SendError(c, biz.ErrScheduleInstanceNotExist)
		return
	}
	if queue.Uid != u.UID {
		h.SendError(c, biz.ErrScheduleInstanceAuthFailed)
		return
	}

	err = ctrl.instanceService.UpdateIsSendMsgStatus(req.Id, req.IsSendMsg)
	if err != nil {
		ctrl.l.WithError(err).Error("UpdateIsSendMsgStatus failed")
		h.SendError(c, err)
		return
	}
	h.SendOK(c, nil)
}

func (ctrl *InstanceController) GetWaitingInstanceScheduleList(c *gin.Context) {
	var req model.GetWaitingInstanceScheduleListReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	u := h.GetUserInfo(c)
	paged, schedules, err := ctrl.instanceService.GetWaitingInstanceScheduleList(model.GetScheduleListParams{
		Uid:    u.UID,
		Status: constant.InstanceQueueStatusWaiting,
	}, &req.GetPagedRangeRequest)
	if err != nil {
		ctrl.l.WithError(err).Error("GetInstanceQueue failed")
		h.SendError(c, err)
		return
	}

	if len(schedules) == 0 {
		paged = &db_helper.PagedData{List: []int{}}
		h.SendOK(c, paged)
		return
	}

	h.SendOK(c, paged)
}

func (ctrl *InstanceController) GetSuccessInstanceScheduleList(c *gin.Context) {
	var req model.GetWaitingInstanceScheduleListReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	u := h.GetUserInfo(c)
	paged, schedules, err := ctrl.instanceService.GetWaitingInstanceScheduleList(model.GetScheduleListParams{
		Uid:    u.UID,
		Status: constant.InstanceQueueStatusSucceeded,
	}, &req.GetPagedRangeRequest)
	if err != nil {
		ctrl.l.WithError(err).Error("GetInstanceQueue failed")
		h.SendError(c, err)
		return
	}

	if len(schedules) == 0 {
		paged = &db_helper.PagedData{List: []int{}}
		h.SendOK(c, paged)
		return
	}

	var list []model.InstanceScheduleSuccessInfo
	for _, schedule := range schedules {
		info := model.InstanceScheduleSuccessInfo{
			Id:             schedule.Id,
			CreatedAt:      schedule.CreatedAt,
			UpdatedAt:      schedule.UpdatedAt,
			DeletedAt:      schedule.DeletedAt,
			Uid:            schedule.Uid,
			InstanceUuid:   schedule.InstanceUuid,
			Status:         schedule.Status,
			TemplateEntity: schedule.TemplateEntity,
			Remark:         schedule.Remark,
			StoppedAt:      schedule.StoppedAt,
			IsSendMsg:      schedule.IsSendMsg,
		}
		list = append(list, info)
	}
	paged.List = list
	h.SendOK(c, paged)
}

func (ctrl *InstanceController) GetInstanceSchedulePricePreview(c *gin.Context) {
	var req model.GetInstanceSchedulePricePreviewReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("bind failed")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	u := h.GetUserInfo(c)

	priceInfo, err := ctrl.instanceService.GetInstanceSchedulePricePreview(u.UID, &req)
	if err != nil {
		h.SendError(c, err)
		return
	}
	h.SendOK(c, priceInfo)

}

func (ctrl *InstanceController) GetCloneCode(c *gin.Context) {
	var req model.GetCloneCodeReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	_, err := ctrl.user.FindByUserUuid(req.TargetUuid)
	if err != nil {
		ctrl.l.WithField("targetUuid", req.TargetUuid).WithError(err).Info("get targetUuid user by uuid failed.")
		h.SendError(c, err)
		return
	}

	u := h.GetUserInfo(c)
	uid := u.UID

	codeInfo, err := ctrl.instanceService.GetCloneCode(uid, req.TargetUuid, req.InstanceUUID)
	if err != nil {
		ctrl.l.WithFields(
			map[string]interface{}{
				"uid":          uid,
				"targetUuid":   req.TargetUuid,
				"instanceUUID": req.InstanceUUID,
			}).WithError(err).Error("GetCloneCode failed")
		h.SendError(c, err)
		return
	}

	h.SendOK(c, codeInfo)
}

func (ctrl *InstanceController) CheckShowClone(c *gin.Context) {
	u := h.GetUserInfo(c)
	targetUuid := u.UUID

	showInfo, err := ctrl.instanceService.CheckShowCloneCode(targetUuid)
	if err != nil {
		ctrl.l.WithFields(
			map[string]interface{}{
				"targetUuid": targetUuid,
			}).WithError(err).Error("Get ShowCloneCode info failed")
		h.SendError(c, err)
		return
	}

	h.SendOK(c, showInfo)
}

func (ctrl *InstanceController) GetSrcCuda(c *gin.Context) {
	var req model.GetSrcMachineReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	u := h.GetUserInfo(c)
	targetUuid := u.UUID

	info, err := ctrl.instanceService.GetSrcData(req.CloneCode, targetUuid)
	if err != nil {
		ctrl.l.WithFields(map[string]interface{}{
			"cloneCode":  req.CloneCode,
			"targetUuid": targetUuid,
		}).WithError(err).Error("GetSrcCuda failed")
		h.SendError(c, err)
		return
	}

	h.SendOK(c, info)
}

type V2GetTagListTags struct {
	TagId   int    `json:"tag_id"`
	TagName string `json:"tag_name"`
}

func (ctrl *InstanceController) V2GetTagList(c *gin.Context) {
	var (
		resp []V2GetTagListTags
	)
	u := h.GetUserInfo(c)

	tags, err := ctrl.instanceService.GetTagList(u.UID, u.SubName)
	if err != nil {
		ctrl.l.ErrorE(err, "get tag list by uid:[%d], sunName:[%d] failed", u.UID, u.SubName)
		h.SendError(c, biz.ErrGetInstanceTagFailed)
		return
	}
	for _, tag := range tags {
		resp = append(resp, V2GetTagListTags{
			TagId:   tag.ID,
			TagName: tag.TagName,
		})
	}

	h.SendOK(c, resp)
}

type V2AddTagRequest struct {
	TagName      string `json:"tag_name"`
	InstanceUuid string `json:"instance_uuid"`
}
type V2AddTagResponse struct {
	TagId int `json:"tag_id"`
}

func (ctrl *InstanceController) V2AddTag(c *gin.Context) {
	var req V2AddTagRequest
	var resp V2AddTagResponse
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	req.TagName = strings.TrimSpace(req.TagName)
	if len(req.TagName) == 0 || utf8.RuneCountInString(req.TagName) > 15 {
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}
	u := h.GetUserInfo(c)
	exist, err := ctrl.instanceService.CheckInstanceTagIsExist(u.UID, req.TagName, constant.InstanceUUIDType(req.InstanceUuid))
	if err != nil {
		h.SendError(c, biz.ErrAddInstanceTag)
		return
	}
	if exist {
		ctrl.l.WithField("instanceUuid", req.InstanceUuid).WithField("tagName", req.TagName).Error(" tag exist")
		h.SendError(c, biz.ErrInstanceTagIsExists)
		return
	}
	tagNum, err := ctrl.instanceService.CheckInstanceTagNum(u.UID, constant.InstanceUUIDType(req.InstanceUuid))
	if err != nil {
		ctrl.l.WithField("instanceUuid", req.InstanceUuid).ErrorE(err, "check instance tag num failed")
		h.SendError(c, biz.ErrCheckBeforeAddTag)
		return
	}
	if tagNum >= 3 {
		ctrl.l.WithField("instanceUuid", req.InstanceUuid).Error("the number of instance tags exceeds the limit")
		h.SendError(c, biz.ErrInstanceTagNum)
		return
	}
	//检测当前用户是否有相同的tag,不关注子账号
	tags, err := ctrl.instanceService.GetTagList(u.UID, "")
	if err != nil {
		ctrl.l.ErrorE(err, "get tag list by uid:[%d], sunName:[%d] failed", u.UID, u.SubName)
		h.SendError(c, biz.ErrGetInstanceTagFailed)
		return
	}

	hasTag := false
	tagId := 0
	for _, tag := range tags {
		if strings.TrimSpace(tag.TagName) == req.TagName {
			hasTag = true
			tagId = tag.ID
			break
		}
	}

	if hasTag { //存在标签,关联原来的标签
		resp.TagId, err = ctrl.instanceService.AddInstanceTagRelation(tagId, req.TagName, constant.InstanceUUIDType(req.InstanceUuid), u.UID, u.SubName)
		if err != nil {
			ctrl.l.WithField("tag_name", req.TagName).WithField("instance_uuid", req.InstanceUuid).WithField("uid", u.UID).Error("add tag failed", err)
			h.SendError(c, biz.ErrAddInstanceTag)
			return
		}
	} else {
		// 确认下原来的
		resp.TagId, err = ctrl.instanceService.AddTag(req.TagName, constant.InstanceUUIDType(req.InstanceUuid), u.UID, u.SubName)
		if err != nil {
			ctrl.l.WithField("tag_name", req.TagName).WithField("instance_uuid", req.InstanceUuid).WithField("uid", u.UID).Error("add tag failed", err)
			h.SendError(c, biz.ErrAddInstanceTag)
			return
		}
	}

	h.SendOK(c, resp)
}

type V2DeleteTagRequest struct {
	TagId        int    `json:"tag_id"`
	InstanceUuid string `json:"instance_uuid"`
}

func (ctrl *InstanceController) V2DeleteTag(c *gin.Context) {
	var req V2DeleteTagRequest
	if err := c.ShouldBind(&req); err != nil {
		ctrl.l.WithError(err).Info("Bind request failed.")
		h.SendError(c, biz.ErrInvalidRequestParams)
		return
	}

	u := h.GetUserInfo(c)

	err := ctrl.instanceService.DeleteTag(req.TagId, constant.InstanceUUIDType(req.InstanceUuid), u.UID)
	if err != nil {
		ctrl.l.WithError(err).Info("delete tag failed.")
		h.SendError(c, biz.ErrDeleteInstanceTag)
		return
	}

	h.SendOK(c, nil)
}
