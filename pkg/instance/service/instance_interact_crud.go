package service

import (
	"database/sql"
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	instanceModel "server/pkg/instance/model"
	redis "server/plugin/redis_plugin"
	"time"

	"github.com/pkg/errors"
	"gorm.io/gorm"
)

// TODO: 去除不必要的 []* -> []

type CRUDHandler interface {
	ExistInstanceUUID(instanceUUID constant.InstanceUUIDType) bool
	AuthAndGet(uid int, instanceUUID constant.InstanceUUIDType) (instance instanceModel.Instance, err error)

	InsertOrUpdateInstance(instance *instanceModel.Instance) error

	GetInstance(instanceUUID constant.InstanceUUIDType) (instanceModel.Instance, error)
	GetInstanceNoEqualRemoving(instanceUUID constant.InstanceUUIDType) (instance instanceModel.Instance, err error)
	GetInstanceUnscoped(instanceUUID constant.InstanceUUIDType) (instance instanceModel.Instance, err error)
	GetInstanceNoExistNoError(instanceUUID constant.InstanceUUIDType) (instance instanceModel.Instance, err error)
	GetInstanceByRuntimeUUID(runtimeUUID constant.ContainerRuntimeUUID) (instanceModel.Instance, error)
	GetInstanceByRuntimeUUIDs(runtimeUUID []constant.ContainerRuntimeUUID) (instance []instanceModel.Instance, err error)
	GetSomeInstances(instanceUUIDs []constant.InstanceUUIDType) ([]*instanceModel.Instance, error) // may contain removing, removed
	GetSomeInstancesUnscoped(instanceUUIDs []constant.InstanceUUIDType) (instances []*instanceModel.Instance, err error)
	GetSomeAvailableInstances(instanceUUIDs []constant.InstanceUUIDType) ([]*instanceModel.Instance, error) // no removing, removed

	GetAllInstancesRunningWithTimedShutdownAt(nowAt time.Time) (instances []instanceModel.Instance, err error)
	GetAllAvailableInstancesByUid(uid int) ([]*instanceModel.Instance, error)
	GetAllInstancesByUidUnscoped(uid int) (instances []*instanceModel.Instance, err error)
	GetAllAvailableInstancesByMachineID(machineID string) ([]*instanceModel.Instance, error)

	GetRunningInstances() ([]*instanceModel.Instance, error)
	GetRunningExpiredNonPaygInstances() ([]*instanceModel.Instance, error)
	GetStoppedHaveGpuExpiredNonPaygInstances() ([]*instanceModel.Instance, error)
	GetExpiredNonPaygAutoTransToPaygInstances() (instances []*instanceModel.Instance, err error)

	CountInstances(userId int) (count int, err error)
	CountUserExistInstances(uid int) (count int64, err error)

	// UpdateInstance NOTE: 注意这里不要传 get instance 对象, 应使用新的 &Instance{}. 否则容易覆盖; TODO: 改为专门对应的更新
	UpdateInstance(tx *gorm.DB, instanceUUID constant.InstanceUUIDType, instance *instanceModel.Instance) error
	UpdateInstanceByRuntimeUUID(tx *gorm.DB, runtimeUUID constant.ContainerRuntimeUUID, instance *instanceModel.Instance) error
	UpdateInstanceMap(tx *gorm.DB, instanceUUID constant.InstanceUUIDType, data map[string]interface{}) error
	UpdateInstanceStartedAtAndStoppedAt(instanceUUID constant.InstanceUUIDType, startedAt, stoppedAt *sql.NullTime) error

	// UpdateScheduledShutdownAt 函数内部会根据 at 是否为 nil 自动判断. nil 表示清除定时关机时间.
	UpdateScheduledShutdownAt(tx *gorm.DB, instanceUUID constant.InstanceUUIDType, at *time.Time) error
	UpdateScheduledShutdownAtSnapshot(tx *gorm.DB, instanceUUID constant.InstanceUUIDType, at *time.Time) error

	DeleteInstance(instanceUUID constant.InstanceUUIDType) error
	ClearHaveGpuFlag(instanceUUID constant.InstanceUUIDType) error

	ListInstances(db *gorm.DB, uid int, isAdmin bool, filter instanceModel.InstanceFilter, pagedReq db_helper.GetPagedRangeRequest) (*db_helper.PagedData, []*instanceModel.Instance, error)

	// SaveInstanceName 对可能为空的用 save, 下同

	SaveInstanceName(instanceUUID constant.InstanceUUIDType, name string) error
	SaveInstanceDescription(instanceUUID constant.InstanceUUIDType, description string) error

	GetMachineBindingInstancesNum(machineID string) (count int64, err error)

	GetAllAvailableInstancesByUID(uid int) (instances []*instanceModel.Instance, err error)
	CountNonGpuNum(uid int) (num int, err error)
}

type CH struct {
	mod ModuleHandler
	t   redis.SyncTimer
}

func NewCRUDHandler(mod ModuleHandler, timer redis.SyncTimer) *CH {
	return &CH{
		mod: mod,
		t:   timer,
	}
}

var hiddenStatusSet = []constant.InstanceStatusType{
	constant.InstanceRemoving,
	constant.InstanceRemoved,
}

// NeverFreelyRetainedStatusSet 下列状态可以开始进行 ‘免费保存30天’ 校验. 迁移的实例保存时间判断在另一处。
var NeverFreelyRetainedStatusSet = []constant.InstanceStatusType{
	constant.InstanceReInitialized,
	constant.InstanceReInitFailed,
	constant.InstanceCreateFailed,

	constant.InstanceShuttingDown,
	constant.InstanceShutdown,
	constant.InstanceShutdownByStartingError,
	constant.InstanceShutdownByRunningError,
}

func (c *CH) ExistInstanceUUID(instanceUUID constant.InstanceUUIDType) bool {
	var count int64
	err := db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: &instanceModel.Instance{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uuid": instanceUUID,
			},
		},
	}, &count).GetError()
	if err != nil {
		return false
	}

	return count != 0
}

func (c *CH) AuthAndGet(uid int, instanceUUID constant.InstanceUUIDType) (instance instanceModel.Instance, err error) {
	err = db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: &instanceModel.Instance{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uid":  uid,
				"uuid": instanceUUID,
			},
		},
	}, &instance).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = biz.ErrInstanceNotFound
		}
		return
	}

	return
}

func (c *CH) InsertOrUpdateInstance(instance *instanceModel.Instance) error {
	var err error

	_ = instance.LoadAllJsonByStruct()
	err = db_helper.InsertOrUpdateOne(
		db_helper.QueryDefinition{
			ModelDefinition: &instanceModel.Instance{},
			Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uuid": instance.InstanceUUID}},
			InsertPayload:   instance,
		}, &instance).GetError()
	if err != nil {
		return err
	}
	return nil
}

func (c *CH) GetInstance(instanceUUID constant.InstanceUUIDType) (instance instanceModel.Instance, err error) {
	err = db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: &instanceModel.Instance{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uuid": instanceUUID,
			},
		},
	}, &instance).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return instance, biz.ErrInstanceNotFound
		}
		return instance, biz.ErrDatabaseError
	}

	_ = instance.LoadAllStructByJson()
	return
}

func (c *CH) GetInstanceNoEqualRemoving(instanceUUID constant.InstanceUUIDType) (instance instanceModel.Instance, err error) {
	err = db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: &instanceModel.Instance{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uuid": instanceUUID,
			},
			NotEqualFilters: map[string]interface{}{
				"status": constant.InstanceRemoving,
			},
		},
	}, &instance).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return instance, biz.ErrInstanceNotFound
		}
		return instance, biz.ErrDatabaseError
	}

	_ = instance.LoadAllStructByJson()
	return
}

func (c *CH) GetInstanceUnscoped(instanceUUID constant.InstanceUUIDType) (instance instanceModel.Instance, err error) {
	err = db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: &instanceModel.Instance{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uuid": instanceUUID,
			},
		},
		Unscoped: true,
	}, &instance).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return instance, nil
		}
		return instance, err
	}

	_ = instance.LoadAllStructByJson()
	return
}

func (c *CH) GetInstanceNoExistNoError(instanceUUID constant.InstanceUUIDType) (instance instanceModel.Instance, err error) {
	err = db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: &instanceModel.Instance{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uuid": instanceUUID,
			},
		},
	}, &instance).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return instance, nil
		}
		return instance, biz.ErrDatabaseError
	}

	_ = instance.LoadAllStructByJson()
	return
}

func (c *CH) GetInstanceByRuntimeUUID(runtimeUUID constant.ContainerRuntimeUUID) (instance instanceModel.Instance, err error) {

	err = db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: &instanceModel.Instance{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"runtime_uuid": runtimeUUID,
			},
		},
	}, &instance).GetError()
	if err != nil {
		return instance, err
	}

	_ = instance.LoadAllStructByJson()
	return
}

func (c *CH) GetInstanceByRuntimeUUIDs(runtimeUUID []constant.ContainerRuntimeUUID) (instance []instanceModel.Instance, err error) {
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &instanceModel.Instance{},
		Filters: db_helper.QueryFilters{
			InFilters: []db_helper.In{{Key: "runtime_uuid", InSet: runtimeUUID}},
		},
	}, &instance).GetError()
	if err != nil {
		return instance, err
	}

	for _, v := range instance {
		_ = v.LoadAllStructByJson()

	}
	return
}

func (c *CH) GetSomeInstances(instanceUUIDs []constant.InstanceUUIDType) (instances []*instanceModel.Instance, err error) {
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &instanceModel.Instance{},
		Filters: db_helper.QueryFilters{
			InFilters: []db_helper.In{
				{
					Key:   "uuid",
					InSet: instanceUUIDs,
				},
			},
			Orders: []string{"id desc"},
		},
	}, &instances).GetError()
	if err != nil {
		return
	}

	for i := range instances {
		_ = instances[i].LoadAllStructByJson()
	}

	return
}

func (c *CH) GetSomeInstancesUnscoped(instanceUUIDs []constant.InstanceUUIDType) (instances []*instanceModel.Instance, err error) {
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &instanceModel.Instance{},
		Unscoped:        true,
		Filters: db_helper.QueryFilters{
			InFilters: []db_helper.In{
				{
					Key:   "uuid",
					InSet: instanceUUIDs,
				},
			},
			Orders: []string{"id desc"},
		},
	}, &instances).GetError()
	if err != nil {
		return
	}

	for i := range instances {
		_ = instances[i].LoadAllStructByJson()
	}

	return
}

func (c *CH) GetAllAvailableInstances() (instances []*instanceModel.Instance, err error) {
	err = db_helper.GlobalDBConn().
		Model(&instanceModel.Instance{}).
		Not("status in (?)", hiddenStatusSet).
		Order("id desc").
		Find(&instances).Error
	if err != nil {
		return
	}

	for i := range instances {
		_ = instances[i].LoadAllStructByJson()
	}

	return
}

func (c *CH) GetAllInstancesRunningWithTimedShutdownAt(nowAt time.Time) (instances []instanceModel.Instance, err error) {
	var runningInstances []instanceModel.Instance
	err = db_helper.GlobalDBConn().Debug().
		Model(&instanceModel.Instance{}).
		Where("status = ?", constant.InstanceRunning).
		Where("scheduled_shutdown_at is not null").
		Order("id desc").
		Find(&runningInstances).Error
	if err != nil {
		return
	}

	for _, instance := range runningInstances {
		if instance.ScheduledShutdownAt.Valid && nowAt.After(instance.ScheduledShutdownAt.Time) {
			instances = append(instances, instance)
		}
	}

	return
}

func (c *CH) GetSomeAvailableInstances(instanceUUIDs []constant.InstanceUUIDType) (instances []*instanceModel.Instance, err error) {
	err = db_helper.GlobalDBConn().
		Model(&instanceModel.Instance{}).
		Where("uuid in (?)", instanceUUIDs).
		Not("status in (?)", hiddenStatusSet).
		Order("id desc").
		Find(&instances).Error
	if err != nil {
		return
	}

	for i := range instances {
		_ = instances[i].LoadAllStructByJson()
	}

	return
}

func (c *CH) GetAllAvailableInstancesByUid(uid int) (instances []*instanceModel.Instance, err error) {
	err = db_helper.GlobalDBConn().
		Model(&instanceModel.Instance{}).
		Where("uid = ?", uid).
		Not("status in (?)", hiddenStatusSet).
		Order("id desc").
		Find(&instances).Error
	if err != nil {
		return
	}

	for i := range instances {
		_ = instances[i].LoadAllStructByJson()
	}

	return
}

func (c *CH) GetAllInstancesByUidUnscoped(uid int) (instances []*instanceModel.Instance, err error) {
	err = db_helper.GlobalDBConn().Unscoped().
		Model(&instanceModel.Instance{}).
		Where("uid = ?", uid).
		Order("id desc").
		Find(&instances).Error
	if err != nil {
		return
	}

	for i := range instances {
		_ = instances[i].LoadAllStructByJson()
	}

	return
}

func (c *CH) GetAllAvailableInstancesByMachineID(machineID string) (instances []*instanceModel.Instance, err error) {
	err = db_helper.GlobalDBConn().
		Model(&instanceModel.Instance{}).
		Where("machine_id = ?", machineID).
		Not("status in (?)", hiddenStatusSet).
		Order("id desc").
		Find(&instances).Error
	if err != nil {
		return
	}

	for i := range instances {
		_ = instances[i].LoadAllStructByJson()
	}

	return
}

func (c *CH) GetRunningExpiredNonPaygInstances() (instances []*instanceModel.Instance, err error) {

	var tmpInstances []*instanceModel.Instance

	db := db_helper.GlobalDBConn().Model(&instanceModel.Instance{})

	db = db.Where("status = ?", constant.InstanceRunning).
		Not("charge_type = ?", constant.ChargeTypePayg)

	err = db.Find(&tmpInstances).Error

	if len(tmpInstances) == 0 {
		return
	}

	for i := range tmpInstances {
		// Where("expired_at < ?", c.t.Now()).
		if c.t.Now().After(tmpInstances[i].ExpiredAt.Time) {
			_ = tmpInstances[i].LoadAllStructByJson()
			instances = append(instances, tmpInstances[i])
		}
	}

	return
}

func (c *CH) GetRunningInstances() (instances []*instanceModel.Instance, err error) {
	db := db_helper.GlobalDBConn().Model(&instanceModel.Instance{})
	db = db.Where("status = ?", constant.InstanceRunning)

	err = db.Find(&instances).Error
	return
}

func (c *CH) GetStoppedHaveGpuExpiredNonPaygInstances() (instances []*instanceModel.Instance, err error) {

	var tmpInstances []*instanceModel.Instance

	db := db_helper.GlobalDBConn().Model(&instanceModel.Instance{})

	db = db.Where("have_gpu_resources = ?", true).
		Not("status in (?)", append(hiddenStatusSet, constant.InstanceRunning)).
		Not("charge_type = ?", constant.ChargeTypePayg)

	err = db.Find(&tmpInstances).Error

	if len(tmpInstances) == 0 {
		return
	}

	for i := range tmpInstances {
		// Where("expired_at < ?", c.t.Now()).
		if c.t.Now().After(tmpInstances[i].ExpiredAt.Time) {
			_ = tmpInstances[i].LoadAllStructByJson()
			instances = append(instances, tmpInstances[i])
		}
	}

	return
}

func (c *CH) GetExpiredNonPaygAutoTransToPaygInstances() (instances []*instanceModel.Instance, err error) {

	var tmpInstances []*instanceModel.Instance

	db := db_helper.GlobalDBConn().Model(&instanceModel.Instance{})

	db = db.Where("auto_trans_to_payg = ?", constant.AutoTransToPaygOpened).
		Not("charge_type = ?", constant.ChargeTypePayg)

	err = db.Find(&tmpInstances).Error

	if len(tmpInstances) == 0 {
		return
	}

	for i := range tmpInstances {
		// Where("expired_at < ?", c.t.Now()).
		if c.t.Now().After(tmpInstances[i].ExpiredAt.Time) {
			_ = tmpInstances[i].LoadAllStructByJson()
			instances = append(instances, tmpInstances[i])
		}
	}

	return
}

func (c *CH) UpdateInstance(tx *gorm.DB, instanceUUID constant.InstanceUUIDType, instance *instanceModel.Instance) error {
	if instance == nil {
		return nil
	}

	_ = instance.LoadAllJsonByStruct()

	err := db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         &instanceModel.Instance{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uuid": instanceUUID,
			},
		},
	}, instance).GetError()
	if err != nil {
		return err
	}

	return nil
}

func (c *CH) UpdateInstanceByRuntimeUUID(tx *gorm.DB, runtimeUUID constant.ContainerRuntimeUUID, instance *instanceModel.Instance) error {
	if instance == nil {
		return nil
	}

	_ = instance.LoadAllJsonByStruct()

	err := db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         &instanceModel.Instance{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"runtime_uuid": runtimeUUID,
			},
		},
	}, instance).GetError()
	if err != nil {
		return err
	}

	return nil
}

func (c *CH) UpdateInstanceMap(tx *gorm.DB, instanceUUID constant.InstanceUUIDType, data map[string]interface{}) error {
	err := db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         &instanceModel.Instance{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uuid": instanceUUID,
			},
		},
	}, data).GetError()
	if err != nil {
		return err
	}

	return nil
}

func (c *CH) UpdateInstanceStartedAtAndStoppedAt(instanceUUID constant.InstanceUUIDType, startedAt, stoppedAt *sql.NullTime) error {
	if startedAt == nil && stoppedAt == nil {
		return nil // nothing happened
	}

	var update = make(map[string]interface{})

	if startedAt != nil {
		update["started_at"] = *startedAt
	}

	if stoppedAt != nil {
		update["stopped_at"] = *stoppedAt
	}
	err := db_helper.UpdateOne(db_helper.QueryDefinition{
		ModelDefinition: &instanceModel.Instance{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uuid": instanceUUID,
			},
		},
	}, update).GetError()
	if err != nil {
		return err
	}

	return nil
}

func (c *CH) UpdateScheduledShutdownAt(tx *gorm.DB, instanceUUID constant.InstanceUUIDType, at *time.Time) error {
	var shutdownAt time.Time
	var valid = at != nil

	if valid {
		shutdownAt = *at
	}

	err := db_helper.UpdateOne(db_helper.QueryDefinition{
		ModelDefinition:         &instanceModel.Instance{},
		DBTransactionConnection: tx,
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uuid": instanceUUID,
			},
		},
	}, map[string]interface{}{
		"scheduled_shutdown_at": sql.NullTime{
			Time:  shutdownAt,
			Valid: valid,
		},
	}).GetError()
	if err != nil {
		return err
	}

	return nil
}

func (c *CH) UpdateScheduledShutdownAtSnapshot(tx *gorm.DB, instanceUUID constant.InstanceUUIDType, at *time.Time) error {
	var shutdownAt time.Time
	var valid = at != nil

	if valid {
		shutdownAt = *at
	}

	err := db_helper.UpdateOne(db_helper.QueryDefinition{
		ModelDefinition:         &instanceModel.Instance{},
		DBTransactionConnection: tx,
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uuid": instanceUUID,
			},
		},
	}, map[string]interface{}{
		"scheduled_shutdown_at_snapshot": sql.NullTime{
			Time:  shutdownAt,
			Valid: valid,
		},
	}).GetError()
	if err != nil {
		return err
	}

	return nil
}

func (c *CH) DeleteInstance(instanceUUID constant.InstanceUUIDType) error {
	err := db_helper.Delete(db_helper.QueryDefinition{
		ModelDefinition: &instanceModel.Instance{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uuid": instanceUUID,
			},
		},
	}, &instanceModel.Instance{}).GetError()
	if err != nil {
		return err
	}

	return nil
}

func (c *CH) ClearHaveGpuFlag(instanceUUID constant.InstanceUUIDType) error {
	err := db_helper.UpdateOne(db_helper.QueryDefinition{
		ModelDefinition: &instanceModel.Instance{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uuid": instanceUUID,
			},
		},
	}, map[string]interface{}{
		"have_gpu_resources": false,
	}).GetError()
	if err != nil {
		return err
	}

	return nil
}

// ListInstances 按需要后续再支持复杂筛选.
func (c *CH) ListInstances(db *gorm.DB, uid int, isAdmin bool, filter instanceModel.InstanceFilter, pagedReq db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, instances []*instanceModel.Instance, err error) {
	// 现在有三种身份获取list，普通用户，管理员和子账号，三者取其一
	// 此判断意为三者都不是的时候报错
	if uid <= 0 && (!isAdmin && !filter.SubUserGet) {
		err = biz.ErrInternalError
		return
	}

	if db == nil {
		db = db_helper.GlobalDBConn()
	}

	db = db.Model(&instanceModel.Instance{})

	// 此处uid可能是用户获取, 也可能是管理员使用手机号精确查询(已经在调用处处理)
	if uid != 0 {
		db = db.Where("uid = ?", uid)
	}

	if filter.Name != "" {
		like := "%" + filter.Name + "%"
		db = db.Where("name LIKE ? or uuid like ?", like, like)
	}

	if isAdmin {
		if filter.FuzzyInstanceUUID != "" {
			db = db.Where("uuid = ?", filter.FuzzyInstanceUUID)
		}

		if filter.FuzzyMachineID != "" {
			db = db.Where("machine_id = ?", filter.FuzzyMachineID)
		}
	} else {
		if filter.FuzzyInstanceUUID != "" {
			like := "%" + filter.FuzzyInstanceUUID + "%"
			db = db.Where("uuid LIKE ?", like)
		}

		if filter.FuzzyMachineID != "" {
			like := "%" + filter.FuzzyMachineID + "%"
			db = db.Where("machine_id LIKE ?", like)
		}
	}

	if len(filter.Status) != 0 {
		db = db.Where("status in (?)", filter.Status)
	}

	if len(filter.ChargeType) != 0 {
		db = db.Where("charge_type in (?)", filter.ChargeType)
	}

	if len(filter.RegionSign) != 0 {
		db = db.Where("region_sign = ?", filter.RegionSign)
	}

	if filter.SubUserGet && filter.SubUserSelfDataGet && filter.SubName != "" {
		db = db.Where("sub_name = ?", filter.SubName)
	}

	// 管理员能看到 abort 的, 用户不能。二者都不能看到 removing 和 removed。
	if isAdmin {
		db = db.Not("status in (?)", hiddenStatusSet)
	} else {
		db = db.Not("status in (?)", append(hiddenStatusSet, constant.InstanceAbort))
	}
	if len(filter.InstanceUuids) != 0 {
		db = db.Where("uuid in (?)", filter.InstanceUuids)
	}
	if pagedReq.DateFrom != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') >= ?", pagedReq.DateFrom)
	}
	if pagedReq.DateTo != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') <= ?", pagedReq.DateTo)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		return
	}

	paged = db_helper.BuildPagedDataUtil(pagedReq.PageIndex, pagedReq.PageSize, int(count), 0)
	paged.List = &instances

	err = db.Offset(paged.Offset).Limit(paged.PageSize).Order("id desc").Find(&instances).Error
	if err != nil {
		return
	}

	return
}

func (c *CH) SaveInstanceName(instanceUUID constant.InstanceUUIDType, name string) error {
	err := db_helper.UpdateOne(db_helper.QueryDefinition{
		ModelDefinition: &instanceModel.Instance{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uuid": instanceUUID,
			},
		},
	}, map[string]interface{}{
		"name": name,
	}).GetError()
	if err != nil {
		return err
	}

	return nil
}

func (c *CH) SaveInstanceDescription(instanceUUID constant.InstanceUUIDType, description string) error {
	err := db_helper.UpdateOne(db_helper.QueryDefinition{
		ModelDefinition: &instanceModel.Instance{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uuid": instanceUUID,
			},
		},
	}, map[string]interface{}{
		"description": description,
	}).GetError()
	if err != nil {
		return err
	}

	return nil
}

func (c *CH) CountInstances(userId int) (count int, err error) {
	if userId == 0 {
		err = biz.ErrInvalidRequestParams
		return
	}
	var instances int64
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: &instanceModel.Instance{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uid": userId}},
	}, &instances).GetError()
	if err != nil {
		return
	}
	count = int(instances)
	return
}

func (c *CH) CountUserExistInstances(uid int) (count int64, err error) {
	if uid == 0 {
		err = biz.ErrInvalidRequestParams
		return
	}
	err = db_helper.GlobalDBConn().Model(&instanceModel.Instance{}).Where("deleted_at is null").Where("uid = ?", uid).
		Where("status not in (?)", []constant.ContainerStatusType{constant.ContainerRemoved, constant.ContainerRemoving, constant.ContainerCloneLocked}).Count(&count).Error
	if err != nil {
		return
	}
	return
}

func (c *CH) GetMachineBindingInstancesNum(machineID string) (count int64, err error) {
	err = db_helper.GlobalDBConn().
		Model(&instanceModel.Instance{}).
		Where("machine_id = ?", machineID).
		Where("deleted_at is null").
		Not("status in (?)", hiddenStatusSet).
		Count(&count).Error
	if err != nil {
		return
	}
	return
}

// GetAllAvailableInstancesByUID 根据 uid 查询所有没有被释放的实例列表
func (c *CH) GetAllAvailableInstancesByUID(uid int) (instances []*instanceModel.Instance, err error) {
	err = db_helper.GlobalDBConn().
		Model(&instanceModel.Instance{}).
		Where("uid = ?", uid).
		Not("status in (?)", append(hiddenStatusSet, constant.InstanceAbort)).
		Find(&instances).Error
	return
}

func (c *CH) CountNonGpuNum(uid int) (num int, err error) {
	var count int64
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: &instanceModel.Instance{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uid":        uid,
				"start_mode": constant.ContainerStartWithoutGPU,
			},
			InFilters: []db_helper.In{{
				Key:   "status",
				InSet: []constant.InstanceStatusType{constant.InstanceCreating, constant.InstanceRestarting, constant.InstanceRunning}}},
		},
	}, &count).GetError()
	num = int(count)
	return
}
