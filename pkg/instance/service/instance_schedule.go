package service

import (
	"fmt"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	agentConstant "server/pkg-agent/agent_constant"
	bcm "server/pkg/billing_center/model"
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	dataDiskStockModel "server/pkg/data_disk_stock/model"
	"server/pkg/db_helper"
	"server/pkg/instance/model"
	machineModel "server/pkg/machine/model"
	regionModel "server/pkg/region/model"
	userModel "server/pkg/user/model"
	"strconv"
	"time"
)

func (svc *Service) CreateInstanceSchedule(uid int, params *model.CreateInstanceScheduleReq) (err error) {
	if !params.Template.Valid() {
		svc.l.Warn("invalid instance queue template: %+v", params.Template)
		return biz.ErrInvalidRequestParams
	}
	for _, v := range params.Template.GpuType {
		gpu, err := svc.GpuType.GetGpuByName(v)
		if err != nil {
			svc.l.WithField("gpuName", v).ErrorE(err, "deployment create: check gpu name failed")
			return err
		}
		params.Template.GpuIDSet = append(params.Template.GpuIDSet, gpu.ID)
	}

	// 实例数量判断
	user, err := svc.user.FindByUserId(uid)
	if err != nil {
		svc.l.WithError(err).WithField("uid", uid).Error("get user failed")
		return
	}

	num, err := svc.CountUserExistInstances(uid)
	if err != nil {
		svc.l.WithField("uid", uid).ErrorE(err, "get user existed instance failed")
		return err
	}

	totalLimit, _ := user.GetMaxInstanceNum("")
	if num+int64(params.InstanceNum) > totalLimit {
		return biz.ErrInstanceMaxNum.New().Format(totalLimit)
	}

	var (
		regionList     []regionModel.Region
		regionSignList = make([]constant.RegionSignType, 0)
	)

	regionList, err = svc.region.GetRegionListByDatacenter(nil, params.Template.DataCenter)
	if err != nil {
		return
	}

	for _, v := range regionList {
		if !v.UsedFor.CanInstance() {
			continue
		}
		regionSignList = append(regionSignList, v.Sign)
	}
	if len(regionSignList) == 0 {
		err = biz.ErrInvalidRequestParams
		return
	}
	params.Template.RegionSignList = regionSignList
	if params.Template.PrivateImageUUID != "" { // 私有镜像
		privateImage, err := svc.privateImage.GetImageByUUID(params.Template.PrivateImageUUID)
		if err != nil {
			svc.l.WithField("imageUUID", params.Template.PrivateImageUUID).ErrorE(err, "find private image by private uuid failed")
			return err
		}
		if privateImage.UID != uid {
			svc.l.WithFields(map[string]interface{}{
				"imageUUID":   params.Template.PrivateImageUUID,
				"uid":         uid,
				"private_uid": privateImage.UID,
			}).ErrorE(err, "private image has insufficient permission")
			return biz.ErrPrivateImageAuthFailed
		}
		params.Template.PrivateImageName = privateImage.Name
	}

	// 余额判断，根据地区和gpu型号找到按量付费价格最高的主机，将其价格和用户余额+代金券进行判断
	machine, err := svc.GetMachineForScheduleInstance(params.Template)
	if err != nil {
		svc.l.WithFields(map[string]interface{}{
			"region_sign_list": params.Template.RegionSignList,
			"gpu_type":         params.Template.GpuType,
		}).ErrorE(err, "get user wallet in CreateInstanceSchedule failed")
		return err
	}
	if machine.ID == 0 {
		return biz.ErrScheduleInstanceMachineNotExist
	}

	wallet, err := svc.bc.GetWalletForCreateOrder(uid, constant.ProductTypeInstance, constant.ChargeTypePayg, machine.MachineID, nil, "")
	if err != nil {
		svc.l.WithField("uid", uid).ErrorE(err, "get user wallet in CreateInstanceSchedule failed")
		return
	}

	var walletCompareAsset int64
	if wallet.Assets >= 0 {
		walletCompareAsset = wallet.Assets + wallet.VoucherBalance
	} else {
		walletCompareAsset = wallet.VoucherBalance
	}

	if walletCompareAsset < machine.PaygPrice*int64(params.Template.GpuNum) {
		return biz.ErrWalletInsufficientBalance
	}

	if params.InstanceNum > 1 {
		for i := 1; i <= params.InstanceNum; i++ {
			schedule := &model.InstanceSchedule{
				Uid:            uid,
				Status:         constant.InstanceQueueStatusWaiting,
				TemplateEntity: params.Template,
				Remark:         params.Remark + "-" + strconv.Itoa(i),
				IsSendMsg:      true,
			}
			err = schedule.InstanceScheduleCreate(nil)
			if err != nil {
				svc.l.WithError(err).ErrorE(err, "create instance schedule failed")
				return err
			}
		}
	} else {
		schedule := &model.InstanceSchedule{
			Uid:            uid,
			Status:         constant.InstanceQueueStatusWaiting,
			TemplateEntity: params.Template,
			Remark:         params.Remark,
			IsSendMsg:      true,
		}
		err = schedule.InstanceScheduleCreate(nil)
		if err != nil {
			svc.l.WithError(err).ErrorE(err, "create instance schedule failed")
			return err
		}
	}

	return nil
}

func (svc *Service) GetMachineForScheduleInstance(template model.Template) (machine machineModel.Machine, err error) {
	db := db_helper.GlobalDBConn().Table(machineModel.TableNameMachine).
		Where("region_sign in (?)", template.RegionSignList).
		Where("on_line in (?)", []constant.OnOffLine{constant.Online}).
		Where("deleted_at is null")
	if len(template.GpuIDSet) != 0 {
		db = db.Where("gpu_type_id in (?)", template.GpuIDSet)
	}

	err = db.Order("payg_price desc").Limit(1).
		Scan(&machine).Error

	if err != nil {
		svc.l.WithField("template", template).ErrorE(err, "get machine for schedule instance failed")
		return machineModel.Machine{}, err
	}

	if machine.ID == 0 {
		return machineModel.Machine{}, nil
	}

	return machine, nil
}

func (svc *Service) GetAllWaitingInstanceSchedule() (instanceSchedules []model.InstanceSchedule, err error) {
	instanceSchedules = make([]model.InstanceSchedule, 0)
	instanceSchedule := &model.InstanceSchedule{}
	instanceSchedules, err = instanceSchedule.InstanceScheduleGetAll(db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"status": constant.InstanceQueueStatusWaiting,
			"uuid":   "",
		},
		NullField: []string{"deleted_at"},
	})

	if err != nil {
		svc.l.WithError(err).ErrorE(err, "get all waiting instance schedule failed")
		return instanceSchedules, err
	}
	return instanceSchedules, nil
}

func (svc *Service) GetMachineToCreateInstance(instanceSchedule *model.InstanceSchedule) (*machineModel.Machine, error) {
	if instanceSchedule == nil {
		return nil, nil
	}

	userMember, err := svc.user.GetUserMemberInfoByUid(instanceSchedule.Uid)
	if err != nil {
		return nil, err
	}

	var machines []machineModel.Machine
	db := db_helper.GlobalDBConn().Table(machineModel.TableNameMachine).
		Joins(fmt.Sprintf("join %s dds on machine.machine_id = dds.machine_id", dataDiskStockModel.TableNameMachineDataDisk)).
		Where("machine.deleted_at is null").
		Where("machine.on_line in (?)", []constant.OnOffLine{constant.Online}).
		Where("machine.status in (?)", []agentConstant.MachineHealthStatus{agentConstant.Normal}).
		Where("machine.region_sign in (?)", instanceSchedule.TemplateEntity.RegionSignList)
	if len(instanceSchedule.TemplateEntity.GpuIDSet) != 0 {
		db = db.Where("machine.gpu_type_id in (?)", instanceSchedule.TemplateEntity.GpuIDSet)
	}
	db = db.Where("machine.gpu_idle_num >= ?", instanceSchedule.TemplateEntity.GpuNum)
	db = db.Where("machine.cpu_per_gpu * ? >= ?", instanceSchedule.TemplateEntity.GpuNum, instanceSchedule.TemplateEntity.MinCpuNum).
		Where("machine.mem_per_gpu * ? >= ?", instanceSchedule.TemplateEntity.GpuNum, instanceSchedule.TemplateEntity.MinMemorySize)

	if userMember.MemberLevel == constant.MemberUser {
		db = db.Where("machine.payg_discount_price * ? <= ?", instanceSchedule.TemplateEntity.GpuNum, instanceSchedule.TemplateEntity.MaxPaygPrice)
	} else {
		db = db.Where("machine.payg_price * ? <= ?", instanceSchedule.TemplateEntity.GpuNum, instanceSchedule.TemplateEntity.MaxPaygPrice)
	}

	db = db.Where("dds.disk_expand_total-dds.disk_expand_allocated>= ?", instanceSchedule.TemplateEntity.ExpandDataDisk).
		Order("machine.id")
	err = db.Limit(20).
		Find(&machines).
		Error
	if err != nil {
		svc.l.WithError(err).Error("get machines failed")
		return nil, err
	}
	return svc.filterMachine(instanceSchedule, machines)
}

func (svc *Service) filterMachine(instanceSchedule *model.InstanceSchedule, machines []machineModel.Machine) (*machineModel.Machine, error) {
	for _, machine := range machines {
		if !svc.checkMachine(instanceSchedule, &machine) {
			continue
		}
		return &machine, nil
	}
	return nil, nil
}

func (svc *Service) checkMachine(instanceSchedule *model.InstanceSchedule, machine *machineModel.Machine) bool {
	gpuNameMeet := false
	for _, v := range instanceSchedule.TemplateEntity.GpuIDSet {
		if machine.GPUTypeID == v {
			gpuNameMeet = true
		}
	}
	if !gpuNameMeet {
		svc.l.WithFields(map[string]interface{}{
			"template.GpuNameSet": instanceSchedule.TemplateEntity.GpuType,
			"machine.GpuName":     machine.GpuName,
		}).Info("scheduler: gpu name not meet-----------------")
		return false
	}

	if int(machine.GPUIdleNum) < instanceSchedule.TemplateEntity.GpuNum {
		svc.l.WithFields(map[string]interface{}{
			"machine.GPUOrderNum": machine.GPUOrderNum,
			"template.GpuNum":     instanceSchedule.TemplateEntity.GpuNum,
		}).Info("scheduler: gpu num not meet-----------------")
		return false
	}

	if machine.MemPerGpu*int64(instanceSchedule.TemplateEntity.GpuNum) < instanceSchedule.TemplateEntity.MinMemorySize {
		svc.l.WithFields(map[string]interface{}{
			"machine.MemPerGpu*int64(queue.TemplateEntity.GpuNum)": machine.MemPerGpu * int64(instanceSchedule.TemplateEntity.GpuNum),
			"queue.TemplateEntity.MinMemorySize":                   instanceSchedule.TemplateEntity.MinMemorySize,
		}).Info("scheduler: memory not meet-----------------")
		return false
	}

	if int(machine.CpuPerGpu)*instanceSchedule.TemplateEntity.GpuNum < instanceSchedule.TemplateEntity.MinCpuNum {
		svc.l.WithFields(map[string]interface{}{
			"int(machine.CpuPerGpu)*queue.TemplateEntity.GpuNum": int(machine.CpuPerGpu) * instanceSchedule.TemplateEntity.GpuNum,
			"queue.TemplateEntity.MinCpuNum":                     instanceSchedule.TemplateEntity.MinCpuNum,
		}).Info("scheduler: cpu not meet-----------------")
		return false
	}

	userMember, err := svc.user.GetUserMemberInfoByUid(instanceSchedule.Uid)
	if err != nil {
		return false
	}

	if userMember.MemberLevel == constant.MemberUser {
		if machine.PaygDiscountPrice*int64(instanceSchedule.TemplateEntity.GpuNum) > instanceSchedule.TemplateEntity.MaxPaygPrice {
			svc.l.WithFields(map[string]interface{}{
				"machine.PaygDiscountPrice":         machine.PaygDiscountPrice,
				"queue.TemplateEntity.MaxPaygPrice": instanceSchedule.TemplateEntity.MaxPaygPrice,
			}).Info("scheduler: payg price not meet-----------------")
			return false
		}
	} else {
		if machine.PaygPrice*int64(instanceSchedule.TemplateEntity.GpuNum) > instanceSchedule.TemplateEntity.MaxPaygPrice {
			svc.l.WithFields(map[string]interface{}{
				"machine.PaygPrice":                 machine.PaygPrice,
				"queue.TemplateEntity.MaxPaygPrice": instanceSchedule.TemplateEntity.MaxPaygPrice,
			}).Info("scheduler: payg price not meet-----------------")
			return false
		}
	}

	if instanceSchedule.TemplateEntity.CudaV != 0 {
		mc, err := strconv.ParseFloat(machine.HighestCudaVersion, 64)
		if err != nil {
			svc.l.WithField("machineCudaV", machine.HighestCudaVersion).ErrorE(err, "parse machine cudaV failed")
			return false
		}
		if instanceSchedule.TemplateEntity.CudaV > int(mc*10) {
			svc.l.WithFields(map[string]interface{}{
				"machine.HighestCudaVersion": mc,
				"queue.TemplateEntity.CudaV": instanceSchedule.TemplateEntity.CudaV,
			}).Info("scheduler: cuda version not meet-----------------")
			return false
		}
	}

	if instanceSchedule.TemplateEntity.ExpandDataDisk != 0 {
		dds, err := svc.dds.Get(nil, machine.MachineID)
		if err != nil {
			svc.l.WithError(err).WithField("machine_id", machine.MachineID).Error("get dds failed")
			return false
		}
		if instanceSchedule.TemplateEntity.ExpandDataDisk > dds.GetAvailable() {
			return false
		}
	}

	return true
}

func (svc *Service) CreateInstanceFromSchedule(schedule *model.InstanceSchedule, machine *machineModel.Machine) (*bcm.Order, error) {
	instanceInfo := constant.CreateContainerTaskRequest{
		Name:             schedule.Remark,
		UID:              schedule.Uid,
		MachineID:        machine.MachineID,
		ChargeType:       constant.ChargeTypePayg,
		ExpandDataDisk:   schedule.TemplateEntity.ExpandDataDisk,
		ReqGPUAmount:     schedule.TemplateEntity.GpuNum,
		ReproductionUUID: schedule.TemplateEntity.CommunityUUID,
		ReproductionID:   schedule.TemplateEntity.CommunityID,
		PrivateImageUUID: schedule.TemplateEntity.PrivateImageUUID,
		Image:            schedule.TemplateEntity.ImageUrl,
	}
	priceInfo := bcm.PriceInfo{
		ChargeType:     constant.ChargeTypePayg,
		Duration:       1,
		ExpandDataDisk: schedule.TemplateEntity.ExpandDataDisk,
		MachineID:      machine.MachineID,
		Num:            schedule.TemplateEntity.GpuNum,
	}

	order, err := svc.bcs.CreateInstance(&bcm.CreateInstanceRequest{
		InstanceInfo: instanceInfo,
		PriceInfo:    priceInfo,
		OperateType:  constant.OrderTypeCreateInstance,
	})
	if err != nil {
		svc.l.WithError(err).Error("CreateInstance failed.")
		return nil, err
	}

	_, _, err = svc.bcs.OrderPay(&bcm.OrderPayCancelParams{
		UID:       schedule.Uid,
		OrderUUID: order.UUID,
	})
	if err != nil {
		svc.l.WithError(err).Error("OrderPay failed.")
		return nil, err
	}

	ro := userModel.OperateRecord{
		UID:           schedule.Uid,
		Operate:       constant.OperateInstanceCreate,
		EntityUUID:    order.ProductUUID,
		PayloadEntity: &instanceInfo,
	}
	err = ro.OperateRecordCreate(nil)
	if err != nil {
		svc.l.WarnE(err, "insert operate record failed")
	}

	return order, nil

}

func (svc *Service) GetInstanceScheduleByID(id int) (*model.InstanceSchedule, error) {
	schedule := &model.InstanceSchedule{}
	err := schedule.InstanceScheduleGetFirst(&db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"id": id,
		},
		NullField: []string{"deleted_at"},
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		svc.l.WithError(err).Error("get instance queue failed")
		return nil, biz.ErrServerBusy
	}
	return schedule, nil
}

func (svc *Service) StopInstanceSchedule(id int) error {
	schedule := &model.InstanceSchedule{}
	err := schedule.InstanceScheduleUpdate(nil, &db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"id": id,
		},
		NullField: []string{"deleted_at"},
	}, map[string]interface{}{
		"status":     constant.InstanceQueueStatusStopped,
		"stopped_at": time.Now(),
	})
	if err != nil {
		svc.l.WithField("id", id).ErrorE(err, "stop instance schedule failed")
		return biz.ErrServerBusy
	}
	return nil
}

func (svc *Service) UpdateIsSendMsgStatus(id int, isSendMsg bool) error {
	schedule := &model.InstanceSchedule{}
	err := schedule.InstanceScheduleUpdate(nil, &db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"id": id,
		},
		NullField: []string{"deleted_at"},
	}, map[string]interface{}{
		"is_send_msg": isSendMsg,
	})
	if err != nil {
		svc.l.WithField("id", id).ErrorE(err, "update IsSendMsgStatus failed")
		return biz.ErrServerBusy
	}
	return nil
}

func (svc *Service) GetWaitingInstanceScheduleList(params model.GetScheduleListParams, pageReq *db_helper.GetPagedRangeRequest) (*db_helper.PagedData, []model.InstanceSchedule, error) {
	db := db_helper.GlobalDBConn().Model(&model.InstanceSchedule{})

	if params.Uid != 0 {
		db = db.Where("uid = ?", params.Uid)
	}
	if params.Status != "" {
		db = db.Where("status = ?", params.Status)
		if params.Status == constant.InstanceQueueStatusSucceeded {
			db = db.Where("created_at > ?", time.Now().Add(-3*24*time.Hour))
		}
	}

	var count int64
	err := db.Count(&count).Error
	if err != nil {
		svc.l.WithError(err).Error("db failed")
		return nil, nil, biz.ErrServerBusy
	}

	var schedules []model.InstanceSchedule
	paged := db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, int(count), 0)

	db = db.Offset(paged.Offset).
		Limit(paged.PageSize)

	if params.Status == constant.InstanceQueueStatusSucceeded {
		db = db.Order("scheduled_at desc")
	} else if params.Status == constant.InstanceQueueStatusWaiting {
		db = db.Order("id desc")
	}

	err = db.Find(&schedules).Error
	if err != nil {
		svc.l.WithError(err).Error("db failed")
		return nil, nil, biz.ErrServerBusy
	}

	paged.List = &schedules

	return paged, schedules, nil
}

func (svc *Service) GetInstanceSchedulePricePreview(uid int, params *model.GetInstanceSchedulePricePreviewReq) (priceInfo model.PriceInfo, err error) {
	var (
		regionList     []regionModel.Region
		regionSignList = make([]constant.RegionSignType, 0)
	)

	regionList, err = svc.region.GetRegionListByDatacenter(nil, params.DataCenter)
	if err != nil {
		return
	}

	for _, v := range regionList {
		if !v.UsedFor.CanInstance() {
			continue
		}
		regionSignList = append(regionSignList, v.Sign)
	}
	if len(regionSignList) == 0 {
		err = biz.ErrInvalidRequestParams
		return
	}
	params.RegionSignList = regionSignList

	if len(params.GpuType) != 0 {
		for _, v := range params.GpuType {
			gpu, err := svc.GpuType.GetGpuByName(v)
			if err != nil {
				svc.l.WithField("gpuName", v).ErrorE(err, "deployment create: check gpu name failed")
				return priceInfo, err
			}
			params.GpuIDSet = append(params.GpuIDSet, gpu.ID)
		}
	}

	var maxPriceMachine, minPriceMachine machineModel.Machine
	db1 := db_helper.GlobalDBConn().Table(machineModel.TableNameMachine).
		Where("region_sign in (?)", params.RegionSignList).
		Where("on_line in (?)", []constant.OnOffLine{constant.Online}).
		Where("deleted_at is null")

	if len(params.GpuIDSet) != 0 {
		db1 = db1.Where("gpu_type_id in (?)", params.GpuIDSet)
	}

	err = db1.Order("payg_price desc").Limit(1).
		Scan(&maxPriceMachine).Error

	if err != nil {
		svc.l.WithFields(map[string]interface{}{
			"gpu_name":    params.GpuType,
			"region_sign": params.RegionSignList,
		}).ErrorE(err, "get max Price machine for schedule instance failed")
		return model.PriceInfo{}, err
	}

	if maxPriceMachine.ID == 0 {
		return model.PriceInfo{}, biz.ErrScheduleInstanceMachineNotExist
	}

	db2 := db_helper.GlobalDBConn().Table(machineModel.TableNameMachine).
		Where("region_sign in (?)", params.RegionSignList).
		Where("on_line in (?)", []constant.OnOffLine{constant.Online}).
		Where("deleted_at is null")

	if len(params.GpuIDSet) != 0 {
		db2 = db2.Where("gpu_type_id in (?)", params.GpuIDSet)
	}

	err = db2.Order("payg_price asc").Limit(1).
		Scan(&minPriceMachine).Error

	if err != nil {
		svc.l.WithFields(map[string]interface{}{
			"gpu_name":    params.GpuType,
			"region_sign": params.RegionSignList,
		}).ErrorE(err, "get min Price machine for schedule instance failed")
		return model.PriceInfo{}, err
	}
	getPriceMaxMachineReq := bcm.PriceInfo{
		ChargeType:     constant.ChargeTypePayg,
		Duration:       1,
		ExpandDataDisk: params.ExpandDataDisk,
		MachineID:      maxPriceMachine.MachineID,
		Num:            params.Num,
	}
	err = svc.bcs.GetPricePreview(&bcm.GetPricePreviewParams{
		UID:       uid,
		OptType:   constant.OrderTypeCreateInstance,
		PriceInfo: &getPriceMaxMachineReq,
	})
	if err != nil {
		svc.l.WithField("machine_id", maxPriceMachine.MachineID).ErrorE(err, "get max Price machine price preview failed")
		return model.PriceInfo{}, err
	}

	getPriceMinMachineReq := bcm.PriceInfo{
		ChargeType:     constant.ChargeTypePayg,
		Duration:       1,
		ExpandDataDisk: params.ExpandDataDisk,
		MachineID:      minPriceMachine.MachineID,
		Num:            params.Num,
	}
	err = svc.bcs.GetPricePreview(&bcm.GetPricePreviewParams{
		UID:       uid,
		OptType:   constant.OrderTypeCreateInstance,
		PriceInfo: &getPriceMinMachineReq,
	})
	if err != nil {
		svc.l.WithField("machine_id", minPriceMachine.MachineID).ErrorE(err, "get min Price machine price preview failed")
		return model.PriceInfo{}, err
	}
	priceInfo = model.PriceInfo{
		MinPriceInfo: getPriceMinMachineReq,
		MaxPriceInfo: getPriceMaxMachineReq,
	}
	return priceInfo, nil
}
