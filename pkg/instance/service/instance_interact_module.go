package service

import (
	"server/pkg/constant"
	imageModel "server/pkg/image/model"
	"server/pkg/libs"
	"server/pkg/logger"
	machineModel "server/pkg/machine/model"
	"server/pkg/module_definition"
	regionModel "server/pkg/region/model"
	"server/pkg/user/model"
)

const secNameModuleHandler = "instance_to_other_module"

type ModuleHandler interface {
	TryToGetUserPhone(uid int) string // just for frontend, omit err
	GetPossibleUidListByFuzzyPhone(fuzzyPhone string) ([]int, error)
	SubUserCheckBelong(uid int, suList []string) (ok bool, err error)
	FindByUserPhone(phone string) (userInfo *model.User, err error)

	CheckMachineRealHealth(machineID string) bool
	CheckMachineBusinessHealth(machineID string) bool
	DetailMachine(machineID string) (detail machineModel.MachineDetailInfo, err error)

	GetUserByUID(uid int) (user *model.User, err error)
	GetUserByUIDs(uid []int) (user []*model.User, err error)
	GetUserByPhone(phone string) (user *model.User, err error)
	GetUserMemberInfo(uid int) (userMember *model.UserMember, err error)

	GetRegionNameByRegionSign(regionSign constant.RegionSignType) (regionName string, err error)
	TryToGetRegionNameMap(regionSignList []constant.RegionSignType) map[constant.RegionSignType]regionModel.Region

	PublicImageReproduce(uid int, reproductionUUID string)
	GetImage(imageUUID string) (commonImage *imageModel.ImageCommonInfo, err error)
}

// --------------------------------------------------------

type MH struct {
	container    module_definition.ContainerRuntimeInterface
	machine      module_definition.MachineInference
	region       module_definition.RegionInterface
	image        module_definition.ImageInference
	privateImage module_definition.PrivateImageInterface
	port         module_definition.PortInterface
	user         module_definition.UserInterface
	bc           module_definition.BCInterface

	l *logger.Logger
}

func NewModuleHandler(
	container module_definition.ContainerRuntimeInterface,
	machine module_definition.MachineInference,

	region module_definition.RegionInterface,
	image module_definition.ImageInference,
	privateImage module_definition.PrivateImageInterface,
	port module_definition.PortInterface,
	user module_definition.UserInterface,
	bc module_definition.BCInterface,
) *MH {
	return &MH{
		container:    container,
		machine:      machine,
		region:       region,
		image:        image,
		privateImage: privateImage,
		port:         port,
		user:         user,
		bc:           bc,
		l:            logger.NewLogger(secNameModuleHandler),
	}
}

func (m *MH) TryToGetUserPhone(uid int) string {
	user, err := m.user.FindByUserId(uid)
	if err != nil {
		m.l.WithField("uid", uid).WarnE(err, "FindByUserId() in TryToGetUserPhone() failed.")
		return ""
	}
	return user.Phone
}

func (m *MH) GetUserByUID(uid int) (user *model.User, err error) {
	return m.user.FindByUserId(uid)
}

func (m *MH) GetUserByUIDs(uid []int) (user []*model.User, err error) {
	return m.user.FindByUserIds(uid)
}

func (m *MH) GetUserByPhone(phone string) (user *model.User, err error) {
	return m.user.FindByUserPhone(phone)
}

func (m *MH) GetUserMemberInfo(uid int) (userMember *model.UserMember, err error) {
	return m.user.GetUserMemberInfoByUid(uid)
}

func (m *MH) GetPossibleUidListByFuzzyPhone(fuzzyPhone string) (uidList []int, err error) {
	return m.user.GetPossibleUidListByFuzzyPhone(fuzzyPhone)
}

func (m *MH) FindByUserPhone(phone string) (userInfo *model.User, err error) {
	return m.user.FindByUserPhone(phone)
}

func (m *MH) SubUserCheckBelong(uid int, suList []string) (ok bool, err error) {
	return m.user.SubUserCheckBelong(uid, suList)
}

func (m *MH) CheckMachineRealHealth(machineID string) bool {
	ok, err := m.machine.CheckMachineStatusHealth(machineID)
	if err != nil {
		m.l.WithField("machine_id", machineID).WarnE(err, "CheckMachineRealHealth() failed.")
		ok = false
		return ok
	}

	return ok
}

func (m *MH) CheckMachineBusinessHealth(machineID string) bool {
	ok, err := m.machine.CheckMachineHealth(machineID)
	if err != nil {
		m.l.WithField("machine_id", machineID).WarnE(err, "CheckMachineBusinessHealth() failed.")
		ok = false
		return ok
	}

	return ok
}

func (m *MH) DetailMachine(machineID string) (detail machineModel.MachineDetailInfo, err error) {
	var d *machineModel.MachineDetailInfo
	d, err = m.machine.Detail(machineID)
	if err != nil {
		return
	}
	d.MachineInstanceInfo = nil
	return *d, err
}

func (m *MH) GetRegionNameByRegionSign(regionSign constant.RegionSignType) (regionName string, err error) {
	region, err := m.region.GetRegionDetail(regionSign)
	if err != nil {
		return
	}

	regionName = region.Name
	return
}

func (m *MH) TryToGetRegionNameMap(regionSignList []constant.RegionSignType) (regionMap map[constant.RegionSignType]regionModel.Region) {
	regionMap = make(map[constant.RegionSignType]regionModel.Region)

	regions, err := m.region.GetRegionList()
	if err != nil {
		return
	}

	for _, region := range regions {
		regionMap[region.Sign] = region
	}
	return
}

func (m *MH) PublicImageReproduce(uid int, reproductionUUID string) {
	_ = m.privateImage.CodeWithGpuReproduce(uid, reproductionUUID)
}

func (m *MH) GetImage(imageUUID string) (commonImage *imageModel.ImageCommonInfo, err error) {
	if !libs.IsCommunityImageUUID(imageUUID) {
		var pi *imageModel.PrivateImage
		pi, err = m.getPrivateImage(imageUUID)
		if err != nil {
			return
		}
		commonImage = pi.ToImageCommonInfo()
	} else {
		var pic *imageModel.CommunityImage
		pic, err = m.getPrivateImageCommunity(imageUUID)
		if err != nil {
			return
		}
		commonImage = pic.ToImageCommonInfo()
	}
	return
}

func (m *MH) getPrivateImage(imageUUID string) (image *imageModel.PrivateImage, err error) {
	image, err = m.privateImage.GetImageByUUIDWithStorageInfo(imageUUID)
	if err != nil {
		m.l.WithField("err", err).WithField("image_uuid", imageUUID).Error("get private image info failed.")
		return
	}
	return
}
func (m *MH) getPrivateImageCommunity(imageUUID string) (image *imageModel.CommunityImage, err error) {
	image, err = m.privateImage.CommunityImageGetUnScoped(imageUUID, true)
	if err != nil {
		m.l.WithField("err", err).WithField("image_uuid", imageUUID).Error("get private image info failed.")
		return
	}
	return
}
