package service

import (
	"context"
	"encoding/json"
	"fmt"
	"server/conf"
	coreContainerModel "server/pkg-core/container_runtime/model"
	bcm "server/pkg/billing_center/model"
	"server/pkg/constant"
	"server/pkg/db_helper"
	instanceModel "server/pkg/instance/model"
	"server/pkg/libs"
	"server/pkg/logger"
	machineModel "server/pkg/machine/model"
	"server/pkg/module_definition"
	"server/pkg/notify"
	notifyModel "server/pkg/notify/model"
	userModel "server/pkg/user/model"
	"server/plugin/queue"
	tsc "server/plugin/redis_plugin/time_service_center"
	"sync"
	"time"

	"gorm.io/gorm"
)

// CRON

func (svc *Service) CronJobRegister(ctx context.Context, cron *libs.Cron) {
	// 找到运行中的, 过期的包年包月并关掉.
	go svc.cronExpiredRunningNonPayg(ctx)

	// 找到关机的, 但没释放 GPU 的过期的包年包月, 释放 GPU.
	go svc.cronExpiredHaveGpusNonPayg(ctx)

	// 找到过期的包年包月的并开启自动转按量付费的实例，转换成按量付费
	go svc.cronExpiredNonPaygAutoTransToPayg(ctx)

	// 免费保存 30 天的过期实例校验, 条件为包年包月过期与按量付费关机.
	go svc.CronNeverFreelyRetainedInstances(ctx, svc.batchRemoveInstance)

	//go svc.cronMigrateInstanceOverRetainLimit(ctx)

	// 每隔一个较慢的时间间隔, 从 container 中同步状态, 作为保底. 如果确实发生变化, 同样触发钩子. 仅同步非 record_inserted 的.
	go svc.cronSyncStatusFromContainer(ctx)

	// 定时关机. 后期优化为通用的定时任务.
	go svc.cronCheckInstanceTimedShutdown(ctx)

	// 定时关机被禁用用户的实例.
	go svc.cronShutdownInstanceOfDisableUser(ctx)

	// 通知预警定时任务，释放和到期的处理逻辑不同，这里直接拆出来两个方法处理逻辑
	go svc.cronNotifyReleaseWarning(ctx)
	go svc.cronNotifyExpireWarning(ctx)
}

func (svc *Service) CoreScheduleInstance(ctx context.Context, cron *libs.Cron) {
	go svc.cronCreateInstanceFromSchedule(ctx)        // 调度创建实例
	go svc.coreStopExpireWaitingScheduleInstance(ctx) // 超过三天未调度的实例取消调度
}

func (svc *Service) MsgRegister() (info []queue.RegisterInfo) {
	return
}

// 找到过期的包年包月的并开启自动转按量付费的实例，转换成按量付费
func (svc *Service) cronExpiredNonPaygAutoTransToPayg(ctx context.Context) {
	l := svc.l.WithField("sub_section", "cronExpiredNonPaygAutoTransToPayg")

	ticker := time.NewTicker(time.Second * 30) // 30s

	for {
		// ticker...
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
		}

		instances, err := svc.crud.GetExpiredNonPaygAutoTransToPaygInstances()
		if err != nil {
			continue
		}

		l.Info("GetExpiredNonPaygAutoTransToPaygInstances count:%+v", len(instances))

		if len(instances) == 0 {
			continue
		}

		wg := sync.WaitGroup{}
		for i := range instances {
			instance := instances[i]
			l = l.WithField("uuid", instance.InstanceUUID)

			// 如果没有开启自动转按量计费则跳过
			if instance.AutoTransToPayg == constant.AutoTransToPaygClosed {
				continue
			}
			// 如果实例的上一次更新时间小于60s则跳过，防止上一个定时任务中的消息处理时下一个定时任务开始执行造成产生两个订单
			if instance.LastAutoTransToPaygTime.Valid && time.Now().Sub(instance.LastAutoTransToPaygTime.Time).Seconds() < 60 {
				continue
			}
			wg.Add(1)
			go func(instance *instanceModel.Instance) {
				defer wg.Done()

				l.Info("auto trans to payg start...")

				// 先更新自动转换的时间，这里不需要事务，先更新时间后即使后面的转换失败了，下次还会再尝试转换（只要间隔上面的60s）
				update := map[string]interface{}{
					"last_auto_trans_to_payg_time": time.Now(),
					"updated_at":                   time.Now(),
				}
				err = svc.crud.UpdateInstanceMap(nil, instance.InstanceUUID, update)
				if err != nil {
					l.WithError(err).Error("auto trans to payg failed")
					return
				}
				_, err = svc.bcs.PrepayToPayg(&bcm.PrepayToPaygParams{InstanceUUID: instance.InstanceUUID})
				if err != nil {
					// 如果转换失败则打印日志，人工处理
					l.WithError(err).Error("auto trans to payg failed")
					return
				}
				l.WithFields(logger.Fields{
					"instance_uuid":      instance.InstanceUUID,
					"charge_type":        instance.ChargeType,
					"auto_trans_to_payg": instance.AutoTransToPayg,
					"now_at":             svc.t.Now(),
				}).Info("cronExpiredNonPaygAutoTransToPayg instance by cron.")
			}(instance)

			wg.Wait()
		}
	}
}

// 找到运行中的, 过期的包年包月并关掉
func (svc *Service) cronExpiredRunningNonPayg(ctx context.Context) {
	ticker := time.NewTicker(constant.InstanceCheckExpiredRunningNonPaygInterval) // 30s

	for {
		// ticker...
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
		}

		instances, err := svc.crud.GetRunningExpiredNonPaygInstances()
		if err != nil {
			continue
		}

		if len(instances) == 0 {
			continue
		}

		for i, ins := range instances {
			if instances[i].AutoTransToPayg == constant.AutoTransToPaygOpened {
				continue
			}
			var stopOpt = constant.OptContainerReq{
				RuntimeUUID: instances[i].RuntimeUUID,
				Opt:         constant.ContainerStopOpt,
				Caller:      constant.OptCallerInstanceCron,
				OptAt:       svc.t.Now(),
			}

			err = svc.container.OperateContainer(stopOpt, constant.OptContainerAuxParams{})
			if err != nil {
				svc.l.WithFields(logger.Fields{
					"instance_uuid": instances[i].InstanceUUID,
					"expire_at":     instances[i].ExpiredAt,
					"charge_type":   instances[i].ChargeType,
					"now_at":        svc.t.Now(),
				}).WarnE(err, "Send releaseGpuOpt to container failed.")
				continue
			}

			ro := userModel.OperateRecord{
				UID:        ins.UID,
				Operate:    constant.OperateInstancePowerOffByPrepayExpired,
				EntityUUID: ins.InstanceUUID.String(),
				SubName:    ins.SubName,
			}
			_ = ro.OperateRecordCreate(nil)

			var releaseGpuOpt = constant.OptContainerReq{
				RuntimeUUID: instances[i].RuntimeUUID,
				Opt:         constant.ContainerReleaseGpuOpt,
				Caller:      constant.OptCallerInstanceCron,
				OptAt:       svc.t.Now(),
				DebugMsg:    "instance cronExpiredRunningNonPayg",
				Timestamp:   tsc.Timestamp(),
			}

			err = svc.container.OperateContainer(releaseGpuOpt, constant.OptContainerAuxParams{})
			if err != nil {
				svc.l.WithFields(logger.Fields{
					"instance_uuid": instances[i].InstanceUUID,
					"expire_at":     instances[i].ExpiredAt,
					"charge_type":   instances[i].ChargeType,
					"now_at":        svc.t.Now(),
				}).WarnE(err, "Send releaseGpuOpt to container failed.")
				continue
			}

			err = svc.crud.ClearHaveGpuFlag(instances[i].InstanceUUID)
			if err != nil {
				svc.l.WithFields(logger.Fields{
					"instance_uuid": instances[i].InstanceUUID,
					"expire_at":     instances[i].ExpiredAt,
					"charge_type":   instances[i].ChargeType,
					"now_at":        svc.t.Now(),
				}).WarnE(err, "ClearHaveGpuFlag() for the stopped expired instance by cron failed.")
				continue
			}

			svc.l.WithFields(logger.Fields{
				"instance_uuid": instances[i].InstanceUUID,
				"expire_at":     instances[i].ExpiredAt,
				"charge_type":   instances[i].ChargeType,
				"now_at":        svc.t.Now(),
			}).Info("Stopped and released_gpu for the running expired instance by cron.")
		}
	}
}

// 找到关机的, 但没释放 GPU 的过期的包年包月, 释放 GPU
func (svc *Service) cronExpiredHaveGpusNonPayg(ctx context.Context) {
	ticker := time.NewTicker(constant.InstanceCheckExpiredHaveGpusNonPaygInterval) // 10s

	for {
		// ticker...
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
		}

		instances, err := svc.crud.GetStoppedHaveGpuExpiredNonPaygInstances()
		if err != nil {
			continue
		}

		if len(instances) == 0 {
			continue
		}

		for i := range instances {
			if instances[i].AutoTransToPayg == constant.AutoTransToPaygOpened {
				continue
			}
			var releaseGpuOpt = constant.OptContainerReq{
				RuntimeUUID: instances[i].RuntimeUUID,
				Opt:         constant.ContainerReleaseGpuOpt,
				Caller:      constant.OptCallerInstanceCron,
				OptAt:       svc.t.Now(),
				DebugMsg:    "instance prepay expired",
				Timestamp:   tsc.Timestamp(),
			}

			err = svc.container.OperateContainer(releaseGpuOpt, constant.OptContainerAuxParams{})
			if err != nil {
				svc.l.WithFields(logger.Fields{
					"instance_uuid": instances[i].InstanceUUID,
					"expire_at":     instances[i].ExpiredAt,
					"charge_type":   instances[i].ChargeType,
					"now_at":        svc.t.Now(),
				}).WarnE(err, "Send releaseGpuOpt to container failed.")
				continue
			}

			err = svc.crud.ClearHaveGpuFlag(instances[i].InstanceUUID)
			if err != nil {
				svc.l.WithFields(logger.Fields{
					"instance_uuid": instances[i].InstanceUUID,
					"expire_at":     instances[i].ExpiredAt,
					"charge_type":   instances[i].ChargeType,
					"now_at":        svc.t.Now(),
				}).WarnE(err, "ClearHaveGpuFlag() for the stopped expired instance by cron failed.")
				continue
			}

			svc.l.WithFields(logger.Fields{
				"instance_uuid": instances[i].InstanceUUID,
				"expire_at":     instances[i].ExpiredAt,
				"charge_type":   instances[i].ChargeType,
				"now_at":        svc.t.Now(),
			}).Info("Released_gpu for the stopped expired instance by cron.")
		}
	}
}

// CronNeverFreelyRetainedInstances 免费保存 30 天的过期实例校验, 条件为包年包月过期与按量付费关机.
func (svc *Service) CronNeverFreelyRetainedInstances(ctx context.Context, rmFunc func(instancesToBeRemoved []*instanceModel.Instance)) {
	var findAndRemove = func() {
		days30 := constant.InstanceFreelyRetained30DaysTimeout
		days15 := constant.InstanceFreelyRetained15DaysTimeout

		// if conf.GetGlobalGsConfig().App.DebugApi {
		//	days = 30 * time.Minute // 30 min for debug
		// }

		var deadline30 = svc.t.Now().Add(-days30)
		var deadline15 = svc.t.Now().Add(-days15)

		// do not use 'order by'
		var batchSize = 100
		var batches []*instanceModel.Instance

		// 对于按量付费的，校验 stopped_at
		err := db_helper.GlobalDBConn().
			Model(&instanceModel.Instance{}).
			Where("stopped_at < ?", deadline30).
			Where("status in (?)", NeverFreelyRetainedStatusSet).
			Where("charge_type = ?", constant.ChargeTypePayg).
			Where("created_at < '2023-07-20 00:00:00'").
			FindInBatches(&batches, batchSize, func(tx *gorm.DB, batch int) error {
				// DO REMOVE
				rmFunc(batches)
				return nil
			}).Error
		if err != nil {
			svc.l.WarnE(err, "Failed to remove all 'payg' instances which was never freely retained.")
			return
		}

		err = db_helper.GlobalDBConn().
			Model(&instanceModel.Instance{}).
			Where("stopped_at < ?", deadline15).
			Where("status in (?)", NeverFreelyRetainedStatusSet).
			Where("charge_type = ?", constant.ChargeTypePayg).
			Where("created_at >= '2023-07-20 00:00:00'").
			FindInBatches(&batches, batchSize, func(tx *gorm.DB, batch int) error {
				// DO REMOVE
				rmFunc(batches)
				return nil
			}).Error
		if err != nil {
			svc.l.WarnE(err, "Failed to remove all 'payg' instances which was never freely retained.")
			return
		}

		// 特殊情况, 按量付费的, 创建异常,执行重置后没开机的实例,没有stoppedAt值,会无法释放
		err = db_helper.GlobalDBConn().
			Model(&instanceModel.Instance{}).
			Where("status_at < ?", deadline30).
			Where("status  = ?", constant.InstanceShutdown).
			Where("charge_type = ?", constant.ChargeTypePayg).
			Where("stopped_at = '' or isnull(stopped_at)=1").
			Where("created_at < '2023-07-20 00:00:00'").
			FindInBatches(&batches, batchSize, func(tx *gorm.DB, batch int) error {
				// DO REMOVE
				rmFunc(batches)
				return nil
			}).Error
		if err != nil {
			svc.l.WarnE(err, "Failed to remove all 'payg' instances which was never freely retained.")
			return
		}

		err = db_helper.GlobalDBConn().
			Model(&instanceModel.Instance{}).
			Where("status_at < ?", deadline15).
			Where("status  = ?", constant.InstanceShutdown).
			Where("charge_type = ?", constant.ChargeTypePayg).
			Where("stopped_at = '' or isnull(stopped_at)=1").
			Where("created_at >= '2023-07-20 00:00:00'").
			FindInBatches(&batches, batchSize, func(tx *gorm.DB, batch int) error {
				// DO REMOVE
				rmFunc(batches)
				return nil
			}).Error
		if err != nil {
			svc.l.WarnE(err, "Failed to remove all 'payg' instances which was never freely retained.")
			return
		}

		// 对于非按量付费的，校验 expired_at
		err = db_helper.GlobalDBConn().
			Model(&instanceModel.Instance{}).
			Where("expired_at < ?", deadline30).
			Where("status in (?)", NeverFreelyRetainedStatusSet).
			Not("charge_type = ?", constant.ChargeTypePayg).
			Where("created_at < '2023-07-20 00:00:00'").
			FindInBatches(&batches, batchSize, func(tx *gorm.DB, batch int) error {
				// DO REMOVE
				rmFunc(batches)
				return nil
			}).Error
		if err != nil {
			svc.l.WarnE(err, "Failed to remove all 'non payg' instances which was never freely retained.")
			return
		}

		err = db_helper.GlobalDBConn().
			Model(&instanceModel.Instance{}).
			Where("expired_at < ?", deadline15).
			Where("status in (?)", NeverFreelyRetainedStatusSet).
			Not("charge_type = ?", constant.ChargeTypePayg).
			Where("created_at >= '2023-07-20 00:00:00'").
			FindInBatches(&batches, batchSize, func(tx *gorm.DB, batch int) error {
				// DO REMOVE
				rmFunc(batches)
				return nil
			}).Error
		if err != nil {
			svc.l.WarnE(err, "Failed to remove all 'non payg' instances which was never freely retained.")
			return
		}
	}

	// 潮汐算力3天到期就释放
	var findAndRemoveWaveInstance = func() {
		var days = constant.InstanceWaveAreaFreelyRetained3DaysTimeout // 30 days

		// if conf.GetGlobalGsConfig().App.DebugApi {
		//	days = 30 * time.Minute // 30 min for debug
		// }

		var deadline = svc.t.Now().Add(-days)

		// do not use 'order by'
		var batchSize = 100
		var batches []*instanceModel.Instance

		// 对于按量付费的，校验 stopped_at
		err := db_helper.GlobalDBConn().
			Model(&instanceModel.Instance{}).
			Where("stopped_at < ?", deadline).
			Where("region_sign = ?", constant.WaveRegionDefault).
			Where("status in (?)", NeverFreelyRetainedStatusSet).
			Where("charge_type = ?", constant.ChargeTypePayg).
			FindInBatches(&batches, batchSize, func(tx *gorm.DB, batch int) error {
				// DO REMOVE
				rmFunc(batches)
				return nil
			}).Error
		if err != nil {
			svc.l.WarnE(err, "Failed to remove all 'payg' instances which was never freely retained.")
			return
		}

		// 对于非按量付费的，校验 expired_at
		err = db_helper.GlobalDBConn().
			Model(&instanceModel.Instance{}).
			Where("expired_at < ?", deadline).
			Where("region_sign = ?", constant.WaveRegionDefault).
			Where("status in (?)", NeverFreelyRetainedStatusSet).
			Not("charge_type = ?", constant.ChargeTypePayg).
			FindInBatches(&batches, batchSize, func(tx *gorm.DB, batch int) error {
				// DO REMOVE
				rmFunc(batches)
				return nil
			}).Error
		if err != nil {
			svc.l.WarnE(err, "Failed to remove all 'non payg' instances which was never freely retained.")
			return
		}
	}

	ticker := time.NewTicker(40 * time.Second)
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			findAndRemove()
			findAndRemoveWaveInstance()
		}
	}
}

func (svc *Service) cronMigrateInstanceOverRetainLimit(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Minute)

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:

		}

		var days = constant.InstanceAbortFreelyRetained7DaysTimeout // 7 days

		// if conf.GetGlobalGsConfig().App.DebugApi {
		// 	days = constant.InstanceAbortFreelyRetainedTestTimeout // 20 min
		// }

		migrateInstances, err := svc.container.GetMigrateRecordNeedToRemove(days)
		if err != nil {
			svc.l.WarnE(err, "get migrate record need to remove failed")
			return
		}

		for i := range migrateInstances {
			var rmOpt = constant.OptContainerReq{
				RuntimeUUID: migrateInstances[i].RuntimeUUID,
				Opt:         constant.ContainerRemoveOpt,
				Caller:      constant.OptCallerInstanceCron,
				OptAt:       svc.t.Now(),
				Timestamp:   tsc.Timestamp(),
			}

			err = svc.container.OperateContainer(rmOpt, constant.OptContainerAuxParams{})
			if err != nil {
				svc.l.WithFields(logger.Fields{
					"runtime_uuid": migrateInstances[i].RuntimeUUID,
					"update_at":    migrateInstances[i].UpdatedAt,
					"now_at":       svc.t.Now(),
				}).WarnE(err, "Cron Operate Container rm opt failed.")
				continue
			}

			svc.l.WithFields(logger.Fields{
				"runtime_uuid": migrateInstances[i].RuntimeUUID,
				"update_at":    migrateInstances[i].UpdatedAt,
				"now_at":       svc.t.Now(),
			}).Info("Send rm opt for the migrate instance freely by cron.")
		}
	}
}

// 每隔一个较慢的时间间隔, 从 container 中同步状态, 作为保底. 如果确实发生变化, 同样触发钩子.
func (svc *Service) cronSyncStatusFromContainer(ctx context.Context) {
	ticker := time.NewTicker(constant.InstanceSyncStatusFromContainerInterval) // 30s

	for {
		// ticker...
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
		}

		var instances []instanceModel.Instance
		err := db_helper.GlobalDBConn().Model(&instanceModel.Instance{}).FindInBatches(&instances, 25, func(tx *gorm.DB, batch int) error {
			if len(instances) == 0 {
				return nil
			}

			var runtimeUUIDList []constant.ContainerRuntimeUUID

			for i := range instances {
				if instances[i].Status == "" {
					continue
				}
				runtimeUUIDList = append(runtimeUUIDList, instances[i].RuntimeUUID)
			}

			infoList, err := svc.container.GetContainerSimplifiedInfoList(runtimeUUIDList, constant.ContainerRuntimeOfInstance)
			if err != nil {
				svc.l.WarnE(err, "Get container simplified info for instance failed. runtimeUUIDList=%+v", runtimeUUIDList)
				return err
			}

			var infoMap = make(map[constant.ContainerRuntimeUUID]*coreContainerModel.ContainerSimplifiedInfo)

			for i := range infoList {
				infoMap[infoList[i].RuntimeUUID] = infoList[i]
			}

			for _, instance := range instances {
				info := infoMap[instance.RuntimeUUID]

				if info == nil {
					continue
				}

				instanceLatestStatus := constant.InstanceStatusType(info.Status)

				// 1. sync started_at, stopped_at
				if instance.StartedAt != info.StartedAt || instance.StoppedAt != info.StoppedAt {
					l := svc.l.WithFields(logger.Fields{
						"instance_uuid":  instance.InstanceUUID,
						"old_started_at": instance.StartedAt,
						"old_stopped_at": instance.StoppedAt,
						"new_started_at": info.StartedAt,
						"new_stopped_at": info.StoppedAt,
					})
					err = svc.crud.UpdateInstanceStartedAtAndStoppedAt(instance.InstanceUUID, &info.StartedAt, &info.StoppedAt)
					if err != nil {
						l.WarnE(err, "UpdateInstanceStartedAtAndStoppedAt failed.")
						continue
					}
					l.Info("instance started_at/updated_at is updated by cron from container")
				}

				// 2. sync status
				if instance.Status == instanceLatestStatus || instanceLatestStatus == "" {
					continue // no change
				}

				// NOTICE
				svc.l.Trace("Sync instance [%s] status '%s' by container [%s]. Latest instance status is: '%s'", instance.InstanceUUID, instance.Status, instance.RuntimeUUID, instanceLatestStatus)

				err = svc.hookOfInstanceStatus(instance.InstanceUUID, instanceLatestStatus, info.OOMKilled, info.StatusAt, info.StatusId)
				if err != nil {
					svc.l.WithFields(logger.Fields{
						"instance_uuid": instance.InstanceUUID,
						"status":        instanceLatestStatus,
						"oom_killed":    info.OOMKilled,
						"status_at":     info.StatusAt,
					}).WarnE(err, "Update instance status in cron failed.")
					continue
				}
			}

			return nil
		}).Error
		if err != nil {
			svc.l.ErrorE(err, "Sync instance in batch failed.")
		}
	}
}

// 定时关机.
func (svc *Service) cronCheckInstanceTimedShutdown(ctx context.Context) {
	ticker := time.NewTicker(constant.InstanceCheckTimedShutdownInterval) // 30s

	for {
		// ticker...
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
		}

		nowAt := svc.t.Now()
		instances, err := svc.crud.GetAllInstancesRunningWithTimedShutdownAt(nowAt)
		if err != nil {
			svc.l.WarnE(err, "Get all running instances to timed shutdown failed.")
			continue
		}

		if len(instances) == 0 {
			continue
		}

		for i := range instances {
			scheduleShutdown := &instanceModel.ScheduledShutdownParams{ScheduledShutdownAtSnapshot: instances[i].ScheduledShutdownAtSnapshot}
			scheduleShutdownAtSnapshot, _ := json.Marshal(scheduleShutdown)
			// 直接调用, 失败还可以下次轮询.
			err = svc.OperateInstance(constant.OptInstanceReq{
				InstanceUUID: instances[i].InstanceUUID.String(),
				OptType:      constant.InstanceStopOpt,
				Caller:       constant.OptCallerInstanceTimed,
				Payload:      string(scheduleShutdownAtSnapshot),
			})
			if err != nil {
				svc.l.WithFields(logger.Fields{
					"instance_uuid":         instances[i].InstanceUUID.String(),
					"scheduled_shutdown_at": instances[i].ScheduledShutdownAt.Time.String(),
					"now_at":                nowAt,
				}).WarnE(err, "Cron Timed Shutdown an instance failed.")
				continue
			}

			svc.l.WithFields(logger.Fields{
				"instance_uuid":         instances[i].InstanceUUID.String(),
				"scheduled_shutdown_at": instances[i].ScheduledShutdownAt.Time.String(),
				"now_at":                nowAt,
			}).Info("Instance timed shutdown opt was called.")
		}
	}
}

// 关机被禁用用户的实例.
func (svc *Service) cronShutdownInstanceOfDisableUser(ctx context.Context) {
	ticker := time.NewTicker(constant.InstanceCheckTimedShutdownInterval) // 30s

	for {
		// ticker...
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
		}

		instances, err := svc.crud.GetRunningInstances()
		if err != nil {
			svc.l.WarnE(err, "Get all running instances to timed shutdown failed.")
			continue
		}

		if len(instances) == 0 {
			continue
		}

		var userIdList []int
		var userInstancesMapping = make(map[int][]*instanceModel.Instance)
		for i := range instances {
			uid := instances[i].UID
			if _, ok := userInstancesMapping[uid]; !ok {
				userIdList = append(userIdList, uid)
				userInstancesMapping[uid] = []*instanceModel.Instance{instances[i]}
			} else {
				userInstancesMapping[uid] = append(userInstancesMapping[uid], instances[i])
			}
		}

		var users []*userModel.User
		users, err = svc.mod.GetUserByUIDs(userIdList)
		if err != nil {
			svc.l.WithFields(logger.Fields{
				"userIdList": userIdList,
			}).WarnE(err, "Cron Shutdown disable user instance get user failed.")
			continue
		}

		for i := range users {
			if users[i].Status == userModel.Disable {
				instancesShouldShutdown := userInstancesMapping[users[i].ID]

				for i := range instancesShouldShutdown {
					// 直接调用, 失败还可以下次轮询.
					err = svc.OperateInstance(constant.OptInstanceReq{
						InstanceUUID: instancesShouldShutdown[i].InstanceUUID.String(),
						OptType:      constant.InstanceStopOpt,
						Caller:       constant.OptCallerInstanceDisableUser,
					})
					if err != nil {
						svc.l.WithFields(logger.Fields{
							"instance_uuid": instancesShouldShutdown[i].InstanceUUID.String(),
						}).WarnE(err, "Cron Shutdown disable user instance failed.")
						continue
					}

					svc.l.WithFields(logger.Fields{
						"instance_uuid": instancesShouldShutdown[i].InstanceUUID.String(),
					}).Info("Instance Shutdown disable user instance was called.")
				}
			}
		}

	}
}

/******************************************* 预警 start ***************************************************/

// notifyWarningPoint 通知预警时间点
type notifyWarningPoint uint8

const (
	notifyWarningPoint6  notifyWarningPoint = 6
	notifyWarningPoint12 notifyWarningPoint = 12
	notifyWarningPoint24 notifyWarningPoint = 24
)

// notifyWarningError 通知预警错误
type notifyWarningError struct {
	instanceUUID string
	err          error
}

type releaseInstance struct {
	instanceModel.Instance
	isMachineRentExpired bool
	machineRentDeadline  time.Time
}

// cronNotifyReleaseWarning 定时任务释放预警（给用户发送消息）
func (svc *Service) cronNotifyReleaseWarning(ctx context.Context) {
	time.Sleep(time.Second * 10)

	action := newNotifyReleaseWarningAction(svc.crud, svc.user, svc.machine, svc.notify, notify.NewWeixinChannel(), notify.NewSmsChannel())
	action.doV2()

	tick := time.Minute * 10
	if conf.GetGlobalGsConfig().App.DebugApi {
		tick = time.Minute
	}

	ticker := time.NewTicker(tick)
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			action.doV2()
		}
	}
}

// notifyReleaseWarningAction 通知释放预警动作
type notifyReleaseWarningAction struct {
	logger           *logger.Logger
	crud             CRUDHandler
	user             module_definition.UserInterface
	machine          module_definition.MachineInference
	notify           module_definition.NotifyInterface
	notifyChannel    notify.Channel
	smsNotifyChannel notify.Channel
	currentTime      time.Time
	instances        []releaseInstance
	uidOpenIDRelate  map[int]string
	uidPhoneRelate   map[int]string
}

func newNotifyReleaseWarningAction(crud CRUDHandler, user module_definition.UserInterface, machine module_definition.MachineInference, notify module_definition.NotifyInterface, notifyChannel notify.Channel, smsNotifyChannel notify.Channel) *notifyReleaseWarningAction {
	return &notifyReleaseWarningAction{
		logger:           logger.NewLogger("notifyReleaseWarningAction"),
		crud:             crud,
		user:             user,
		machine:          machine,
		notify:           notify,
		notifyChannel:    notifyChannel,
		smsNotifyChannel: smsNotifyChannel,
	}
}

// doV2 处理逻辑，优化，分批查询，在循环里直接完成业务逻辑，避免OOM
// TODO 上线稳定后删除不需要的方法；改成分批查询；
func (action *notifyReleaseWarningAction) doV2() {
	action.logger.Info("notifyReleaseWarningAction v2 doing...")

	// 后面改成分批查询

	var (
		paygInstances        []instanceModel.Instance // 按量付费的实例
		notPaygInstances     []instanceModel.Instance // 包年包月的实例
		rentExpiredInstances []instanceModel.Instance // 关联机器租期到期的实例
		instances            []releaseInstance        // 汇总的实例
		now                  = time.Now()
		start30              = now.Add(-time.Hour * 24 * 30)
		end30                = now.Add(-time.Hour * 24 * 29)
		start15              = now.Add(-time.Hour * 24 * 15)
		end15                = now.Add(-time.Hour * 24 * 14)
	)

	// 这里解释一下查询规则，我们是要查询即将释放的实例：
	// 1.对于按量付费的实例来说，只要关机时间超过30天就会释放（stopped_at）
	// 2.对于包年包月的实例来说，只要到期时间超过30天就会释放（expired_at; 到期后也会有定时任务执行关机操作，不过我们这里还是根据 expired_at 查询）
	// 3.machine 是有租期的，当 machine 的租期到期时，相关实例也需要释放

	//if conf.GetGlobalGsConfig().App.DebugApi {
	//	end15 = now.Add(-time.Minute * 10)
	//}
	// 对于按量付费的，根据 stopped_at 查询
	err := db_helper.GlobalDBConn().
		Model(&instanceModel.Instance{}).
		Where("stopped_at <= ?", end30).
		Where("stopped_at >= ?", start30).
		Where("status in (?)", NeverFreelyRetainedStatusSet).
		Where("charge_type = ?", constant.ChargeTypePayg).
		Where("created_at < '2023-07-20 00:00:00'").
		FindInBatches(&paygInstances, 100, func(tx *gorm.DB, batch int) error {
			action.logger.Info("searchInstancesFromDB payg len(%d)", len(paygInstances))
			for _, instance := range paygInstances {
				instances = append(instances, releaseInstance{
					Instance: instance,
				})
			}
			return nil
		}).Error
	if err != nil {
		action.logger.ErrorE(err, "get payg instances failed")
		return
	}

	err = db_helper.GlobalDBConn().
		Model(&instanceModel.Instance{}).
		Where("stopped_at <= ?", end15).
		Where("stopped_at >= ?", start15).
		Where("status in (?)", NeverFreelyRetainedStatusSet).
		Where("charge_type = ?", constant.ChargeTypePayg).
		Where("created_at >= '2023-07-20 00:00:00'").
		FindInBatches(&paygInstances, 100, func(tx *gorm.DB, batch int) error {
			action.logger.Info("searchInstancesFromDB payg len(%d)", len(paygInstances))
			for _, instance := range paygInstances {
				instances = append(instances, releaseInstance{
					Instance: instance,
				})
			}
			return nil
		}).Error
	if err != nil {
		action.logger.ErrorE(err, "get payg instances failed")
		return
	}

	// 对于非按量付费的，根据 expired_at 查询
	err = db_helper.GlobalDBConn().
		Model(&instanceModel.Instance{}).
		Where("expired_at <= ?", end30).
		Where("expired_at >= ?", start30).
		Where("status in (?)", NeverFreelyRetainedStatusSet).
		Not("charge_type = ?", constant.ChargeTypePayg).
		Where("created_at < '2023-07-20 00:00:00'").
		FindInBatches(&notPaygInstances, 100, func(tx *gorm.DB, batch int) error {
			action.logger.Info("searchInstancesFromDB not payg len(%d)", len(notPaygInstances))
			for _, instance := range notPaygInstances {
				instances = append(instances, releaseInstance{
					Instance: instance,
				})
			}
			return nil
		}).Error
	if err != nil {
		action.logger.ErrorE(err, "get not payg instances failed")
		return
	}
	err = db_helper.GlobalDBConn().
		Model(&instanceModel.Instance{}).
		Where("expired_at <= ?", end15).
		Where("expired_at >= ?", start15).
		Where("status in (?)", NeverFreelyRetainedStatusSet).
		Not("charge_type = ?", constant.ChargeTypePayg).
		Where("created_at >= '2023-07-20 00:00:00'").
		FindInBatches(&notPaygInstances, 100, func(tx *gorm.DB, batch int) error {
			action.logger.Info("searchInstancesFromDB not payg len(%d)", len(notPaygInstances))
			for _, instance := range notPaygInstances {
				instances = append(instances, releaseInstance{
					Instance: instance,
				})
			}
			return nil
		}).Error
	if err != nil {
		action.logger.ErrorE(err, "get not payg instances failed")
		return
	}

	instanceUUIDs := make([]constant.InstanceUUIDType, 0, len(instances))
	for _, i := range instances {
		instanceUUIDs = append(instanceUUIDs, i.InstanceUUID)
	}

	// 查询租期截止时间在1天范围内的 machine
	machines, err := action.machine.GetRentDeadline1Day(now)
	if err != nil {
		action.logger.ErrorE(err, "get machine failed")
		return
	}

	machineMap := make(map[string]*machineModel.Machine)
	machineIDs := make([]string, 0, len(machines))
	for _, m := range machines {
		machineIDs = append(machineIDs, m.MachineID)
		machineMap[m.MachineID] = m
	}

	if len(machineIDs) != 0 {
		err = db_helper.GlobalDBConn().
			Model(&instanceModel.Instance{}).
			Where("machine_id in (?)", machineIDs).
			Not("uuid in (?)", instanceUUIDs).
			Not("status in (?)", append(hiddenStatusSet, constant.InstanceAbort)).
			FindInBatches(&rentExpiredInstances, 100, func(tx *gorm.DB, batch int) error {
				action.logger.Info("searchInstancesFromDB machine rent expired len(%d)", len(rentExpiredInstances))
				for _, instance := range rentExpiredInstances {
					instances = append(instances, releaseInstance{
						Instance:             instance,
						isMachineRentExpired: true,
						machineRentDeadline:  *machineMap[instance.MachineID].RentDeadline,
					})
				}
				return nil
			}).Error
		if err != nil {
			action.logger.ErrorE(err, "get machine rent expired instances failed")
			return
		}
	}

	for _, instance := range instances {
		if !instance.isMachineRentExpired && instance.IsPayg() && !instance.StoppedAt.Valid {
			continue
		}

		if !instance.isMachineRentExpired && !instance.IsPayg() && !instance.ExpiredAt.Valid {
			continue
		}

		setting, err := action.user.GetSetting(instance.UID)
		if err != nil {
			action.logger.
				WithField("uid", setting.UID).
				ErrorE(err, "get user setting failed")
			continue
		}

		if setting == nil {
			continue
		}

		// 如果用户的通知实例预警是关闭状态，则跳过
		if setting.NotifyInstanceWarningStatus.Closed() {
			continue
		}

		now := time.Now()

		// 获取时间差
		var subTime time.Duration

		releaseData := 15
		flag, _ := time.ParseInLocation(constant.FormatTimeString, "2023-07-20 00:00:00", time.Local)
		if instance.CreatedAt.Before(flag) {
			releaseData = 30
		}
		if instance.isMachineRentExpired {
			subTime = instance.machineRentDeadline.Sub(now)
		} else {
			if instance.IsPayg() {
				subTime = instance.StoppedAt.Time.Add(time.Hour * 24 * time.Duration(releaseData)).Sub(now)
			} else {
				subTime = instance.ExpiredAt.Time.Add(time.Hour * 24 * time.Duration(releaseData)).Sub(now)
			}
		}

		// 获取通知时间点
		var point notifyWarningPoint

		if subTime.Hours() <= 6 {
			point = notifyWarningPoint6
		} else if subTime.Hours() <= 12 {
			point = notifyWarningPoint12
		} else if subTime.Hours() <= 24 {
			point = notifyWarningPoint24
		}

		// 如果这个实例要发送通知的时间 point 与上一次的 point 相同，且上一次通知时间没有超过24小时，则跳过
		if instance.LastNotifyReleaseWarningPoint == uint8(point) &&
			instance.LastNotifyReleaseWarningTime.Valid &&
			now.Sub(instance.LastNotifyReleaseWarningTime.Time).Hours() <= 24 {
			continue
		}

		subName, area, phone, openID, ok := action.user.GetPhoneForSmsNotify(instance.UID, instance.SubName)
		if !ok {
			continue
		}

		wg := sync.WaitGroup{}
		wg.Add(1)
		go func(instance releaseInstance) {
			defer wg.Done()

			if openID == "" {
				return
			}

			var remark string

			if subTime.Hours() < 1 {
				remark = fmt.Sprintf("您的实例将在%.0f分钟之后释放", subTime.Minutes())
			} else {
				subMinute := subTime.Minutes()
				hour := int(subMinute / 60)
				minute := int(subMinute) % 60
				if minute == 0 {
					remark = fmt.Sprintf("您的实例将在%d小时之后释放", hour)
				} else {
					remark = fmt.Sprintf("您的实例将在%d小时%d分钟之后释放", hour, minute)
				}
			}

			body := notify.WeixinMessageBody{
				Title:    "实例释放预警",
				Keyword1: fmt.Sprintf("%s %s", instance.InstanceUUID.String(), "即将释放"),
				Keyword2: now.Format(constant.FormatTimeString),
				Remark:   remark,
			}

			input := notify.WeixinNotifyInput{
				TemplateID:  constant.TemplateID,
				OpenID:      openID,
				MessageBody: body,
			}

			_, err := action.notifyChannel.Notify(input)
			if err != nil {
				action.logger.WithField("uid", setting.UID).ErrorE(err, "[instance release] wx notify failed")
				return
			}

			// 记录到 DB
			err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
				// 更新通知时间点
				err = action.crud.UpdateInstanceMap(tx, instance.InstanceUUID, map[string]interface{}{
					"last_notify_release_warning_point": point,
					"last_notify_release_warning_time":  now,
				})
				if err != nil {
					return err
				}

				// 保存通知记录
				err = action.notify.SaveNotifyRecord(tx, &notifyModel.NotifyRecord{
					UID:         instance.UID,
					SubName:     subName,
					ProductUUID: instance.InstanceUUID.String(),
					Type:        notifyModel.NotifyTypeInstanceReleaseWarning,
					Channel:     notify.ChannelWeixin,
					Content:     input.String(),
					Phone:       phone,
				})
				if err != nil {
					return err
				}

				return
			})
			if err != nil {
				action.logger.WithField("uid", setting.UID).ErrorE(err, "[instance release] wx handle db failed")
				return
			}
		}(instance)

		wg.Add(1)
		go func(instance releaseInstance) {
			defer wg.Done()

			// 短信只发送12小时内的，其它的不发送
			if point != notifyWarningPoint12 && point != notifyWarningPoint6 {
				return
			}

			// 如果上一次发送时间点不是24，说明12小时的已发送过1次，则跳过
			if instance.LastNotifyReleaseWarningPoint != uint8(notifyWarningPoint24) && now.Sub(instance.LastNotifyReleaseWarningTime.Time).Hours() <= 24 {
				return
			}

			if phone == "" {
				return
			}

			var timeDetail string

			if subTime.Hours() < 1 {
				timeDetail = fmt.Sprintf("%.0f分钟", subTime.Minutes())
			} else {
				subMinute := subTime.Minutes()
				hour := int(subMinute / 60)
				minute := int(subMinute) % 60
				if minute == 0 {
					timeDetail = fmt.Sprintf("%d小时", hour)
				} else {
					timeDetail = fmt.Sprintf("%d小时%d分钟", hour, minute)
				}
			}

			instanceInfo, isRetry, err := libs.InstanceNameDetectAndProcess(string(instance.InstanceUUID), instance.Name)
			if err != nil {
				action.logger.WithFields(map[string]interface{}{
					"instance_uuid": instance.InstanceUUID,
					"instance_name": instance.Name,
				}).ErrorE(err, "instance name detection and processing failed .")
			}

			input := notify.SmsNotifyInput{
				PhoneArea:        area,
				Phone:            libs.PhoneBuildWithArea(area, phone),
				SmsTemplateParam: libs.BuildSMSTemplateParamInstanceReleaseWarning(instanceInfo, timeDetail),
				SMSType:          constant.InstanceReleaseWarning,
			}

			bizId, err := action.smsNotifyChannel.Notify(input)
			if err != nil {
				if instance.InstanceUUID.String() != instanceInfo {
					input := notify.SmsNotifyInput{
						PhoneArea:        area,
						Phone:            libs.PhoneBuildWithArea(area, phone),
						SmsTemplateParam: libs.BuildSMSTemplateParamInstanceReleaseWarning(instance.InstanceUUID.String(), timeDetail),
						SMSType:          constant.InstanceReleaseWarning,
					}

					bizId, err = action.smsNotifyChannel.Notify(input)
					if err != nil {
						action.logger.WithField("uid", setting.UID).ErrorE(err, "[instnace release] The second sms notify failed")
						return
					}
					isRetry = false
				} else {
					action.logger.WithField("uid", setting.UID).ErrorE(err, "[instnace release] sms notify failed")
					return
				}
			}

			// 记录到 DB
			err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
				// 更新通知时间点
				err = action.crud.UpdateInstanceMap(tx, instance.InstanceUUID, map[string]interface{}{
					"last_notify_release_warning_point": point,
					"last_notify_release_warning_time":  now,
				})
				if err != nil {
					return err
				}

				action.logger.WithField("retry_on_failure", isRetry).Info("get retry_on_failure")

				// 保存通知记录
				err = action.notify.SaveNotifyRecord(tx, &notifyModel.NotifyRecord{
					UID:            instance.UID,
					SubName:        subName,
					ProductUUID:    instance.InstanceUUID.String(),
					Type:           notifyModel.NotifyTypeInstanceReleaseWarning,
					Channel:        notify.ChannelSMS,
					Content:        input.String(),
					Phone:          phone,
					SmsBizID:       bizId,
					RetryOnFailure: isRetry,
				})
				return
			})
			if err != nil {
				action.logger.WithField("uid", setting.UID).ErrorE(err, "[instance release] sms handle db failed")
				return
			}
		}(instance)

		wg.Wait()
	}
}

// cronNotifyExpireWarning 定时任务到期预警（给用户发送消息）
func (svc *Service) cronNotifyExpireWarning(ctx context.Context) {
	time.Sleep(time.Second * 20)

	action := newNotifyExpireWarningAction(svc.crud, svc.user, svc.notify, notify.NewWeixinChannel(), notify.NewSmsChannel())
	action.doV2()

	tick := time.Minute * 5
	if conf.GetGlobalGsConfig().App.DebugApi {
		tick = time.Minute
	}

	ticker := time.NewTicker(tick)
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			action.doV2()
		}
	}
}

// notifyExpireWarningAction 通知到期预警动作
type notifyExpireWarningAction struct {
	logger           *logger.Logger
	crud             CRUDHandler
	user             module_definition.UserInterface
	notify           module_definition.NotifyInterface
	notifyChannel    notify.Channel
	smsNotifyChannel notify.Channel
	currentTime      time.Time
	instances        []instanceModel.Instance
	uidOpenIDRelate  map[int]string
	uidPhoneRelate   map[int]string
}

func newNotifyExpireWarningAction(crud CRUDHandler, user module_definition.UserInterface, notify module_definition.NotifyInterface, notifyChannel notify.Channel, smsNotifyChannel notify.Channel) *notifyExpireWarningAction {
	return &notifyExpireWarningAction{
		logger:           logger.NewLogger("notifyExpireWarningAction"),
		crud:             crud,
		user:             user,
		notify:           notify,
		notifyChannel:    notifyChannel,
		smsNotifyChannel: smsNotifyChannel,
	}
}

// doV2 处理逻辑，优化，分批查询，在循环里直接完成业务逻辑，避免OOM
// TODO 上线稳定后删除不需要的方法
func (action *notifyExpireWarningAction) doV2() {
	action.logger.Info("notifyExpireWarningAction v2 doing...")

	// 到期的情况只有包年包月的，根据 expired_at 查询
	// 到期是查询未来24小时内要到期的实例
	db := db_helper.GlobalDBConn().
		Model(&instanceModel.Instance{}).
		Where("expired_at <= ?", time.Now().Add(time.Hour*24)).
		Where("expired_at >= ?", time.Now()).
		Not("charge_type = ?", constant.ChargeTypePayg)

	var count int64

	err := db.Count(&count).Error
	if err != nil {
		action.logger.ErrorE(err, "db count setting failed")
		return
	}

	if count < 1 {
		return
	}

	pageSize := 1000
	instances := make([]instanceModel.Instance, 0, pageSize)
	paged := db_helper.BuildPagedDataUtil(1, pageSize, int(count), 0)
	pageTotal := paged.MaxPage

	for i := 1; i <= pageTotal; i++ {
		paged = db_helper.BuildPagedDataUtil(i, pageSize, int(count), 0)

		err = db.Offset(paged.Offset).
			Limit(paged.PageSize).
			Find(&instances).
			Error
		if err != nil {
			action.logger.ErrorE(err, "db find instance failed")
			return
		}

		for _, instance := range instances {
			setting, err := action.user.GetSetting(instance.UID)
			if err != nil {
				action.logger.
					WithField("uid", setting.UID).
					ErrorE(err, "get user setting failed")
				continue
			}

			if setting == nil {
				continue
			}

			// 如果用户的通知实例预警是关闭状态，则跳过
			if setting.NotifyInstanceWarningStatus.Closed() {
				continue
			}

			if !instance.ExpiredAt.Valid {
				continue
			}

			now := time.Now()

			// 获取时间差
			subTime := instance.ExpiredAt.Time.Sub(now)

			// 获取通知时间点
			var point notifyWarningPoint

			if subTime.Hours() <= 6 {
				point = notifyWarningPoint6
			} else if subTime.Hours() <= 12 {
				point = notifyWarningPoint12
			} else if subTime.Hours() <= 24 {
				point = notifyWarningPoint24
			}

			// 如果这个实例要发送通知的时间 point 与上一次的 point 相同，且上一次通知时间没有超过24小时，则跳过
			if instance.LastNotifyExpireWarningPoint == uint8(point) &&
				instance.LastNotifyExpireWarningTime.Valid &&
				now.Sub(instance.LastNotifyExpireWarningTime.Time).Hours() <= 24 {
				continue
			}

			subName, area, phone, openID, ok := action.user.GetPhoneForSmsNotify(instance.UID, instance.SubName)
			if !ok {
				continue
			}

			wg := sync.WaitGroup{}
			wg.Add(1)
			go func(instance instanceModel.Instance) {
				defer wg.Done()

				if openID == "" {
					return
				}

				var remark string

				if subTime.Hours() < 1 {
					remark = fmt.Sprintf("您的实例将在%.0f分钟之后到期", subTime.Minutes())
				} else {
					subMinute := subTime.Minutes()
					hour := int(subMinute / 60)
					minute := int(subMinute) % 60
					if minute == 0 {
						remark = fmt.Sprintf("您的实例将在%d小时之后到期", hour)
					} else {
						remark = fmt.Sprintf("您的实例将在%d小时%d分钟之后到期", hour, minute)
					}
				}

				body := notify.WeixinMessageBody{
					Title:    "实例到期预警",
					Keyword1: fmt.Sprintf("%s %s", instance.InstanceUUID.String(), "即将到期"),
					Keyword2: now.Format(constant.FormatTimeString),
					Remark:   remark,
				}

				input := notify.WeixinNotifyInput{
					TemplateID:  constant.TemplateID,
					OpenID:      openID,
					MessageBody: body,
				}

				_, err = action.notifyChannel.Notify(input)
				if err != nil {
					action.logger.WithField("uid", setting.UID).ErrorE(err, "[instance expire] wx notify failed")
					return
				}

				// 记录到 DB
				err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
					// 更新通知时间点
					err = action.crud.UpdateInstanceMap(tx, instance.InstanceUUID, map[string]interface{}{
						"last_notify_expire_warning_point": point,
						"last_notify_expire_warning_time":  now,
					})
					if err != nil {
						return err
					}

					// 保存通知记录
					err = action.notify.SaveNotifyRecord(tx, &notifyModel.NotifyRecord{
						UID:         instance.UID,
						SubName:     subName,
						ProductUUID: instance.InstanceUUID.String(),
						Type:        notifyModel.NotifyTypeInstanceExpireWarning,
						Channel:     notify.ChannelWeixin,
						Content:     input.String(),
						Phone:       phone,
					})
					if err != nil {
						return err
					}

					return
				})
				if err != nil {
					action.logger.WithField("uid", setting.UID).ErrorE(err, "[instance expire] handle wx notify db failed")
					return
				}
			}(instance)

			wg.Add(1)
			go func(instance instanceModel.Instance) {
				defer wg.Done()

				// 短信只发送12小时内的，其它的不发送
				if point != notifyWarningPoint12 && point != notifyWarningPoint6 {
					return
				}

				// 如果上一次发送时间点不是24，说明12小时的已发送过1次，则跳过
				if instance.LastNotifyExpireWarningPoint != uint8(notifyWarningPoint24) && now.Sub(instance.LastNotifyExpireWarningTime.Time).Hours() <= 24 {
					return
				}

				if phone == "" {
					return
				}

				var timeDetail string

				if subTime.Hours() < 1 {
					timeDetail = fmt.Sprintf("%.0f分钟", subTime.Minutes())
				} else {
					subMinute := subTime.Minutes()
					hour := int(subMinute / 60)
					minute := int(subMinute) % 60
					if minute == 0 {
						timeDetail = fmt.Sprintf("%d小时", hour)
					} else {
						timeDetail = fmt.Sprintf("%d小时%d分钟", hour, minute)
					}
				}

				instanceInfo, isRetry, err := libs.InstanceNameDetectAndProcess(string(instance.InstanceUUID), instance.Name)
				if err != nil {
					action.logger.WithFields(map[string]interface{}{
						"instance_uuid": instance.InstanceUUID,
						"instance_name": instance.Name,
					}).ErrorE(err, "instance name detection and processing failed .")
				}

				input := notify.SmsNotifyInput{
					PhoneArea:        area,
					Phone:            libs.PhoneBuildWithArea(area, phone),
					SmsTemplateParam: libs.BuildSMSTemplateParamInstanceExpireWarning(instanceInfo, timeDetail),
					SMSType:          constant.InstanceExpireWarning,
				}

				bizId, err := action.smsNotifyChannel.Notify(input)
				if err != nil {
					if instance.InstanceUUID.String() != instanceInfo {
						input := notify.SmsNotifyInput{
							PhoneArea:        area,
							Phone:            libs.PhoneBuildWithArea(area, phone),
							SmsTemplateParam: libs.BuildSMSTemplateParamInstanceExpireWarning(instance.InstanceUUID.String(), timeDetail),
							SMSType:          constant.InstanceExpireWarning,
						}

						bizId, err = action.smsNotifyChannel.Notify(input)
						if err != nil {
							action.logger.WithField("uid", setting.UID).ErrorE(err, "[instance expire] The second sms notify failed")
							return
						}
						isRetry = false
					} else {
						action.logger.WithField("uid", setting.UID).ErrorE(err, "[instance expire] notify failed")
						return
					}

				}

				// 记录到 DB
				err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
					// 更新通知时间点
					err = action.crud.UpdateInstanceMap(tx, instance.InstanceUUID, map[string]interface{}{
						"last_notify_expire_warning_point": point,
						"last_notify_expire_warning_time":  now,
					})
					if err != nil {
						return err
					}

					// 保存通知记录
					err = action.notify.SaveNotifyRecord(tx, &notifyModel.NotifyRecord{
						UID:            instance.UID,
						SubName:        subName,
						ProductUUID:    instance.InstanceUUID.String(),
						Type:           notifyModel.NotifyTypeInstanceExpireWarning,
						Channel:        notify.ChannelSMS,
						Content:        input.String(),
						Phone:          phone,
						SmsBizID:       bizId,
						RetryOnFailure: isRetry,
					})
					if err != nil {
						return err
					}

					return
				})
				if err != nil {
					action.logger.WithField("uid", setting.UID).ErrorE(err, "[instance expire] handle sms notify db failed")
					return
				}
			}(instance)

			wg.Wait()
		}
	}
}

/******************************************* 预警 end ******************************************/

func (svc *Service) cronCreateInstanceFromSchedule(ctx context.Context) {
	l := svc.l.WithField("job", "cronCreateInstanceFromSchedule")
	l.Info("scheduler begin running ....")

	timer := time.NewTicker(time.Second * 3)
	for {
		select {
		case <-ctx.Done():
			l.Info("quit gracefully...")
			return
		case <-timer.C:
			l.Info("running")
			instanceSchedules, err := svc.GetAllWaitingInstanceSchedule()
			if err != nil {
				l.WithError(err).Error("getWaitingInstanceSchedule failed")
				continue
			}

			for _, instanceSchedule := range instanceSchedules {
				machine, err := svc.GetMachineToCreateInstance(&instanceSchedule)
				if err != nil {
					l.Error("instance queue get machine failed: %v %d", err, instanceSchedule.Id)
					continue
				}
				if machine == nil {
					l.Info("instance queue get machine failed, machine is nil: %d", instanceSchedule.Id)
					continue
				}
				//检查主机实例数量限制
				ok, err := svc.machine.CheckMaxInstanceNum(machine.MachineID)
				if err != nil {
					l.Warn("checkMachineInstanceNum failed, err:%+v, queue.Id:%d, machine:%+v", err, instanceSchedule.Id, machine)
					continue
				}

				if !ok {
					//l.Error("instance num exceed max limit,err:%+v, queue.Id:%d, machine:%+v", err, instanceSchedule.Id, machine)
					continue
				}

				order, err := svc.CreateInstanceFromSchedule(&instanceSchedule, machine)
				if err != nil {
					//l.Error("instance queue create instance failed: %v %d", err, instanceSchedule.Id)
					continue
				}

				err = instanceSchedule.InstanceScheduleUpdate(nil, &db_helper.QueryFilters{
					EqualFilters: map[string]interface{}{
						"id": instanceSchedule.Id,
					},
				}, map[string]interface{}{
					"status":       constant.InstanceQueueStatusSucceeded,
					"uuid":         order.ProductUUID,
					"order_uuid":   order.UUID,
					"scheduled_at": time.Now(),
				})
				if err != nil {
					l.Error("make instance queue succeeded failed: %v %d", err, instanceSchedule.Id)
					continue
				}

				if instanceSchedule.IsSendMsg && time.Now().Sub(instanceSchedule.CreatedAt) > 10*time.Minute {
					// 发送短信
					user, err := svc.user.FindByUserId(instanceSchedule.Uid)
					if err != nil {
						l.WithField("instance_schedule_id", instanceSchedule.Id).ErrorE(err, "find user by uid failed")
						continue
					}
					input := notify.SmsNotifyInput{
						PhoneArea:        user.PhoneArea,
						Phone:            user.Phone,
						SmsTemplateParam: libs.BuildSMSTemplateScheduleInstanceSuccess(instanceSchedule.Remark),
						SMSType:          constant.InstanceScheduleSuccess,
					}

					_, err = svc.smsNotifyChannel.Notify(input)
					if err != nil {
						svc.l.WithFields(map[string]interface{}{
							"uid":   user.ID,
							"input": input,
						}).ErrorE(err, "notify failed")
						continue
					}
				}
			}
		}
	}
}

func (svc *Service) coreStopExpireWaitingScheduleInstance(ctx context.Context) {
	l := svc.l.WithField("job", "coreStopExpireWaitingScheduleInstance")
	l.Info("scheduler begin running ....")

	timer := time.NewTicker(time.Second * 30)
	for {
		select {
		case <-ctx.Done():
			return
		case <-timer.C:
			instanceSchedule := &instanceModel.InstanceSchedule{}
			err := instanceSchedule.InstanceScheduleUpdate(nil, &db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"status": constant.InstanceQueueStatusWaiting,
				},
				CompareFilters: []db_helper.Compare{{Key: "created_at", Sign: db_helper.SmallerThan, CompareValue: time.Now().Add(-72 * time.Hour)}},
				//CompareFilters: []db_helper.Compare{{Key: "created_at", Sign: db_helper.SmallerThan, CompareValue: time.Now().Add(-10 * time.Minute)}},
				NullField: []string{"deleted_at"},
			}, map[string]interface{}{
				"status":     constant.InstanceQueueStatusStopped,
				"stopped_at": time.Now(),
			})
			if err != nil {
				l.WithError(err).Error("getWaitingInstanceSchedule failed")
				continue
			}
		}
	}
}
