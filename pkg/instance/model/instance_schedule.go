package model

import (
	"encoding/json"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"
)

const TableNameInstanceSchedule = "instance_schedule"

type InstanceSchedule struct {
	Id             int                             `gorm:"column:id;type:int unsigned;not null;auto_increment;primary key;" json:"id"`
	CreatedAt      time.Time                       `gorm:"column:created_at;type:datetime;not null;" json:"created_at"`
	UpdatedAt      time.Time                       `gorm:"column:updated_at;type:datetime;not null;" json:"updated_at"`
	DeletedAt      gorm.DeletedAt                  `gorm:"column:deleted_at;type:datetime;default:null;index;" json:"-"`
	Uid            int                             `gorm:"column:uid;type:int unsigned;not null;" json:"uid"`
	InstanceUuid   string                          `gorm:"column:uuid;type:varchar(255);not null;default:'';" json:"uuid"`
	OrderUuid      string                          `gorm:"column:order_uuid;type:varchar(255);not null;default:'';" json:"order_uuid"`
	Status         constant.InstanceScheduleStatus `gorm:"column:status;type:varchar(10);not null;" json:"status"`
	TemplateJson   datatypes.JSON                  `gorm:"column:template;type:json" json:"-"`
	TemplateEntity Template                        `gorm:"-" json:"template"`
	Remark         string                          `gorm:"column:remark;type:varchar(255);not null;default:'';" json:"remark"`
	StoppedAt      *time.Time                      `gorm:"type:datetime;column:stopped_at;default:null;" json:"stopped_at"`
	IsSendMsg      bool                            `gorm:"column:is_send_msg;type:tinyint(1);" json:"is_send_msg"`
	ScheduledAt    *time.Time                      `gorm:"type:datetime;column:scheduled_at;default:null;" json:"scheduled_at"` // 实例调度成功时间
}

func (*InstanceSchedule) TableName() string {
	return TableNameInstanceSchedule
}

func (i *InstanceSchedule) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&InstanceSchedule{})
}

type BaseImageInfo struct {
	FrameworkName    constant.FrameworkNameType    `json:"framework_name"`
	FrameworkVersion constant.FrameworkVersionType `json:"framework_version"`
	PythonVersion    constant.PythonVersionType    `json:"python_version"`
	CudaVersion      constant.CudaVersionType      `json:"cuda_version"`
	ChipCorp         constant.ChipCorp             `json:"chip_corp"`
}

type Template struct {
	DataCenter     string                    `json:"data_center"`
	RegionSignList []constant.RegionSignType `json:"region_sign_list"`
	GpuType        []string                  `json:"gpu_type"`
	GpuIDSet       []int                     `json:"gpu_id_set"` // 用于筛选机器
	GpuNum         int                       `json:"gpu_num"`
	MinMemorySize  int64                     `json:"min_memory_size"`
	MinCpuNum      int                       `json:"min_cpu_num"`
	MaxPaygPrice   int64                     `json:"max_payg_price"`
	CudaV          int                       `json:"cuda_v"`
	ExpandDataDisk int64                     `json:"expand_data_disk"`

	ImageUrl string `json:"image_url"` // 镜像地址
	//IsPrivateImage   bool   `json:"is_private_image"`
	PrivateImageUUID string `json:"private_image_uuid"`
	//IsCommunityImage bool   `json:"is_community_image"`
	CommunityUUID    string        `json:"reproduction_uuid"` // 社区镜像uuid
	CommunityID      int           `json:"reproduction_id"`
	BaseImageInfo    BaseImageInfo `json:"base_image_info"`
	PrivateImageName string        `json:"private_image_name"`
}

func (d *Template) Valid() bool {
	if d.DataCenter == "" ||
		len(d.GpuType) == 0 ||
		d.GpuNum < 1 ||
		d.GpuNum > 8 ||
		d.ImageUrl == "" ||
		d.ExpandDataDisk < 0 {
		return false
	}

	d.MinMemorySize = d.MinMemorySize * 1024 * 1024 * 1024
	return true
}

func (i *InstanceSchedule) BeforeCreate(*gorm.DB) (err error) {
	i.TemplateJson = i.TemplateEntity.Marshal()
	return
}

func (d *Template) Marshal() []byte {
	j, _ := json.Marshal(d)
	return j
}

func (i *InstanceSchedule) AfterFind(*gorm.DB) (err error) {
	if len(i.TemplateJson) != 0 {
		err = json.Unmarshal(i.TemplateJson, &i.TemplateEntity)
		if err != nil {
			return
		}
	}
	return
}

func (i *InstanceSchedule) InstanceScheduleCreate(tx *gorm.DB) error {
	return db_helper.InsertOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         i,
		InsertPayload:           i,
	}).GetError()
}

func (i *InstanceSchedule) InstanceScheduleUpdate(tx *gorm.DB, filter *db_helper.QueryFilters, um map[string]interface{}) (err error) {
	um["updated_at"] = time.Now()
	return db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         i,
		Filters:                 *filter,
	}, um).GetError()
}

func (i *InstanceSchedule) InstanceScheduleGetAll(filter db_helper.QueryFilters) (list []InstanceSchedule, err error) {
	list = []InstanceSchedule{}
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: i,
		Filters:         filter,
	}, &list).GetError()
	return
}

func (i *InstanceSchedule) InstanceScheduleGetFirst(filter *db_helper.QueryFilters) (err error) {
	err = db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: i,
		Filters:         *filter,
	}, &i).GetError()
	return
}
