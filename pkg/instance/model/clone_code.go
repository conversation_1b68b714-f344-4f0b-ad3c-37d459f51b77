package model

import (
	"encoding/json"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"server/pkg/db_helper"
	"time"
)

const TableNameCloneCode string = "clone_code"

type CloneCode struct {
	ID           int            `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"` //主键id
	Uid          int            `gorm:"column:uid" json:"uid"`                                        //uid
	InstanceUUID string         `gorm:"type:varchar(255);column:instance_uuid;" json:"instance_uuid"` //实例uuid
	CloneCode    int            `gorm:"column:clone_code;index:clone_code;" json:"clone_code"`        //克隆码
	IsUse        int            `gorm:"column:is_use" json:"is_use"`                                  //是否使用了，0：未使用，1：已使用
	TargetUuid   string         `gorm:"type:varchar(255);column:target_uuid;" json:"target_uuid"`     //他人uuid
	CreatedAt    time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`           //创建时间
	UpdatedAt    time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`           //更新时间
	ExtJson      datatypes.JSON `gorm:"column:ext;type:json" json:"ext"`                              //扩展字段，记录过往生成的克隆码和时间，便于追溯
	ExtEntity    []*Ext         `gorm:"-" json:"ext_entity"`
}

type Ext struct {
	CloneCode int       `json:"clone_code"`
	UpdatedAt time.Time `json:"updated_at"`
}

func (c *CloneCode) JsonExt(extEntity []*Ext) (err error) {
	c.ExtJson, err = json.Marshal(extEntity)
	if err != nil {
		return
	}
	return nil
}

func (c *CloneCode) TableName() string {
	return TableNameCloneCode
}

// Init 实现 db_helper 接口.
func (c *CloneCode) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&CloneCode{})
}

func (c *CloneCode) BeforeCreate(db *gorm.DB) (err error) {
	c.ExtJson, err = json.Marshal(c.ExtEntity)
	if err != nil {
		return
	}
	return nil
}

func (c *CloneCode) AfterFind(db *gorm.DB) (err error) {
	if len(c.ExtJson) != 0 {
		c.ExtEntity = []*Ext{}
		err = json.Unmarshal(c.ExtJson, &c.ExtEntity)
		if err != nil {
			return
		}
	}
	return nil
}

func (c *CloneCode) BeforeSave(db *gorm.DB) (err error) {
	c.ExtJson, err = json.Marshal(c.ExtEntity)
	if err != nil {
		return
	}
	return nil
}

func (c *CloneCode) GetOne(filters *db_helper.QueryFilters) error {
	return db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: c,
		Filters:         *filters,
	}, &c).GetError()
}

func (c *CloneCode) Find(filters *db_helper.QueryFilters) error {
	return db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: c,
		Filters:         *filters,
	}, &c).GetError()
}

func (c *CloneCode) Count(filters *db_helper.QueryFilters) (count int64, err error) {
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: c,
		Filters:         *filters,
	}, &count).GetError()
	return
}

func (c *CloneCode) InsertOrUpdate(tx *gorm.DB, cc *CloneCode) error {
	return db_helper.InsertOrUpdateOne(db_helper.QueryDefinition{
		ModelDefinition: c,
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{"uid": cc.Uid, "instance_uuid": cc.InstanceUUID},
		},
		InsertPayload: cc,
	}, c).GetError()
}

func (c *CloneCode) Insert(tx *gorm.DB, cc *CloneCode) error {
	return db_helper.InsertOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         c,
		InsertPayload:           cc,
	}).GetError()
}

func (c *CloneCode) UpdateById(tx *gorm.DB, id int, um interface{}) error {
	return db_helper.UpdateOne(db_helper.QueryDefinition{
		ModelDefinition: c,
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{"id": id},
		},
	}, um).GetError()
}
