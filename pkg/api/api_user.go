package server_api

import (
	"encoding/json"
	"net/http"
	regionModel "server/pkg/region/model"
	"server/pkg/user/model"
)

type GetUserDetailReq struct {
	UID int `json:"id"`
}

type GetUserDetailRes struct {
	ResBase
	Data model.User `json:"data"`
}

func (api *Api) GetUserDetail(req GetUserDetailReq, token string, client *http.Client) (GetUserDetailRes, error) {
	body, _, err := api.setToken(token).get("/api/v1/user/detail", req)
	if err != nil {
		return GetUserDetailRes{}, err
	}
	var res GetUserDetailRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetUserDetailRes{}, err
	}
	return res, nil
}

type GetSubUserSPACReq struct {
	UID     int    `form:"uid" json:"uid"`
	SubName string `form:"subName" json:"subName"`
}

type GetSubUserSPACRes struct {
	ResBase
	Data string `json:"data"`
}

func (api *Api) GetSubUserSPAC(key GetSubUserSPACReq) (res GetSubUserSPACRes, code int, err error) {
	body := []byte{}
	body, code, err = api.get(GetSubUserSPACPath, key)
	if err != nil {
		return
	}

	err = json.Unmarshal(body, &res)
	if err != nil {
		return
	}
	err = res.GetErr()
	if err != nil {
		return
	}
	return
}

type GetExclusiveNfsRes struct {
	ResBase
	Data regionModel.ExclusiveNfs `json:"data"`
}

func (api *Api) GetExclusiveNfs(req regionModel.ExclusiveNfsInfoParams) (GetExclusiveNfsRes, error) {
	body, _, err := api.post("/admin/v1/exclusive_nfs/info", req)
	if err != nil {
		return GetExclusiveNfsRes{}, err
	}

	var res GetExclusiveNfsRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetExclusiveNfsRes{}, err
	}

	return res, nil
}
