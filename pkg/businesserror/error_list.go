package businesserror

import (
	intl "server/pkg/international"
)

/**
 * 增加新错误的示例如下.
 * 如果需要增加新文案, 则需要先去 international 包添加 MessageCode, 以及对应的翻译.
 */

var (
	TestError01 = NewBizError(CodeServerError, intl.MTest00001)
	TestError02 = NewBizError(CodeServerError, intl.MTest00002)
	TestError03 = NewBizError(CodeServerError, intl.MTest00003)

	ErrInternalError                              = NewBizError(CodeInternalError, intl.MCommon00001)
	ErrInvalidRequestParams                       = NewBizError(CodeRequestParameterIsWrong, intl.MCommon00002)
	ErrEmptyUIDInParams                           = NewBizError(CodeRequestParameterIsWrong, intl.MCommon00003)
	ErrDatabaseError                              = NewBizError(CodeDatabaseError, intl.MCommon00004)
	ErrServerBusy                                 = NewBizError(CodeServerBusy, intl.MCommon00007)
	ErrRecordNotFoundError                        = NewBizError(CodeRecordNotFoundError, intl.MCommon00009)
	ErrOptimisticLockingRollbackButNotReturnError = NewBizError(CodeInternalError, intl.MCommon00010)
	ErrServiceUpgrade                             = NewBizError(CodeInternalError, intl.MCommon00011)
	ErrEmptyBox                                   = NewBizError(CodeBadRequest, intl.MCommon00012)
	ErrFileTypeError                              = NewBizError(CodeBadRequest, intl.MCommon00013)
	ErrFileMax10M                                 = NewBizError(CodeBadRequest, intl.MCommon00014)
	ErrFileUploadFailed                           = NewBizError(CodeBadRequest, intl.MCommon00015)
	ErrFileNotExist                               = NewBizError(CodeBadRequest, intl.MCommon00016)

	ErrAuthorizeFailed          = NewBizError(CodeAuthorizeFailed, intl.MAuth00001)
	ErrResourceAccessAuthFailed = NewBizError(CodeBadRequest, intl.MAuth00002)
	ErrAccessSpeedIsLimited     = NewBizError(CodeBadRequest, intl.MAuth00003)

	// --------------------- instance ---------------
	ErrInstanceCreateExceptionOccurred         = NewBizError(CodeInternalError, intl.MInstance00001)
	ErrInstanceInvalidCustomImage              = NewBizError(CodeRecordNotFoundError, intl.MInstance00002)
	ErrInstanceNotFound                        = NewBizError(CodeRecordNotFoundError, intl.MInstance00008)
	ErrInstanceExpired                         = NewBizError(CodeServerError, intl.MInstance00009)
	ErrInstanceConfigNotMeetCondition          = NewBizError(CodeInternalError, intl.MInstance00010)
	ErrInstanceShutdownTimeBeforeNow           = NewBizError(CodeServerError, intl.MInstance00011)
	ErrInstanceWrongStartMode                  = NewBizError(CodeServerError, intl.MInstance00012)
	ErrUserCloneInstanceAtMostTimesPerDay      = NewBizError(CodeInternalError, intl.MInstance00013)
	ErrCloneInstanceDiskAlerts                 = NewBizError(CodeBadRequest, intl.MInstance00014)
	ErrInstanceNotMeetCloneCondition           = NewBizError(CodeInternalError, intl.MInstance00015)
	ErrInstanceStatusNotShutdown               = NewBizError(CodeInternalError, intl.MInstance00016)
	ErrInstanceMigrateNotFailed                = NewBizError(CodeInternalError, intl.MInstance00017)
	ErrInstanceMaxNum                          = NewBizError(CodeServerError, intl.MInstance00018)
	ErrInstanceSomeCanNotOperate               = NewBizError(CodeBadRequest, intl.MInstance00019)
	ErrInstanceAllocateSomeFailed              = NewBizError(CodeBadRequest, intl.MInstance00020)
	ErrInstanceSubUserAuthFailed               = NewBizError(CodeBadRequest, intl.MInstance00021)
	ErrFileTransferTooManyTimes                = NewBizError(CodeServerBusy, intl.MInstance00022)
	ErrRegionMaxInstanceNumLimit               = NewBizError(CodeServerBusy, intl.MInstance00023)
	ErrRegionCannotCreateInstance              = NewBizError(CodeBadRequest, intl.MInstance00024)
	ErrInstanceReleaseNotAllow                 = NewBizError(CodeBadRequest, intl.MInstance00025)
	ErrInstanceRegionDifferent                 = NewBizError(CodeBadRequest, intl.MInstance00026)
	ErrInstanceDelayReleasePrepayError         = NewBizError(CodeBadRequest, intl.MInstance00027)
	ErrInstanceDelayReleasePrepayInstanceError = NewBizError(CodeBadRequest, intl.MInstance00028)
	ErrInstanceStatusRunning                   = NewBizError(CodeBadRequest, intl.MInstance00029)
	ErrStoppedErrInstanceNotFound              = NewBizError(CodeBadRequest, intl.MInstance00030)
	ErrInitInstanceNotFountPrivateImage        = NewBizError(CodeBadRequest, intl.MInstance00031)
	ErrNewSSHPwdFormatError                    = NewBizError(CodeBadRequest, intl.MInstance00032)
	ErrOldInstanceTransIsNotValid              = NewBizError(CodeBadRequest, intl.MInstance00033)
	ErrNewInstanceTransIsNotValid              = NewBizError(CodeBadRequest, intl.MInstance00034)
	ErrInstanceBelongToDifferentUser           = NewBizError(CodeBadRequest, intl.MInstance00035)
	ErrInstanceIsShutdownCloning               = NewBizError(CodeBadRequest, intl.MInstance00036)
	ErrInstanceDelayReleaseError               = NewBizError(CodeBadRequest, intl.MInstance00037)
	ErrInstanceIsNotInRemoveCache              = NewBizError(CodeBadRequest, intl.MInstance00038)
	ErrInstanceStatusCanNotOpt                 = NewBizError(CodeBadRequest, intl.MInstance00039)
	ErrInstanceIsRemove                        = NewBizError(CodeBadRequest, intl.MInstance00040)
	ErrInstanceCanNotOptInShutdown             = NewBizError(CodeBadRequest, intl.MInstance00041)
	ErrCloneCodeNotExist                       = NewBizError(CodeBadRequest, intl.MInstance00042)
	ErrGetByCloneCode                          = NewBizError(CodeBadRequest, intl.MInstance00043)
	ErrCloneCodeExpired                        = NewBizError(CodeBadRequest, intl.MInstance00044)
	ErrUpdateCloneCode                         = NewBizError(CodeBadRequest, intl.MInstance00045)
	ErrCloneCodeHasUse                         = NewBizError(CodeBadRequest, intl.MInstance00046)
	ErrInstanceUuidEmpty                       = NewBizError(CodeBadRequest, intl.MInstance00047)
	ErrCloneCodeFormat                         = NewBizError(CodeBadRequest, intl.MInstance00048)
	ErrCloneMachine                            = NewBizError(CodeBadRequest, intl.MInstance00049)
	ErrGetCloneNum                             = NewBizError(CodeBadRequest, intl.MInstance00050)
	ErrCloneNumExceed                          = NewBizError(CodeBadRequest, intl.MInstance00051)
	ErrCloneNumSet                             = NewBizError(CodeBadRequest, intl.MInstance00052)
	ErrCloneInstanceNotShutdown                = NewBizError(CodeBadRequest, intl.MInstance00054)
	ErrCloneSrcPathInvalid                     = NewBizError(CodeBadRequest, intl.MInstance00055)

	ErrGetInstanceTagFailed = NewBizError(CodeBadRequest, intl.MInstance00056)
	ErrInstanceTagIsExists  = NewBizError(CodeBadRequest, intl.MInstance00057)
	ErrInstanceTagNum       = NewBizError(CodeBadRequest, intl.MInstance00058)
	ErrCheckBeforeAddTag    = NewBizError(CodeBadRequest, intl.MInstance00059)
	ErrAddInstanceTag       = NewBizError(CodeBadRequest, intl.MInstance00060)
	ErrDeleteInstanceTag    = NewBizError(CodeBadRequest, intl.MInstance00061)
	// ------------------------- user ----------------------
	ErrUserNotExist                        = NewBizError(CodeRequestParameterIsWrong, intl.MUser00001)
	ErrUserPhoneOrPasswordWrong            = NewBizError(CodeRequestParameterIsWrong, intl.MUser00002)
	ErrLoginIsLimitedFmt                   = NewBizError(CodeRequestParameterIsWrong, intl.MUser00003)
	ErrGetUserFailed                       = NewBizError(CodeRequestParameterIsWrong, intl.MUser00004)
	ErrGetVCodeFailed                      = NewBizError(CodeInternalError, intl.MUser00005)
	ErrSetTicketFailed                     = NewBizError(CodeInternalError, intl.MUser00006)
	ErrCaptchaExpired                      = NewBizError(CodeBadRequest, intl.MUser00010)
	ErrCheckCaptchaFailed                  = NewBizError(CodeInternalError, intl.MUser00011)
	ErrRequestCaptchaIsWrong               = NewBizError(CodeBadRequest, intl.MUser00012)
	ErrUpdateUserFailed                    = NewBizError(CodeInternalError, intl.MUser00013)
	ErrUserAlreadyExist                    = NewBizError(CodeAlreadyExist, intl.MUser00014)
	ErrUserStatusError                     = NewBizError(CodeRequestParameterIsWrong, intl.MUser00015)
	ErrTokenMissing                        = NewBizError(CodeRequestParameterIsWrong, intl.MUser00016)
	ErrParseTokenFailed                    = NewBizError(CodeInternalError, intl.MUser00017)
	ErrUserCanNotOperate                   = NewBizError(CodeBadRequest, intl.MUser00018)
	ErrPhoneWrongFormat                    = NewBizError(CodeInternalError, intl.MUser00020)
	ErrSendSMSFailed                       = NewBizError(CodeInternalError, intl.MUser00021)
	ErrEmailAlreadyInUse                   = NewBizError(CodeAlreadyExist, intl.MUser00022)
	ErrUserUpdateNicknameFailed            = NewBizError(CodeInternalError, intl.MUser00023)
	ErrGetTicketFailed                     = NewBizError(CodeInternalError, intl.MUser00024)
	ErrRegexpFormatFailed                  = NewBizError(CodeInternalError, intl.MUser00025)
	ErrEmailFormatWrong                    = NewBizError(CodeInternalError, intl.MUser00026)
	ErrUserNotRegister                     = NewBizError(CodeInternalError, intl.MUser00027)
	ErrInitNotRequired                     = NewBizError(CodeInternalError, intl.MUser00028)
	ErrUserUpdateEmailFailed               = NewBizError(CodeInternalError, intl.MUser00029)
	ErrUpdatePasswordFailed                = NewBizError(CodeInternalError, intl.MUser00030)
	ErrEmailCodeWrong                      = NewBizError(CodeInternalError, intl.MUser00031)
	ErrEmailBindFailed                     = NewBizError(CodeInternalError, intl.MUser00032)
	ErrCreateUserFailed                    = NewBizError(CodeInternalError, intl.MUser00033)
	ErrUserPasswordInconsistent            = NewBizError(CodeBadRequest, intl.MUser00036)
	ErrUserResetPasswordFailed             = NewBizError(CodeInternalError, intl.MUser00037)
	ErrUserNotAdmin                        = NewBizError(CodeRequestParameterIsWrong, intl.MUser00038)
	ErrUserUpdatePhoneFailed               = NewBizError(CodeInternalError, intl.MUser00039)
	ErrGetAccessTokenFailed                = NewBizError(CodeInternalError, intl.MUser00040)
	ErrCheckUserVCodeFailed                = NewBizError(CodeInternalError, intl.MUser00041)
	ErrRegisterIsLimitedFmt                = NewBizError(CodeInternalError, intl.MUser00042)
	ErrUserUpdateNameFailed                = NewBizError(CodeInternalError, intl.MUser00043)
	ErrResetPasswordIsLimitedFmt           = NewBizError(CodeInternalError, intl.MUser00044)
	ErrGetUserInviteDetailFailed           = NewBizError(CodeInternalError, intl.MUser00045)
	ErrCountUserInviteRewardFailed         = NewBizError(CodeInternalError, intl.MUser00046)
	ErrReadWechatBodyFailed                = NewBizError(CodeInternalError, intl.MUser00047)
	ErrCreateUserFeedBackFailed            = NewBizError(CodeInternalError, intl.MUser00048)
	ErrPhoneHasBound                       = NewBizError(CodeInternalError, intl.MUser00049)
	ErrGetWxUserInfoFailed                 = NewBizError(CodeInternalError, intl.MUser00050)
	ErrUserBindWechatFailed                = NewBizError(CodeInternalError, intl.MUser00051)
	ErrParseXmlFailed                      = NewBizError(CodeInternalError, intl.MUser00053)
	ErrCheckSignatureFailed                = NewBizError(CodeInternalError, intl.MUser00054)
	ErrPhoneHasBindWithOtherOpenId         = NewBizError(CodeInternalError, intl.MUser00057)
	ErrBindWeXinPlease                     = NewBizError(CodeBadRequest, intl.MUser00058)
	ErrUserDisabled                        = NewBizError(CodeBadRequest, intl.MUser00059)
	ErrEduEmailVerifyFailed                = NewBizError(CodeInternalError, intl.MUser00061)
	ErrGetUserMemberInfoFailed             = NewBizError(CodeInternalError, intl.MUser00062)
	ErrOverPersonalTokenLimit              = NewBizError(CodeInternalError, intl.MUser00063)
	GetUserPersonalTokenFailed             = NewBizError(CodeInternalError, intl.MUser00064)
	ErrUserCmdPersonalTokenPerDayOverLimit = NewBizError(CodeInternalError, intl.MUser00065)
	ErrUserCmdPersonalTokenPerMinOverLimit = NewBizError(CodeInternalError, intl.MUser00066)
	ErrUserPhoneNotExist                   = NewBizError(CodeBadRequest, intl.MUser00067)
	ErrUserPhoneAreaNotSupport             = NewBizError(CodeBadRequest, intl.MUser00068)
	ErrUserSubUserNotAllow                 = NewBizError(CodeBadRequest, intl.MUser00069)
	ErrUserSubUserNumNotEnough             = NewBizError(CodeBadRequest, intl.MUser00070)
	ErrUserSubUserAlreadyExist             = NewBizError(CodeBadRequest, intl.MUser00071)
	ErrUserSubUserSomeCanNotOperate        = NewBizError(CodeBadRequest, intl.MUser00072)
	ErrUserSubUserCanNotDelete             = NewBizError(CodeBadRequest, intl.MUser00073)
	ErrNeedBindWeixinOpenID                = NewBizError(CodeBadRequest, intl.MUser00074)
	ErrUserRealNameAuthFailed              = NewBizError(CodeBadRequest, intl.MUser00075)
	ErrUserRealNameAuthIDNumberAlreadyUsed = NewBizError(CodeBadRequest, intl.MUser00076)
	ErrUserAutopanelTokenAlreadyExist      = NewBizError(CodeBadRequest, intl.MUser00077)
	ErrUserUnbindOpenIDTimeNotAllow        = NewBizError(CodeNoticeMsg, intl.MUser00078)
	ErrUserUnbindNotAllow                  = NewBizError(CodeNoticeMsg, intl.MUser00079)
	ErrChangePhoneAlreadyRegister          = NewBizError(CodeBadRequest, intl.MUser00080)
	ErrChangePhoneIsLimitedFmt             = NewBizError(CodeBadRequest, intl.MUser00081)
	ErrUserPersonalTokenInvalid            = NewBizError(CodeBadRequest, intl.MUser00082)
	ErrUserCertificationStatusError        = NewBizError(CodeBadRequest, intl.MUser00083)
	ErrUserCertificationAlreadyExist       = NewBizError(CodeBadRequest, intl.MUser00084)
	ErrUserCertificationDeleteFailed       = NewBizError(CodeCertificationApplicationReviewed, intl.MUser00085)
	ErrUserSubUserNotFound                 = NewBizError(CodeRecordNotFoundError, intl.MUser00086)
	ErrUserNotEnterpriseCanNotDeployment   = NewBizError(CodeBadRequest, intl.MUser00087)
	ErrUserSubUserSrcPasswordError         = NewBizError(CodeBadRequest, intl.MUser00088)
	ErrUserSubUserPhoneOrPasswordWrong     = NewBizError(CodeRequestParameterIsWrong, intl.MUser00002)
	ErrUserDisableLogin                    = NewBizError(CodeBadRequest, intl.MUser00090)
	ErrInvalidEmail                        = NewBizError(CodeBadRequest, intl.MUser00091)
	ErrSendEmailInvalidEmail               = NewBizError(CodeBadRequest, intl.MUser00092)
	ErrUpdateCloneNumberFailed             = NewBizError(CodeInternalError, intl.MUser00093)
	ErrOverExpireTimeLimit                 = NewBizError(CodeBadRequest, intl.MUser00094)
	ErrUserPasscodeWrong                   = NewBizError(CodeInternalError, intl.MUser00095)
	ErrUserRecertificationAlreadyExist     = NewBizError(CodeBadRequest, intl.MUser00096)
	ErrUserSetMaxInstanceNum               = NewBizError(CodeBadRequest, intl.MUser00097)
	ErrUserRechargeLimit                   = NewBizError(CodeUserRechargeLimit, intl.MUser00098)
	ErrUserLoginRecord                     = NewBizError(CodeBadRequest, intl.MUser00099)
	ErrPushGatewayAddressError             = NewBizError(CodeBadRequest, intl.MUser00100)
	ErrPushGatewayAuthorizationFailed      = NewBizError(CodeBadRequest, intl.MUser00101)

	// --------------------- user feedback-----------------------------------------

	ErrHandleFeedBackFailed     = NewBizError(CodeInternalError, intl.MFeedBack00001)
	ErrFeedBackNotExist         = NewBizError(CodeInternalError, intl.MFeedBack00002)
	ErrGetFeedBackListFailed    = NewBizError(CodeInternalError, intl.MFeedBack00003)
	ErrGetFeedBackDetailFailed  = NewBizError(CodeInternalError, intl.MFeedBack00004)
	ErrGetFeedBackImgPathFailed = NewBizError(CodeInternalError, intl.MFeedBack00005)

	// ---------------------- user group ---------------------------------------------

	ErrCreateUserGroupFailed   = NewBizError(CodeInternalError, intl.MUserGroup00001)
	ErrCountUserInGroupFailed  = NewBizError(CodeInternalError, intl.MUserGroup00003)
	ErrUpdateUserGroupFailed   = NewBizError(CodeInternalError, intl.MUserGroup00004)
	ErrDeleteUserInGroupFailed = NewBizError(CodeInternalError, intl.MUserGroup00006)
	ErrGetUserGroupFailed      = NewBizError(CodeInternalError, intl.MUserGroup00008)

	// --------------------------- user member ------------------------------------------
	ErrCountColumnFailed = NewBizError(CodeInternalError, intl.MUserMember00001)

	// --------------------------- user ssh key ----------------------------------------------
	ErrUserSSHKeyNumLimit = NewBizError(CodeBadRequest, intl.MUserPubKey00001)

	// -------------------- file -------------------------------------------------

	ErrReadLogoFailedFmt   = NewBizError(CodeInternalError, intl.MFile00001)
	ErrFileIsNotImage      = NewBizError(CodeInternalError, intl.MFile00002)
	ErrUploadFileFailedFmt = NewBizError(CodeInternalError, intl.MFile00003)
	ErrUploadFileFailed    = NewBizError(CodeInternalError, intl.MFile00004)
	ErrFileSizeOverRange   = NewBizError(CodeInternalError, intl.MFile00005)
	ErrFileGetFailed       = NewBizError(CodeInternalError, intl.MFile00006)

	// ------------------- port ------------------------------------------------

	ErrPortHasBeenExhausted  = NewBizError(CodeInternalError, intl.MPort00001)
	ErrPortNumbersNotCorrect = NewBizError(CodeDatabaseError, intl.MPort00002)
	ErrFreePortsFailed       = NewBizError(CodeInternalError, intl.MPort00003)
	ErrPortNotFound          = NewBizError(CodeDatabaseError, intl.MPort00004)
	ErrPortLockIsBusy        = NewBizError(CodeInternalError, intl.MPort00005)
	ErrGetPortsFailed        = NewBizError(CodeDatabaseError, intl.MPort00007)

	// ------------------ wallet -------------------
	ErrWalletRechargeOneYuan            = NewBizError(CodeBadRequest, intl.MWallet00001)
	ErrWalletWxPayError                 = NewBizError(CodeWxClientError, intl.MWallet00003)
	ErrWalletParseWxNotifyError         = NewBizError(CodeWxClientError, intl.MWallet00004)
	ErrWalletRechargeRecordNotFound     = NewBizError(CodeRecordNotFoundError, intl.MWallet00005)
	ErrWalletAliPayError                = NewBizError(CodeAliClientError, intl.MWallet00006)
	ErrWalletWaitPayTimeout             = NewBizError(CodeBadRequest, intl.MWallet00007)
	ErrWalletInsufficientBalance        = NewBizError(InsufficientBalance, intl.MWallet00008)
	ErrSubWalletNotExist                = NewBizError(CodeBadRequest, intl.MWallet00009)
	ErrSubUserWalletInsufficientBalance = NewBizError(CodeBadRequest, intl.MWallet00010)
	ErrWalletParseAliNotifyError        = NewBizError(CodeBadRequest, intl.MWallet00011)
	ErrWalletBTRecordNotFound           = NewBizError(CodeRecordNotFoundError, intl.MWallet00012)
	ErrWalletBTRecordAlreadyConfirmed   = NewBizError(CodeBadRequest, intl.MWallet00013)
	ErrWalletRefundAssets               = NewBizError(CodeBadRequest, intl.MWallet00014)
	ErrWalletCreateRefundAssets         = NewBizError(CodeBadRequest, intl.MWallet00015)
	ErrWalletRefundUuid                 = NewBizError(CodeBadRequest, intl.MWallet00016)
	ErrWalletRefundInfo                 = NewBizError(CodeBadRequest, intl.MWallet00017)
	ErrRefundFailed                     = NewBizError(CodeBadRequest, intl.MWallet00018)
	ErrBTSubunitUsedUp                  = NewBizError(CodeBadRequest, intl.MWallet00019)
	ErrBTSubunitOpenForEnterprise       = NewBizError(CodeBadRequest, intl.MWallet00020)

	// ------------------ order --------------------
	ErrOrderNotFound                                   = NewBizError(CodeRecordNotFoundError, intl.MOrder000001)
	ErrOrderUnpaidMoreThanThree                        = NewBizError(OrderUnpaidMoreThanThree, intl.MOrder000002)
	ErrOrderReplicateRenewal                           = NewBizError(CodeBadRequest, intl.MOrder000003)
	ErrOrderUnusedMoreThanThree                        = NewBizError(CodeBadRequest, intl.MOrder000004)
	ErrOrderChangeChargeTypeUnpaidCanNotMoreThanOne    = NewBizError(CodeBadRequest, intl.MOrder000005)
	ErrOrderUserChangeChargeTypeCanNotMoreThanTenToday = NewBizError(CodeBadRequest, intl.MOrder000006)
	ErrOrderExistUnpaidRenewal                         = NewBizError(CodeBadRequest, intl.MOrder000007)
	ErrOrderChangeChargeTypeInstanceStatusError        = NewBizError(CodeBadRequest, intl.MOrder000008)
	ErrOrderCannotReleaseInstanceByExistUnpaid         = NewBizError(CodeBadRequest, intl.MOrder000009)
	ErrOrderMachineRentDeadLineLimit                   = NewBizError(CodeBadRequest, intl.MOrder000010)
	ErrOrderMachineRentDeadLineExpired                 = NewBizError(CodeBadRequest, intl.MOrder000011)
	ErrOrderTimeout                                    = NewBizError(CodeBadRequest, intl.MOrder000012)
	ErrOrderPrepayToPaygIsRunning                      = NewBizError(CodeBadRequest, intl.MOrder000013)
	ErrOrderAlreadyPay                                 = NewBizError(CodeBadRequest, intl.MOrder000014)
	ErrOrderChangeToPrepaySourceTypeNotPayg            = NewBizError(CodeBadRequest, intl.MOrder000015)
	ErrOrderForPrepayExpandDataDiskUnpaidLimit         = NewBizError(CodeBadRequest, intl.MOrder000016)
	ErrOrderForPrepayExpandDataDiskInsExpired          = NewBizError(CodeBadRequest, intl.MOrder000017)
	ErrOrderCannotPay                                  = NewBizError(CodeBadRequest, intl.MOrder000018)

	// ------------------ machine ----------------------
	ErrYourInstanceInvalid           = NewBizError(CodeBadRequest, intl.MMachine00001)
	ErrMachineHardwareInfo           = NewBizError(CodeInternalError, intl.MMachine00002)
	ErrMachineRegistered             = NewBizError(CodeInternalError, intl.MMachine00003)
	ErrMachineGpuNumNotEnough        = NewBizError(CodeInternalError, intl.MMachine00004)
	ErrMachineGpuNumUpdateFailed     = NewBizError(MachineGpuNumUpdateFailed, intl.MMachine00005)
	ErrMachineUnhealthy              = NewBizError(CodeInternalError, intl.MMachine00006)
	ErrMachineRequiredInfo           = NewBizError(CodeInternalError, intl.MMachine00007)
	ErrMachineMaxInstanceNumErr      = NewBizError(CodeInternalError, intl.MMachine00008)
	ErrMachineRentDeadlineErr        = NewBizError(CodeInternalError, intl.MMachine00009)
	ErrMachineChargeTypeErr          = NewBizError(CodeInternalError, intl.MMachine00010)
	ErrMachineChargeConfigErr        = NewBizError(CodeInternalError, intl.MMachine00011)
	ErrMachineNeedAllocateRegionSign = NewBizError(CodeBadRequest, intl.MMachine00012)
	ErrMachineRegionHaveNoNFS        = NewBizError(CodeBadRequest, intl.MMachine00013)
	ErrMachineTargetNotNetDisk       = NewBizError(CodeBadRequest, intl.MMachine00014)
	ErrMachineRecharging             = NewBizError(CodeBadRequest, intl.MMachine00015)
	ErrMachinePrivateNetworkDisabled = NewBizError(CodeBadRequest, intl.MMachine00016)
	ErrMachineSrcNotNetDisk          = NewBizError(CodeBadRequest, intl.MMachine00017)
	ErrMachineWorkOrderNotFound      = NewBizError(CodeRecordNotFoundError, intl.MMachine00018)
	ErrMachineNotFound               = NewBizError(CodeRecordNotFoundError, intl.MMachine00019)
	ErrMachineCanNotCloneFormat      = NewBizError(FacilityNotSupported, intl.MMachine00020)
	ErrMachineNetworkException       = NewBizError(CodeInternalError, intl.MMachine00021)
	ErrMachineRentDeadLineLimit      = NewBizError(CodeInternalError, intl.MMachine00022)
	ErrMachineId                     = NewBizError(CodeBadRequest, intl.MMachine00023)
	ErrMachineIsCreate               = NewBizError(CodeBadRequest, intl.MMachine00024)
	ErrMachineGpuNumNotMatch         = NewBizError(CodeBadRequest, intl.MMachine00025)
	ErrMachineOnlineIsNotStartUp     = NewBizError(CodeBadRequest, intl.MMachine00026)
	ErrMachineOffline                = NewBizError(CodeBadRequest, intl.MMachine00027)
	ErrMachineChipCorpCpuArch        = NewBizError(CodeBadRequest, intl.MMachine00028)
	ErrMachineIsExistUsingSource     = NewBizError(CodeBadRequest, intl.MMachine00029)

	// -------------------- bill ------------------------
	ErrBillNotFound          = NewBizError(CodeRecordNotFoundError, intl.MBill000001)
	ErrBillBalanceNotEnough  = NewBizError(CodeServerError, intl.MBill000002)
	ErrBillExportTimeTooLong = NewBizError(CodeServerError, intl.MBill000003)
	ErrBillRechargeAuth      = NewBizError(CodeServerError, intl.MBill000004)
	ErrBillHasBeenPreCharge  = NewBizError(CodeBadRequest, intl.MBill000005)

	// ---------------------- invoice --------------------------
	ErrInvoiceAmountErr           = NewBizError(CodeServerError, intl.MInvoice000001)
	ErrInvoiceAmountTooSmallErr   = NewBizError(CodeServerError, intl.MInvoice000002)
	ErrInvoiceAlreadyExistInBill  = NewBizError(CodeServerError, intl.MInvoice000003)
	ErrInvoiceAlreadyExistInOrder = NewBizError(CodeServerError, intl.MInvoice000004)

	// -------------------- voucher -----------------------
	// 这里前台用code来判断的错误，并且值用两种，CodeAlreadyExist和CodeRecordNotFoundError
	ErrVoucherNotFound                = NewBizError(CodeRecordNotFoundError, intl.MVoucher00001)
	ErrVoucherRegisterExist           = NewBizError(CodeAlreadyExist, intl.MVoucher00002)
	ErrVoucherTypeErr                 = NewBizError(CodeBadRequest, intl.MVoucher00003)
	ErrVoucherExchangeCodeErr         = NewBizError(CodeRecordNotFoundError, intl.MVoucher00004)
	ErrVoucherOutOfStock              = NewBizError(CodeAlreadyExist, intl.MVoucher00005)
	ErrVoucherExchangeCodeAlreadyUsed = NewBizError(CodeAlreadyExist, intl.MVoucher00006)
	ErrGetInviteVoucherRulesFailed    = NewBizError(CodeInternalError, intl.MVoucher00007)
	ErrVoucherNameAlreadyExist        = NewBizError(CodeAlreadyExist, intl.MVoucher00008)
	ErrVoucherExchangeStatusReady     = NewBizError(CodeRecordNotFoundError, intl.MVoucher00009)
	ErrVoucherExchangeStatusDone      = NewBizError(CodeAlreadyExist, intl.MVoucher00010)
	ErrVoucherAppointmentStatusDone   = NewBizError(CodeBadRequest, intl.MVoucher00011)
	ErrVoucherAppointmentInvalid      = NewBizError(CodeBadRequest, intl.MVoucher00012)
	ErrVoucherInvitationUpdateNumErr  = NewBizError(CodeBadRequest, intl.MVoucher00013)
	ErrVoucherRevoked                 = NewBizError(CodeBadRequest, intl.MVoucher00014)

	// ------------------ gpu_type ----------------------
	ErrGpuTypeUsed     = NewBizError(CodeServerError, intl.GpuType00001)
	ErrGpuNameNotExist = NewBizError(CodeBadRequest, intl.GpuType00002)

	// ------------------ gpu stock ----------------------
	ErrGpuStockNotEnough            = NewBizError(CodeInternalError, intl.GpuStock00001)
	ErrGpuStockReleaseByAdminFailed = NewBizError(CodeInternalError, intl.GpuStock00002)
	ErrGpuStockReserveByAdminFailed = NewBizError(CodeInternalError, intl.GpuStock00003)
	ErrGpuStockDeleteByAdminFailed  = NewBizError(CodeInternalError, intl.GpuStock00004)

	// ------------------idle_job--------------------------
	ErrIdleJobUserNotAuthority = NewBizError(IdleJobDeleteErr, intl.IdleJob000002)

	// ----------------- container -------------------------
	ErrContainerNonGPULimited = NewBizError(CodeContainerWrongStartMode, intl.Container000001)
	ErrContainerNotFound      = NewBizError(CodeRecordNotFoundError, intl.Container000002)

	// --------------------- region -----------------------------
	ErrRegionSionNotFound                   = NewBizError(CodeRecordNotFoundError, intl.Region00001)
	ErrRegionSionAlreadyExist               = NewBizError(CodeAlreadyExist, intl.Region00002)
	ErrRegionNameAlreadyExist               = NewBizError(CodeAlreadyExist, intl.Region00003)
	ErrRegionNFSHaveNoQuotaInfo             = NewBizError(CodeBadRequest, intl.Region00004)
	ErrRegionNetDiskLimited                 = NewBizError(CodeBadRequest, intl.Region00005)
	ErrRegionUserNotInit                    = NewBizError(CodeBadRequest, intl.Region00006)
	ErrRegionNfsNotEnough                   = NewBizError(CodeBadRequest, intl.Region00007)
	ErrRegionNetDiskMaxCapacityLimit        = NewBizError(CodeBadRequest, intl.Region00008)
	ErrRegionNetDiskUnavailable             = NewBizError(CodeBadRequest, intl.Region00009)
	ErrRegionNetDiskExpansionRecordNotFound = NewBizError(CodeRecordNotFoundError, intl.Region00010)
	ErrRegionNotDeployment                  = NewBizError(CodeBadRequest, intl.Region00011)
	ErrRegionFSNotAvailable                 = NewBizError(CodeBadRequest, intl.Region00012)
	ErrRegionNameOrSignNotFound             = NewBizError(CodeRecordNotFoundError, intl.Region00013)
	ErrRegionFileStorageConfigNotFound      = NewBizError(CodeRecordNotFoundError, intl.Region00014)

	// ---------------------- invoice ------------------------------
	ErrReviewingInvoiceExisted = NewBizError(CodeServerError, intl.Invoice00001)
	ErrInvoiceRejectErr        = NewBizError(CodeServerError, intl.Invoice00002)

	// ------------------- common data ------------------------------------------
	ErrCommonDataAlreadyExist = NewBizError(CodeAlreadyExist, intl.CommonData00001)

	// ------------------------ coupon --------------------------------------
	ErrCouponAlreadyReceived             = NewBizError(CodeBadRequest, intl.Coupon00001)
	ErrCouponNotOpenReceive              = NewBizError(CodeBadRequest, intl.Coupon00002)
	ErrCouponEmpty                       = NewBizError(CodeBadRequest, intl.Coupon00003)
	ErrCouponMismatchCondition           = NewBizError(CodeBadRequest, intl.Coupon00004)
	ErrUserCouponAlreadyUsed             = NewBizError(CodeBadRequest, intl.Coupon00005)
	ErrUserCouponAlreadyEndValid         = NewBizError(UserCouponAlreadyEndValid, intl.Coupon00006)
	ErrUserCouponUseConditionLimit       = NewBizError(CodeBadRequest, intl.Coupon00007)
	ErrUserCouponOutOfScopeOfApplication = NewBizError(CodeBadRequest, intl.Coupon00008)
	ErrUserCouponAuthFailed              = NewBizError(CodeBadRequest, intl.Coupon00009)
	ErrCouponNumErr                      = NewBizError(CodeBadRequest, intl.Coupon00010)
	ErrUserCouponChargeTypeError         = NewBizError(CodeBadRequest, intl.Coupon00011)
	ErrCouponExchangeCodeErr             = NewBizError(CouponExchangeCodeNotExist, intl.Coupon00012)
	ErrCouponExchangeCodeReceived        = NewBizError(CodeExchangeCodeReceived, intl.Coupon00013)

	// --------------------- user prize -------------------------------------------
	ErrUserPrizeRecordExist = NewBizError(CodeAlreadyExist, intl.UserPrize00001)
	ErrUserPrizeAuthority   = NewBizError(CodeServerError, intl.UserPrize00002)
	ErrPrizeDrawDate        = NewBizError(CodeServerError, intl.UserPrize00003)
	ErrPrizeDrawQueryWL     = NewBizError(CodeServerError, intl.UserPrize00004)
	ErrPrizeDrawNoWhitelist = NewBizError(CodeServerError, intl.UserPrize00005)
	ErrUserPrizeAccumulate  = NewBizError(CodeServerError, intl.UserPrize00006)
	ErrPrizeWhitelistAdd    = NewBizError(CodeServerError, intl.UserPrize00007)
	ErrPrizeNoUser          = NewBizError(CodeServerError, intl.UserPrize00008)

	// --------------------- private_image --------------------------------------
	ErrPrivateImageRecordExist        = NewBizError(CodeAlreadyExist, intl.PrivateImage00001)
	ErrPrivateImageRecordNotFound     = NewBizError(CodeRecordNotFoundError, intl.PrivateImage00002)
	ErrPrivateImageStatusIsSaving     = NewBizError(CodeServerError, intl.PrivateImage00003)
	ErrPrivateImageStatusNotSaved     = NewBizError(CodeServerError, intl.PrivateImage00004)
	ErrPrivateImageNotShared          = NewBizError(CodeServerError, intl.PrivateImage00005)
	ErrPrivateImageSharing            = NewBizError(CodeServerError, intl.PrivateImage00006)
	ErrPrivateImageNumErr             = NewBizError(CodeServerError, intl.PrivateImage00007)
	ErrPrivateImageIsBusy             = NewBizError(CodeBadRequest, intl.PrivateImage00008)
	ErrCodeWithGpuImageNotReady       = NewBizError(CodeBadRequest, intl.PrivateImage00009)
	ErrPrivateImageNotExist           = NewBizError(CodeRecordNotFoundError, intl.PrivateImage00010)
	ErrBaseImageNotExist              = NewBizError(CodeRecordNotFoundError, intl.PrivateImage00011)
	ErrPrivateImageAllocateSomeFailed = NewBizError(CodeBadRequest, intl.PrivateImage00012)
	ErrPrivateImageAuthFailed         = NewBizError(CodeBadRequest, intl.PrivateImage00013)

	// ----------------------- data disk --------------------------------
	ErrDataDiskMaxExpandSizeLimit      = NewBizError(CodeBadRequest, intl.DataDisk00001)
	ErrDataDiskReserveInsufficientDisk = NewBizError(CodeBadRequest, intl.DataDisk00002)
	ErrDataDiskChangeSizeFailed        = NewBizError(CodeBadRequest, intl.DataDisk00003)
	ErrDataDiskOnlyPaygCanChangeSize   = NewBizError(CodeBadRequest, intl.DataDisk00004)
	ErrDataDiskReduceFreeSizeLimit     = NewBizError(CodeBadRequest, intl.DataDisk00005)
	ErrDataDiskReduceUsedSizeLimit     = NewBizError(CodeBadRequest, intl.DataDisk00006)
	ErrDataDiskChangeSizeInsShouldDown = NewBizError(CodeBadRequest, intl.DataDisk00007)
	ErrDataDiskReduceOnlyPayg          = NewBizError(CodeBadRequest, intl.DataDisk00008)

	// ---------------------- credit wallet -------------------------------
	ErrCreditWalletUserNotFound                  = NewBizError(CodeBadRequest, intl.CreditWallet00001)
	ErrCreditWalletReduceCreditLimitLessThanUsed = NewBizError(CodeBadRequest, intl.CreditWallet00002)
	ErrCreditWalletNotCreated                    = NewBizError(CodeBadRequest, intl.CreditWallet00003)
	ErrCreditWalletCreditLimitNotEnough          = NewBizError(CodeBadRequest, intl.CreditWallet00004)
	ErrCreditWalletRepayCreditLimitUsedLimit     = NewBizError(CodeBadRequest, intl.CreditWallet00005)
	ErrCreditWalletExist                         = NewBizError(CodeBadRequest, intl.CreditWallet00006)
	ErrCreditWalletOperateLimitOneYuan           = NewBizError(CodeBadRequest, intl.CreditWallet00007)

	// ---------------------------- public data -------------------
	ErrpublicDataNotFound  = NewBizError(CodeRecordNotFoundError, intl.PublicData00001)
	ErrOfficialLinkTooLong = NewBizError(CodeRecordNotFoundError, intl.PublicData00002)

	// ------------------- notice -------------------
	ErrNoticeExpiredAtError = NewBizError(CodeBadRequest, intl.Notice00001)

	// ------------------- codewithgpu -------------------------
	ErrCommunityImageNotExist = NewBizError(CodeBadRequest, intl.Community0001)

	// ------------------- deployment ---------------------
	ErrDeploymentRecordNotFound              = NewBizError(CodeRecordNotFoundError, intl.Deployment0001)
	ErrDCRecordNotFound                      = NewBizError(CodeRecordNotFoundError, intl.Deployment0002)
	ErrDeploymentDeleteFailed                = NewBizError(CodeBadRequest, intl.Deployment0003)
	ErrSetDeploymentBlacklist                = NewBizError(CodeBadRequest, intl.Deployment0004)
	ErrDeploymentStoppedWhenCreateDDP        = NewBizError(CodeBadRequest, intl.Deployment0005)
	ErrDeploymentCreateDDPFailed             = NewBizError(CodeBadRequest, intl.Deployment0006)
	ErrDeploymentDDPNotExist                 = NewBizError(CodeRecordNotFoundError, intl.Deployment0007)
	ErrDeploymentRefundDDPFailed             = NewBizError(CodeBadRequest, intl.Deployment0008)
	ErrDeploymentDDPNotFoundMachine          = NewBizError(CodeRecordNotFoundError, intl.Deployment0009)
	ErrDeploymentUserVersionExist            = NewBizError(CodeBadRequest, intl.Deployment0010)
	ErrDeploymentUserVersionLength           = NewBizError(CodeBadRequest, intl.Deployment0011)
	ErrDeploymentBlockExpiryMinuteIsNegative = NewBizError(CodeBadRequest, intl.Deployment0012)
	ErrGetDeploymentBlacklist                = NewBizError(CodeBadRequest, intl.Deployment0013)

	// -------------------- storage agent ------------------------
	ErrStorageAgentUninitialized = NewBizError(CodeBadRequest, intl.StorageAgent0001)

	// -------------------- work order ------------------------
	ErrWorkOrderRecordNotFound                    = NewBizError(CodeRecordNotFoundError, intl.WorkOrder0001)
	UnProcessedSameMonitorExist                   = NewBizError(CodeAlreadyExist, intl.WorkOrder0002)
	UnFinishedSameWorkOrderExist                  = NewBizError(CodeAlreadyExist, intl.WorkOrder0003)
	ErrWorkOrderTenantSignExist                   = NewBizError(CodeAlreadyExist, intl.WorkOrder0004)
	ErrWorkOrderMaterialNameExist                 = NewBizError(CodeAlreadyExist, intl.WorkOrder0005)
	ErrWorkOrderCustomNotExist                    = NewBizError(CodeRecordNotFoundError, intl.WorkOrder0006)
	ErrRangeWorkOrderMachineExist                 = NewBizError(CodeAlreadyExist, intl.WorkOrder0007)
	ErrDispatchOrderSparePartNotExist             = NewBizError(CodeRecordNotFoundError, intl.WorkOrder0008)
	ErrWorkOrderMachineTypeNameExist              = NewBizError(CodeAlreadyExist, intl.WorkOrder0009)
	ErrWorkOrderMaterialExistInMachineType        = NewBizError(CodeAlreadyExist, intl.WorkOrder0010)
	ErrWorkOrderMachineNotExist                   = NewBizError(CodeRecordNotFoundError, intl.WorkOrder0011)
	ErrDispatchOrderSparePartExistInDispatchOrder = NewBizError(CodeAlreadyExist, intl.WorkOrder0012)
	ErrCustomExist                                = NewBizError(CodeAlreadyExist, intl.WorkOrder0013)
	ErrAutoUserExistInTenant                      = NewBizError(CodeAlreadyExist, intl.WorkOrder0014)
	ErrWorkOrderTenantNotExist                    = NewBizError(CodeRecordNotFoundError, intl.WorkOrder0015)
	ErrDispatchOrderNotExistInTenant              = NewBizError(CodeRecordNotFoundError, intl.WorkOrder0016)
	ErrWorkOrderRepairNotExistInDispatchOrder     = NewBizError(CodeRecordNotFoundError, intl.WorkOrder0017)
	ErrMachineNotExistInTenant                    = NewBizError(CodeRecordNotFoundError, intl.WorkOrder0018)
	ErrWorkOrderSparePartNotExist                 = NewBizError(CodeAlreadyExist, intl.WorkOrder0019)
	ErrWorkOrderSparePartHasBoundDispatchOrder    = NewBizError(CodeAlreadyExist, intl.WorkOrder0020)
	ErrWorkOrderRepairNotExist                    = NewBizError(CodeAlreadyExist, intl.WorkOrder0021)
	ErrWorkOrderRepairHasBoundDispatchOrder       = NewBizError(CodeAlreadyExist, intl.WorkOrder0022)
	ErrWorkOrderSmsNameExist                      = NewBizError(CodeAlreadyExist, intl.WorkOrder0023)
	ErrWorkOrderSmsCodeNotExist                   = NewBizError(CodeAlreadyExist, intl.WorkOrder0024)
	ErrWorkOrderInstanceNotifyExist               = NewBizError(CodeAlreadyExist, intl.WorkOrder0025)
	ErrWorkOrderInstanceNotifyNotExist            = NewBizError(CodeRecordNotFoundError, intl.WorkOrder0026)
	ErrWorkOrderInstanceNotifyExistInMachine      = NewBizError(CodeRecordNotFoundError, intl.WorkOrder0027)
	ErrWorkOrderInstanceFormatError               = NewBizError(FeishuRemindFailed, intl.WorkOrder0028)
	ErrWorkOrderFeishuUrlError                    = NewBizError(FeishuUrlFailed, intl.WorkOrder0029)
	ErrWorkOrderStatusIsFinishNotSubscribe        = NewBizError(WorkOrderSubscribeErr, intl.WorkOrder0030)

	// -------------------- Sms ------------------------
	ErrSmsPhoneErr = NewBizError(CodeBadRequest, intl.Sms0001)

	// -------------------- ScheduleInstance ------------------------
	ErrScheduleInstanceMachineNotExist = NewBizError(CodeRecordNotFoundError, intl.ScheduleInstance0001)
	ErrScheduleInstanceNotExist        = NewBizError(CodeRecordNotFoundError, intl.ScheduleInstance0002)
	ErrScheduleInstanceAuthFailed      = NewBizError(CodeBadRequest, intl.ScheduleInstance0003)
	ErrScheduleInstanceSuccess         = NewBizError(CodeBadRequest, intl.ScheduleInstance0004)

	// -------------------- Construct ------------------------
	ErrContractBillStatusError       = NewBizError(CodeBadRequest, intl.Contract0001)
	ErrContractUUIDExist             = NewBizError(CodeAlreadyExist, intl.Contract0002)
	ErrContractBillStatusUpdateError = NewBizError(CodeBadRequest, intl.Contract0003)
)
