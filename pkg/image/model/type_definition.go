package model

import (
	"server/pkg-agent/agent_constant"
	"server/pkg/constant"
	"time"
)

type ImageMetaData struct {
	ID        int    `json:"id"`
	ImageName string `json:"image_name"`
}

type ImageCompleteInfo struct {
	// key: framework_name, framework_version, python_version, cuda_version, val: ImageMetaData
	Images map[constant.FrameworkNameType]map[constant.FrameworkVersionType]map[constant.PythonVersionType]map[constant.CudaVersionType]ImageMetaData
}

func (i *ImageCompleteInfo) initBaseMap() {
	i.Images = make(map[constant.FrameworkNameType]map[constant.FrameworkVersionType]map[constant.PythonVersionType]map[constant.CudaVersionType]ImageMetaData)
}

func (i *ImageCompleteInfo) InMap(image Image) {
	if i.Images == nil {
		i.initBaseMap()
	}

	meta := ImageMetaData{
		ID:        image.ID,
		ImageName: image.ImageName,
	}

	if i.Images[image.FrameworkName] == nil {
		i.Images[image.FrameworkName] = make(map[constant.FrameworkVersionType]map[constant.PythonVersionType]map[constant.CudaVersionType]ImageMetaData)
	}

	if i.Images[image.FrameworkName][image.FrameworkVersion] == nil {
		i.Images[image.FrameworkName][image.FrameworkVersion] = make(map[constant.PythonVersionType]map[constant.CudaVersionType]ImageMetaData)
	}

	if i.Images[image.FrameworkName][image.FrameworkVersion][image.PythonVersion] == nil {
		i.Images[image.FrameworkName][image.FrameworkVersion][image.PythonVersion] = make(map[constant.CudaVersionType]ImageMetaData)
	}

	i.Images[image.FrameworkName][image.FrameworkVersion][image.PythonVersion][image.CudaVersion] = meta
}

type ValidUserImageParams struct {
	UID              int
	PTitle           string
	TagSuffix        string
	PrivateImageUUID string
}

type ValidUserImageResp struct {
	PublicImageName  string
	PrivateImageSize int64
	PrivateImageName string
	Framework        string
	FrameworkV       string
	CudaV            string
	ChipCorp         constant.ChipCorp
	CpuArch          constant.CpuArch
}

type ImageWorkerGetWaitingReply struct {
	CommunityImageUUID  string                          `json:"community_image_uuid"`
	BuildCommunityImage bool                            `json:"build_community_image"` // 为true代表有任务
	BuildPublicImage    bool                            `json:"build_public_image"`
	PublicImage         string                          `json:"public_image"`
	BaseImage           string                          `json:"base_image"`
	PullBucket          agent_constant.MinioBucketInfo  `json:"pull_bucket"`
	PullCredential      agent_constant.MinioCredentials `json:"pull_credential"`
	PushBucket          agent_constant.MinioBucketInfo  `json:"push_bucket"`
	PushCredential      agent_constant.MinioCredentials `json:"push_credential"`
	CommunityDiffFile   string                          `json:"community_diff_file"` // 如果不为空，则需要上传
}

func (p *ImageWorkerGetWaitingReply) IsValid() bool {
	return p.BuildPublicImage || p.BuildCommunityImage
}

type ImageWorkerPostStatusParams struct {
	CommunityImageUUID string `json:"community_image_uuid"`

	BuildPublicImage    bool                       `json:"build_public_image"`
	PublicImageStatus   constant.PublicImageStatus `json:"public_image_status"`
	PublicImageProgress int                        `json:"public_image_progress"`
	PublicImagErrMsg    string                     `json:"public_imag_err_msg"`
	PublicImageSize     int64                      `json:"public_image_size"`

	BuildCommunityImage    bool                       `json:"build_community_image"` // 为true代表有任务
	CommunityImageStatus   constant.PublicImageStatus `json:"community_image_status"`
	CommunityImageProgress int                        `json:"community_image_progress"`
	CommunityImageErrMsg   string                     `json:"community_image_err_msg"`
	CommunityImageSize     int64                      `json:"community_image_size"`
}

type PublicImageWorkerPostPICStatusParams struct {
	PrivateImageUUID string                     `json:"private_image_uuid"`
	Status           constant.PublicImageStatus `json:"status"`
	Progress         int                        `json:"progress"`
	OssFileSize      int64                      `json:"oss_file_size"`
	ErrMsg           string                     `json:"err_msg"`
}

type CodeWithGpuValidImageResp struct {
	Username           string                     `json:"username"`
	EntityUUID         string                     `json:"entity_uuid"`
	EntityID           int                        `json:"entity_id"`
	CommunityImageUUID string                     `json:"private_uuid,omitempty"`
	Image              string                     `json:"image"`
	PythonV            constant.PythonVersionType `json:"python_v"`
	CudaV              constant.CudaVersionType   `json:"cuda_v"`
	Framework          string                     `json:"framework"`   // 框架
	FrameworkV         string                     `json:"framework_v"` // 框架版本
	CreatedAt          time.Time                  `json:"created_at"`  // 发布时间
	DiskSize           int64                      `json:"disk_size"`
	ChipCorp           string                     `json:"chip_corp"`
	CpuArch            string                     `json:"cpu_arch"`
}

type UserUsageInfo struct {
	MaxUsage     int64   `json:"max_usage"`
	CurrentUsage int64   `json:"current_usage"`
	ToCost       float64 `json:"to_cost"`
	FreeSize     int64   `json:"free_size"`
}

type PrivateImageGetListForAllocateReq struct {
	Uid       int    `form:"-" json:"-"`
	ImageUuid string `form:"image_uuid" json:"image_uuid"`
}
type PrivateImageGetListForAllocateRes struct {
	Uuid    string `json:"uuid"`
	Name    string `json:"name"`
	Creator string `json:"creator"`
	SubName string `json:"sub_name"`
	Uid     int    `json:"uid"`
}

type PrivateImageAllocateReq struct {
	ImageUuid string `json:"image_uuid"`
	SubName   string `json:"sub_name"`
}

type GetAllPrivateImageReq struct {
	ImageUuid string              `form:"image_uuid" json:"image_uuid"`
	ChipCorp  []constant.ChipCorp `json:"chip_corp"` //芯片厂商
	CpuArch   []constant.CpuArch  `json:"cpu_arch"`  //cpu架构
}
