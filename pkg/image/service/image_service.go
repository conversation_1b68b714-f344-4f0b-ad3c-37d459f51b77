package service

import (
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/image/model"
	"server/pkg/logger"
	"sort"
	"time"
)

const ModuleName = "image_service"

type ImageService struct {
	log *logger.Logger
}

func NewImageServiceProvider() *ImageService {
	return &ImageService{
		log: logger.NewLogger(ModuleName),
	}
}

func (svc *ImageService) Create(params *model.CreateImageParams) (err error) {
	if params == nil {
		svc.log.Error("create image params is nil")
		err = businesserror.ErrInvalidRequestParams
		return
	}
	image := &model.Image{ImageName: params.ImageName,
		FrameworkName:    params.FrameworkName,
		FrameworkVersion: params.FrameworkVersion,
		PythonVersion:    params.PythonVersion,
		CudaVersion:      params.CudaVersion,
		CreatedAt:        time.Now(),
		ChipCorp:         params.ChiCorp,
	}
	err = db_helper.InsertOne(db_helper.QueryDefinition{ModelDefinition: &model.Image{}, InsertPayload: image}).GetError()
	if err != nil {
		svc.log.WithField("err", err).Error("create image failed.")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *ImageService) Update(params *model.UpdateImageParams) (err error) {
	if params == nil {
		svc.log.Error("update image params is null")
		err = businesserror.ErrInvalidRequestParams
		return
	}
	image := &model.Image{ImageName: params.ImageName,
		FrameworkName:    params.FrameworkName,
		FrameworkVersion: params.FrameworkVersion,
		PythonVersion:    params.PythonVersion,
		CudaVersion:      params.CudaVersion,
		ChipCorp:         params.ChiCorp,
		CpuArch:          params.CpuArch,
	}
	err = db_helper.UpdateOne(db_helper.QueryDefinition{ModelDefinition: &model.Image{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": params.ImageID}}}, image).GetError()
	if err != nil {
		svc.log.WithField("err", err).WithField("id ", params.ImageID).Error("update image failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *ImageService) List(db *gorm.DB, imageName string, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []*model.Image, err error) {
	list = make([]*model.Image, 0)

	if db == nil {
		db = db_helper.GlobalDBConn()
	}

	db = db.Table(model.TableNameImage).Where("deleted_at is null")

	if imageName != "" {
		name := "%" + imageName + "%"
		db = db.Where("image_name like ?", name)
	}
	if pageReq.DateFrom != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') >= ?", pageReq.DateFrom)
	}
	if pageReq.DateTo != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') <= ?", pageReq.DateTo)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithField("err", err).Error("Count failed.")
		err = businesserror.ErrDatabaseError
		return
	}

	paged = db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, int(count), 0)
	paged.List = &list

	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("rank desc").
		Find(&list).
		Error
	if err != nil {
		svc.log.WithField("image_name", imageName).WithField("err", err).Error("get image failed.")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *ImageService) Delete(imageID int) (err error) {
	if imageID < 1 {
		svc.log.WithField("image_id", imageID).Error("image_id must greater than 0")
		err = businesserror.ErrInvalidRequestParams
		return
	}
	err = db_helper.Delete(db_helper.QueryDefinition{ModelDefinition: &model.Image{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": imageID}}}, &model.Image{}).GetError()
	if err != nil {
		svc.log.WithField("err", err).WithField("image_id", imageID).Error("delete image failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *ImageService) Get(params *model.GetImageParams) (images []*model.Image, err error) {
	return svc.get(db_helper.GlobalDBConn(), params)
}

func (svc *ImageService) GetFromRO(params *model.GetImageParams) (images []*model.Image, err error) {
	return svc.get(db_helper.GlobalDBConnForRead(), params)
}

func (svc *ImageService) get(db *gorm.DB, params *model.GetImageParams) (images []*model.Image, err error) {
	images = make([]*model.Image, 0)
	if params == nil {
		svc.log.Error("filter is null, can not find image")
		err = businesserror.ErrInvalidRequestParams
		return
	}
	filter := map[string]interface{}{}
	if len(params.ImageName) > 0 {
		filter["image_name"] = params.ImageName
	}
	if len(params.FrameworkName) > 0 {
		filter["framework_name"] = params.FrameworkName
	}
	if len(params.FrameworkVersion) > 0 {
		filter["framework_version"] = params.FrameworkVersion
	}
	if len(params.PythonVersion) > 0 {
		filter["python_version"] = params.PythonVersion
	}
	if len(params.CudaVersion) > 0 {
		filter["cuda_version"] = params.CudaVersion
	}
	if len(params.ChiCorp) > 0 {
		filter["chi_corp"] = params.ChiCorp
	}
	if len(params.CpuArch) > 0 {
		filter["cpu_arch"] = params.CpuArch
	}

	if db == nil {
		db = db_helper.GlobalDBConn()
	}

	err = db_helper.GetAll(db_helper.QueryDefinition{
		DBTransactionConnection: db,
		ModelDefinition:         &model.Image{},
		Filters: db_helper.QueryFilters{
			EqualFilters: filter,
		},
	}, &images).GetError()
	if err != nil {
		svc.log.WithFields(logger.Fields{"framework_name": params.FrameworkName,
			"framework_version": params.FrameworkVersion,
			"python_version":    params.PythonVersion,
			"cuda_version":      params.CudaVersion}).WithField("err", err).Error("get image failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *ImageService) GetByUUID(imageUUID string) (image *model.Image, err error) {
	image = &model.Image{}
	err = db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: image,
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uuid": imageUUID}},
	}, &image).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = businesserror.ErrBaseImageNotExist
			return nil, err
		}
		svc.log.WithField("imageUUID", imageUUID).ErrorE(err, "get image by uuid failed")
		return nil, err
	}
	return
}

func (svc *ImageService) CheckImageName(imageName string, imageID int) (existed bool, err error) {
	return svc.checkImageName(db_helper.GlobalDBConn(), imageName, imageID)
}

func (svc *ImageService) CheckImageNameFromRO(imageName string, imageID int) (existed bool, err error) {
	return svc.checkImageName(db_helper.GlobalDBConnForRead(), imageName, imageID)
}

func (svc *ImageService) checkImageName(db *gorm.DB, imageName string, imageID int) (existed bool, err error) {
	if len(imageName) <= 0 {
		svc.log.Error("image_name can not be null")
		err = businesserror.ErrInvalidRequestParams
		return
	}

	if db == nil {
		db = db_helper.GlobalDBConn()
	}

	image := &model.Image{}
	err = db_helper.GetOne(db_helper.QueryDefinition{
		DBTransactionConnection: db,
		ModelDefinition:         &model.Image{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{"image_name": imageName}}}, image).GetError()
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, nil
		}
		svc.log.WithField("err", err).Error("get image by image_name failed")
		err = businesserror.ErrDatabaseError
		return
	}
	if imageID > 0 {
		if image.ID == imageID {
			return false, nil
		}
	}
	return true, nil

}

func (svc *ImageService) GetImageFramework() (frameworks []string, err error) {
	images := make([]*model.Image, 0)
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.Image{},
	}, &images).GetError()
	if err != nil {
		svc.log.Error("get image failed")
		err = businesserror.ErrDatabaseError
		return
	}
	frameworkMap := map[string]int{}
	for _, image := range images {
		if _, ok := frameworkMap[image.FrameworkName.String()]; !ok {
			frameworkMap[image.FrameworkName.String()] = 1
			frameworks = append(frameworks, image.FrameworkName.String())
		}
	}
	return
}

// GetImageCompleteInfo 新版前端下拉框, 暂时返回所有, 需要前端继续考虑分段拼接下拉框的可行性
func (svc *ImageService) GetImageCompleteInfo(chipCorp []constant.ChipCorp, cpuArch []constant.CpuArch) (list model.ImageMetaUnitSort, err error) {
	var images []model.Image
	list = make([]model.ImageMetaUnit, 0)
	infilters := make([]db_helper.In, 0)
	if len(chipCorp) > 0 {
		infilters = append(infilters, db_helper.In{
			Key:   "chip_corp",
			InSet: chipCorp,
		})
	}
	if len(cpuArch) > 0 {
		infilters = append(infilters, db_helper.In{
			Key:   "cpu_arch",
			InSet: cpuArch,
		})
	}
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.Image{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"is_off": false, // 普通用户使用. 仅能看到上架的.
			},
			InFilters: infilters,
		},
	}, &images).GetError()
	if err != nil {
		svc.log.WarnE(err, "Get all image failed in GetImageCompleteInfo().")
		err = businesserror.ErrDatabaseError
		return
	}

	if len(images) == 0 {
		return
	}

	// 00 00 00 00
	//imageMap := make(map[int]ImageFrameworkMap)
	cudaVMap := make(map[int]model.ImageCudaV)
	pythonVMap := make(map[int]model.ImagePythonVMap)
	frameworkVMap := make(map[int]model.ImageFrameworkVMap)
	frameworkMap := make(map[int]model.ImageFrameworkMap)

	for _, image := range images {
		frameworkSort := image.Rank / 1000000 // framework
		frameworkVSort := image.Rank / 10000  // frameworkV
		pythonVSort := image.Rank / 100       // pythonV
		cudaVSort := image.Rank               // cudaV -> imageName

		meta := model.ImageCudaV{
			Sort:      cudaVSort,
			ID:        image.ID,
			CudaV:     image.CudaVersion,
			ImageName: image.ImageName,
			ImageUUID: image.UUID,
			ChipCorp:  image.ChipCorp,
			CpuArch:   image.CpuArch,
		}

		cudaVMap[cudaVSort] = meta
		pythonVMap[pythonVSort] = model.ImagePythonVMap{Sort: pythonVSort, PythonV: image.PythonVersion, CudaV: map[int]model.ImageCudaV{}}
		frameworkVMap[frameworkVSort] = model.ImageFrameworkVMap{Sort: frameworkVSort, FrameworkV: image.FrameworkVersion, PythonV: map[int]model.ImagePythonVMap{}}
		frameworkMap[frameworkSort] = model.ImageFrameworkMap{Sort: frameworkSort, Framework: image.FrameworkName, FrameworkV: map[int]model.ImageFrameworkVMap{}}
	}

	for k, v := range frameworkVMap {
		frameworkMap[k/100].FrameworkV[k] = v
	}
	for k, v := range pythonVMap {
		frameworkMap[k/10000].FrameworkV[k/100].PythonV[k] = v
	}
	for k, v := range cudaVMap {
		frameworkMap[k/1000000].FrameworkV[k/10000].PythonV[k/100].CudaV[k] = v
	}

	for _, framework := range frameworkMap {
		frameworkVList := make(model.ImageMetaUnitSort, 0)
		for _, frameworkV := range framework.FrameworkV {
			pythonVList := make(model.ImageMetaUnitSort, 0)
			for _, pythonV := range frameworkV.PythonV {
				cudaVList := make(model.ImageMetaUnitSort, 0)
				for _, meta := range pythonV.CudaV {
					cudaVList = append(cudaVList, model.ImageMetaUnit{Sort: meta.Sort, Label: meta.CudaV.String(),
						LabelName: map[string]interface{}{"i": meta.ImageName, "v": meta.CudaV, "uuid": meta.ImageUUID, "chip_corp": meta.ChipCorp, "cpu_arch": meta.CpuArch}})
				}
				sort.Sort(cudaVList)
				pythonVUnit := model.ImageMetaUnit{Sort: pythonV.Sort, Label: pythonV.PythonV.String(), LabelName: pythonV.PythonV.String(), Children: cudaVList}
				pythonVList = append(pythonVList, pythonVUnit)
			}
			sort.Sort(pythonVList)
			frameworkVUnit := model.ImageMetaUnit{Sort: frameworkV.Sort, Label: frameworkV.FrameworkV.String(), LabelName: frameworkV.FrameworkV.String(), Children: pythonVList}
			frameworkVList = append(frameworkVList, frameworkVUnit)
		}
		sort.Sort(frameworkVList)
		frameworkUnit := model.ImageMetaUnit{Sort: framework.Sort, Label: framework.Framework.String(), LabelName: framework.Framework.String(), Children: frameworkVList}
		list = append(list, frameworkUnit)
	}
	sort.Sort(list)

	return
}

// PutOn 上架
func (svc *ImageService) PutOn(imageID int) error {
	err := db_helper.UpdateOne(db_helper.QueryDefinition{
		ModelDefinition: &model.Image{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"id": imageID,
			},
		},
	}, map[string]interface{}{
		"is_off": false,
	}).GetError()
	if err != nil {
		svc.log.WarnE(err, "Update image_id=%d 'is_off' to false failed.", imageID)
		return err
	}
	return nil
}

// PullOff 下架
func (svc *ImageService) PullOff(imageID int) error {
	err := db_helper.UpdateOne(db_helper.QueryDefinition{
		ModelDefinition: &model.Image{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"id": imageID,
			},
		},
	}, map[string]interface{}{
		"is_off": true,
	}).GetError()
	if err != nil {
		svc.log.WarnE(err, "Update image_id=%d 'is_off' to true failed.", imageID)
		return err
	}
	return nil
}

func (svc *ImageService) IsImageOff(imageID int) (off bool, err error) {
	return svc.isImageOff(db_helper.GlobalDBConn(), imageID)
}

func (svc *ImageService) IsImageOffFromRO(imageID int) (off bool, err error) {
	return svc.isImageOff(db_helper.GlobalDBConnForRead(), imageID)
}

func (svc *ImageService) isImageOff(db *gorm.DB, imageID int) (off bool, err error) {
	if db == nil {
		db = db_helper.GlobalDBConn()
	}

	var image model.Image
	err = db_helper.GetOne(db_helper.QueryDefinition{
		DBTransactionConnection: db,
		ModelDefinition:         &model.Image{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"id": imageID,
			},
		},
	}, &image).GetError()
	if err != nil {
		svc.log.WarnE(err, "Get image_id=%d failed.", imageID)
		return false, err
	}

	off = image.IsOff
	return
}

func (svc *ImageService) GetOnShelves(params *model.GetImageParams) (images []*model.Image, err error) {
	images = make([]*model.Image, 0)
	if params == nil {
		svc.log.Warn("filter is null, can not find image in GetOnShelves()")
		err = businesserror.ErrInvalidRequestParams
		return
	}

	filter := map[string]interface{}{
		"is_off": false, // 普通用户使用. 仅能看到上架的.
	}

	// TODO: 优化此处
	if len(params.ImageName) > 0 {
		filter["image_name"] = params.ImageName
	}
	if len(params.FrameworkName) > 0 {
		filter["framework_name"] = params.FrameworkName
	}
	if len(params.FrameworkVersion) > 0 {
		filter["framework_version"] = params.FrameworkVersion
	}
	if len(params.PythonVersion) > 0 {
		filter["python_version"] = params.PythonVersion
	}
	if len(params.CudaVersion) > 0 {
		filter["cuda_version"] = params.CudaVersion
	}
	if len(params.ChiCorp) > 0 {
		filter["chi_corp"] = params.ChiCorp
	}

	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.Image{},
		Filters: db_helper.QueryFilters{
			EqualFilters: filter,
		},
	}, &images).GetError()
	if err != nil {
		svc.log.WarnE(err, "User get on shelves image failed. params: %+v", params)
		return nil, businesserror.ErrDatabaseError
	}
	return
}

func (svc *ImageService) GetOnShelvesImageFramework() (frameworks []string, err error) {
	var images []model.Image
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.Image{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"is_off": false, // 普通用户使用. 仅能看到上架的.
			},
		},
	}, &images).GetError()
	if err != nil {
		svc.log.WarnE(err, "User get on shelves image framework failed.")
		return frameworks, businesserror.ErrDatabaseError
	}

	frameworkMap := map[string]int{}
	for _, image := range images {
		if _, ok := frameworkMap[image.FrameworkName.String()]; !ok {
			frameworkMap[image.FrameworkName.String()] = 1
			frameworks = append(frameworks, image.FrameworkName.String())
		}
	}
	return
}
