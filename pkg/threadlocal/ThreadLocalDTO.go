package threadlocal

import (
	"github.com/timandy/routine"
)

var (
	HttpHeaderXRequestIDKey = "X-Request-ID"
	threadLocalContext      = routine.NewInheritableThreadLocal[*ThreadLocalContext]()
)

// ThreadLocalContext ThreadLocal上下文结构体，提供类型安全的上下文管理
type ThreadLocalContext struct {
	// requestID 请求ID，用于请求链路追踪
	requestID string // RequestID
}

const RequestID = "request_id"

// getContext 获取ThreadLocal上下文，如果不存在则返回nil
func getContext() *ThreadLocalContext {
	return threadLocalContext.Get()
}

// getOrCreateContext 获取或创建ThreadLocal上下文
func getOrCreateContext() *ThreadLocalContext {
	ctx := threadLocalContext.Get()
	if ctx == nil {
		ctx = &ThreadLocalContext{}
		threadLocalContext.Set(ctx)
	}
	return ctx
}

// GetRequestID 获取RequestID，保持向后兼容
func GetRequestID() string {
	ctx := getContext()
	if ctx != nil && ctx.requestID != "" {
		return ctx.requestID
	}

	return ""
}

// SetRequestID 设置RequestID
func SetRequestID(requestID string) {
	ctx := getOrCreateContext()
	ctx.requestID = requestID
}

// ClearContext 清理ThreadLocal上下文
func ClearContext() {
	threadLocalContext.Remove()
}

// GetContext 获取完整的ThreadLocal上下文（用于调试和高级用法）
func GetContext() *ThreadLocalContext {
	ctx := getContext()
	if ctx == nil {
		return &ThreadLocalContext{} // 返回空的上下文而不是nil，避免空指针
	}

	// 返回副本，避免外部修改
	return &ThreadLocalContext{
		requestID: ctx.requestID,
	}
}

// HasRequestID 检查是否设置了RequestID
func HasRequestID() bool {
	ctx := getContext()
	return ctx != nil && ctx.requestID != ""
}
