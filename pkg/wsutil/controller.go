package wsutil

import (
	"context"
	"encoding/json"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	logger "github.com/sirupsen/logrus"
)

type Message struct {
	ID      string `json:"id"`
	Route   string `json:"route"`
	Payload string `json:"payload"`
}

type Handler func(ctx context.Context, message Message, writing chan<- Message) error

type Controller struct {
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup

	conn *websocket.Conn

	routes  map[string]Handler
	reading chan Message
	writing chan Message

	logger *logger.Entry
	debug  bool
}

func NewController(ctx context.Context, conn *websocket.Conn, routes map[string]Handler) *Controller {
	ctx, cancel := context.WithCancel(ctx)
	return &Controller{
		ctx:     ctx,
		cancel:  cancel,
		conn:    conn,
		routes:  routes,
		reading: make(chan Message, 100),
		writing: make(chan Message, 100),
		logger:  logger.WithContext(ctx),
	}
}

func (c *Controller) Writing() chan<- Message {
	return c.writing
}

func (c *Controller) SetDebug(debug bool) {
	c.debug = debug
}

func (c *Controller) Run() {
	c.wg.Add(3)
	go c.read()
	go c.write()
	go c.handle()
	c.wg.Wait()

	close(c.reading)
	close(c.writing)
}

func (c *Controller) read() {
	c.logger.Info("[websocket] starting read loop")
	defer func() {
		c.logger.Info("[websocket] stopping read loop")
		c.cancel()
		c.wg.Done()
		c.conn.Close()
	}()

	c.conn.SetPongHandler(func(string) error {
		if err := c.conn.SetReadDeadline(time.Now().Add(time.Second * 30)); err != nil {
			c.logger.Errorf("[websocket] %v", err)
			return err
		}
		return nil
	})

	if err := c.conn.SetReadDeadline(time.Now().Add(time.Second * 30)); err != nil {
		c.logger.Errorf("[websocket] %v", err)
		return
	}

	var (
		err     error
		b       []byte
		message Message
	)
	for {
		select {
		case <-c.ctx.Done():
			return
		default:
			_, b, err = c.conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					c.logger.Errorf("[websocket] %v", err)
				} else {
					c.logger.Warnf("[websocket] %v", err)
				}
				return
			}

			if err = json.Unmarshal(b, &message); err != nil {
				c.logger.Errorf("[websocket] %v", err)
				continue
			}

			if c.debug {
				logger.Infof("[websocket] read message: %s", string(b))
			}

			c.reading <- message
		}
	}
}

func (c *Controller) write() {
	c.logger.Info("[websocket] starting write loop")
	defer func() {
		c.logger.Info("[websocket] stopping write loop")
		c.cancel()
		c.wg.Done()
		c.conn.Close()
	}()

	var (
		err     error
		b       []byte
		message Message
		ok      bool
		ticker  = time.NewTicker(time.Second * 10)
	)
	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			if err = c.conn.SetWriteDeadline(time.Now().Add(time.Second * 10)); err != nil {
				c.logger.Errorf("[websocket] %v", err)
				return
			}

			if err = c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				c.logger.Errorf("[websocket] %v", err)
				return
			}
		case message, ok = <-c.writing:
			if !ok {
				c.logger.Error("[websocket] failed to read writing channel")
				return
			}

			if err = c.conn.SetWriteDeadline(time.Now().Add(time.Second * 10)); err != nil {
				c.logger.Errorf("[websocket] %v", err)
				return
			}

			b, err = json.Marshal(message)
			if err != nil {
				c.logger.Errorf("[websocket] %v", err)
				continue
			}

			if err = c.conn.WriteMessage(websocket.TextMessage, b); err != nil {
				c.logger.Errorf("[websocket] %v", err)
				return
			}

			if c.debug {
				logger.Infof("[websocket] write message: %s", string(b))
			}
		}
	}
}

func (c *Controller) handle() {
	c.logger.Info("[websocket] starting handle loop")
	defer func() {
		c.logger.Info("[websocket] stopping handle loop")
		c.cancel()
		c.wg.Done()
		c.conn.Close()
	}()

	var (
		message Message
		ok      bool
	)
	for {
		select {
		case <-c.ctx.Done():
			return
		case message, ok = <-c.reading:
			if !ok {
				c.logger.Error("[websocket] failed to read reading channel")
				return
			}

			handler, ok := c.routes[message.Route]
			if !ok {
				c.logger.Errorf("[websocket] handler not found, route %s", message.Route)
				continue
			}

			if err := handler(c.ctx, message, c.writing); err != nil {
				c.logger.Errorf("[websocket] handle route %s: %v", message.Route, err)
				continue
			}
		}
	}
}
