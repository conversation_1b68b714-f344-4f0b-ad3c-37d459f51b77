package service

import (
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/invoice/model"
	"time"
)

func (svc *InvoiceService) SetInvoiceTitleInfo(uid int, param *model.CreateOrUpdateInvoiceTitleInfoParam) (err error) {
	if param == nil {
		svc.log.Error("create invoice title info param is nil")
		err = businesserror.ErrInvalidRequestParams
		return
	}
	var isReasonable bool
	if param.Type == constant.SpecialVATInvoice {
		isReasonable = param.CheckSpecialInvoiceTitle()
	} else {
		isReasonable = param.CheckPlainInvoiceTitle()
	}
	if !isReasonable {
		svc.log.Error("special invoice title info param format error")
		err = businesserror.ErrInvalidRequestParams
		return err
	}

	InvoiceTitleInfo := &model.InvoiceTitleInfo{
		UID:         uid,
		Title:       param.Title,
		Type:        param.Type,
		USCI:        param.USCI,
		BankName:    param.BankName,
		BankAccount: param.BankAccount,
		Address:     param.Address,
		Telephone:   param.Telephone,
		CreatedAt:   time.Now(),
	}
	err = db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: &model.InvoiceTitleInfo{},
		InsertPayload:   InvoiceTitleInfo,
	}).GetError()
	if err != nil {
		svc.log.WithField("err", err).WithField("uid", uid).Error("create invoice title info failed")
		err = businesserror.ErrDatabaseError
		return err
	}
	return
}

func (svc *InvoiceService) UpdateInvoiceTitleInfo(param *model.CreateOrUpdateInvoiceTitleInfoParam) (err error) {
	if param == nil {
		svc.log.Error("create invoice title info param is nil")
		err = businesserror.ErrInvalidRequestParams
		return
	}
	var isReasonable bool
	if param.Type == constant.SpecialVATInvoice {
		isReasonable = param.CheckSpecialInvoiceTitle()
	} else {
		isReasonable = param.CheckPlainInvoiceTitle()
	}
	if !isReasonable {
		svc.log.Error("special invoice title info param format error")
		err = businesserror.ErrInvalidRequestParams
		return err
	}
	_, err = svc.GetInvoiceTitleInfo(param.InvoiceTitleID)
	if err != nil {
		svc.log.WithField("err", err).WithField("invoice_title_id", param.InvoiceTitleID).Error("get invoice title info failed")
		err = businesserror.ErrDatabaseError
		return
	}
	data := map[string]interface{}{
		"title":        param.Title,
		"type":         param.Type.String(),
		"usci":         param.USCI,
		"bank_name":    param.BankName,
		"bank_account": param.BankAccount,
		"address":      param.Address,
		"telephone":    param.Telephone,
	}
	err = db_helper.UpdateOne(db_helper.QueryDefinition{ModelDefinition: &model.InvoiceTitleInfo{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": param.InvoiceTitleID}}},
		data).GetError()
	if err != nil {
		svc.log.WithField("err", err).WithField("invoice_title_id", param.InvoiceTitleID).Error("update invoice title info failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *InvoiceService) InvoiceTitleInfoList(uid int, param *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []*model.InvoiceTitleInfo, err error) {
	if uid < 1 {
		svc.log.Error("uid is error")
		err = businesserror.ErrInvalidRequestParams
		return
	}
	db := db_helper.GlobalDBConn().Table(model.TableNameInvoiceTitleInfo).Where("uid = ?", uid).Where("deleted_at is null")

	if param.DateFrom != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') >= ?", param.DateFrom)
	}
	if param.DateTo != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') <= ?", param.DateTo)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Warn("Count failed.")
		err = businesserror.ErrInternalError
		return
	}
	paged = db_helper.BuildPagedDataUtil(param.PageIndex, param.PageSize, int(count), 0)

	// 按照创建时间进行倒序排序
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("id desc").
		Find(&list).
		Error
	if err != nil {
		svc.log.WithError(err).Warn("Get invoice title info list failed.")
		err = businesserror.ErrInternalError
		return
	}
	paged.List = list
	return
}

func (svc *InvoiceService) DeleteInvoiceTitleInfo(invoiceID int) (err error) {
	if invoiceID < 1 {
		svc.log.Error("invoice_id is error")
		err = businesserror.ErrInvalidRequestParams
		return
	}
	_, err = svc.GetInvoiceTitleInfo(invoiceID)
	if err != nil {
		svc.log.WithField("err", err).WithField("invoice_info_id", invoiceID).Error("get invoice title info failed")
		err = businesserror.ErrDatabaseError
		return
	}
	err = db_helper.Delete(db_helper.QueryDefinition{ModelDefinition: &model.InvoiceTitleInfo{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": invoiceID}}}, &model.InvoiceTitleInfo{}).GetError()
	if err != nil {
		svc.log.WithField("err", err).WithField("invoice_info_id", invoiceID).Error("delete invoice title info failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *InvoiceService) GetInvoiceTitleInfo(invoiceId int) (InvoiceTitleInfo *model.InvoiceTitleInfo, err error) {
	err = db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: &model.InvoiceTitleInfo{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"id": invoiceId,
			},
		},
	}, &InvoiceTitleInfo).GetError()
	if err != nil {
		return
	}
	return
}

func (svc *InvoiceService) GetInvoiceTitleInfoByUID(uid int) (InvoiceTitleInfos []*model.InvoiceTitleInfo, err error) {
	InvoiceTitleInfos = make([]*model.InvoiceTitleInfo, 0)
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.InvoiceTitleInfo{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uid": uid,
			},
		},
		NoLimit: true,
	}, &InvoiceTitleInfos).GetError()
	if err != nil {
		return
	}
	return
}

func (svc *InvoiceService) FindInvoiceTitleInfoByIds(InvoiceTitleInfoIds []int) (InvoiceTitleInfos []*model.InvoiceTitleInfo, err error) {
	InvoiceTitleInfos = make([]*model.InvoiceTitleInfo, 0)
	err = db_helper.GlobalDBConn().Table(model.TableNameInvoiceTitleInfo).Where("deleted_at is null").
		Where("id in (?)", InvoiceTitleInfoIds).Find(&InvoiceTitleInfos).Error
	if err != nil {
		svc.log.WithField("err", err).WithField("invoice_info_ids", InvoiceTitleInfoIds).Error("Get invoice title info  failed.")
		return nil, businesserror.ErrDatabaseError
	}
	return
}
