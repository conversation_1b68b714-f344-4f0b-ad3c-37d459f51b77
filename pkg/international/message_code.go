package international

/**
 * 此处完全定位一条 "信息". 由上层的 businesserror 包调用, 也可以作为其它地方的文案.
 * 注意值只是用来区分不同信息的. 真正的中文或其它语言的文案需要另行定义.
 */

var (
	MDefault   = New("MDefault")   // 报错: 没有语言
	MTest00001 = New("MTest00001") // 示例信息 1
	MTest00002 = New("MTest00002") // 示例信息 2, 用于 fmt
	MTest00003 = New("MTest00003") // 示例信息 3, 用于测试无文案时的输出

	MSuccess = New("MSuccess") // 成功

	MCommon00001 = New("MCommon00001") // 服务端错误，请联系客服
	MCommon00002 = New("MCommon00002") // 请求参数错误
	MCommon00003 = New("MCommon00003") // 请求参数中 uid 为空
	MCommon00004 = New("MCommon00004") // 数据库错误
	MCommon00005 = New("MCommon00005") // 内部函数参数错误
	MCommon00006 = New("MCommon00006") // 意外宕机提示：很抱歉由于系统故障导致实例关机，您可以尝试重新开机或者联系客服：1234567890
	MCommon00007 = New("MCommon00007") // 服务正忙，请稍后再试
	MCommon00009 = New("MCommon00009") // 记录不存在
	MCommon00010 = New("MCommon00010") // skip, 乐观锁生效，执行update失败
	MCommon00011 = New("MCommon00011") // 当前服务升级中，暂不可用
	MCommon00012 = New("MCommon00012") // %v
	MCommon00013 = New("MCommon00013") // 不支持的文件类型, 请重试
	MCommon00014 = New("MCommon00014") // 文件大小超过限制, 当前最大支持10M
	MCommon00015 = New("MCommon00015") // 文件上传失败，请重试
	MCommon00016 = New("MCommon00016") // 文件不存在

	MAuth00001 = New("MAuth00001") // 登陆超时，请重新登录
	MAuth00002 = New("MAuth00002") // 无当前资源访问权限
	MAuth00003 = New("MAuth00003") // 访问受限，请降低访问频次

	MInstance00001 = New("MInstance00001") // 创建实例异常，请重试 > 实例申请端口出错
	MInstance00002 = New("MInstance00002") // 您选择更换的镜像不存在
	MInstance00008 = New("MInstance00008") // 实例不存在
	MInstance00009 = New("MInstance00009") // 实例已过期
	MInstance00010 = New("MInstance00010") // 不符合升降配标准
	MInstance00011 = New("MInstance00011") // 自动关机时间不能早于当前时间
	MInstance00012 = New("MInstance00012") // 不支持的启动模式
	MInstance00013 = New("MInstance00013") // 每日最多克隆实例%d次
	MInstance00014 = New("MInstance00014") // 系统盘预警，请先清理后再进行克隆
	MInstance00015 = New("MInstance00015") // 仅按量计费且关机状态下才允许实例克隆
	MInstance00016 = New("MInstance00016") // 保存实例镜像前，请确保实例是关机状态
	MInstance00017 = New("MInstance00017") // 实例不处于迁移失败状态，无法取消迁移实例
	MInstance00018 = New("MInstance00018") // 最多可创建实例数量上限为%d，您已超限
	MInstance00019 = New("MInstance00019") // 部分实例不可操作，请刷新后再试
	MInstance00020 = New("MInstance00020") // 部分实例权限变更失败，请刷新后再试
	MInstance00021 = New("MInstance00021") // 无此实例操作权限
	MInstance00022 = New("MInstance00022") // 文件传输次数过多，请稍后再试
	MInstance00023 = New("MInstance00023") // 您在%s地区创建实例数量上限为%d
	MInstance00024 = New("MInstance00024") // 选择的地区无法创建实例
	MInstance00025 = New("MInstance00025") // 请在实例关机状态下执行释放操作
	MInstance00026 = New("MInstance00026") // 不支持跨地区克隆实例
	MInstance00027 = New("MInstance00027") // 包卡实例延长到期时间不合理（实例到期时间超过当前时间，导致用户可以开机使用），请缩短延长的时间
	MInstance00028 = New("MInstance00028") // 该包卡实例到期时间异常，请联系技术人员确认
	MInstance00029 = New("MInstance00029") // 当前实例正在运行中
	MInstance00030 = New("MInstance00030") // 未找到异常宕机实例
	MInstance00031 = New("MInstance00031") // 您的实例正在使用的镜像已被删除，无法重置系统
	MInstance00032 = New("MInstance00032") // 新ssh密码格式不正确
	MInstance00033 = New("MInstance00033") // 请检查原实例是否为包卡实例且未到期
	MInstance00034 = New("MInstance00034") // 请检查新实例是否为按量计费实例且处于运行中状态
	MInstance00035 = New("MInstance00035") // 请检查原实例和新实例是否属于同一个用户
	MInstance00036 = New("MInstance00036") // 当前实例正在克隆锁定中，请稍后再试
	MInstance00037 = New("MInstance00037") // 实例延长到期时间不合理（实例关机时间超过当前时间），请缩短延长的时间
	MInstance00038 = New("MInstance00038") // 当前实例未释放,请检查后重试
	MInstance00039 = New("MInstance00039") // 当前实例状态无法进行%s操作, 请稍后再试
	MInstance00040 = New("MInstance00040") // 当前实例已释放, 无法进行%s操作
	MInstance00041 = New("MInstance00041") // 当前实例已关机, 无需进行此操作
	MInstance00042 = New("MInstance00042") // 该用户下不存在该克隆码
	MInstance00043 = New("MInstance00043") // 克隆码获取对应信息错误
	MInstance00044 = New("MInstance00044") // 克隆码已过期
	MInstance00045 = New("MInstance00045") // 更新克隆码信息错误
	MInstance00046 = New("MInstance00046") // 克隆码已使用
	MInstance00047 = New("MInstance00047") // 实例ID参数为空
	MInstance00048 = New("MInstance00048") // 克隆码格式错误
	MInstance00049 = New("MInstance00049") // 源实例镜像只保存在 xx 机器，无法克隆到 xx 机器
	MInstance00050 = New("MInstance00050") // 获取克隆次数错误
	MInstance00051 = New("MInstance00051") // 克隆次数超过%s次
	MInstance00052 = New("MInstance00052") // 设置克隆次数错误
	MInstance00054 = New("MInstance00054") // 为确保数据一致性，请将源实例关机后再进行克隆
	MInstance00055 = New("MInstance00055") // 拷贝路径非法

	MInstance00056 = New("MInstance00056") // 获取标签失败
	MInstance00057 = New("MInstance00057") // 该实例已存在相同标签名，请勿重复添加
	MInstance00058 = New("MInstance00058") // 实例的标签数不能超过2个
	MInstance00059 = New("MInstance00059") // 创建标签失败
	MInstance00060 = New("MInstance00060") // 添加标签失败
	MInstance00061 = New("MInstance00061") // 删除标签失败

	MBareMetalMachine00001 = New("MBareMetalMachine00001") //该裸金属主机正在被占用

	MPort00001 = New("MPort00001") // 端口模块出错: 端口已用尽
	MPort00002 = New("MPort00002") // 端口模块出错: 端口号不正确
	MPort00003 = New("MPort00003") // 模块端口出错: 端口释放失败
	MPort00004 = New("MPort00004") // 端口模块出错: 端口不存在
	MPort00005 = New("MPort00005") // 端口分配出错: 端口模块正忙
	MPort00007 = New("MPort00006") // 端口模块出错: 端口获取失败

	MUser00001 = New("MUser00001") // 用户不存在
	MUser00002 = New("MUser00002") // 手机号或密码错误,请重新输入
	MUser00003 = New("MUser00003") // 连续超过 %v 次错误，请于 %v 分钟后重试
	MUser00004 = New("MUser00004") // 获取用户失败
	MUser00005 = New("MUser00005") // 获取短信验证码失败
	MUser00006 = New("MUser00006") // 设置ticket失败,但已经生成
	MUser00010 = New("MUser00010") // 验证码已过期，请刷新
	MUser00011 = New("MUser00011") // 校验验证码错误
	MUser00012 = New("MUser00012") // 验证码错误
	MUser00013 = New("MUser00013") // 编辑用户错误
	MUser00014 = New("MUser00014") // 手机号已注册，请登录
	MUser00015 = New("MUser00015") // 该用户状态下不可编辑
	MUser00016 = New("MUser00016") // 授权失败, 请重新登录
	MUser00017 = New("MUser00017") // 解析token失败
	MUser00018 = New("MUser00018") // 当前用户权限下无法操作
	MUser00020 = New("MUser00020") // 手机号格式错误,请重新输入
	MUser00021 = New("MUser00021") // 发送短信失败
	MUser00022 = New("MUser00022") // 邮箱已被其他用户使用，请重新输入
	MUser00023 = New("MUser00023") // 用户修改昵称失败
	MUser00024 = New("MUser00024") // 获取用户ticket失败
	MUser00025 = New("MUser00025") // 正则表达式格式错误
	MUser00026 = New("MUser00026") // 邮箱格式错误
	MUser00027 = New("MUser00027") // 用户未注册
	MUser00028 = New("MUser00028") // 已完成初始化，请勿重复初始化
	MUser00029 = New("MUser00029") // 用户更改邮箱失败
	MUser00030 = New("MUser00030") // 用户更新密码错误
	MUser00031 = New("MUser00031") // 邮箱验证码输入错误
	MUser00032 = New("MUser00032") // 用户绑定邮箱失败
	MUser00033 = New("MUser00033") // 创建用户失败
	MUser00036 = New("MUser00036") // 用户两次密码不一致
	MUser00037 = New("MUser00037") // 用户重置密码失败
	MUser00038 = New("MUser00038") // 用户不是客服
	MUser00039 = New("MUser00039") // 用户更改手机号失败
	MUser00040 = New("MUser00040") // 获取微信平台access_token失败
	MUser00041 = New("MUser00041") // 短信验证码校验失败
	MUser00042 = New("MUser00042") // 连续超过 %v 次错误，请于 %v 分钟后重新注册
	MUser00043 = New("MUser00043") // 用户修改用户名失败
	MUser00044 = New("MUser00044") // 连续超过 %v 次错误，请于 %v 分钟后重新修改密码
	MUser00045 = New("MUser00045") // 查看用户邀请有礼详情失败
	MUser00046 = New("MUser00046") // 获取用户邀请奖励失败
	MUser00047 = New("MUser00047") // 读取微信回调body失败
	MUser00048 = New("MUser00048") // 用户反馈失败
	MUser00049 = New("MUser00049") // 手机号已绑定其他微信号
	MUser00050 = New("MUser00050") // 获取用户微信信息失败
	MUser00051 = New("MUser00051") // 用户绑定微信失败
	MUser00053 = New("MUser00053") // 解析微信回调body成xml失败
	MUser00054 = New("MUser00054") // 校验微信加密签名失败
	MUser00057 = New("MUser00057") // 该手机号已绑定其他微信号
	MUser00058 = New("MUser00058") // 请先绑定微信
	MUser00059 = New("MUser00059") // 用户已被禁用
	MUser00061 = New("MUser00061") // 学生教育邮箱认证失败
	MUser00062 = New("MUser00062") // 获取用户会员信息失败
	MUser00063 = New("MUser00063") // 每个用户最多只能创建10个token
	MUser00064 = New("MUser00064") // 获取用户个人token失败
	MUser00065 = New("MUser00065") // 每个用户每天只允许调用25次
	MUser00066 = New("MUser00066") // 每个用户每分钟只允许调用5次
	MUser00067 = New("MUser00067") // 手机号不存在
	MUser00068 = New("MUser00068") // 网站暂不支持当前区号手机号注册，后续会陆续开放更多国家注册，敬请期待
	MUser00069 = New("MUser00069") // 您当前无法添加子帐号，请联系客服开通
	MUser00070 = New("MUser00070") // 您当前子帐号数量达到上限，请扩容
	MUser00071 = New("MUser00071") // 子帐号已存在
	MUser00072 = New("MUser00072") // 部分子帐号不可操作
	MUser00073 = New("MUser00073") // 子帐号正在使用中，无法删除子帐号
	MUser00074 = New("MUser00074") //
	MUser00075 = New("MUser00075") // 身份核验不通过，请重新输入
	MUser00076 = New("MUser00076") // 当前身份证号已被使用，请重新输入，如有疑问请联系在线客服
	MUser00077 = New("MUser00077") // 授权信息已存在，请先删除再添加新授权
	MUser00078 = New("MUser00078") // 距离您上次绑定微信不足1个月，请于%d天后解绑，谢谢！
	MUser00079 = New("MUser00079") // 此账号暂不支持修改微信绑定相关状态，请联系客服
	MUser00080 = New("MUser00080") // 该手机号已注册, 请更换手机号
	MUser00081 = New("MUser00081") // 连续超过 %v 次错误，请于 %v 分钟后重试
	MUser00082 = New("MUser00082") // 开发者token已失效, 请重新获取
	MUser00083 = New("MUser00083") // 当前申请已审核过或用户已撤销, 请刷新页面后再试
	MUser00084 = New("MUser00084") // 您目前已进行过%s认证, 如有疑问, 请联系客服
	MUser00085 = New("MUser00085") // 当前申请已审核, 请刷新页面后查看最新状态
	MUser00086 = New("MUser00086") // 子账号不存在
	MUser00087 = New("MUser00087") // 您当前非企业用户, 无法使用弹性部署功能
	MUser00088 = New("MUser00088") // 原密码不正确
	MUser00089 = New("MUser00089") // 用户名或密码错误，请重试
	MUser00090 = New("MUser00090") // 当前账号已被禁用，如有疑问请联系管理员
	MUser00091 = New("MUser00091") // 邮箱地址无效，请检查后再试
	MUser00092 = New("MUser00092") // 您今日已经累计提交无效邮箱地址3次，将暂时禁用您发送邮件功能，请明日再试
	MUser00093 = New("MUser00093") // 更新拷贝数据和克隆次数失败
	MUser00094 = New("MUser00094") // 过期时间超出限制，当前可输入最大天数为60天
	MUser00095 = New("MUser00095") // 通行码错误
	MUser00096 = New("MUser00096") // 您目前已进行过%s重新认证, 如有疑问, 请联系客服
	MUser00097 = New("MUser00097") // 设置用户创建实例数量上限失败
	MUser00098 = New("MUser00098") // 账号可能存在违规受限无法充值，如有疑问请联系客服处理
	MUser00099 = New("MUser00099") // 用户登录信息记录失败
	MUser00100 = New("MUser00100") // pushgateway服务不可用, 请确认后重试
	MUser00101 = New("MUser00101") // pushgateway鉴权失败, 请确认后重试

	// ---------------- feedback --------------------
	MFeedBack00001 = New("MFeedBack00001") // 处理用户反馈失败
	MFeedBack00002 = New("MFeedBack00002") // 用户反馈不存在
	MFeedBack00003 = New("MFeedBack00003") // 获取反馈列表失败
	MFeedBack00004 = New("MFeedBack00004") // 获取反馈详情失败
	MFeedBack00005 = New("MFeedBack00005") // 获取反馈截图失败

	// ------------------ user group -------------------------------------------
	MUserGroup00001 = New("MUserGroup00001") // 创建用户组失败
	MUserGroup00003 = New("MUserGroup00003") // 统计用户组中的用户数量失败
	MUserGroup00004 = New("MUserGroup00004") // 编辑用户组失败
	MUserGroup00006 = New("MUserGroup00006") // 删除用户组失败
	MUserGroup00008 = New("MUserGroup00008") // 获取用户组失败

	// -----------------user member-----------------------------
	MUserMember00001 = New("MUserMember00001") // 统计会员成长值失败

	// ---------------- user ssh key ------------------------
	MUserPubKey00001 = New("MUserPubKey00001") // 每个用户最多只能配置两个公钥

	// ---------------- file --------------------
	MFile00001 = New("MFile00001") // 获取文件失败 fmt
	MFile00002 = New("MFile00002") // 非法文件，请上传图片文件
	MFile00003 = New("MFile00003") // 上传文件失败 fmt
	MFile00004 = New("MFile00004") // 上传文件失败 fmt
	MFile00005 = New("MFile00005") // 文件大小超出限制
	MFile00006 = New("MFile00006") // 读取文件失败

	// --------------- machine ------------------
	MMachine00001 = New("MMachine00001") // 您所选择的实例配置库存不足，请刷新后再试
	MMachine00002 = New("MMachine00002") // 机器硬件信息错误
	MMachine00003 = New("MMachine00003") // 机器已经注册
	MMachine00004 = New("MMachine00004") // 机器空闲gpu不足
	MMachine00005 = New("MMachine00005") // GPU库存发生变更，请刷新后重新选择
	MMachine00006 = New("MMachine00006") // 该主机存储用量较高，请等待客服处理后开机
	MMachine00007 = New("MMachine00007") // 上架前，请编辑机器必填信息
	MMachine00008 = New("MMachine00008") // 该主机已租满，请更换主机重新创建实例
	MMachine00009 = New("MMachine00009") // 机器可租的截止时间已经到期，请调整可租时间
	MMachine00010 = New("MMachine00010") // 该机器不支持当前计费方式, 请确认后重试
	MMachine00011 = New("MMachine00011") // 机器计费配置错误
	MMachine00012 = New("MMachine00012") // 机器需要配置地区信息，请重新配置
	MMachine00013 = New("MMachine00013") // 该地区无NFS，无法挂载网盘
	MMachine00014 = New("MMachine00014") // 目标机器还未挂载网盘,暂时无法迁移实例
	MMachine00015 = New("MMachine00015") // 该机器暂时不支持您选择的计费方式
	MMachine00016 = New("MMachine00016") // 该机器暂未开启快速网络传输功能，请确认后重试
	MMachine00017 = New("MMachine00017") // 源机器还未挂载网盘,暂时无法迁移实例
	MMachine00018 = New("MMachine00018") // 主机维护工单不存在
	MMachine00019 = New("MMachine00019") // 主机不存在
	MMachine00020 = New("MMachine00020") // %s机器暂不支持克隆实例, 请联系客服
	MMachine00021 = New("MMachine00021") // 机器网络异常，请稍后重试
	MMachine00022 = New("MMachine00021") // 机器租期已到期
	MMachine00023 = New("MMachine00023") // 主机ID错误
	MMachine00024 = New("MMachine00024") // 当前实例正在开机，请勿重新操作
	MMachine00025 = New("MMachine00025") // GPU库存数量和主机GPU数量不同，请检查库存
	MMachine00026 = New("MMachine00026") // 主机由于存在潜在异常暂不支持续费, 请联系客服处理
	MMachine00027 = New("MMachine00027") // 主机正在维护检修，请稍后重试或联系客服
	MMachine00028 = New("MMachine00028") // 新主机和源实例主机的厂商和cpu架构不一致，不能克隆
	MMachine00029 = New("MMachine00029") // 清除主机失败，主机上仍存在使用中的资源，请处理后重试

	// --------------- order ---------------------
	MOrder000001 = New("MOrder000001") // 订单不存在
	MOrder000002 = New("MOrder000002") // 您有3个未支付订单，请先完成支付。
	MOrder000003 = New("MOrder000003") // 当前实例已存在未付款的续费订单, 请勿重复续费
	MOrder000004 = New("MOrder000004") // 未生效的续费订单不能超过3个
	MOrder000005 = New("MOrder000005") // 您有待付款的订单，请先付款
	MOrder000006 = New("MOrder000006") // 每人每日最多转换实例计费类型10次，请明日再试
	MOrder000007 = New("MOrder000007") // 您有待付款的续费订单，请先取消或付款
	MOrder000008 = New("MOrder000008") // 仅运行中与已关机状态下可以进行转计费方式
	MOrder000009 = New("MOrder000009") // 存在未支付的订单，不允许释放实例 TODO：待产品定义文案
	MOrder000010 = New("MOrder000010") // 您选择的租用时长超过当前机器最大可租用时长，请重新选择
	MOrder000011 = New("MOrder000011") // 机器已到期，不可租用
	MOrder000012 = New("MOrder000012") // 订单已超时
	MOrder000013 = New("MOrder000013") // 包卡转按量正在进行中，请勿重复操作
	MOrder000014 = New("MOrder000014") // 当前订单已支付，请勿重复付款
	MOrder000015 = New("MOrder000015") // 当前实例计费方式已经为包卡，无需转计费方式
	MOrder000016 = New("MOrder000016") // 您有待付款预付费实例扩容数据盘订单, 为保证数据一致性, 请先付款后再进行操作
	MOrder000017 = New("MOrder000017") // 实例已到期, 请续费或转按量计费后扩容
	MOrder000018 = New("MOrder000018") // 当前订单不可支付

	// ---------------- bill ----------------------
	MBill000001 = New("MBill000001") // 账单不存在
	MBill000002 = New("MBill000002") // 余额不足
	MBill000003 = New("MBill000003") //用户所选天数大于365天
	MBill000004 = New("MBill000004") // 无充值权限
	MBill000005 = New("MBill000004") // 当前账单预充值金额已恢复

	// ---------------- invoice ------------------
	MInvoice000001 = New("MInvoice000001") // 开票金额超过实际能开金额上限
	MInvoice000002 = New("MInvoice000002") // 开票金额不足1元
	MInvoice000003 = New("MInvoice000003") // 账单已开票，请勿重复开票
	MInvoice000004 = New("MInvoice000004") // 订单已开票，请勿重复开票

	// ---------------- wallet ---------------------
	MWallet00001 = New("MWallet00001") // 充值最低1元
	MWallet00002 = New("MWallet00002") // (internal) 更新wallet失败, asset_cc不一致, 稍后重试
	MWallet00003 = New("MWallet00003") // 生成微信订单失败, 请稍后重试
	MWallet00004 = New("MWallet00004") // 解析微信回调信息失败
	MWallet00005 = New("MWallet00005") // 未查询到充值记录, 请确认后重试
	MWallet00006 = New("MWallet00006") // 生成支付宝订单失败, 请稍后重试
	MWallet00007 = New("MWallet00007") // 支付超时，请重新创建
	MWallet00008 = New("MWallet00008") // 余额不足
	MWallet00009 = New("MWallet00009") // 子账户钱包不存在
	MWallet00010 = New("MWallet00010") // 子账户余额不足
	MWallet00011 = New("MWallet00011") // 解析Ali回调信息失败
	MWallet00012 = New("MWallet00012") //
	MWallet00013 = New("MWallet00013") //
	MWallet00014 = New("MWallet00014") // 可提现余额不足
	MWallet00015 = New("MWallet00015") // 请输入正确的提现金额
	MWallet00016 = New("MWallet00016") // 请选择要提现的账单
	MWallet00017 = New("MWallet00017") // 退款信息错误，请确认后重试
	MWallet00018 = New("MWallet00018") // 退款失败
	MWallet00019 = New("MWallet00019") // 专属汇款码已用尽, 请联系客服
	MWallet00020 = New("MWallet00020") // 当前专属汇款码只对企业用户开放, 如有疑问请联系客服

	// ----------------- voucher ---------------------
	MVoucher00001 = New("MVoucher00001") // 代金券不存在
	MVoucher00002 = New("MVoucher00002") // 你已领取过注册券，请在代金券页面中查看
	MVoucher00003 = New("MVoucher00003") // 定向券类型错误， 请检查券设置
	MVoucher00004 = New("MVoucher00004") // 兑换码不存在，请检查后重试
	MVoucher00005 = New("MVoucher00005") // 兑换券已抢完
	MVoucher00006 = New("MVoucher00006") // 您已兑换过该兑换券，请在代金券页面中查看
	MVoucher00007 = New("MVoucher00007") // 获取注册券规则失败
	MVoucher00008 = New("MVoucher00008") // 代金券名称已存在
	MVoucher00009 = New("MVoucher00009") // 兑换券未开始兑换，到时再来吧
	MVoucher00010 = New("MVoucher00010") // 兑换券已截止兑换，下次早点来吧
	MVoucher00011 = New("MVoucher00011") // 定向券已截止发放
	MVoucher00012 = New("MVoucher00012") // 定向券已过使用时间，请使用新券
	MVoucher00013 = New("MVoucher00013") // 累计邀请人数不可重复
	MVoucher00014 = New("MVoucher00014") // 该代金券已失效, 无需再作废

	// ---------------- gpu_type ---------------------
	GpuType00001 = New("GpuType00001") // gpu号型已被占用
	GpuType00002 = New("GpuType00002") // gpu号型 %s 不存在

	// ---------------- gpu stock -----------------
	GpuStock00001 = New("GpuStock00001") // gpu库存不足，请等待有空闲gpu后再重试
	GpuStock00002 = New("GpuStock00002") // 指定的gpu占用不存在, 请刷新后重试
	GpuStock00003 = New("GpuStock00003") // 录入占用失败, 请刷新后重试
	GpuStock00004 = New("GpuStock00004") // GPU删除失败, 请刷新后重试

	// -----------------idle_job -----------------
	IdleJob000002 = New("IdleJob000002") // 没有创建闲时作业权限

	// ----------------- container -------------------------
	Container000001 = New("Container000001") // 在同一时刻最多允许开启 %d 个无卡模式的实例
	Container000002 = New("Container000002") // 容器不存在

	// ------------------- region --------------------------
	Region00001 = New("Region00001") // 地区标识不存在
	Region00002 = New("Region00002") // 地区标识已存在
	Region00003 = New("Region00003") // 地区名称已存在
	Region00004 = New("Region00004") // 地区NFS未配置默认网盘容量，请联系客服
	Region00005 = New("Region00005") // 地区NFS已满
	Region00006 = New("Region00006") // 该用户在该地区还没有初始化网盘
	Region00007 = New("Region00007") // 网盘容量不足，终止上传
	Region00008 = New("Region00008") // 网盘最大扩容2TiB
	Region00009 = New("Region00009") // 该网盘不可用，请联系客服
	Region00010 = New("Region00010") // 扩容记录不存在
	Region00011 = New("Region00011") // 当前地区不可创建弹性部署
	Region00012 = New("Region00012") // 当前地区文件存储不可用, 请联系客服
	Region00013 = New("Region00013") // 地区名称或地区签名错误
	Region00014 = New("Region00014") // 文件存储配置不存在

	// -------------------- invoice -------------------------
	Invoice00001 = New("Invoice00001") // 在审核的发票已存在,请等待完成再开新的发票
	Invoice00002 = New("Invoice00002") // 已完成或被驳回的发票无法再驳回

	// ---------------- common data -----------------------------------
	CommonData00001 = New("CommonData00001") // 公共数据已经存在

	// ----------------- coupon ----------------------
	Coupon00001 = New("Coupon00001") // 您已领取过该券，每人限领一张
	Coupon00002 = New("Coupon00002") // 该优惠券未开放领取，有疑问请联系客服
	Coupon00003 = New("Coupon00003") // 优惠券已抢完
	Coupon00004 = New("Coupon00004") // 您暂时不符合领取条件，请查看领券规则
	Coupon00005 = New("Coupon00005") // 优惠券已使用，请重新选择优惠券
	Coupon00006 = New("Coupon00006") // 当前不在优惠券可用日期内，请重新选择优惠券
	Coupon00007 = New("Coupon00007") // 订单金额不满足优惠券使用条件，请重新选择
	Coupon00008 = New("Coupon00008") // 您选择的优惠券超出使用范围，请重新选择
	Coupon00009 = New("Coupon00009") // 您选择的优惠券不符合使用条件，请重新选择
	Coupon00010 = New("Coupon00010") // 您传入的优惠券生成数量大于当前优惠券的剩余发行量，请您稍后重试
	Coupon00011 = New("Coupon00011") // 您选择的优惠券与该实例的计费方式不符，请重新选择
	Coupon00012 = New("Coupon00012") // 兑换码不存在，请检查后重试
	Coupon00013 = New("Coupon00013") // 该兑换码下的优惠券已被领取，请检查后重试

	// ------------------ user prize ------------------------------------

	UserPrize00001 = New("UserPrize00001") // 您已参与本轮抽奖
	UserPrize00002 = New("UserPrize00002") // 您的充值金额不足，无法抽奖
	UserPrize00003 = New("UserPrize00003") // 还未到抽奖时间
	UserPrize00004 = New("UserPrize00004") // 查询抽奖白名单错误
	UserPrize00005 = New("UserPrize00005") // 请完成第一步并等待审核通过再参与抽奖
	UserPrize00006 = New("UserPrize00006") // 您的累计消费金额未超过10元，不满足参与要求
	UserPrize00007 = New("UserPrize00007") // 添加白名单错误
	UserPrize00008 = New("UserPrize00008") // 没有抽奖的人

	// ------------------ private_image ---------------------------------
	PrivateImage00001 = New("PrivateImage00001") // 该镜像名称已经存在
	PrivateImage00002 = New("PrivateImage00002") // 实例镜像已被删除
	PrivateImage00003 = New("PrivateImage00003") // 实例正在保存镜像.请稍后重试
	PrivateImage00004 = New("PrivateImage00004") // 镜像还未保存成功
	PrivateImage00005 = New("PrivateImage00005") // 您的镜像还未共享
	PrivateImage00006 = New("PrivateImage00006") // 您的镜像正在共享中，请先取消共享再删除
	PrivateImage00007 = New("PrivateImage00007") // 镜像数量已经超过上限，您最多可保存3个镜像
	PrivateImage00008 = New("PrivateImage00008") // 您的镜像正在使用，请稍后再试
	PrivateImage00009 = New("PrivateImage00009") // 镜像未就绪，请稍后再试
	PrivateImage00010 = New("PrivateImage00010") // 私有镜像不存在
	PrivateImage00011 = New("PrivateImage00011") // 基础镜像不存在
	PrivateImage00012 = New("PrivateImage00012") // 部分镜像权限变更失败，请刷新后再试
	PrivateImage00013 = New("PrivateImage00013") // 无镜像操作权限
	// ----------------- data disk --------------------
	DataDisk00001 = New("DataDisk00001") // 扩容超过最大容量限制
	DataDisk00002 = New("DataDisk00002") // 磁盘空间不足，预留数据盘失败，请刷新重试
	DataDisk00003 = New("DataDisk00003") // 更改数据盘大小失败
	DataDisk00004 = New("DataDisk00004") // 仅按量计费实例支持数据盘扩容/缩容
	DataDisk00005 = New("DataDisk00005") // 缩容后容量不得小于免费容量
	DataDisk00006 = New("DataDisk00006") // 缩容后容量不得小于当前实例已使用容量
	DataDisk00007 = New("DataDisk00007") // 请在实例关机状态下进行扩容/缩容数据盘
	DataDisk00008 = New("DataDisk00008") // 不支持包卡实例到期前中途缩容，如您确有此需求，您可以选择转按量后进行数据盘缩容操作

	// ------------------------ credit wallet --------------------
	CreditWallet00001 = New("CreditWallet00001") // 该用户未开通授信额度，请确认后重试
	CreditWallet00002 = New("CreditWallet00002") // 调整额度后新额度不能小于已使用金额
	CreditWallet00003 = New("CreditWallet00003") // 您暂未开通授信额度，请联系客服开通
	CreditWallet00004 = New("CreditWallet00004") // 授信额度不足，请调整操作金额或联系客服提额
	CreditWallet00005 = New("CreditWallet00005") // 您填写的还款金额大于该账户欠款金额，请确认后重试
	CreditWallet00006 = New("CreditWallet00006") // 授信钱包已存在，无需再次开通
	CreditWallet00007 = New("CreditWallet00007") // 授信钱包操作金额最低1元

	// -------------------- public data ----------------------------
	PublicData00001 = New("PublicData00001") // 共享数据不存在，请刷新后再试
	PublicData00002 = New("PublicData00002") // 数据集官方地址字数超过最大限制，请重新输入

	// ----------------------- notice ----------------------------
	Notice00001 = New("Notice00001") // 到期时间不能早于当前时间

	// ---------------------- community ---------------------------
	Community0001 = New("Community0001") // 镜像不存在

	// ---------------------- deployment --------------------------
	Deployment0001 = New("Deployment0001")
	Deployment0002 = New("Deployment0002")
	Deployment0003 = New("Deployment0003")
	Deployment0004 = New("Deployment0004")
	Deployment0005 = New("Deployment0005")
	Deployment0006 = New("Deployment0006")
	Deployment0007 = New("Deployment0007")
	Deployment0008 = New("Deployment0008")
	Deployment0009 = New("Deployment0009")
	Deployment0010 = New("Deployment0010") // 当前版本号已存在，请提供新的版本号后重试
	Deployment0011 = New("Deployment0011") // 当前版本号过长，请缩短版本号后重试
	Deployment0012 = New("Deployment0012") // 失效时间不能为负数，如需立即解除黑名单请设置为 0
	Deployment0013 = New("Deployment0013") // 获取弹性部署调度黑名单列表失败

	// ------------------------ storage agent ------------------
	StorageAgent0001 = New("StorageAgent0001")

	// ------------------------ work order ------------------
	WorkOrder0001 = New("WorkOrder0001")
	WorkOrder0002 = New("WorkOrder0002")
	WorkOrder0003 = New("WorkOrder0003")
	WorkOrder0004 = New("WorkOrder0004")
	WorkOrder0005 = New("WorkOrder0005")
	WorkOrder0006 = New("WorkOrder0006")
	WorkOrder0007 = New("WorkOrder0007")
	WorkOrder0008 = New("WorkOrder0008")
	WorkOrder0009 = New("WorkOrder0009")
	WorkOrder0010 = New("WorkOrder0010")
	WorkOrder0011 = New("WorkOrder0011")
	WorkOrder0012 = New("WorkOrder0012")
	WorkOrder0013 = New("WorkOrder0013")
	WorkOrder0014 = New("WorkOrder0014")
	WorkOrder0015 = New("WorkOrder0015")
	WorkOrder0016 = New("WorkOrder0016")
	WorkOrder0017 = New("WorkOrder0017")
	WorkOrder0018 = New("WorkOrder0018")
	WorkOrder0019 = New("WorkOrder0019")
	WorkOrder0020 = New("WorkOrder0020")
	WorkOrder0021 = New("WorkOrder0021")
	WorkOrder0022 = New("WorkOrder0022")
	WorkOrder0023 = New("WorkOrder0023")
	WorkOrder0024 = New("WorkOrder0024")
	WorkOrder0025 = New("WorkOrder0025")
	WorkOrder0026 = New("WorkOrder0026")
	WorkOrder0027 = New("WorkOrder0027")
	WorkOrder0028 = New("WorkOrder0028")
	WorkOrder0029 = New("WorkOrder0029")
	WorkOrder0030 = New("WorkOrder0030")

	// ------------------------ sms ------------------
	Sms0001 = New("Sms00011")

	// -------------------- ScheduleInstance ------------------------
	ScheduleInstance0001 = New("ScheduleInstance0001")
	ScheduleInstance0002 = New("ScheduleInstance0002")
	ScheduleInstance0003 = New("ScheduleInstance0003")
	ScheduleInstance0004 = New("ScheduleInstance0004")

	// -------------------- Contract ------------------------
	Contract0001 = New("Contract0001")
	Contract0002 = New("Contract0002")
	Contract0003 = New("Contract0003")
)
