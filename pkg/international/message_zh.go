package international

var ZH = map[MessageCode]string{
	MDefault:   "没有翻译, 请联系客服. 信息码为: ",
	MTest00001: "示例信息1",
	MTest00002: "连续超过 %v 次错误，请于 %v 分钟后重新登陆",

	MCommon00001: "服务异常，请稍后重试",
	MCommon00002: "请求参数错误",
	MCommon00003: "请求参数错误",
	MCommon00004: "服务异常，请稍后重试",
	MCommon00005: "服务异常，请稍后重试",
	MCommon00006: "意外宕机提示：很抱歉由于系统故障导致实例关机，您可以尝试重新开机或者联系客服：%s",
	MCommon00007: "服务正忙，请稍后再试",
	MCommon00009: "记录不存在",
	MCommon00010: "skip, 乐观锁生效，执行update失败",
	MCommon00011: "当前服务升级中，暂不可用",
	MCommon00012: "%v",
	MCommon00013: "不支持的文件类型, 请重试",
	MCommon00014: "文件大小超过限制, 当前最大支持10M",
	MCommon00015: "文件上传失败，请重试",
	MCommon00016: "文件不存在",

	MAuth00001: "登陆超时，请重新登录",
	MAuth00002: "无当前资源访问权限",
	MAuth00003: "当前%s访问受限，请降低访问频次",

	MInstance00001: "创建实例异常，请重试",
	MInstance00002: "您选择更换的镜像不存在", // 待产品确认报错文案
	MInstance00008: "未查询到相关实例",
	MInstance00009: "实例已到期，请续费",
	MInstance00010: "实例不符合升降配置标准",
	MInstance00011: "实例自动关机时间不能早于当前时间", // 待产品确认报错文案
	MInstance00012: "不支持的启动模式",                 // 待产品确认报错文案, (gpu / 无 gpu)
	MInstance00013: "每日仅支持克隆实例%d次，请于明日再进行克隆",
	MInstance00014: "系统盘预警，请先清理后再进行克隆",
	MInstance00015: "仅按量计费且关机状态下才允许实例克隆",
	MInstance00016: "保存实例镜像前，请确保实例是关机状态",
	MInstance00017: "实例不处于迁移失败状态，无法取消迁移实例",
	MInstance00018: "最多可创建实例数量上限为%d，您已超限",
	MInstance00019: "部分实例不可操作，请刷新后再试",
	MInstance00020: "部分实例权限变更失败，请刷新后再试",
	MInstance00021: "无此实例操作权限",
	MInstance00022: "文件传输次数过多，请稍后再试",
	MInstance00023: "您在%s地区创建实例数量上限为%d",
	MInstance00024: "选择的地区无法创建实例",
	MInstance00025: "请在实例关机状态下执行释放操作",
	MInstance00026: "不支持跨地区克隆实例",
	MInstance00027: "包卡实例延长到期时间不合理（实例到期时间超过当前时间，导致用户可以开机使用），请缩短延长的时间",
	MInstance00028: "该包卡实例到期时间异常，请联系技术人员确认",
	MInstance00029: "当前实例正在运行中",
	MInstance00030: "未找到异常宕机实例",
	MInstance00031: "您的实例正在使用的镜像已被删除，无法重置系统",
	MInstance00032: "新ssh密码格式不正确",
	MInstance00033: "请检查原实例是否为包卡实例且未到期",
	MInstance00034: "请检查新实例是否为按量计费实例且处于运行中状态",
	MInstance00035: "请检查原实例和新实例是否属于同一个用户",
	MInstance00036: "当前实例正在克隆锁定中，请稍后再试",
	MInstance00037: "实例延长到期时间不合理（实例关机时间超过当前时间），请缩短延长的时间",
	MInstance00038: "当前实例未释放,请检查后重试",
	MInstance00039: "当前实例状态无法进行%s操作, 请稍后再试",
	MInstance00040: "当前实例已释放, 无法进行%s操作",
	MInstance00041: "当前实例已关机, 无需进行此操作",
	MInstance00042: "该用户下不存在该克隆码",
	MInstance00043: "克隆码获取对应信息错误",
	MInstance00044: "克隆码已过期",
	MInstance00045: "更新克隆码信息错误",
	MInstance00046: "克隆码已使用",
	MInstance00047: "实例ID参数为空",
	MInstance00048: "克隆码格式错误",
	MInstance00049: "源实例镜像只保存在 %v 机器，无法克隆到 %v 机器",
	MInstance00050: "获取克隆次数错误",
	MInstance00051: "克隆次数超过 %v 次",
	MInstance00052: "设置克隆次数错误",
	MInstance00054: "为确保数据一致性，请将源实例关机后再进行克隆",
	MInstance00055: "拷贝路径非法",

	MInstance00056: "获取标签失败",
	MInstance00057: "该实例已存在相同标签名，请勿重复添加",
	MInstance00058: "实例的标签数不能超过3个",
	MInstance00059: "创建标签失败",
	MInstance00060: "添加标签失败",
	MInstance00061: "删除标签失败",

	MBareMetalMachine00001: "主机已被占用，请选择其他主机",

	MUser00001: "手机号或密码错误，请重试", // 避免报用户不存在的状态，恶意试探用户
	MUser00002: "手机号或密码错误，请重试",
	MUser00003: "连续超过 %v 次错误，请于 %v 分钟后重试",
	MUser00004: "手机号或密码错误，请重试",
	MUser00005: "验证码无效，请重新获取",
	MUser00006: "登录失败，请重试",
	MUser00010: "图形验证码已过期，请刷新再次输入",
	MUser00011: "图形验证码错误",
	MUser00012: "图形验证码错误",
	MUser00013: "更新失败",
	MUser00014: "手机号已注册",
	MUser00015: "用户已被禁用",
	MUser00016: "登录超时，请重新登录",
	MUser00017: "登录失败，请重试",
	MUser00018: "当前用户权限下无法操作",
	MUser00020: "手机号格式错误",
	MUser00021: "发送短信验证码失败",
	MUser00022: "该邮箱已被使用",
	MUser00023: "编辑昵称失败",
	MUser00024: "登录异常，请重新登录",
	MUser00025: "格式错误，请按提示正确填写",
	MUser00026: "邮箱格式错误",
	MUser00027: "帐号未注册",
	MUser00028: "已完成初始化，请勿重复初始化",
	MUser00029: "用户更改邮箱失败",
	MUser00030: "修改密码失败，请重试",
	MUser00031: "邮箱验证码错误",
	MUser00032: "绑定邮箱失败",
	MUser00033: "创建用户失败",
	MUser00036: "密码不一致，请重新输入", // NOTE: 从此开始文案未校对
	MUser00037: "用户重置密码失败",
	MUser00038: "非管理员禁止登录",
	MUser00039: "用户手机号更改失败",
	MUser00040: "获取微信平台access_token失败",
	MUser00041: "验证码无效，请重新获取",
	MUser00042: "连续超过 %v 次错误，请于 %v 分钟后重新注册",
	MUser00043: "用户重置名称失败",
	MUser00044: "连续超过 %v 次错误，请于 %v 分钟后重新修改密码",
	MUser00045: "查看用户邀请有礼失败",
	MUser00046: "获取用户邀请奖励失败",
	MUser00047: "读取微信回调body失败",
	MUser00048: "用户反馈失败",
	MUser00049: "此微信号已绑定其他账号",
	MUser00050: "获取用户微信详情失败",
	MUser00051: "用户绑定微信失败",
	MUser00053: "解析微信回调失败",
	MUser00054: "校验微信加密签名失败",
	MUser00057: "此手机号已绑定其他微信账号",
	MUser00058: "请先绑定微信",
	MUser00059: "用户已被禁用",
	MUser00061: "学生认证失败",
	MUser00062: "获取用户会员信息失败",
	MUser00063: "每个用户最多只能创建10个token",
	MUser00064: "获取用户个人token失败",
	MUser00065: "每个用户每天只允许调用25次",
	MUser00066: "每个用户每分钟只允许调用5次",
	MUser00067: "手机号不存在",
	MUser00068: "网站暂不支持当前区号手机号注册，后续会陆续开放更多国家注册，敬请期待",
	MUser00069: "您当前无法添加子帐号，请联系客服开通",
	MUser00070: "您当前子帐号数量达到上限，请扩容",
	MUser00071: "子帐号已存在",
	MUser00072: "部分子帐号不可操作",
	MUser00073: "子帐号正在使用中，无法删除子帐号",
	MUser00074: "需要绑定微信才能发送通知",
	MUser00075: "身份核验不通过，请重新输入",
	MUser00076: "当前身份证号已被使用，请重新输入，如有疑问请联系在线客服",
	MUser00077: "授权信息已存在，请先删除再添加新授权",
	MUser00078: "距离您上次绑定微信不足1个月，请于%d天后解绑，谢谢！",
	MUser00079: "此账号暂不支持修改微信绑定相关状态，请联系客服",
	MUser00080: "该手机号已注册, 请更换手机号",
	MUser00081: "连续超过 %v 次错误，请于 %v 分钟后重试",
	MUser00082: "开发者token已失效, 请重新获取",
	MUser00083: "当前申请已审核过或用户已撤销, 请刷新页面后再试",
	MUser00084: "您目前已进行过%s认证, 如有疑问, 请联系客服",
	MUser00085: "当前申请已审核, 请刷新页面后查看最新状态",
	MUser00086: "子账号不存在",
	MUser00087: "您当前非企业用户, 无法使用弹性部署功能",
	MUser00088: "原密码不正确",
	MUser00089: "用户名或密码错误，请重试",
	MUser00090: "当前账号已被禁用，如有疑问请联系管理员",
	MUser00091: "邮箱地址无效，请检查后再试",
	MUser00092: "您今日已经累计提交无效邮箱地址3次，将暂时禁用您发送邮件功能，请明日再试",
	MUser00093: "更新拷贝数据和克隆次数失败",
	MUser00094: "过期时间超出限制，当前可输入最大天数为60天",
	MUser00095: "通行码错误",
	MUser00096: "您目前已进行过%s重新认证, 如有疑问, 请联系客服",
	MUser00097: "设置用户创建实例数量上限失败",
	MUser00098: "账号可能存在违规受限无法充值，如有疑问请联系客服处理",
	MUser00099: "用户登录信息记录失败",
	MUser00100: "pushgateway服务不可用, 请确认后重试",
	MUser00101: "pushgateway鉴权失败, 请确认后重试",

	MFeedBack00001: "处理用户反馈失败",
	MFeedBack00002: "用户反馈不存在",
	MFeedBack00003: "获取反馈列表失败",
	MFeedBack00004: "获取反馈详情失败",
	MFeedBack00005: "获取反馈截图失败",

	MUserGroup00001: "创建用户组失败",
	MUserGroup00003: "统计用户组中的用户数量失败",
	MUserGroup00004: "编辑用户组失败",
	MUserGroup00006: "删除用户组中的用户失败",
	MUserGroup00008: "获取用户组失败",

	MUserMember00001: "统计会员成长值失败",

	MUserPubKey00001: "每个用户最多只能配置两个公钥",

	MFile00001: "获取Logo %s 失败",
	MFile00002: "非法文件，请确认是否为图片格式",
	MFile00003: "上传文件 %s 失败，请重试",
	MFile00004: "上传文件失败，请重试",
	MFile00005: "文件大小超出限制",
	MFile00006: "读取文件失败",

	MPort00001: "端口号已用尽",
	MPort00002: "端口号格式错误",
	MPort00003: "释放端口失败",
	MPort00004: "端口不存在",
	MPort00005: "服务正忙，请稍后重试",
	MPort00007: "端口获取失败",

	// ------------- machine ---------------
	MMachine00001: "您选择的实例配置库存不足，请刷新后重新选择",
	MMachine00002: "获取机器硬件信息失败",
	MMachine00003: "机器已经注册",
	MMachine00004: "机器空闲GPU不足",
	MMachine00005: "GPU库存发生变更，请刷新后重新选择",
	MMachine00006: "该主机存储用量较高，请等待客服处理后开机",
	MMachine00007: "上架前，请编辑机器必填信息",
	MMachine00008: "该主机已租满，请更换主机重新创建实例",
	MMachine00009: "机器可租的截止时间已经到期，请调整可租时间",
	MMachine00010: "该机器不支持当前计费方式, 请确认后重试",
	MMachine00011: "机器计费配置错误",
	MMachine00012: "机器需要配置地区信息，请重新配置",
	MMachine00013: "该地区无NFS，无法挂载网盘",
	MMachine00014: "该机器还未挂载网盘,暂时无法迁移实例",
	MMachine00015: "该机器暂时不支持您选择的计费方式",
	MMachine00016: "该机器暂未开启快速网络传输功能，请确认后重试",
	MMachine00017: "源机器还未挂载网盘,暂时无法迁移实例",
	MMachine00018: "主机维护工单不存在",
	MMachine00019: "主机不存在",
	MMachine00020: "%s机器暂不支持克隆实例, 请联系客服",
	MMachine00021: "机器网络异常，请稍后重试",
	MMachine00022: "机器租期已到期",
	MMachine00023: "主机ID错误",
	MMachine00024: "当前实例正在开机或已经运行，请勿重新操作",
	MMachine00025: "GPU库存数量和主机GPU数量不同, 请检查库存",
	MMachine00026: "主机由于存在潜在异常暂不支持续费, 请联系客服处理",
	MMachine00027: "主机正在维护检修，请稍后重试或联系客服",
	MMachine00029: "清除主机失败，主机上仍存在使用中的资源，请处理后重试",
	MMachine00028: "新主机和源实例主机的厂商和cpu架构不一致，不能克隆",

	// -------------- order ----------------
	MOrder000001: "订单不存在",
	MOrder000002: "您有3个未付款订单，请先处理后重新下单",
	MOrder000003: "当前实例已存在续费订单, 请勿重复续费",
	MOrder000004: "未生效的续费订单不能超过3个",
	MOrder000005: "您有待付款的订单，请先付款",
	MOrder000006: "每人每日最多转换实例计费类型10次，请明日再试",
	MOrder000007: "您有待付款的续费订单，请先取消或付款",
	MOrder000008: "仅运行中与已关机状态下可以进行转计费方式",
	MOrder000009: "存在未支付的订单，不允许释放实例",
	MOrder000010: "您选择的租用时长超过当前机器最大可租用时长，请重新选择",
	MOrder000011: "机器已到期，不可租用",
	MOrder000012: "订单已超时",
	MOrder000013: "包卡转按量正在进行中，请勿重复操作",
	MOrder000014: "当前订单已支付，请勿重复付款",
	MOrder000015: "当前实例计费方式已经为包卡，无需转计费方式",
	MOrder000016: "您有待付款预付费实例扩容数据盘订单, 为保证数据一致性, 请先付款后再进行操作",
	MOrder000017: "实例已到期, 请续费或转按量计费后扩容",
	MOrder000018: "当前订单不可支付",

	// ---------------- bill ----------------
	MBill000001: "账单不存在",
	MBill000002: "余额不足",
	MBill000003: "用户所选天数大于365天",
	MBill000004: "无充值权限",
	MBill000005: "当前账单预充值金额已恢复",

	// ---------------- invoice -------------
	MInvoice000001: "您的开票金额超过实际能开金额上限",
	MInvoice000002: "您的开票金额不足1元",
	MInvoice000003: "账单已开票，请勿重复开票",
	MInvoice000004: "订单已开票，请勿重复开票",

	// --------------- wallet -----------------
	MWallet00001: "充值金额最低1元",
	MWallet00002: "钱包更新繁忙，请重试", // internal
	MWallet00003: "生成微信订单失败, 请稍后重试",
	MWallet00004: "解析微信回调信息失败",
	MWallet00005: "未查询到充值记录, 请确认后重试",
	MWallet00006: "生成支付宝订单失败, 请稍后重试",
	MWallet00007: "支付超时，请重新创建",
	MWallet00008: "余额不足",
	MWallet00009: "子账户钱包不存在",
	MWallet00010: "子账户余额不足",
	MWallet00011: "解析Ali回调信息失败",
	MWallet00012: "银行汇款记录[%s]不存在，请确认后重试",
	MWallet00013: "银行汇款记录[%s]已经认款，无需重复认款，请确认后重试",
	MWallet00014: "可提现余额不足",
	MWallet00015: "请输入正确的提现金额",
	MWallet00016: "请选择要提现的账单",
	MWallet00017: "退款信息错误，请确认后重试",
	MWallet00018: "退款失败",
	MWallet00019: "专属汇款码已用尽, 请联系客服",
	MWallet00020: "当前专属汇款码只对企业用户开放, 如有疑问请联系客服",

	// -------------- voucher -------------------
	MVoucher00001: "代金券不存在",
	MVoucher00002: "你已领取过注册券，请在代金券页面中查看",
	MVoucher00003: "定向券类型错误， 请检查券设置",
	MVoucher00004: "未查到代金券，请重新输入",
	MVoucher00005: "兑换券已抢完",
	MVoucher00006: "您已兑换过该兑换券，请在代金券页面中查看",
	MVoucher00007: "获取注册券规则错误",
	MVoucher00008: "代金券名称已存在",
	MVoucher00009: "兑换券未开始兑换，到时再来吧",
	MVoucher00010: "兑换券已截止兑换，下次早点来吧",
	MVoucher00011: "定向券已截止发放",
	MVoucher00012: "定向券已过使用时间，请使用新券",
	MVoucher00013: "累计邀请人数不可重复",
	MVoucher00014: "该代金券已失效, 无需再作废",

	// --------------- gpu_type -----------------
	GpuType00001: "GPU型号正在被使用，请先解除关联后重试",
	GpuType00002: "gpu号型 %s 不存在",

	// ---------------- gpu stock ----------------
	GpuStock00001: "gpu库存不足，请等待有空闲gpu后再重试",
	GpuStock00002: "指定的gpu占用不存在, 请刷新后重试",
	GpuStock00003: "录入占用失败, 请刷新后重试",
	GpuStock00004: "GPU删除失败, 请刷新后重试",

	// ---------------- idle_job -----------------
	IdleJob000002: "没有创建闲时作业权限",

	// ---------------- container -----------------
	Container000001: "在同一时刻最多允许开启 %d 个无卡模式的实例",
	Container000002: "容器不存在",

	// ---------------- region ------------------
	Region00001: "地区标识不存在",
	Region00002: "地区标识已存在",
	Region00003: "地区名称已存在",
	Region00004: "地区NFS未配置默认网盘容量，请联系客服",
	Region00005: "地区NFS已满",
	Region00006: "该用户在该地区还没有初始化网盘",
	Region00007: "网盘容量不足，终止上传",
	Region00008: "网盘最大扩容500G",
	Region00009: "该网盘不可用,请联系客服",
	Region00010: "扩容记录不存在",
	Region00011: "当前地区不可创建弹性部署",
	Region00012: "当前地区文件存储不可用, 请联系客服",
	Region00013: "地区名称或地区签名错误",
	Region00014: "文件存储配置不存在",

	// ----------------- invoice --------------------
	Invoice00001: "待审核的发票已经存在，请稍后重试",
	Invoice00002: "已完成或被驳回的发票无法再驳回",

	// -------------- common data -------------------
	CommonData00001: "公共数据已经存在",

	// -------------- coupon -----------------·
	Coupon00001: "您已领取过该券，每人限领一张",
	Coupon00002: "该优惠券未开放领取，有疑问请联系客服",
	Coupon00003: "优惠券已抢完",
	Coupon00004: "您暂时不符合领取条件，请查看领券规则",
	Coupon00005: "优惠券已使用，请重新选择优惠券",
	Coupon00006: "当前不在优惠券可用日期内，请重新选择优惠券",
	Coupon00007: "订单金额不满足优惠券使用条件，请重新选择",
	Coupon00008: "您选择的优惠券超出使用范围，请重新选择",
	Coupon00009: "您选择的优惠券不符合使用条件，请重新选择",
	Coupon00010: "您传入的优惠券生成数量大于当前优惠券的剩余发行量，请您稍后重试",
	Coupon00011: "您选择的优惠券与该实例的计费方式不符，请重新选择",
	Coupon00012: "兑换码不存在，请检查后重试",
	Coupon00013: "该兑换码下的优惠券已被领取，请检查后重试",

	// --------------- user prize --------------------
	UserPrize00001: "您已参与本轮抽奖",
	UserPrize00002: "您的充值金额不足，无法抽奖",
	UserPrize00003: "还未到抽奖时间",
	UserPrize00004: "查询抽奖白名单错误",
	UserPrize00005: "请完成第一步并等待审核通过再参与抽奖",
	UserPrize00006: "您的累计消费金额未超过10元，不满足参与要求",
	UserPrize00007: "添加白名单错误",
	UserPrize00008: "没有抽奖的人",

	// ---------------private_image ------------------
	PrivateImage00001: "该镜像名称已经存在",
	PrivateImage00002: "实例镜像已被删除",
	PrivateImage00003: "实例正在保存镜像.请稍后重试",
	PrivateImage00004: "镜像还未保存成功",
	PrivateImage00005: "您的镜像还未共享",
	PrivateImage00006: "您的镜像正在共享中，请先取消共享再删除",
	PrivateImage00007: "镜像数量已经超过上限，您最多可保存3个镜像",
	PrivateImage00008: "您的镜像正在被%s使用中，请稍后再试",
	PrivateImage00009: "镜像未就绪，请稍后再试",
	PrivateImage00010: "私有镜像不存在",
	PrivateImage00011: "基础镜像不存在",
	PrivateImage00012: "部分实例权限变更失败，请刷新后再试",
	PrivateImage00013: "无镜像操作权限",

	// ---------------- data disk ------------------
	DataDisk00001: "扩容超过最大容量限制",
	DataDisk00002: "磁盘空间不足，预留数据盘失败，请刷新重试",
	DataDisk00003: "更改数据盘大小失败",
	DataDisk00004: "仅按量计费实例支持数据盘扩容/缩容",
	DataDisk00005: "缩容后容量不得小于免费容量",
	DataDisk00006: "缩容后容量不得小于当前实例已使用容量",
	DataDisk00007: "请在实例关机状态下进行扩容/缩容数据盘",
	DataDisk00008: "不支持包卡实例到期前中途缩容，如您确有此需求，您可以选择转按量后进行数据盘缩容操作",

	// ---------------- credit wallet -----------------
	CreditWallet00001: "该用户未开通授信额度，请确认后重试",
	CreditWallet00002: "调整额度后新额度不能小于已使用金额",
	CreditWallet00003: "您暂未开通授信额度，请联系客服开通",
	CreditWallet00004: "授信额度不足，请调整操作金额或联系客服提额",
	CreditWallet00005: "您填写的还款金额大于该账户欠款金额，请确认后重试",
	CreditWallet00006: "授信钱包已存在，无需再次开通",
	CreditWallet00007: "授信钱包操作金额最低1元",

	// ----------------- public data --------------------
	PublicData00001: "共享数据不存在，请刷新后再试",
	PublicData00002: "数据集官方地址字数超过最大限制，请重新输入",

	Notice00001: "到期时间不能早于当前时间",

	Community0001: "镜像不存在",

	// ------------------ deployment --------------------
	Deployment0001: "弹性部署不存在",
	Deployment0002: "弹性部署容器不存在",
	Deployment0003: "弹性部署删除失败, 您可以尝试先停止部署后再进行删除操作",
	Deployment0004: "弹性部署调度黑名单设置失败",
	Deployment0005: "弹性部署已经停止或将要停止，无需购买时长包",
	Deployment0006: "购买弹性部署时长包异常，请刷新后查看",
	Deployment0007: "所选弹性部署时长包 %s 不存在",
	Deployment0008: "退订弹性部署时长包异常，请刷新后查看",
	Deployment0009: "没有符合条件的弹性部署时长包，请联系客服",
	Deployment0010: "当前版本号已存在，请提供新的版本号后重试",
	Deployment0011: "当前版本号过长，请缩短版本号后重试",
	Deployment0012: "失效时间不能为负数，如需立即解除黑名单请设置为 0",
	Deployment0013: "获取弹性部署调度黑名单列表失败",

	// ------------------- storage agent ---------------------
	StorageAgent0001: "文件存储未初始化",

	// ------------------- work order ---------------------
	WorkOrder0001: "工单不存在",
	WorkOrder0002: "监控中存在未处理的相同记录",
	WorkOrder0003: "工单中存在未完成的相同记录",
	WorkOrder0004: "租户中已存在相同的租户标识",
	WorkOrder0005: "物料已存在相同的名称",
	WorkOrder0006: "该客户不存在",
	WorkOrder0007: "该范围已经存在机器",
	WorkOrder0008: "该备件发货单不存在",
	WorkOrder0009: "机型已存在相同的名称",
	WorkOrder0010: "当前机型已存在该物料，请勿重复添加",
	WorkOrder0011: "机器不存在",
	WorkOrder0012: "当前备件发货单已存在该物料，请勿重复添加",
	WorkOrder0013: "您已添加过此客户，请勿重复添加",
	WorkOrder0014: "当前租户已存在该autodl用户，请勿重复添加",
	WorkOrder0015: "租户不存在",
	WorkOrder0016: "该发货单不在当前租户下，请您重新选择",
	WorkOrder0017: "未选择返修工单",
	WorkOrder0018: "该机器不在当前租户下，请您重新选择",
	WorkOrder0019: "该备件工单不存在",
	WorkOrder0020: "该备件工单已经绑定发货单，状态无法修改",
	WorkOrder0021: "该返修工单不存在",
	WorkOrder0022: "该返修工单已经绑定发货单，状态无法修改",
	WorkOrder0023: "该短信模板名称已存在",
	WorkOrder0024: "短信发送模板ID不存在",
	WorkOrder0025: "该实例的回执已存在，请勿重复添加",
	WorkOrder0026: "该实例的回执不存在",
	WorkOrder0027: "当前主机下存在未结束的实例通知，请勿重复添加",
	WorkOrder0028: "实例格式不正确，请重新输入",
	WorkOrder0029: "飞书发送短信的webhook访问令牌[%s]无效，请检查后再重试",
	WorkOrder0030: "该工单状态为已完成，无法订阅主机空闲事件",

	// ------------------- sms ---------------------
	Sms0001: "短信发送失败, 请检查手机号是否正确",

	// -------------------- ScheduleInstance ------------------------
	ScheduleInstance0001: "当前地区和gpu型号下不存在符合条件的主机，请您重新选择",
	ScheduleInstance0002: "该调度创建的实例不存在",
	ScheduleInstance0003: "无该调度实例的操作权限",
	ScheduleInstance0004: "该实例已调度成功，请刷新页面获取实例最新调度状态",

	// -------------------- Contract ------------------------
	Contract0001: "账单的当前状态不支持确认，请稍后重试",
	Contract0002: "该合同id已存在，请勿重复添加",
	Contract0003: "该账单状态为已支付，无法进行后续状态的修改",
}
