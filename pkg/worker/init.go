package worker

import (
	"context"
	"server/conf"
	"server/entrance/initialize"
	"server/pkg-agent/agent_constant"
	coreContainerModel "server/pkg-core/container_runtime/model"
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/libs"
	"server/pkg/logger"
	message_model "server/pkg/message/model"
	"server/plugin/queue"
	"server/plugin/queue_interface"
	"time"

	"github.com/pkg/errors"
)

type Worker struct {
	*initialize.ServiceFactory
	l    *logger.Logger
	cron *libs.Cron
}

func NewWorker(svc *initialize.ServiceFactory) *Worker {
	g := &Worker{
		ServiceFactory: svc,
		l:              logger.NewLogger("Worker"),
		cron:           libs.NewCron(),
	}
	return g
}

// Run TODO: 完善api相关router, 注意合理的panicChan
func (w *Worker) Run(ctx context.Context) error {
	w.l.Info("ready to run worker...")
	defer func() {
		w.l.Info("worker quit...")
	}()

	// pkg init
	go w.CronSyncChargingListAndRunningInstance(ctx) // 同步charging状态

	// 用协程轮询的任务在此处
	// TODO: review, 这里有进一步封装的动机, 各独立模块暴露出cronJobRegister和msgRegister, 这里只负责循环注册, 将定义与模块实现放在一起, 降低模块内容的离散度, 也可以降低维护worker的难度和心智负担

	// instance worker
	go w.Instance.CronJobRegister(ctx, w.cron)
	w.l.Info("Instance cron jobs registered.")

	go w.Region.CronJobRegister(ctx)
	w.l.Info("region cron jobs registered")

	go w.Machine.CronJobRegister(ctx)
	w.l.Info("machine cron jobs registered")

	// work order 定时任务很少，暂时不拆分单独的worker，目前运行在work order api server中
	//go w.WorkOrder.CronWorkOrder(ctx)
	//w.l.Info("work order cron jobs registered")

	// 对消息队列的 handler, 在此函数内部阻塞
	w.l.Info("Worker is running...")
	return w.HandleQueue(ctx) // if err, panic
}

func (w *Worker) Run2(ctx context.Context) error {
	go w.CronRegisterOfLockHandler2(ctx)

	go w.PrivateImage.CronJobRegister(ctx)
	w.l.Info("private-image cron jobs registered")

	go w.DataDiskStock.CronJobRegister(ctx)
	w.l.Info("data disk cron jobs registered")

	/**
	 * 一些 Sub 单实例权限控制.
	 */
	w.Queue.RunSubscribe(ctx)
	w.l.Info("Worker2 handler quit gracefully...")
	return nil
}

func (w *Worker) Run3(ctx context.Context) error {

	go w.CronRegisterOfLockHandler3(ctx)

	go w.Promotion.CronJobRegister(ctx)
	w.l.Info("promotion cron jobs registered")

	// public data
	go w.PublicData.CronJobRegister(ctx)
	w.l.Info("public data cron jobs registered")

	/**
	 * 一些 Sub 单实例权限控制.
	 */
	w.Queue.RunSubscribe(ctx)
	w.l.Info("Worker2 handler quit gracefully...")
	return nil
}

func (w *Worker) Run4(ctx context.Context) error {
	// user worker
	go w.User.CronSyncUserMemberInfo(ctx)
	w.l.Info("cron sync user growth value registered.")

	<-ctx.Done()
	w.l.Info("Worker4 handler quit gracefully...")
	return nil
}

func (w *Worker) Run5(ctx context.Context) error {
	go w.Deployment.CronJobRegister(ctx)
	w.l.Info("deployment cron job registered")

	// scheduled instance worker
	go w.Instance.CoreScheduleInstance(ctx, w.cron)
	w.l.Info("Schedle Instance cron registered.")

	go w.Notify.CornJobRegister(ctx, w.cron)
	w.l.Info("notify cron registered.")

	<-ctx.Done()
	w.l.Info("Worker5 handler quit gracefully...")
	return nil
}

func (w *Worker) Run6(ctx context.Context) error {
	// bill worker
	w.BC.CronJobRegister(ctx, w.cron)
	w.l.Info("BC cron jobs registered.")

	<-ctx.Done()
	w.l.Info("Worker6 handler quit gracefully...")
	return nil
}

func (w *Worker) HandleQueue(ctx context.Context) (err error) {
	// 作为queue的后台任务, 放在这里上下文相关, 更为紧凑
	go func(ctx context.Context) {
		cronSendMsgToMQTicker := time.NewTicker(time.Second * 3)
		for {
			select {
			case <-ctx.Done():
				return
			case <-cronSendMsgToMQTicker.C:
				errMsg := w.Queue.CronPub()
				if len(errMsg) != 0 {
					w.l.Error("failed to cron pub " + errMsg)
				}
			}
		}
	}(ctx)

	/**
	 * 订阅消息
	 */
	for _, v := range w.BC.MsgRegister() {
		w.Queue.SubscribeRegister(v)
	}

	// 用户被禁用后
	w.Queue.SubscribeRegister(queue.RegisterInfo{
		Type:    queue_interface.AfterDisableUser,
		Handler: w.afterDisableHandler,
	})

	w.Queue.SubscribeRegister(queue.RegisterInfo{
		Type:    queue_interface.AfterUserRecharge,
		Handler: w.afterUserRechargeGrowthValueHandler,
	})

	// core通信queue
	w.CoreQueue.SubscribeRegister(queue.RegisterInfo{
		Type:    queue_interface.GetCoreToBusinessUpdateContainerStatusChannelType(conf.GetGlobalGsConfig().App.Platform),
		Handler: w.coreToBusinessContainerStatusHook,
	})
	w.CoreQueue.SubscribeRegister(queue.RegisterInfo{
		Type:    queue_interface.GetCoreToBusinessModifyDataChannelType(conf.GetGlobalGsConfig().App.Platform),
		Handler: w.coreToBusinessModifyDataHandler,
	})

	// 计费任务

	// 通过内部消息回调更新实例. 主要是用于更新状态. 注意为单实例.
	// 需要注册为Lock类型的handler由异步方法检查添加
	go w.CronRegisterOfLockHandler(ctx)

	/**
	 * 一些 Sub 单实例权限控制.
	 */
	go w.CoreQueue.RunSubscribe(ctx)
	w.Queue.RunSubscribe(ctx)
	w.l.Info("Worker handler quit gracefully...")

	return
}

/**
 * Handler 合集
 */

func (w *Worker) coreToBusinessModifyDataHandler(msg queue.ElementPayload, ack queue.ConsumerHandlerInterface) {
	payload := &queue_interface.NewQueueForCoreToBusinessModifyData{}
	errLogger := w.l.WithFields(logger.Fields{"type": payload.Type(), "info": msg})
	var parseError error
	var err error
	defer func() {
		if parseError != nil {
			ack.Drop()
		} else if err != nil {
			err = ack.Nack()
			if err != nil {
				errLogger.ErrorE(err, "nack failed")
			}
		} else {
			err = ack.Ack()
			if err != nil {
				errLogger.ErrorE(err, "ack failed")
			}
		}
	}()

	parseError = payload.ParseFromContent(msg.Payload)
	if parseError != nil {
		errLogger.WarnE(parseError, "Failed to parse content. Will drop it.")
		return
	}

	if payload.Req.Tenant != conf.GetGlobalGsConfig().App.Platform {
		w.l.WithField("msg req", payload.Req).Warn("req source error, continue")
		return
	}

	w.l.WithField("OptType", payload.Req.OptType).WithField("payload", payload.Req.Payload).Info("coreToBusinessModifyDataHandler will handle msg")

	switch payload.Req.OptType {
	case constant.MQCoreToBusinessModifyMachineOpt,
		constant.MQCoreToBusinessModifyDataDiskOpt,
		constant.MQCoreToBusinessModifyGpuTypeOpt,
		constant.MQCoreToBusinessModifyRegionOpt:
		modifyPayload := &constant.MQCoreBusinessModifyDataPayload{}
		parseError = modifyPayload.ParseFromContent(payload.Req.Payload)
		if parseError != nil {
			errLogger.ErrorE(parseError, "parse inner content failed, skip")
			return
		}

		switch payload.Req.OptType {
		case constant.MQCoreToBusinessModifyMachineOpt:
			err = w.Machine.CoreModifyMachine(modifyPayload)
		case constant.MQCoreToBusinessModifyDataDiskOpt:
			err = w.DataDiskStock.CoreModifyDataDiskStock(modifyPayload)
		case constant.MQCoreToBusinessModifyGpuTypeOpt:
			err = w.GpuType.CoreModifyGpuType(modifyPayload)
		case constant.MQCoreToBusinessModifyRegionOpt:
			err = w.Region.CoreModifyRegion(modifyPayload)
		}
	case constant.MQCoreToBusinessInitFileStorageOpt:
		err = w.Region.InitFileStorageFinal(payload.Req.Payload)

	case constant.MQCoreToBusinessUploadImageProcessOpt:
		modifyPayload := &agent_constant.UploadOssInfo{}
		parseError = modifyPayload.ParseFromString(payload.Req.Payload)
		if parseError != nil {
			errLogger.ErrorE(parseError, "parse inner content failed, skip")
			return
		}
		err = w.PrivateImage.UpdateImageUploadInfo(modifyPayload)

	case constant.MQCoreToBusinessUploadImageCancelOpt:
		modifyPayload := &agent_constant.CancelUploadImageInfo{}
		parseError = modifyPayload.ParseFromString(payload.Req.Payload)
		if parseError != nil {
			errLogger.ErrorE(parseError, "parse inner content failed, skip")
			return
		}
		err = w.PrivateImage.ConfirmCancelUploadImageAndDeleteRecord(modifyPayload)
	default:
		errLogger.Error("payload operate type not found, skip")
		parseError = errors.New("payload operate type not found")
	}

	return
}

// coreToBusinessContainerStatusHook 不论是instance，idleJob，dc，由container发送回来的，更新product状态的消息，统一在此处接收
func (w *Worker) coreToBusinessContainerStatusHook(msg queue.ElementPayload, ack queue.ConsumerHandlerInterface) {
	w.l.Info("coreToBusinessContainerStatusHook get a msg: %s", msg.MsgUUID)
	payload := &queue_interface.NewQueueForCoreToBusinessUpdateContainerStatus{}
	errLogger := w.l.WithFields(logger.Fields{"type": payload.Type(), "info": msg})
	var parseError error
	var err error
	defer func() {
		if parseError != nil {
			ack.Drop()
		} else if err != nil {
			err = ack.Nack()
			if err != nil {
				errLogger.ErrorE(err, "nack failed")
			}
		} else {
			err = ack.Ack()
			if err != nil {
				errLogger.ErrorE(err, "ack failed")
			}
		}
	}()

	parseError = payload.ParseFromContent(msg.Payload)
	if parseError != nil {
		errLogger.WarnE(parseError, "Failed to parse content. Will drop it.")
		return
	}

	for _, req := range payload.Reqs {
		if req.Tenant != conf.GetGlobalGsConfig().App.Platform {
			w.l.WithField("msg req", req).Warn("req source error, continue")
			return
		}

		switch req.OptType {
		case constant.MQCoreToBusinessUpdateContainerStatusOpt:
			pubPayload := &queue_interface.NewQueueForTelnetMachine{
				Tenant:    req.Tenant,
				MachineID: req.MachineID,
				Opt:       queue_interface.UpdateProductStatus,
				Payload:   req.Payload,
			}
			err = message_model.SimplePubMessage(errLogger, w.Queue, pubPayload)
			if err != nil {
				errLogger.WithField("pubPayload", pubPayload).ErrorE(err, "CoreToBusinessUpdateContainerStatus pub payload failed")
			}
		case constant.MQCoreToBusinessDeleteInstanceRecordOpt:
			// payload 就是实例uuid
			if req.Payload == "" {
				errLogger.Warn("CoreToBusinessDeleteInstanceRecord payload is empty, skip")
				continue
			}

			err = w.Instance.DeleteInstance(constant.InstanceUUIDType(req.Payload))
			if err != nil {
				errLogger.ErrorE(err, "CoreToBusinessDeleteInstanceRecord failed")
				continue
			}
			errLogger.Info("CoreToBusinessDeleteInstanceRecord success")

		default:
			errLogger.Error("payload operate type not found, skip")
			parseError = errors.New("payload operate type not found")
		}
	}

}

// telnetMachineHandler 由 coreToBusinessContainerStatusHook 分流得来的，每个machine持有的单独的channel，用于更新product状态
func (w *Worker) telnetMachineHandler(msg queue.ElementPayload, ack queue.ConsumerHandlerInterface) {
	msgPayload := &queue_interface.NewQueueForTelnetMachine{}
	errLogger := w.l.WithFields(logger.Fields{"type": msgPayload.Type(), "info": msg})
	var parseError error
	var err error
	defer func() {
		if parseError != nil {
			ack.Drop()
		} else if err != nil {
			err = ack.Nack()
			if err != nil {
				errLogger.ErrorE(err, "nack failed")
			}
		} else {
			err = ack.Ack()
			if err != nil {
				errLogger.ErrorE(err, "ack failed")
			}
		}
	}()
	parseError = msgPayload.ParseFromContent(msg.Payload)
	if parseError != nil {
		errLogger.WarnE(parseError, "Failed to parse content. Will drop it.")
		return
	}

	switch msgPayload.Opt {
	case queue_interface.UpdateInstanceStatus, queue_interface.UpdateProductStatus:
		modifyPayload := &coreContainerModel.DoHookParams{}
		parseError = modifyPayload.ParseFromContent(msgPayload.Payload)
		if parseError != nil {
			errLogger.ErrorE(parseError, "parse inner content failed, skip")
		}
		if modifyPayload.Tenant != conf.GetGlobalGsConfig().App.Platform {
			parseError = errors.Errorf("container status update: tenant [%s] error.", modifyPayload.Tenant)
			return
		}

		switch modifyPayload.RuntimeType {
		case constant.ContainerRuntimeOfInstance:
			err = w.Instance.InstanceStatusUpdateByContainer(*modifyPayload)
		case constant.ContainerRuntimeOfDeployment:
			err = w.Deployment.DCStatusUpdateByContainer(*modifyPayload)
		}
		return
	default:
		errLogger.Error("payload operate type not found, skip optIs: %s", msgPayload.Opt)
		parseError = errors.New("payload operate type not found")
	}
}

// 更新实例
func (w *Worker) hookOperateInstanceHandler(notifyInfo queue.ElementPayload, consumerHandler queue.ConsumerHandlerInterface) {
	w.l.Debug("hookOperateInstanceHandler() get a msg: %s", notifyInfo.MsgUUID)

	payload := &queue_interface.NewQueueForInstanceOnMachine{}
	errLogger := w.l.WithFields(logger.Fields{
		"type": payload.Type(),
		"info": notifyInfo,
	})

	// parse
	parseErr := payload.ParseFromContent(notifyInfo.Payload)
	if parseErr != nil {
		errLogger.WithError(parseErr).Warn("Failed to parse content. Will drop it.")
		consumerHandler.Drop() // DROP
		return
	}

	var queueErr error
	defer func() {
		w.l.Debug("hookOperateInstanceHandler() msg done. err: '%+v'", queueErr)

		if queueErr != nil {
			// 空闲 GPU 不足等错误不需要重试
			if biz.CanOmitTheReturnErrorForQueue(queueErr) {
				errLogger.Info("Omit some business err or record not found err for queue: %+v", queueErr.Error())
				queueErr = nil
				consumerHandler.Drop() // DROP
				return
			}

			w.l.WithError(queueErr).Error("Handle queue failed. Try to reject.")

			uErr := consumerHandler.Reject() // REJECT
			if uErr != nil {
				w.l.WithError(uErr).Error("Reject queue failed.")
				return
			}
		}
	}()

	// 处理过程
	switch payload.RuntimeType {
	case constant.ContainerRuntimeOfDeployment:
		queueErr = w.Deployment.DCOperate(payload.OptReqs...)
	default:
		queueErr = w.Instance.OperateInstance(payload.OptReqs...)
	}
	if queueErr != nil {
		errLogger.WithError(queueErr).Warn("Handle SMSStatusQueue failed.")
		return
	}

	// ACK
	ackErr := consumerHandler.Ack() // ACK
	if ackErr != nil {
		errLogger.WithError(ackErr).Warn("Ack queue failed.")
		return
	}
}

func (w *Worker) afterDisableHandler(afterDisableUser queue.ElementPayload, consumerHandler queue.ConsumerHandlerInterface) {
	payload := &queue_interface.NewQueueForAfterDisableUser{}
	w.l.WithField("type", payload.Type()).WithField("info", afterDisableUser)

	// parse
	parseErr := payload.ParseFromContent(afterDisableUser.Payload)
	if parseErr != nil {
		w.l.WithError(parseErr).Warn("Failed to parse content. Will drop it.")
		consumerHandler.Drop()
		return
	}

	// 进行处理,通知实例进行关机
	err := w.Instance.StopInstancesForUsers(payload.UserId)
	if err != nil {
		w.l.WithError(err).Error("Handler queue failed.")
		return
	}

	// ACk
	ackErr := consumerHandler.Ack()
	if ackErr != nil {
		w.l.WithError(ackErr).Warn("Ack queue failed.")
		return
	}
}

func (w *Worker) afterUserRechargeGrowthValueHandler(updateGrowthValue queue.ElementPayload, consumerHandler queue.ConsumerHandlerInterface) {
	payload := &queue_interface.NewQueueForUserRecharge{}
	w.l.WithField("type", payload.Type()).WithField("info", updateGrowthValue).Info("Handle growth value after user recharge.")

	// parse
	parseErr := payload.ParseFromContent(updateGrowthValue.Payload)
	if parseErr != nil {
		w.l.WithError(parseErr).Warn("Failed to parse content. Will drop it.")
		consumerHandler.Drop()
		return
	}

	err := w.User.AddUserGrowthValueCmd(payload.UserId, payload.Asset, constant.VipRecharge)
	if err != nil {
		w.l.WithError(err).Error("Handler queue failed.")
		return
	}

	// ACk
	ackErr := consumerHandler.Ack()
	if ackErr != nil {
		w.l.WithError(ackErr).Warn("Ack queue failed.")
		return
	}
}
