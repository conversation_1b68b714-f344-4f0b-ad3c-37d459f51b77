package model

import (
	"server/pkg/constant"
	"server/pkg/logger"
	"sync"
	"time"
)

/**
 * 提供注册状态改变 hook 的方法.
 */

const ContainerHookSecName = "container_hook"

const HookAll constant.ContainerRuntimeUUID = "all"

type ContainerStatusHookFunc func(params DoHookParams) error

type StatusHookFuncBody struct {
	F    ContainerStatusHookFunc
	Name string
}

type HooksBucket map[constant.ContainerRuntimeType][]StatusHookFuncBody

type DoHookParams struct {
	RuntimeUUID constant.ContainerRuntimeUUID `json:"runtime_uuid"`

	RuntimeType constant.ContainerRuntimeType     `json:"runtime_type"`
	OldStat     constant.ContainerStatusType      `json:"old_stat"`
	StatusInfo  constant.ContainerStatusUpdateReq `json:"status_info"`
	ValidAt     time.Time                         `json:"valid_at"`
	//Container   Container                         `json:"container"`
}

type Hooker struct {
	Bucket HooksBucket
	lock   sync.Mutex
	logAll bool
	l      *logger.Logger
}
