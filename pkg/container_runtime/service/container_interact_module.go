package service

import (
	"fmt"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gorm.io/gorm"
	"server/pkg-agent/agent_constant"
	coreContainerModel "server/pkg-core/container_runtime/model"
	corePortModel "server/pkg-core/port/model"
	coreRegionModel "server/pkg-core/region/model"
	bcm "server/pkg/billing_center/model"
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	deploymentModel "server/pkg/deployment/model"
	imageModel "server/pkg/image/model"
	"server/pkg/libs"
	"server/pkg/logger"
	"server/pkg/machine/model"
	"server/pkg/module_definition"
	regionModel "server/pkg/region/model"
	userModel "server/pkg/user/model"
	"server/plugin/queue"
	"server/plugin/redis_plugin"
)

const secNameModuleHandler = "container_runtime_to_other_module"

type ModuleHandler interface {
	CheckMachineRealHealth(machineID string, runtimeType constant.ContainerRuntimeType) bool

	RequirePorts(uid int, runtimeUUID constant.ContainerRuntimeUUID, regionSign constant.RegionSignType, protocol string) (instancePorts corePortModel.InstancePortDistributor, err error)
	FreePorts(runtimeUUID constant.ContainerRuntimeUUID) error

	AvailableGpus(runtimeUUID constant.ContainerRuntimeUUID, machineID string, level constant.PriorityType) (gpuList []string, gpuCaps []string, err error)

	GetOrder(orderUUID string) (*bcm.Order, error)

	GetConfigTheFirstTime(
		runtimeUUID constant.ContainerRuntimeUUID,
		orderUUID string,
	) (config coreContainerModel.ContainerParamBuildRequest, err error)

	CloneConfigTheFirstTime(
		srcContainerParam coreContainerModel.ContainerParam,
		dstRuntimeUUID constant.ContainerRuntimeUUID,
		orderUUID string,
	) (config coreContainerModel.ContainerParamBuildRequest, err error)

	UpdatePrivateImage(progressInfo *agent_constant.UploadOssInfo) error
	GetImage(imageUUID string) (commonImage *imageModel.ImageCommonInfo, err error)
	CancelUploadPrivateImage(cancelUploadImageInfo *agent_constant.CancelUploadImageInfo) error

	GetLatestNetDiskMountAndProxy(
		uid int,
		subName, machineID string,
		runtimeType constant.ContainerRuntimeType,
	) (info constant.LatestUserMachineRegionInfo, err error)

	GetMachineRegionSign(machineID string) (rs constant.RegionSignType, err error)

	// GetUserPublicKey 用户公钥
	GetUserPublicKey(uid int) (pubKey string, err error)
	GetAutopanelToken(uid int) (token string, err error)
	GetUser(uid int) (user *userModel.User, err error)

	GetMachine(machineID string) (machine *model.Machine, err error)

	GetMachineStorageOssCredentials(machineID string) (credentials agent_constant.MinioCredentials, err error)
	GetMachineImageStorageOssCredentials(machineID, imageUUID string) (bucket agent_constant.MinioBucketInfo, credentials agent_constant.MinioCredentials, err error)

	GetSSLDomainPort(rs constant.RegionSignType) (port string, err error)
	DCGet(uuid string) (*deploymentModel.DeploymentContainer, error)
	ContainerMonitorGet(uid int, runtimeType constant.ContainerRuntimeType) string
}

// ---------------------------------------------------------------------------

type MH struct {
	// 此处持有其他模块的 interface
	machine      module_definition.MachineInference
	port         module_definition.PortInterface
	gpuStock     module_definition.GpuStockInterface
	gpuType      module_definition.GpuTypeInference
	bc           module_definition.BCInterface
	deployment   module_definition.DeploymentInterface
	privateImage module_definition.PrivateImageInterface
	region       module_definition.RegionInterface
	user         module_definition.UserInterface
	dds          module_definition.DataDiskStockInterface
	t            redis_plugin.SyncTimer
	q            *queue.Q
	l            *logger.Logger
}

func NewModuleHandler(
	machineInterface module_definition.MachineInference,
	portInterface module_definition.PortInterface,
	gpuStockInterface module_definition.GpuStockInterface,
	gpuType module_definition.GpuTypeInference,
	bcInterface module_definition.BCInterface,
	deployment module_definition.DeploymentInterface,
	privateImageInterface module_definition.PrivateImageInterface,
	region module_definition.RegionInterface,
	user module_definition.UserInterface,
	dds module_definition.DataDiskStockInterface,
	t redis_plugin.SyncTimer,
	q *queue.Q,
) *MH {
	return &MH{
		machine:      machineInterface,
		port:         portInterface,
		gpuStock:     gpuStockInterface,
		gpuType:      gpuType,
		bc:           bcInterface,
		deployment:   deployment,
		privateImage: privateImageInterface,
		region:       region,
		user:         user,
		dds:          dds,
		t:            t,
		q:            q,
		l:            logger.NewLogger(secNameModuleHandler),
	}
}

// CheckMachineRealHealth 检查 false 之后返回 err = biz.ErrMachineUnhealthy. 此处只关心心跳.
func (m *MH) CheckMachineRealHealth(machineID string, runtimeType constant.ContainerRuntimeType) bool {
	machineStatus, err := m.machine.GetMachineStatus(machineID)
	if err != nil {
		m.l.WithField("machine_id", machineID).Error("get machine status from redis failed")
		return false
	}
	if machineStatus.HealthStatus == agent_constant.Normal || machineStatus.HealthStatus == agent_constant.StorageMildException {
		return true
	}
	if runtimeType == constant.ContainerRuntimeOfDeployment && machineStatus.HealthStatus == agent_constant.StorageSeriousException {
		return true
	}
	if machineStatus.HealthStatus == agent_constant.StorageSeriousException {
		machine, err := m.GetMachine(machineID)
		if err != nil {
			return false
		}
		if err = machine.CanRunInstance(); err != nil {
			return false
		}
		return true
	}

	m.l.WithField("machineID", machineID).WithField("machineStatus", machineStatus.HealthStatus).Error("CheckMachineStatusHealth: machine unhealthy")

	return false
}

// CheckMachineBusinessHealth 此处关心业务上是否上架下架.
func (m *MH) CheckMachineBusinessHealth(machineID string) bool {
	ok, err := m.machine.CheckMachineHealth(machineID)
	if err != nil || !ok {
		return false
	}

	return true
}

func (m *MH) RequirePorts(
	uid int,
	runtimeUUID constant.ContainerRuntimeUUID,
	regionSign constant.RegionSignType,
	protocol string,
) (instancePorts corePortModel.InstancePortDistributor, err error) {

	m.l.WithField("runtime_uuid", runtimeUUID).Info("Try to require instance ports.")

	instancePorts, err = m.port.RequirePortsForInstance(regionSign, int64(uid), runtimeUUID.String(), false, protocol)
	if err != nil {
		m.l.WarnE(err, "Container[%s] require ports in region:%s failed.", runtimeUUID, regionSign)
		err = biz.ErrInstanceCreateExceptionOccurred
		return
	}

	ok := instancePorts.Validate()
	if !ok {
		m.l.WarnE(err, "Container[%s] got wrong ports: %+v in region:%s.", runtimeUUID, instancePorts, regionSign)
		err = biz.ErrInternalError
		return
	}

	m.l.Info("Bind ports %+v for Container[%s] in region: %s.", instancePorts, runtimeUUID, regionSign)
	return
}

func (m *MH) FreePorts(runtimeUUID constant.ContainerRuntimeUUID) error {
	err := m.port.FreePorts(runtimeUUID.String())
	if err != nil {
		return err
	}

	m.l.WithField("container_runtime_uuid", runtimeUUID).Info("Free ports for ContainerRuntime.")
	return nil
}

func (m *MH) AvailableGpus(runtimeUUID constant.ContainerRuntimeUUID, machineID string, level constant.PriorityType) (gpuList []string, gpuCaps []string, err error) {
	gpuList, gpuCaps, err = m.gpuStock.GetReserveDetail(runtimeUUID.String(), machineID, level)
	if err != nil {
		return
	}

	m.l.WithField("runtime_uuid", runtimeUUID).Info("Get available gpus for ContainerRuntime: %+v", gpuList)
	return
}

func (m *MH) GetOrder(orderUUID string) (*bcm.Order, error) {
	order, err := m.bc.GetOrder(orderUUID)
	if err != nil {
		return nil, err
	}

	return order, nil
}

func (m *MH) GetConfigTheFirstTime(
	runtimeUUID constant.ContainerRuntimeUUID,
	orderUUID string,
) (config coreContainerModel.ContainerParamBuildRequest, err error) {
	l := logger.NewLogger("ContainerConfigBuilder_" + runtimeUUID.String())
	order, err := m.bc.GetOrder(orderUUID)
	if err != nil {
		return config, err
	}

	var (
		orderSettings                          = order.RuntimeEntity
		snapshot                               = order.MachineEntity
		privateImageUUID                       = orderSettings.PrivateImageUUID
		commonImageInfo                        = &imageModel.ImageCommonInfo{}
		preCmd                                 = []string{}
		onlyPreCmd                             = false
		gpuList                                []string
		gpuCaps                                []string
		containerParamUpdateBeforeStartRequest coreContainerModel.ContainerParamUpdateBeforeStartRequest
	)

	if orderSettings == nil || snapshot == nil {
		l.Error("order.RuntimeEntity or order.MachineEntity is nil! runtime_uuid: %s, order_uuid: %s, order: %+v", runtimeUUID, orderUUID, order)
		err = biz.ErrInternalError
		return
	}

	if orderSettings.ReproductionUUID != "" {
		privateImageUUID, err = m.privateImage.CodeWithGpuValidImageLocal(orderSettings.ReproductionUUID, orderSettings.ReproductionID)
		if err != nil {
			return
		}
	}

	if privateImageUUID != "" {
		commonImageInfo, err = m.GetImage(privateImageUUID)
		if err != nil {
			return
		}
	}

	switch order.RuntimeType {
	case constant.ContainerRuntimeOfInstance:
		// 尝试从快照中获取 region, 如果 (老数据) 没有就从机器中获取.
		var regionSign = snapshot.RegionSign
		if len(regionSign) == 0 {
			regionSign, err = m.GetMachineRegionSign(orderSettings.MachineID)
			if err != nil {
				l.Error("Get region from machine failed! runtime_uuid: %s, order_uuid: %s, machine: %s.", runtimeUUID, orderUUID, orderSettings.MachineID)
				return
			}
		}

		ports, err := m.RequirePorts(order.UID, runtimeUUID, regionSign, order.RuntimeEntity.ServicePortProtocol)
		if err != nil {
			return config, err
		}

		gpuList, gpuCaps, err = m.AvailableGpus(runtimeUUID, orderSettings.MachineID, constant.InstanceLevel)
		if err != nil {
			return config, err
		}

		latestInfo, err := m.GetLatestNetDiskMountAndProxy(order.UID, order.SubName, orderSettings.MachineID, order.RuntimeType)
		if err != nil {
			return config, err
		}

		containerParamUpdateBeforeStartRequest = coreContainerModel.ContainerParamUpdateBeforeStartRequest{
			UID:                  order.UID,
			ExistUserNetDisk:     latestInfo.ExistUserNetDisk,
			UserNetDiskQuotaOK:   latestInfo.UserNetDiskQuotaOK,
			ExistMachineNetDisk:  latestInfo.ExistMachineNetDisk,
			ExistMachineADFSDisk: latestInfo.ExistMachineADFSDisk,
			UserAutoFsNoBGJob:    latestInfo.UserAutoFsNoBGJob,
			FsSubPath:            latestInfo.FsSubPath,
			FsType:               latestInfo.FsType,
			ExistUserADFSDisk:    latestInfo.UserADFSAuth,
			ExistExclusiveNfs:    latestInfo.ExistExclusiveNfs,
			ProxyHosts:           latestInfo.ProxyHosts,
			ProxyHost:            latestInfo.ProxyHost,
			ProxyHostPublic:      latestInfo.ProxyHostPublic,
			ProxyPort:            latestInfo.ProxyPort,
			ProxyToken:           latestInfo.ProxyToken,
			Ports:                ports,
			GPUListBeforeStart:   gpuList,
			GPUCapsBeforeStart:   gpuCaps,
		}

	case constant.ContainerRuntimeOfDeployment:
		// 尝试从快照中获取 region, 如果 (老数据) 没有就从机器中获取.
		var regionSign = snapshot.RegionSign
		if len(regionSign) == 0 {
			regionSign, err = m.GetMachineRegionSign(orderSettings.MachineID)
			if err != nil {
				l.Error("Get region from machine failed! runtime_uuid: %s, order_uuid: %s, machine: %s.", runtimeUUID, orderUUID, orderSettings.MachineID)
				return
			}
		}

		gpuList, gpuCaps, err = m.AvailableGpus(runtimeUUID, orderSettings.MachineID, constant.InstanceLevel)
		// do not reserve
		if err != nil {
			return config, err
		}
		dc, err := m.deployment.DCGetByRuntimeUUID(runtimeUUID)
		if err != nil {
			return config, err
		}

		// 弹性部署希望使用正常的实例启动过程 + 自定义命令，不要设置onlyPreCmd = true
		onlyPreCmd = false
		preCmdStr := dc.SnapshotEntity.PreCMD()
		if preCmdStr != "" {
			preCmd = append(preCmd, preCmdStr)
		}

		ports, err := m.RequirePorts(order.UID, runtimeUUID, regionSign, order.RuntimeEntity.ServicePortProtocol)
		if err != nil {
			return config, err
		}

		latestInfo, err := m.GetLatestNetDiskMountAndProxy(order.UID, order.SubName, orderSettings.MachineID, order.RuntimeType)
		if err != nil {
			return config, err
		}

		containerParamUpdateBeforeStartRequest = coreContainerModel.ContainerParamUpdateBeforeStartRequest{
			UID:                  order.UID,
			ExistUserNetDisk:     latestInfo.ExistUserNetDisk,
			UserNetDiskQuotaOK:   latestInfo.UserNetDiskQuotaOK,
			ExistMachineNetDisk:  latestInfo.ExistMachineNetDisk,
			ExistMachineADFSDisk: latestInfo.ExistMachineADFSDisk,
			UserAutoFsNoBGJob:    latestInfo.UserAutoFsNoBGJob,
			FsSubPath:            latestInfo.FsSubPath,
			FsType:               latestInfo.FsType,
			ExistUserADFSDisk:    latestInfo.UserADFSAuth,
			ExistExclusiveNfs:    latestInfo.ExistExclusiveNfs,
			ProxyHosts:           latestInfo.ProxyHosts,
			ProxyHost:            latestInfo.ProxyHost,
			ProxyHostPublic:      latestInfo.ProxyHostPublic,
			ProxyPort:            latestInfo.ProxyPort,
			ProxyToken:           latestInfo.ProxyToken,
			GPUListBeforeStart:   gpuList,
			GPUCapsBeforeStart:   gpuCaps,
			Ports:                ports,
		}
	}

	// build config
	config = coreContainerModel.ContainerParamBuildRequest{
		// constant
		ContainerRuntimeUUID: runtimeUUID,
		MachineID:            orderSettings.MachineID,
		Image:                orderSettings.Image,
		PrivateImageUUID:     privateImageUUID,
		BucketName:           commonImageInfo.BucketName,
		ObjectName:           commonImageInfo.ObjectName,
		PreCmd:               preCmd,
		OnlyPreCmd:           onlyPreCmd,
		StopTimeout:          constant.ContainerDefaultStopTimeout,
		OrderUUID:            orderUUID,

		// snapshot
		MaxLocalDiskSizeInByte: snapshot.MaxInstanceDiskSize,
		SnapshotGpuAliasName:   snapshot.GpuType.Name,

		// pwd
		RootPassword: libs.RandStrForPwd(constant.ContainerRootPasswordLength),
		JupyterToken: libs.JupyterToken(runtimeUUID),

		// gpu
		ContainerParamGPURequest: coreContainerModel.ContainerParamGPURequest{
			ReqGpuAmount: orderSettings.ReqGPUAmount,
			GpuList:      gpuList, // TODO: 将所有 gpu 相关内容交给 start opt 之前生效
			GpuCaps:      gpuCaps,
		},

		// cpu & mem
		ContainerParamCPUAndMemRequest: constant.ContainerParamCPUAndMemRequest{
			CpuLimit:       float64(snapshot.CpuUsed),
			MemLimitInByte: snapshot.MemUsed,
		},

		// user & net disk & region & cpu mem
		ContainerParamUpdateBeforeStartRequest: containerParamUpdateBeforeStartRequest,

		KeepSrcUserServiceAddressAfterClone: orderSettings.KeepSrcUserServiceAddressAfterClone,
	}

	m.l.WithField("order_uuid", orderUUID).Info("Pre-Build container param config when creating by order.")
	return
}

func (m *MH) CloneConfigTheFirstTime(
	srcContainerParam coreContainerModel.ContainerParam,
	runtimeUUID constant.ContainerRuntimeUUID,
	orderUUID string,
) (config coreContainerModel.ContainerParamBuildRequest, err error) {
	l := logger.NewLogger("ContainerCloneConfigBuilder_" + runtimeUUID.String())
	order, err := m.bc.GetOrder(orderUUID)
	if err != nil {
		return config, err
	}

	var freeDisk int64
	if order.MachineEntity == nil {
		freeDisk = srcContainerParam.MaxLocalDiskSizeInByte
	} else {
		freeDisk = order.MachineEntity.MaxInstanceDiskSize
	}

	orderSettings := order.RuntimeEntity
	snapshot := order.MachineEntity
	if orderSettings == nil || snapshot == nil {
		l.Error("order.RuntimeEntity or order.MachineEntity is nil! runtime_uuid: %s, order_uuid: %s, order: %+v", runtimeUUID, orderUUID, order)
		err = biz.ErrInternalError
		return
	}

	// 尝试从快照中获取 region, 如果 (老数据) 没有就从机器中获取.
	var regionSign = snapshot.RegionSign
	if len(regionSign) == 0 {
		regionSign, err = m.GetMachineRegionSign(orderSettings.MachineID)
		if err != nil {
			l.Error("Get region from machine failed! runtime_uuid: %s, order_uuid: %s, machine: %s.", runtimeUUID, orderUUID, orderSettings.MachineID)
			return
		}
	}

	ports, err := m.RequirePorts(order.UID, runtimeUUID, regionSign, order.RuntimeEntity.ServicePortProtocol)
	if err != nil {
		return config, err
	}

	gpuList, gpuCaps, err := m.AvailableGpus(runtimeUUID, orderSettings.MachineID, constant.InstanceLevel)
	if err != nil {
		return config, err
	}

	latestInfo, err := m.GetLatestNetDiskMountAndProxy(order.UID, order.SubName, orderSettings.MachineID, order.RuntimeType)
	if err != nil {
		return config, err
	}

	config = coreContainerModel.ContainerParamBuildRequest{
		// constant
		ContainerRuntimeUUID: runtimeUUID,
		MachineID:            orderSettings.MachineID,
		Image:                orderSettings.Image,
		PreCmd:               nil,
		OnlyPreCmd:           false,
		StopTimeout:          constant.ContainerDefaultStopTimeout,
		OrderUUID:            orderUUID,

		// snapshot
		MaxLocalDiskSizeInByte: freeDisk,
		SnapshotGpuAliasName:   snapshot.GpuType.Name,

		// pwd
		RootPassword: srcContainerParam.RootPassword,
		JupyterToken: srcContainerParam.JupyterToken,

		// gpu
		ContainerParamGPURequest: coreContainerModel.ContainerParamGPURequest{
			ReqGpuAmount: orderSettings.ReqGPUAmount,
			GpuList:      gpuList, // TODO: 将所有 gpu 相关内容交给 start opt 之前生效
			GpuCaps:      gpuCaps,
		},

		// cpu & mem
		ContainerParamCPUAndMemRequest: constant.ContainerParamCPUAndMemRequest{
			CpuLimit:       float64(snapshot.CpuUsed),
			MemLimitInByte: snapshot.MemUsed,
		},

		// user & net disk & region & cpu mem
		ContainerParamUpdateBeforeStartRequest: coreContainerModel.ContainerParamUpdateBeforeStartRequest{
			UID:                  order.UID,
			ExistUserNetDisk:     latestInfo.ExistUserNetDisk,
			UserNetDiskQuotaOK:   latestInfo.UserNetDiskQuotaOK,
			ExistMachineNetDisk:  latestInfo.ExistMachineNetDisk,
			ExistMachineADFSDisk: latestInfo.ExistMachineADFSDisk,
			UserAutoFsNoBGJob:    latestInfo.UserAutoFsNoBGJob,
			FsSubPath:            latestInfo.FsSubPath,
			FsType:               latestInfo.FsType,
			ExistUserADFSDisk:    latestInfo.UserADFSAuth,
			ExistExclusiveNfs:    latestInfo.ExistExclusiveNfs,
			ProxyHosts:           latestInfo.ProxyHosts,
			ProxyHost:            latestInfo.ProxyHost,
			ProxyHostPublic:      latestInfo.ProxyHostPublic,
			ProxyPort:            latestInfo.ProxyPort,
			ProxyToken:           latestInfo.ProxyToken,
			Ports:                ports,
			GPUListBeforeStart:   gpuList,
			GPUCapsBeforeStart:   gpuCaps,
		},

		KeepSrcUserServiceAddressAfterClone: orderSettings.KeepSrcUserServiceAddressAfterClone,
	}

	return
}

func (m *MH) GetUserNetDiskMountPath(uid int, regionSign constant.RegionSignType) (path string, exist, quotaOK bool, err error) {
	return m.region.GetUserNetDiskMountForInstance(uid, regionSign)
}

// GetLatestNetDiskMountAndProxy 获取网盘挂载信息和 container frpc proxy
func (m *MH) GetLatestNetDiskMountAndProxy(uid int, subName, machineID string, runtimeType constant.ContainerRuntimeType) (info constant.LatestUserMachineRegionInfo, err error) {
	l := m.l.WithFields(map[string]interface{}{"uid": uid, "sub_name": subName, "machine": machineID})

	user, err := m.user.FindByUserId(uid)
	if err != nil {
		return
	}
	machine, err := m.machine.Get(machineID)
	if err != nil {
		return
	}

	// nas
	info.ExistMachineNetDisk = machine.MountNetDisk
	if user.MountNetDiskAuthority && machine.MountNetDisk {
		_, info.ExistUserNetDisk, info.UserNetDiskQuotaOK, _ = m.GetUserNetDiskMountPath(uid, machine.RegionSign)
	}

	// exclusive nfs
	nfs, err := m.region.ExclusiveNfsGetForProduct(uid, machine.RegionSign, runtimeType)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			l.ErrorE(err, "ExclusiveNfsGetForProduct failed")
		}
	}

	if nfs != nil {
		info.ExistExclusiveNfs = true
		info.ExclusiveNfsAddr = nfs.NfsAddr
	} else {
		// adfs文件存储配置
		info.ExistMachineADFSDisk = machine.MountADFSDisk
		canMount, _ := m.region.FileStorageMount(uid, machine.RegionSign)
		subUserCanMount := true
		subPath := ""
		if subName != "" {
			subUser, errSubUser := m.user.SubUserGet(subName)
			if errSubUser != nil {
				l.ErrorE(err, "get sub user failed")
				subUserCanMount = false
			} else {
				if subUser.Roles.FileStorage == constant.SubUserNoPermission {
					subUserCanMount = false
				}
				subPath = subUser.Roles.FileStorageSubPath
			}
		}
		if canMount && subUserCanMount {
			fs, err := m.region.GetFileStorageByUids([]int{uid}, machine.RegionSign)
			if err != nil {
				l.ErrorE(err, "get file storage failed")
				return info, err
			}
			if len(fs) != 0 && fs[0].Status == constant.FileStorageCreated {
				info.UserADFSAuth = true
				info.UserADFSQuotaSize = fmt.Sprintf("%dGiB", fs[0].QuotaTotal/1024/1024/1024)
				info.UserADFSQuotaInode = cast.ToInt(fs[0].QuotaInodeTotal)
				info.UserADFSConcurrentLimit = fs[0].ConcurrentLimit
				info.UserFSMaxUploads = fs[0].MaxUploads
				info.UserAutoFSBufferSize = fs[0].BufferSize
				info.UserAutoFsFSCacheSize = fs[0].CacheSize
				info.UserAutoFsNoBGJob = fs[0].NoBGJob
				info.FsType = fs[0].FsType
				info.FsSubPath = subPath
				info.FsConfigVersion = fs[0].FsConfigVersion
			}
		}
	}

	info.ProxyHosts, info.ProxyHost, info.ProxyHostPublic, info.ProxyPort, _, info.ProxyToken, _, err = m.region.DistributeFrpcProxy(machine.RegionSign)
	return
}

func (m *MH) UpdatePrivateImage(progressInfo *agent_constant.UploadOssInfo) error {
	err := m.privateImage.UpdateImageUploadInfo(progressInfo)
	if err != nil {
		m.l.WithField("image_uuid", progressInfo.ImageUUID).Error("update instance image info failed.")
		return err
	}
	return nil
}

func (m *MH) GetImage(imageUUID string) (commonImage *imageModel.ImageCommonInfo, err error) {
	if !libs.IsCommunityImageUUID(imageUUID) {
		var pi *imageModel.PrivateImage
		pi, err = m.getPrivateImage(imageUUID)
		if err != nil {
			return
		}
		commonImage = pi.ToImageCommonInfo()
	} else {
		var pic *imageModel.CommunityImage
		pic, err = m.getPrivateImageCommunity(imageUUID)
		if err != nil {
			return
		}
		commonImage = pic.ToImageCommonInfo()
	}
	return
}

func (m *MH) getPrivateImage(imageUUID string) (image *imageModel.PrivateImage, err error) {
	image, err = m.privateImage.GetImageByUUIDWithStorageInfo(imageUUID)
	if err != nil {
		m.l.WithField("err", err).WithField("image_uuid", imageUUID).Error("get private image info failed.")
		return
	}
	return
}
func (m *MH) getPrivateImageCommunity(imageUUID string) (image *imageModel.CommunityImage, err error) {
	image, err = m.privateImage.CommunityImageGetUnScoped(imageUUID, true)
	if err != nil {
		m.l.WithField("err", err).WithField("image_uuid", imageUUID).Error("get private image info failed.")
		return
	}
	return
}

func (m *MH) CancelUploadPrivateImage(cancelUploadImageInfo *agent_constant.CancelUploadImageInfo) error {
	err := m.privateImage.ConfirmCancelUploadImageAndDeleteRecord(cancelUploadImageInfo)
	if err != nil {
		m.l.WithField("image_uuid", cancelUploadImageInfo.ImageUUID).Error("cancel upload private image info failed.")
		return err
	}
	return nil
}

func (m *MH) GetMachineRegionSign(machineID string) (rs constant.RegionSignType, err error) {
	rs, err = m.machine.GetMachineRegion(machineID)
	if err != nil {
		return
	}

	if len(rs) == 0 {
		return rs, biz.ErrInternalError
	}
	return
}

func (m *MH) GetUserPublicKey(uid int) (pubKey string, err error) {
	return m.user.GeneratePubKeyForContainer(uid)
}

func (m *MH) GetAutopanelToken(uid int) (token string, err error) {

	//return m.user.GetAutopanelToken(uid)
	// todo: fix it
	return m.user.GetAutopanelToken(uid)

}

func (m *MH) GetUser(uid int) (user *userModel.User, err error) {
	return m.user.FindByUserId(uid)
}

func (m *MH) GetMachine(machineID string) (machine *model.Machine, err error) {
	return m.machine.Get(machineID)
}

func (m *MH) GetMachineStorageOssCredentials(machineID string) (credentials agent_constant.MinioCredentials, err error) {
	return m.machine.GetMachineStorageOssCredentials(machineID)
}

func (m *MH) GetMachineImageStorageOssCredentials(machineID, imageUUID string) (bucket agent_constant.MinioBucketInfo, credentials agent_constant.MinioCredentials, err error) {
	commonImage, err := m.GetImage(imageUUID)
	if err != nil {
		m.l.WithField("occu", "getMachineImageStorageOssCredentials").WarnE(err, "get private image failed")
		return
	}
	bucket = agent_constant.MinioBucketInfo{
		BucketName: commonImage.BucketName,
		ObjectName: commonImage.ObjectName,
		ObjectSize: commonImage.ImageSize,
	}
	credentials = agent_constant.MinioCredentials{
		Endpoint:        commonImage.StorageOSS.PublicAddr,
		AccessKeyID:     commonImage.StorageOSS.AccessKey,
		SecretAccessKey: commonImage.StorageOSS.SecretKey,
	}

	defer func() {
		m.l.WithFields(logger.Fields{
			"machine id": machineID,
			"image uuid": imageUUID,
			"oss addr":   credentials.Endpoint,
		}).Trace("finial addr in this case")
	}()

	machine, getMachineErr := m.GetMachine(machineID)
	if getMachineErr != nil {
		m.l.WithField("machine id", machine).ErrorE(err, "get machine failed")
		return
	}
	if !machine.PrivateNetAccess {
		return
	}

	var storageInfo coreRegionModel.RegionStorageOSSDetailList
	_, storageInfo, err = m.region.GetRegionDetailWithStorageInfo(machine.RegionSign)
	if err != nil {
		m.l.WithField("machine id", machine).ErrorE(err, "GetRegionDetailWithStorageInfo failed")
		return
	}

	for _, v := range storageInfo {
		if v.StorageOSSSign == commonImage.StorageOssSign {
			if v.PrivateNetAccess {
				credentials.Endpoint = commonImage.StorageOSS.PrivateAddr
				m.l.WithFields(logger.Fields{
					"machine id": machineID,
					"image uuid": imageUUID,
					"oss addr":   credentials.Endpoint,
				}).Trace("use private addr in this case")
			}
		}
	}

	return
}

func (m *MH) GetSSLDomainPort(rs constant.RegionSignType) (port string, err error) {
	var region *regionModel.Region
	err = db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: &regionModel.Region{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"sign": rs}},
	}, &region).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = biz.ErrRegionSionNotFound
			return
		}
		m.l.WithError(err).WithField("sign", rs).Error("get region by sign failed")
		err = biz.ErrDatabaseError
		return
	}
	return region.SSLDomainPortForVisit, nil
}

func (m *MH) DCGet(uuid string) (*deploymentModel.DeploymentContainer, error) {
	return m.deployment.DCGet(uuid)
}

func (m *MH) ContainerMonitorGet(uid int, runtimeType constant.ContainerRuntimeType) string {
	um, err := m.user.GetUserMemberInfoByUid(uid)
	if err != nil {
		m.l.WithField("uid", uid).ErrorE(err, "GetUserMember failed")
		return ""
	}

	if !um.IsEnterprise() {
		return ""
	}

	ucms, err := m.user.ContainerMonitorGet(uid)
	if err != nil {
		return ""
	}

	if ucms == nil {
		return ""
	}

	if !ucms.StatusEnable {
		return ""
	}

	var setting *constant.ContainerMonitorSetting

	switch ucms.MonitorScope {
	case constant.ProductTypeAllFake:
		setting = ucms.ToMonitorSetting()
	case constant.ProductTypeInstance:
		if runtimeType == constant.ContainerRuntimeOfInstance {
			setting = ucms.ToMonitorSetting()
		}
	case constant.ProductTypeDeployment:
		if runtimeType == constant.ContainerRuntimeOfDeployment {
			setting = ucms.ToMonitorSetting()
		}
	}

	if setting == nil {
		return ""
	}

	return string(libs.AutoDLAesEncode(setting.Marshal()))
}
