package service

import (
	"server/pkg-core/api/coreapi"
	"server/pkg/constant"
	"server/pkg/logger"
	"server/pkg/module_definition"
	"server/plugin/queue"
	"server/plugin/redis_plugin"
)

/**
 * 实现 ContainerRuntimeInterface 接口
 */

const secNameContainerRuntime = "container_runtime"

type CR struct {
	//crud CRUDHandler
	mod ModuleHandler
	t   redis_plugin.SyncTimer
	cu  *redis_plugin.ContainerUsagePlugin
	//
	q *queue.Q
	//mutex *redis_plugin.MutexRedis
	l *logger.Logger
	//
	handlers map[constant.OptType]handler
	coreApi  *coreapi.Api
}

func NewContainerRuntime(
	machineInterface module_definition.MachineInference,
	portInterface module_definition.PortInterface,
	gpuStockInterface module_definition.GpuStockInterface,
	gpuType module_definition.GpuTypeInference,
	bcInterface module_definition.BCInterface,
	cu *redis_plugin.ContainerUsagePlugin,
	deploymentInterface module_definition.DeploymentInterface,
	PrivateImageInterface module_definition.PrivateImageInterface,
	region module_definition.RegionInterface,
	user module_definition.UserInterface,
	dds module_definition.DataDiskStockInterface,
	timer redis_plugin.SyncTimer,
	q *queue.Q,
	mutex *redis_plugin.MutexRedis,
	coreApi *coreapi.Api,
) *CR {
	c := &CR{
		mod:     NewModuleHandler(machineInterface, portInterface, gpuStockInterface, gpuType, bcInterface, deploymentInterface, PrivateImageInterface, region, user, dds, timer, q),
		t:       timer,
		q:       q,
		l:       logger.NewLogger(secNameContainerRuntime),
		cu:      cu,
		coreApi: coreApi,
	}
	return c
}
