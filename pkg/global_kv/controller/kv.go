package controller

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"server/pkg-agent/file_transfer"
)

type SetKeyPayload struct {
	Data string `json:"data"`
}

// Set key
// @Summary Set key
// @Description author: ningfd
// @Tags kv
// @Accept  json
// @Produce  json
// @Param account body SetKeyPayload true "Set key"
// @Success 200 {string} string
// @Router /api/v1/internal/kv/key/:key [post]
func (ctrl *KVController) Set(c *gin.Context) {
	var req SetKeyPayload
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithField("err", err).Warn("KVController.Create invalid request params.")
		c.String(http.StatusBadRequest, err.Error())
		return
	}

	key := c.Param("key")
	if len(key) == 0 {
		c.String(http.StatusBadRequest, "key must long than 0")
		return
	}

	err := ctrl.kvPlugin.Set(key, req.Data)
	if err != nil {
		c.String(http.StatusInternalServerError, err.Error())
		return
	}
	c.Status(http.StatusOK)
}

// Get key
// @Summary Get key
// @Description author: ningfd
// @Tags kv
// @Accept  json
// @Produce  json
// @Response 1 {int} int "string类型 用户当日数据盘迁移次数 Ft.ul_userUUID_thisDay"
// @Response 2 {object} file_transfer.TaskStatus "string类型 数据迁移状态 Ft.status_srcInstanceUUID_dstInstanceUUID"
// @Router /api/v1/internal/kv/key/:key [get]
func (ctrl *KVController) Get(c *gin.Context) {
	key := c.Param("key")
	if len(key) == 0 {
		c.String(http.StatusBadRequest, "key must long than 0")
		return
	}

	res, err := ctrl.kvPlugin.Get(key)
	if err != nil {
		c.String(http.StatusInternalServerError, err.Error())
		return
	}

	c.String(http.StatusOK, res)
}

func (ctrl *FTController) FTTaskStatusUpdate(c *gin.Context) {
	var req file_transfer.TaskStatus
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithField("err", err).Warn("KVController.FTTaskStatusUpdate invalid request params.")
		c.String(http.StatusBadRequest, err.Error())
		return
	}

	ctrl.log.Info("FTTaskStatusUpdate get new msg: %+v", req)

	err := ctrl.ft.FTTaskStatusUpdate(&req)
	if err != nil {
		c.String(http.StatusBadRequest, err.Error())
		return
	}
	c.Status(http.StatusOK)
}
