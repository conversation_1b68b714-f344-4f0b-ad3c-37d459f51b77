package bc_server

import (
	"server/pkg-core/api/coreapi"
	"server/pkg/logger"
	"server/pkg/module_definition"
	"server/plugin/kv_plugin"
	"server/plugin/queue"
	redis "server/plugin/redis_plugin"
)

/*
	FIXME: 有这么一层封装是因为：为了方便后期计费模块单独部署，在BC中，要避免调用其他模块，所以BCService中都是进行的很原子化的操作
	FIXME: 这样就导致很多具体的逻辑需要放在controller中做，而controller又不能被外部调用，导致了很多代码冗余
	FIXME: 所以 BCServer 相当于一个连接Server与BC的中间层的概念。在后面的代码中，外部应该调用BCServer
*/

const ModuleName = "BCServer"

type BCServer struct {
	q     *queue.Q
	log   *logger.Logger
	mutex *redis.MutexRedis

	bc           module_definition.BCInterface
	user         module_definition.UserInterface
	machine      module_definition.MachineInference
	gpuType      module_definition.GpuTypeInference
	instance     module_definition.InstanceInterface
	gpuStock     module_definition.GpuStockInterface
	region       module_definition.RegionInterface
	privateImage module_definition.PrivateImageInterface
	dds          module_definition.DataDiskStockInterface
	invoice      module_definition.InvoiceInterface
	container    module_definition.ContainerRuntimeInterface
	deployment   module_definition.DeploymentInterface
	coreApi      *coreapi.Api
	kv           *kv_plugin.KVPlugin
}

func NewBCServerProvider(
	self *BCServer,
	q *queue.Q,
	mutex *redis.MutexRedis,

	bc module_definition.BCInterface,
	user module_definition.UserInterface,
	machine module_definition.MachineInference,
	gpuType module_definition.GpuTypeInference,
	instance module_definition.InstanceInterface,
	gpuStock module_definition.GpuStockInterface,
	region module_definition.RegionInterface,
	privateImage module_definition.PrivateImageInterface,
	dds module_definition.DataDiskStockInterface,
	invoice module_definition.InvoiceInterface,
	container module_definition.ContainerRuntimeInterface,
	deployment module_definition.DeploymentInterface,
	coreApi *coreapi.Api,
	kv *kv_plugin.KVPlugin,
) {
	self.q = q.New()
	self.log = logger.NewLogger(ModuleName)
	self.mutex = mutex
	self.bc = bc
	self.user = user
	self.machine = machine
	self.gpuType = gpuType
	self.instance = instance
	self.gpuStock = gpuStock
	self.region = region
	self.privateImage = privateImage
	self.dds = dds
	self.invoice = invoice
	self.container = container
	self.coreApi = coreApi
	self.kv = kv
	self.deployment = deployment
	return
}
