package bc_server

import (
	"database/sql"
	"math"
	bcm "server/pkg/billing_center/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	ddsModel "server/pkg/data_disk_stock/model"
	"server/pkg/db_helper"
	im "server/pkg/instance/model"
	"server/pkg/libs"
	mm "server/pkg/machine/model"
	regionModel "server/pkg/region/model"
	um "server/pkg/user/model"
	"server/plugin/kv_plugin"
	"server/plugin/queue_interface"
	redis "server/plugin/redis_plugin"
	"strconv"
	"time"

	goset "github.com/deckarep/golang-set/v2"
	"gorm.io/gorm"
)

func (bcs *BCServer) GetAfterHook(order *bcm.Order) (afterHook queue_interface.ElementPayloadContent, afterFunc func(tx *gorm.DB) error) {
	return bcs.getAfterHook(order)
}

// order
// 获取实例到期时间
func (bcs *BCServer) getInstanceExpireTime(instance *im.Instance, chargeType constant.ChargeType, duration int) (beginAt, expiredAt *time.Time, err error) {
	var lastExpiredAt *time.Time
	now := time.Now()
	if instance == nil || !instance.ExpiredAt.Valid {
		lastExpiredAt = &now
		beginAt = &now
	} else {
		if time.Now().After(instance.ExpiredAt.Time) {
			lastExpiredAt = &now
			beginAt = &now
		} else {
			lastExpiredAt = &instance.ExpiredAt.Time
			beginAt = &instance.ExpiredAt.Time
		}
	}

	expiredAt, err = libs.GetInstanceExpireTime(lastExpiredAt, chargeType, duration)
	if err != nil {
		bcs.log.WithError(err).Warn("renewal instance: get expire time failed")
	}
	return
}

// [order] 创建订单前置校验
func (bcs *BCServer) beforeCreateOrderCheck(params *bcm.CreateInstanceRequest, cloneSrcInstanceUUID string) (err error) {
	var (
		ok      bool
		machine *mm.Machine
	)

	machine, err = bcs.getMachine(params.PriceInfo.MachineID)
	if err != nil {
		bcs.log.WithField("err", err).Error("get machine failed")
		return err
	}
	if err = machine.CanRunInstance(); err != nil {
		return
	}
	if machine.Online != constant.Online {
		return businesserror.ErrMachineUnhealthy
	}

	region, err := bcs.region.GetRegionDetail(machine.RegionSign)
	if err != nil {
		return err
	}
	if !region.UsedFor.CanInstance() {
		return businesserror.ErrRegionCannotCreateInstance
	}

	if params.CloneSrcInstanceUUID == "" {
		if params.InstanceInfo.ReproductionUUID != "" {
			// 社区复现镜像校验
			_, err = bcs.privateImage.CodeWithGpuValidImage(params.InstanceInfo.UID, params.InstanceInfo.ReproductionUUID, params.InstanceInfo.ReproductionID)
			if err != nil {
				return
			}
		} else if params.InstanceInfo.PrivateImageUUID != "" {
			_, err = bcs.privateImage.GetImageByUUID(params.InstanceInfo.PrivateImageUUID)
			if err != nil {
				bcs.log.WithField("err", err).Error("image not found")
				return businesserror.ErrPrivateImageRecordNotFound
			}
		}
	}

	err = bcs.renewalCheck(params.InstanceInfo.UID, params.PriceInfo.MachineID, machine, params.PriceInfo.ChargeType)
	if err != nil {
		return
	}

	// 机器最大实例数量
	ok, err = bcs.machine.CheckMaxInstanceNum(params.PriceInfo.MachineID)
	if err != nil {
		bcs.log.WithError(err).Warn("check machine max instance num info failed")
		return err
	}
	if !ok {
		return businesserror.ErrMachineMaxInstanceNumErr
	}

	// 检查数据盘扩容容量
	if params.PriceInfo.ExpandDataDisk != 0 {
		if params.PriceInfo.ExpandDataDisk > machine.SurplusMaxDataDiskExpandSize {
			return businesserror.ErrDataDiskMaxExpandSizeLimit
		}
	}

	user, err := bcs.user.FindByUserId(params.InstanceInfo.UID)
	if err != nil {
		bcs.log.WithError(err).WithField("uid", params.InstanceInfo.UID).Error("get user failed")
		return
	}

	num, err := bcs.instance.CountUserExistInstances(params.InstanceInfo.UID)
	if err != nil {
		bcs.log.Warn("get user existed instance failed")
		return err
	}

	totalLimit, _ := user.GetMaxInstanceNum("")
	if num >= totalLimit {
		return businesserror.ErrInstanceMaxNum.New().Format(totalLimit)
	}

	return
}

// renewalCheck 由beforeCreateOrderCheck 拆出来的，实例续费不需要校验机器最大实例数量，续费的实例本身就占着机器实例库存
func (bcs *BCServer) renewalCheck(uid int, machineID string, machine *mm.Machine, chargeType constant.ChargeType) (err error) {
	var (
		list []*bcm.Order
	)

	// 未付款订单不能超过3个
	_, list, err = bcs.bc.GetOrderList(&bcm.OrderGetListParams{UID: uid, Status: constant.OrderStatusUnpaid}, nil)
	if err != nil {
		bcs.log.WithError(err).Warn("get order list failed")
		return
	}
	if len(list) >= 3 {
		return businesserror.ErrOrderUnpaidMoreThanThree
	}

	// 机器健康状态
	if machine == nil {
		machine, err = bcs.getMachine(machineID)
		if err != nil {
			return
		}
	}
	if err = machine.CanRunInstance(); err != nil {
		return
	}

	// 目标机器是否有目标计费方式
	err = bcs.checkMachineChargeType(chargeType, machineID)
	return
}

// [order] 补全机器快照
func (bcs *BCServer) getMachineSnapshot(machineID string, machine *mm.Machine, gpuNum int64) (ss *bcm.MachineSnapShot, err error) {
	if machine == nil {
		machine, err = bcs.machine.Get(machineID)
		if err != nil {
			bcs.log.WithField("machineID", machineID).Warn("get machine failed")
			return
		}
	}

	if machine.NewMachineSkuContent == nil {
		err = businesserror.ErrInternalError
		return
	}

	gpuType := new(mm.GPUType)
	gpuType, err = bcs.gpuType.GetGpu(machine.GPUTypeID)
	if err != nil {
		bcs.log.WithField("gpuTypeID", machine.GPUTypeID).Warn("get gpuType failed")
		return
	}

	return &bcm.MachineSnapShot{
		BaseMachineInfo:     *machine.MachineBaseContent,
		CpuUsed:             machine.CpuPerGpu * gpuNum,
		MemUsed:             machine.MemPerGpu * gpuNum,
		MaxInstanceDiskSize: machine.MaxInstanceDiskSize,
		GpuTypeID:           machine.GPUTypeID,
		GpuType:             bcm.MachineGpuType{Name: gpuType.GpuName, Memory: gpuType.GpuMemory},
		MachineSku:          machine.NewMachineSkuContent,
		RegionSign:          machine.RegionSign,
		RegionName:          machine.RegionName,
	}, nil
}

type getPriceParams struct {
	uid           int
	priceInfo     *bcm.PriceInfo
	levelName     constant.MemberLevelName
	productType   constant.ProductType
	specialCoupon map[int]bool
}

func (bcs *BCServer) GetPrice(params *bcm.GetPriceParams) (err error) {
	return bcs.getPrice(&getPriceParams{
		uid:         params.Uid,
		priceInfo:   params.PriceInfo,
		levelName:   params.LevelName,
		productType: params.ProductType,
	})
}

// [order] 获取价格信息
func (bcs *BCServer) getPrice(params *getPriceParams) (err error) {
	if params.priceInfo == nil {
		return businesserror.ErrInternalError.AppendStr("get price but priceInfo is nil")
	}

	if params.specialCoupon == nil {
		params.specialCoupon = map[int]bool{}
	}

	var (
		memberDiscount              int64           // 机器计费方式下的会员折扣
		durationDiscount            int64           // 时长折扣
		dataDiskDiscountRate        int64           // 数据盘折扣
		uc                          *bcm.UserCoupon // 优惠券
		dailyPrice                  int64           // 按日价格
		maxDeductPrice              int64           // 最多折扣金额
		maxDeductLimit              bool
		machine                     *mm.Machine
		paygExceptCouponMemberPrice int64
	)

	// machine
	machine, err = bcs.getMachine(params.priceInfo.MachineID)
	if err != nil {
		return err
	}

	// 整理计费方式对应的duration，以复用同一段逻辑
	if params.priceInfo.ChargeType.IsPayg() {
		params.priceInfo.Duration = 1
	}

	// 实例原价, 各个折扣信息
	for _, skuInfo := range machine.NewMachineSkuContent {
		// 判断有没有包日计费方式
		if skuInfo.Type == constant.ChargeTypeDaily {
			dailyPrice = skuInfo.CurrentPrice
		}

		// 找到实例要使用的计费方式
		if skuInfo.Type == params.priceInfo.ChargeType {
			// 要使用的计费方式的原价
			params.priceInfo.OriginPrice = skuInfo.CurrentPrice * int64(params.priceInfo.Num) * int64(params.priceInfo.Duration)

			// 找到会员等级对应的折扣
			for i := range skuInfo.LevelConfig {
				if skuInfo.LevelConfig[i].LevelName == params.levelName {
					memberDiscount = skuInfo.LevelConfig[i].Discount

					if params.priceInfo.ChargeType.IsPayg() {
						paygExceptCouponMemberPrice = skuInfo.LevelConfig[i].DiscountedPrice * int64(params.priceInfo.Num) // 按量付费没有优惠券的会员折扣价
					}
					break
				}
			}

			// 时长折扣
			if params.productType != constant.ProductTypeDeploymentDurationPkg {
				durationDiscount = int64(skuInfo.GetDurationDiscount(params.priceInfo.Duration))
			}
		}
	}
	if params.priceInfo.OriginPrice == 0 {
		err = businesserror.ErrMachineRecharging
		return
	}

	// 优惠券折扣
	var exceptCouponOriginPrice, exceptCouponTotalReduction int64
	var couponValidEnd sql.NullTime
	var afterCouponDiscountPrice = params.priceInfo.OriginPrice
	if len(params.priceInfo.CouponIDList) != 0 {
		uc, err = bcs.bc.UserCouponGetForFrontEnd(params.priceInfo.CouponIDList[0])
		if err != nil {
			bcs.log.WithError(err).Trace("get user coupon failed")
			return
		}

		err = bcs.ucCheck(params.uid, uc, machine, !params.specialCoupon[uc.ID], params.priceInfo)
		if err != nil {
			return
		}

		// 计算最高抵扣
		if uc.CouponSnapshotEntity.UseConditionEntity != nil {
			maxDeductPrice, maxDeductLimit = uc.CouponSnapshotEntity.UseConditionEntity.CalculateMaxDeductionPrice(dailyPrice)
		}

		// 计算折后价格
		afterCouponDiscountPrice = uc.CouponSnapshotEntity.CalculateThePrice(params.priceInfo.OriginPrice, maxDeductPrice, maxDeductLimit)

		// 添加折扣信息
		params.priceInfo.DiscountList = append(params.priceInfo.DiscountList, bcm.DiscountInfo{
			Type:           constant.CouponDiscount,
			OriginPrice:    params.priceInfo.OriginPrice,
			TotalReduction: params.priceInfo.OriginPrice - afterCouponDiscountPrice,
			CouponDiscount: &bcm.CouponDiscountInfo{
				CouponID:         uc.CouponID,
				CouponType:       uc.CouponSnapshotEntity.Type,
				Discount:         uc.CouponSnapshotEntity.Discount,
				CashBack:         uc.CouponSnapshotEntity.CashBack,
				UseConditionInfo: *uc.CouponSnapshotEntity.UseConditionEntity,
			},
		})

		// 这三个值,只有在使用了优惠券后的会员折扣信息才需要
		exceptCouponOriginPrice = params.priceInfo.OriginPrice
		exceptCouponTotalReduction = params.priceInfo.OriginPrice - paygExceptCouponMemberPrice
		couponValidEnd = sql.NullTime{
			Time:  uc.CouponSnapshotEntity.ValidEnd,
			Valid: true,
		}
	}

	// 会员折扣
	var afterMemberDiscountPrice = afterCouponDiscountPrice
	//if params.priceInfo.ChargeType.IsPayg() {
	// 按量付费，暂时不走优惠券折扣
	//params.priceInfo.DiscountList = append(params.priceInfo.DiscountList, bcm.DiscountInfo{
	//	ProductType:    constant.ProductTypeInstance,
	//	Type:           constant.MemberDiscount,
	//	OriginPrice:    params.priceInfo.OriginPrice,
	//	TotalReduction: params.priceInfo.OriginPrice - params.priceInfo.PaygPrice,
	//	DiscountRate:   memberDiscount,
	//})
	//} else {
	// 预付费

	afterMemberDiscountPrice = libs.DiscountPrice(afterCouponDiscountPrice, memberDiscount, false)
	params.priceInfo.DiscountList = append(params.priceInfo.DiscountList, bcm.DiscountInfo{
		ProductType:                constant.ProductTypeInstance,
		Type:                       constant.MemberDiscount,
		OriginPrice:                afterCouponDiscountPrice,
		TotalReduction:             afterCouponDiscountPrice - afterMemberDiscountPrice,
		DiscountRate:               memberDiscount,
		ExceptCouponOriginPrice:    exceptCouponOriginPrice,
		ExceptCouponTotalReduction: exceptCouponTotalReduction,
		CouponValidEnd:             couponValidEnd,
	})
	//}

	if params.priceInfo.ChargeType.IsPayg() {
		params.priceInfo.PaygPrice = afterMemberDiscountPrice
	}

	// 时长折扣
	var afterDurationDiscountPrice = afterMemberDiscountPrice
	if durationDiscount != 100 && durationDiscount != 0 {
		afterDurationDiscountPrice = libs.DiscountPrice(afterMemberDiscountPrice, durationDiscount, false)
		params.priceInfo.DiscountList = append(params.priceInfo.DiscountList, bcm.DiscountInfo{
			ProductType:    constant.ProductTypeInstance,
			Type:           constant.DurationDiscount,
			OriginPrice:    afterMemberDiscountPrice,
			TotalReduction: afterMemberDiscountPrice - afterDurationDiscountPrice,
			DiscountRate:   durationDiscount,
		})
	}

	if !params.priceInfo.ChargeType.IsPayg() {
		params.priceInfo.DealPrice = afterDurationDiscountPrice
	}

	dataDiskDiscountRate, err = machine.GetDiscountRate(params.priceInfo.ChargeType, params.levelName)
	if err != nil {
		bcs.log.WithField("params.priceInfo", params.priceInfo).ErrorE(err, "GetDiscountRate failed")
		return
	}

	bcs.getPriceOfDataDisk(params.priceInfo, machine, dataDiskDiscountRate, false)

	// ddp, 折合按量计费单价。
	if params.productType == constant.ProductTypeDeploymentDurationPkg {
		params.priceInfo.DDPPaygPrice = libs.AssetRound(params.priceInfo.DealPrice/(params.priceInfo.ChargeType.ToHour()*int64(params.priceInfo.Duration)), true)
	}

	return
}

// getPriceOfDataDisk 单独获取数据盘价格。
func (bcs *BCServer) getPriceOfDataDisk(priceInfo *bcm.PriceInfo, machine *mm.Machine, memberDiscount int64, isPrepayToPayg bool) {
	if priceInfo.DataDiskInfo == nil {
		priceInfo.DataDiskInfo = &bcm.DataDiskInfo{}
	}

	var afterMemberDiscountPrise int64
	durationDiscount := 0
	durationDiscountValid := false
	expandDataDiskGB := libs.DataDiskToGB(priceInfo.ExpandDataDisk) // todo:zt 防止除完以后大小不相等

	if priceInfo.PrepayExpandDataDisk {
		// 考虑实例到期后扩容数据盘
		sizeGB := libs.DataDiskToGB(priceInfo.DataDiskInfo.ChangeSize)
		duration := priceInfo.DataDiskInfo.ExpiredAt.Unix() - priceInfo.DataDiskInfo.TakeEffectAt.Unix()

		if duration < 0 {
			duration = 0
		}

		priceInfo.DataDiskInfo.FinallyChargeType = bcs.stepChargingGetChargeType(constant.ChargeTypeYearly, duration, machine.NewMachineSkuContent)
		priceInfo.DataDiskInfo.Duration = float64(duration) / float64(priceInfo.DataDiskInfo.FinallyChargeType.ToSecond())
		if priceInfo.DataDiskInfo.FinallyChargeType.IsPayg() {
			priceInfo.DataDiskInfo.Duration = 1
			priceInfo.DataDiskInfo.FinallyChargeType = constant.ChargeTypeDaily
		}

		if duration > 0 && priceInfo.DataDiskInfo.ChangeSize > 0 {
			isLimit001 := true
			if isPrepayToPayg {
				// 取整完整天数后会剩下的秒
				leftSecondsAfterWholeDays := duration % priceInfo.DataDiskInfo.FinallyChargeType.ToSecond()
				priceInfo.DataDiskInfo.Duration = float64(duration-leftSecondsAfterWholeDays) / float64(priceInfo.DataDiskInfo.FinallyChargeType.ToSecond())
				isLimit001 = false
			}

			priceInfo.DataDiskInfo.DataDiskOriginPrice = libs.AssetRound(int64(priceInfo.DataDiskInfo.Duration*float64(machine.DataDiskExpandPrice.GetPrice(priceInfo.DataDiskInfo.FinallyChargeType)*sizeGB)), isLimit001)
			afterMemberDiscountPrise = libs.DiscountPrice(priceInfo.DataDiskInfo.DataDiskOriginPrice, memberDiscount, isLimit001)

			// duration discount
			priceInfo.DataDiskInfo.DataDiskDealPrice = afterMemberDiscountPrise
			for _, v := range machine.NewMachineSkuContent {
				if v.Type == priceInfo.DataDiskInfo.FinallyChargeType {
					durationDiscount = v.GetDurationDiscount(int(math.Floor(priceInfo.DataDiskInfo.Duration)))
					if durationDiscount != 100 {
						durationDiscountValid = true
						priceInfo.DataDiskInfo.DataDiskDealPrice = libs.DiscountPrice(afterMemberDiscountPrise, int64(durationDiscount), true)
					}
				}
			}
		}

		priceInfo.DataDiskInfo.DataDiskOriginDailyPrice = machine.DataDiskExpandPrice.DailyPrice * expandDataDiskGB
		priceInfo.DataDiskInfo.DataDiskDealDailyPrice = libs.DiscountPrice(priceInfo.DataDiskInfo.DataDiskOriginDailyPrice, memberDiscount, true)

	} else {
		// 原价
		priceInfo.DataDiskInfo.DataDiskOriginDailyPrice = machine.DataDiskExpandPrice.DailyPrice * expandDataDiskGB
		priceInfo.DataDiskInfo.DataDiskOriginPrice = machine.DataDiskExpandPrice.GetPrice(priceInfo.ChargeType) * int64(priceInfo.Duration) * expandDataDiskGB

		if priceInfo.ExpandDataDisk == 0 {
			return
		}

		// 折后价
		priceInfo.DataDiskInfo.DataDiskDealDailyPrice = libs.DiscountPrice(priceInfo.DataDiskInfo.DataDiskOriginDailyPrice, memberDiscount, false)
		if !priceInfo.ChargeType.IsPayg() {
			afterMemberDiscountPrise = libs.DiscountPrice(priceInfo.DataDiskInfo.DataDiskOriginPrice, memberDiscount, true)

			// duration discount
			priceInfo.DataDiskInfo.DataDiskDealPrice = afterMemberDiscountPrise
			for _, v := range machine.NewMachineSkuContent {
				if v.Type == priceInfo.ChargeType {
					durationDiscount = v.GetDurationDiscount(priceInfo.Duration)
					if durationDiscount != 100 {
						durationDiscountValid = true
						priceInfo.DataDiskInfo.DataDiskDealPrice = libs.DiscountPrice(afterMemberDiscountPrise, int64(durationDiscount), true)
					}
				}
			}
		}
	}

	if priceInfo.ExpandDataDisk == 0 {
		return
	}

	// 添加折扣信息
	if priceInfo.ChargeType.IsPayg() {
		priceInfo.DiscountList = append(priceInfo.DiscountList, bcm.DiscountInfo{
			ProductType:    constant.ProductTypeDataDisk,
			Type:           constant.MemberDiscount,
			OriginPrice:    priceInfo.DataDiskInfo.DataDiskOriginDailyPrice,
			TotalReduction: priceInfo.DataDiskInfo.DataDiskOriginDailyPrice - priceInfo.DataDiskInfo.DataDiskDealDailyPrice,
			DiscountRate:   memberDiscount,
		})

	} else {
		priceInfo.DiscountList = append(priceInfo.DiscountList, bcm.DiscountInfo{
			ProductType:    constant.ProductTypeDataDisk,
			Type:           constant.MemberDiscount,
			OriginPrice:    priceInfo.DataDiskInfo.DataDiskOriginPrice,
			TotalReduction: priceInfo.DataDiskInfo.DataDiskOriginPrice - afterMemberDiscountPrise,
			DiscountRate:   memberDiscount,
		})

		if durationDiscountValid {
			priceInfo.DiscountList = append(priceInfo.DiscountList, bcm.DiscountInfo{
				ProductType:    constant.ProductTypeDataDisk,
				Type:           constant.DurationDiscount,
				OriginPrice:    afterMemberDiscountPrise,
				TotalReduction: afterMemberDiscountPrise - priceInfo.DataDiskInfo.DataDiskDealPrice,
				DiscountRate:   int64(durationDiscount),
			})
		}
	}

	return
}

// 获取afterHook
func (bcs *BCServer) getAfterHook(order *bcm.Order) (afterHook queue_interface.ElementPayloadContent, afterFunc func(tx *gorm.DB) error) {
	switch order.OrderType {
	case constant.OrderTypeCreateInstance:
		afterHook = &queue_interface.NewQueueForInstanceOnMachine{
			MachineID:   order.RuntimeEntity.MachineID,
			RuntimeType: constant.ContainerRuntimeOfInstance,
			OptReqs: []constant.OptInstanceReq{{
				InstanceUUID: order.ProductUUID,
				Caller:       constant.OptCallerBC,
				OptType:      constant.InstanceCreateByOrderOpt,
				Payload:      order.UUID,
			}},
		}
		afterFunc = nil
	case constant.OrderTypeCreateDeploymentContainer:
		// 此处将复用 queue_interface.NewQueueForInstanceOnMachine
		// 原本该channel消息数量并不多，不用再创建出来一套（会额外增加当前系统机器数量的的channel）

		afterHook = &queue_interface.NewQueueForInstanceOnMachine{
			MachineID:   order.RuntimeEntity.MachineID,
			RuntimeType: constant.ContainerRuntimeOfDeployment,
			OptReqs: []constant.OptInstanceReq{{
				InstanceUUID: order.ProductUUID,
				Caller:       constant.OptCallerBC,
				OptType:      constant.DCCreateOpt, // 此处也将复用（在instanceOptType内定义一些deployment的opt）
				Payload:      order.UUID,
			}},
		}
		afterFunc = nil
	case constant.OrderTypeRenewalInstance:
		//afterHook = &queue_interface.NewQueueForInstanceOnMachine{
		//	MachineID: order.RuntimeEntity.MachineID,
		//	OptReqs: []constant.OptInstanceReq{{
		//		InstanceUUID: order.ProductUUID,
		//		OptType:      constant.InstanceRenewalByOrderOpt,
		//		Caller:       constant.OptCallerBC,
		//		Payload:      order.UUID,
		//	}},
		//}
		afterHook = nil
		afterFunc = func(tx *gorm.DB) error {
			return bcs.instance.UpdateInstanceByOrder(tx, order, constant.OptInstanceReq{
				InstanceUUID: order.ProductUUID,
				OptType:      constant.InstanceRenewalByOrderOpt,
				Caller:       constant.OptCallerBC,
				Payload:      order.UUID,
			})
		}
	case constant.OrderTypeImproveConfiguration, constant.OrderTypeReduceConfiguration:
		//afterHook = &queue_interface.NewQueueForInstanceOnMachine{
		//	MachineID: order.RuntimeEntity.MachineID,
		//	OptReqs: []constant.OptInstanceReq{{
		//		InstanceUUID: order.ProductUUID,
		//		Caller:       constant.OptCallerBC,
		//		OptType:      constant.InstanceChangeConfigByOrderOpt,
		//		Payload:      order.UUID,
		//	}},
		//}
		afterHook = nil
		afterFunc = func(tx *gorm.DB) error {
			return bcs.instance.UpdateInstanceByOrder(tx, order, constant.OptInstanceReq{
				InstanceUUID: order.ProductUUID,
				Caller:       constant.OptCallerBC,
				OptType:      constant.InstanceChangeConfigByOrderOpt,
				Payload:      order.UUID,
			})
		}
	//case constant.OrderTypeMigrateInstance, constant.OrderTypeCloneInstance:
	case constant.OrderTypeCloneInstance:
		afterHook = &queue_interface.NewQueueForInstanceOnMachine{
			MachineID: order.MachineID,
			OptReqs: []constant.OptInstanceReq{{
				InstanceUUID: order.ProductUUID,
				Caller:       constant.OptCallerBC,
				OptType:      constant.InstanceCloneOpt,
				Payload:      order.UUID,
			}},
		}
	case constant.OrderTypeChangeDaily,
		constant.OrderTypeChangeWeekly,
		constant.OrderTypeChangeMonthly,
		constant.OrderTypeChangeYearly,
		constant.OrderTypeChangePayg:
		//afterHook = &queue_interface.NewQueueForInstanceOnMachine{
		//	MachineID: order.RuntimeEntity.MachineID,
		//	OptReqs: []constant.OptInstanceReq{{
		//		InstanceUUID: order.ProductUUID,
		//		Caller:       constant.OptCallerBC,
		//		OptType:      constant.InstanceChangeChargeTypeOpt,
		//		Payload:      order.UUID,
		//	}},
		//}

		afterHook = nil
		afterFunc = func(tx *gorm.DB) error {
			return bcs.instance.UpdateInstanceByOrder(tx, order, constant.OptInstanceReq{
				InstanceUUID: order.ProductUUID,
				Caller:       constant.OptCallerBC,
				OptType:      constant.InstanceChangeChargeTypeOpt,
				Payload:      order.UUID,
			})
		}
	case constant.OrderTypeDataDiskExpand, constant.OrderTypeDataDiskExpandOfPrepay, constant.OrderTypeDataDiskReduce:
		//afterHook = &queue_interface.NewQueueForInstanceOnMachine{
		//	MachineID: order.MachineID,
		//	OptReqs: []constant.OptInstanceReq{{
		//		InstanceUUID: order.ProductUUID,
		//		Caller:       constant.OptCallerBC,
		//		OptType:      constant.InstanceUpdateDataDiskSizeOpt,
		//		Payload:      order.UUID,
		//	}},
		//}
		afterHook = nil
		afterFunc = func(tx *gorm.DB) error {
			return bcs.instance.UpdateInstanceByOrder(tx, order, constant.OptInstanceReq{
				InstanceUUID: order.ProductUUID,
				Caller:       constant.OptCallerBC,
				OptType:      constant.InstanceUpdateDataDiskSizeOpt,
				Payload:      order.UUID,
			})
		}
	}
	return
}

// [lock] 续费实例锁
func (bcs *BCServer) setLockForRenewalInstance(instanceUUID string) (lock *redis.RedisMutex, err error) {
	// 功能说明：该锁目的是防止在instance临近到期时间，在续费订单付款的5分钟内主动释放gpu
	lock = bcs.mutex.NewRedisMutex(redis.MutexRenewalInstanceType, "renewal_"+instanceUUID)
	var locked bool
	locked, err = lock.LockWithNoRetry(constant.RenewalInstanceLock)
	if err != nil {
		bcs.log.WithError(err).WithField("is locked", locked).Warn("get redis lock failed.")
		err = businesserror.ErrServerBusy
		return
	}

	if !locked {
		bcs.log.WithError(err).WithField("is locked", locked).Warn("get redis lock failed.")
		err = businesserror.ErrOrderReplicateRenewal
		return
	}
	return
}

// [lock] 取消续费实例锁
func (bcs *BCServer) unLockForRenewalInstance(instanceUUID string) (err error) {
	lock := bcs.mutex.NewRedisMutex(redis.MutexRenewalInstanceType, "renewal_"+instanceUUID)
	err = lock.DoNotUseThisForceUnLock()
	return
}

func (bcs *BCServer) checkMachineChargeType(chargeType constant.ChargeType, mchUUID string) (err error) {
	mch := new(mm.Machine)
	mch, err = bcs.machine.Get(mchUUID)
	if err != nil {
		bcs.log.WithError(err).Error("get machine failed")
		return
	}

	err = businesserror.ErrMachineChargeTypeErr
	switch chargeType {
	case constant.ChargeTypeDaily:
		if !mch.PayDaily {
			return
		}
	case constant.ChargeTypeWeekly:
		if !mch.PayWeekly {
			return
		}
	case constant.ChargeTypeMonthly:
		if !mch.PayMonthly {
			return
		}
	case constant.ChargeTypeYearly:
		if !mch.PayYearly {
			return
		}
	case constant.ChargeTypePayg:
		if !mch.Payg {
			return
		}
	default:
		return
	}

	return nil
}

// 对订单付款校验，进行一些前置操作
func (bcs *BCServer) orderPayBeforeHandler(params *bcm.OrderPayCancelParams, order *bcm.Order) (err error) {
	if order == nil {
		return businesserror.ErrInternalError
	}

	var (
		ok                 bool
		wallet             *bcm.UserWallet
		walletCompareAsset int64
		orderCompareAsset  int64
		region             *regionModel.Region
		user               *um.User
	)

	if order.PayAt != nil {
		return businesserror.ErrOrderAlreadyPay
	}
	if order.Status != constant.OrderStatusUnpaid {
		return businesserror.ErrOrderCannotPay
	}

	user, err = bcs.user.FindByUserId(order.UID)
	if err != nil {
		bcs.log.WithError(err).WithField("uid", order.UID).Error("get user failed")
		return
	}

	if order.CreatedAt.Add(time.Minute * 5).Before(time.Now()) {
		return businesserror.ErrOrderTimeout
	}

	if (params.UID != order.UID) || (params.SubName != "" && params.SubName != order.SubName) {
		return businesserror.ErrResourceAccessAuthFailed
	}

	if order.OrderType == constant.OrderTypeCreateInstance {
		num, err := bcs.instance.CountUserExistInstances(order.UID)
		if err != nil {
			bcs.log.WithField("order_uuid", order.UUID).Warn("get user existed instance failed")
			return err
		}

		totalLimit, regionLimit := user.GetMaxInstanceNum(order.RuntimeEntity.RegionSign)
		if num >= totalLimit {
			return businesserror.ErrInstanceMaxNum.New().Format(totalLimit)
		}

		if regionLimit != 0 && num >= regionLimit {
			region, err = bcs.region.GetRegionDetail(order.MachineEntity.RegionSign)
			if err != nil {
				return err
			}
			return businesserror.ErrRegionMaxInstanceNumLimit.New().Format(region.Name, regionLimit)
		}
	}

	// wallet
	productType := constant.ProductTypeInstance
	if order.RuntimeType == constant.ContainerRuntimeOfDeployment {
		productType = constant.ProductTypeDeployment
	}
	wallet, err = bcs.bc.GetWalletForCreateOrder(order.UID, productType, order.ChargeType, order.MachineID, nil, "")
	if err != nil {
		return
	}

	// 校验价格。只要是余额与代金券的和不足订单账面金额，一律报错
	if wallet.Assets >= 0 {
		walletCompareAsset = wallet.Assets + wallet.VoucherBalance
	} else {
		walletCompareAsset = wallet.VoucherBalance
	}
	if order.ChargeType.IsPayg() {
		orderCompareAsset = order.RuntimeEntity.PaygPrice
	} else {
		orderCompareAsset = order.PriceEntity.ToFinallyDealPrice()
	}
	if walletCompareAsset < orderCompareAsset {
		return businesserror.ErrWalletInsufficientBalance
	}

	// sub user wallet check
	if order.SubName != "" {
		var suw *bcm.SubUserWallet
		suw, err = bcs.bc.SubUserGetWalletForCreateOrder(order.UID, order.SubName, productType, order.ChargeType, order.MachineID)
		if err != nil {
			return
		}
		if suw.PaymentMethod == constant.SUPaymentMethodQuota && suw.Quota < orderCompareAsset {
			return businesserror.ErrSubUserWalletInsufficientBalance
		}
	}

	// 根据订单类型校验
	switch order.OrderType {
	case constant.OrderTypeCreateInstance:
		ok, err = bcs.machine.CheckMaxInstanceNum(order.RuntimeEntity.MachineID)
		if err != nil {
			bcs.log.WithError(err).Error("check max instance num failed")
			return businesserror.ErrInternalError
		}
		if !ok {
			return businesserror.ErrMachineMaxInstanceNumErr
		}
	case constant.OrderTypeCreateDeploymentContainer:
		// note:zt 用不用再次校验实例数量，部署状态啥的
	case constant.OrderTypeRenewalInstance:

	case constant.OrderTypeImproveConfiguration, constant.OrderTypeReduceConfiguration:

	case constant.OrderTypeChangeDaily, constant.OrderTypeChangeWeekly, constant.OrderTypeChangeMonthly, constant.OrderTypeChangeYearly:

	case constant.OrderTypeChangePayg:

	}

	return nil
}

func (bcs *BCServer) ddpBeforeCreateOrderCheck(uid int, subName string, chargeType constant.ChargeType, orderPrice int64) error {
	// wallet
	wallet, err := bcs.bc.GetWalletForCreateOrder(uid, constant.ProductTypeDeploymentDurationPkg, chargeType, "", nil, "")
	if err != nil {
		return err
	}

	// 校验价格。只要是余额与代金券的和不足订单账面金额，一律报错
	walletCompareAsset := wallet.VoucherBalance
	if wallet.Assets >= 0 {
		walletCompareAsset = wallet.Assets + wallet.VoucherBalance
	}

	if walletCompareAsset < orderPrice {
		return businesserror.ErrWalletInsufficientBalance
	}

	// sub user wallet check
	if subName != "" {
		var suw *bcm.SubUserWallet
		suw, err = bcs.bc.SubUserGetWalletForCreateOrder(uid, subName, constant.ProductTypeDeploymentDurationPkg, "", "")
		if err != nil {
			return err
		}
		if suw.PaymentMethod == constant.SUPaymentMethodQuota && suw.Quota < orderPrice {
			return businesserror.ErrSubUserWalletInsufficientBalance
		}
	}
	return nil
}

// 付款后进行的后置操作
func (bcs *BCServer) orderPayAfterHandler(order *bcm.Order, isAdminClone bool, srcCloneUid int) (err error) {
	if order == nil {
		return businesserror.ErrInternalError
	}

	// 根据订单类型校验
	switch order.OrderType {
	case constant.OrderTypeRenewalInstance:
		err = bcs.unLockForRenewalInstance(order.ProductUUID)
		if err != nil {
			bcs.log.WithError(err).Info("pay order for renewal: unlock failed")
		}
		err = bcs.unLockForRenewalInstance(order.ProductUUID)
		if err != nil {
			bcs.log.WithError(err).Info("pay order for renewal: unlock failed")
		}
	case constant.OrderTypeCloneInstance:
		if isAdminClone {
			return
		}
		uid := order.UID
		if srcCloneUid != 0 {
			uid = srcCloneUid
		}
		user, uErr := bcs.user.FindByUserId(uid)
		if uErr != nil {
			bcs.log.WithField("uid", uid).ErrorE(uErr, "omit. get user by uid failed")
			return
		}

		ftKey := kv_plugin.FileTransferCloneContainerUserLimitGenerateUUID(user.UUID, time.Now().Day())
		val, uErr := bcs.kv.InternalGet(ftKey)
		if uErr != nil {
			bcs.log.WithField("key", ftKey).ErrorE(uErr, "bcs.kv.InternalGet failed")
			return businesserror.ErrServerBusy
		}
		times := kv_plugin.FileTransferUserLimitGetValue(val)
		times++

		uErr = bcs.kv.InternalSet(ftKey, strconv.Itoa(times))
		if uErr != nil {
			bcs.log.WithField("key", ftKey).ErrorE(uErr, "omit. bcs.kv.InternalSetTTL failed")
			return
		}
	}
	return
}

// 包卡转按量获取当前生效订单的消费金额: start 生效订单的开始时间; end 当前时间
func (bcs *BCServer) prepayToPaygGetCostAsset(machine *mm.Machine, start, end time.Time, order *bcm.Order, level constant.MemberLevelName) (costAsset, costInstance, costDataDisk int64, err error) {
	var (
		chargePrice      int64
		chargeType       = order.ChargeType
		duration         = end.Unix() - start.Unix() // 秒数
		memberDiscount   int64
		durationDiscount int64
		skuInfo          mm.SkuInfo
	)

	// 根据使用时间，找到当前适用的退款计费方式
	chargeType = bcs.stepChargingGetChargeType(chargeType, duration, machine.NewMachineSkuContent)

	for _, v := range machine.NewMachineSkuContent {
		if v == nil {
			err = businesserror.ErrInternalError
			return
		}
		if chargeType == v.Type {
			skuInfo = *v
			break
		}
	}

	durationNum := duration / chargeType.ToSecond()
	durationDiscount = int64(skuInfo.GetDurationDiscount(int(durationNum)))

	// 根据具体计费方式与会员等级，找到退款的单价
	for _, v := range skuInfo.LevelConfig {
		if v == nil {
			err = businesserror.ErrInternalError
			return
		}
		if level == v.LevelName {
			chargePrice = v.DiscountedPrice * int64(order.PriceEntity.Num)
			memberDiscount = v.Discount
			break
		}
	}

	// 如果当前订单有数据盘扩容
	if order.PriceEntity.ExpandDataDisk != 0 && order.PriceEntity.DataDiskInfo != nil {
		var (
			ct = chargeType
			du float64
		)

		// 取整完整天数后会剩下的秒
		leftSecondsAfterWholeDays := duration % constant.ChargeTypeDaily.ToSecond()
		du = float64(duration-leftSecondsAfterWholeDays) / float64(chargeType.ToSecond())

		costDataDiskAfterMemberDiscount := libs.AssetRound(int64(du*float64(libs.DiscountPrice(machine.DataDiskExpandPrice.GetPrice(ct)*libs.DataDiskToGB(order.PriceEntity.ExpandDataDisk), memberDiscount, true))), false)
		costDataDisk = libs.AssetRound(libs.DiscountPrice(costDataDiskAfterMemberDiscount, durationDiscount, false), false)
	}

	// 根据时间与单价，找到应该扣除的金额,具体怎么扣钱，由外部处理
	costInstanceAfterMemberDiscount := libs.AssetRound(int64(float64(duration)/float64(chargeType.ToSecond())*float64(chargePrice)), false)
	costInstance = libs.AssetRound(libs.DiscountPrice(costInstanceAfterMemberDiscount, durationDiscount, false), false)
	costAsset = costInstance + costDataDisk
	return
}

// 包卡转按量获取当前生效订单的消费金额: start 生效订单的开始时间; end 当前时间
func (bcs *BCServer) ddpRefundGetCostAsset(machineSku []*mm.SkuInfo, duration int64, order *bcm.Order, level constant.MemberLevelName) (costAsset int64, chargeType constant.ChargeType, err error) {
	var (
		chargePrice      int64
		durationDiscount int64
		skuInfo          mm.SkuInfo
	)

	// 根据使用时间，找到当前适用的退款计费方式
	chargeType = order.ChargeType
	chargeType = bcs.stepChargingGetChargeType(chargeType, duration, machineSku)

	for _, v := range machineSku {
		if v == nil {
			err = businesserror.ErrInternalError.New().AppendStr("machine sku error")
			return
		}
		if chargeType == v.Type {
			skuInfo = *v
			break
		}
	}

	durationNum := duration / chargeType.ToSecond()
	durationDiscount = int64(skuInfo.GetDurationDiscount(int(durationNum)))

	// 根据具体计费方式与会员等级，找到退款的单价
	for _, v := range skuInfo.LevelConfig {
		if v == nil {
			err = businesserror.ErrInternalError.New().AppendStr("machine sku level config error")
			return
		}
		if level == v.LevelName {
			chargePrice = v.DiscountedPrice * int64(order.PriceEntity.Num)
			break
		}
	}

	// 根据时间与单价，找到应该扣除的金额,具体怎么扣钱，由外部处理
	costInstanceAfterMemberDiscount := libs.AssetRound(int64(float64(duration)/float64(chargeType.ToSecond())*float64(chargePrice)), false)
	costAsset = libs.AssetRound(libs.DiscountPrice(costInstanceAfterMemberDiscount, durationDiscount, false), false)
	return
}

func (bcs *BCServer) prepayToPaygGetCostAssetForPrepayExpandDataDiskOrder(machine *mm.Machine, end time.Time, order *bcm.Order, level constant.MemberLevelName) (cost int64, err error) {
	var (
		duration       = end.Unix() - order.PriceEntity.DataDiskInfo.TakeEffectAt.Unix() // 秒数
		memberDiscount int64
	)

	// 根据使用时间，找到当前适用的退款计费方式
	chargeType := bcs.stepChargingGetChargeType(order.ChargeType, duration, machine.NewMachineSkuContent)

	memberDiscount, err = machine.GetDiscountRate(chargeType, level)
	if err != nil {
		bcs.log.WithField("chargeType", chargeType).ErrorE(err, "get discount rate failed")
		return
	}

	fakePriceInfo := &bcm.PriceInfo{
		ChargeType:     order.PriceEntity.ChargeType,
		ExpandDataDisk: order.PriceEntity.ExpandDataDisk,
		DataDiskInfo: &bcm.DataDiskInfo{
			ChangeSize:   order.PriceEntity.DataDiskInfo.ChangeSize,
			TakeEffectAt: order.PriceEntity.DataDiskInfo.TakeEffectAt,
			ExpiredAt:    &end,
		},
		PrepayExpandDataDisk: true,
	}

	bcs.getPriceOfDataDisk(fakePriceInfo, machine, memberDiscount, true)

	return fakePriceInfo.DataDiskInfo.DataDiskDealPrice, nil
}

// 对于需要阶梯计费的场景,用于获取阶梯计费的最终计费方式
func (bcs *BCServer) stepChargingGetChargeType(chargeType constant.ChargeType, duration int64, machineSku []*mm.SkuInfo) constant.ChargeType {
	chargeMap := make(map[constant.ChargeType]struct{})

	for _, v := range machineSku {
		if v != nil {
			chargeMap[v.Type] = struct{}{}
		}
	}
	for {
		switch chargeType {
		case constant.ChargeTypeYearly:
			if _, ok := chargeMap[constant.ChargeTypeYearly]; !ok || duration < chargeType.ToSecond() {
				chargeType = constant.ChargeTypeMonthly
				continue
			}
			break
		case constant.ChargeTypeMonthly:
			if _, ok := chargeMap[constant.ChargeTypeMonthly]; !ok || duration < chargeType.ToSecond() {
				chargeType = constant.ChargeTypeWeekly
				continue
			}
			break
		case constant.ChargeTypeWeekly:
			if _, ok := chargeMap[constant.ChargeTypeWeekly]; !ok || duration < chargeType.ToSecond() {
				chargeType = constant.ChargeTypeDaily
				continue
			}
			break
		case constant.ChargeTypeDaily:
			if _, ok := chargeMap[constant.ChargeTypeDaily]; !ok || duration < chargeType.ToSecond() {
				chargeType = constant.ChargeTypePayg
				continue
			}
			break
		case constant.ChargeTypePayg:
			break
		}
		break
	}

	return chargeType
}

func (bcs *BCServer) beforeCreateOrderFillInformation(instance *im.Instance, uid int, priceInfo *bcm.PriceInfo) (user *um.User, ss *bcm.MachineSnapShot, takeEffectAt, expiredAt *time.Time, err error) {
	// 获取用户
	user, err = bcs.user.FindByUserId(uid)
	if err != nil {
		bcs.log.WithError(err).Warn("get user detail failed")
		return
	}

	userMember, err := bcs.user.GetUserMemberInfoByUid(user.ID)
	if err != nil {
		bcs.log.WithField("uid", uid).WarnE(err, "get user member detail failed")
		return
	}

	// 获取价格, 补全信息，
	// todo： 关于productType,方法内部只需要判断是不是ddp
	//  此处类型因为暂时不影响，不加以区分。如果哪天方法内部需要判断了，此处记得改为准确的类型
	err = bcs.getPrice(&getPriceParams{
		uid:           user.ID,
		priceInfo:     priceInfo,
		levelName:     userMember.MemberLevel,
		productType:   constant.ProductTypeInstance,
		specialCoupon: priceInfo.SpecialCoupon,
	})
	if err != nil {
		bcs.log.WithError(err).Warn("get real price failed")
		return
	}

	takeEffectAt, expiredAt, err = bcs.getInstanceExpireTime(instance, priceInfo.ChargeType, priceInfo.Duration)
	if err != nil {
		bcs.log.Warn("renewal: get time failed")
		return
	}

	// 获取机器信息, 补全信息
	machine := new(mm.Machine)
	machine, err = bcs.machine.Get(priceInfo.MachineID)
	if err != nil {
		bcs.log.WithField("machineID", priceInfo.MachineID).Warn("get machine failed")
		return
	}

	// 校验最大租用时长
	err = bcs.checkMachineRentDeadLine(priceInfo.MachineID, machine, expiredAt)
	if err != nil {
		bcs.log.WithError(err).Info("checkMachineRentDeadLine")
		return
	}

	ss, err = bcs.getMachineSnapshot(priceInfo.MachineID, machine, int64(priceInfo.Num))
	if err != nil {
		bcs.log.Warn("get machine snapshot failed")
	}
	return
}

// 预付费扩容数据盘与现有逻辑冲突, 单抽出一个方法
func (bcs *BCServer) beforeCreateOrderFillInformationForPrepayExpandDataDisk(uid int, priceInfo *bcm.PriceInfo) (user *um.User, ss *bcm.MachineSnapShot, err error) {
	// 获取用户
	user, err = bcs.user.FindByUserId(uid)
	if err != nil {
		bcs.log.WithError(err).Warn("get user detail failed")
		return
	}

	userMember, err := bcs.user.GetUserMemberInfoByUid(uid)
	if err != nil {
		bcs.log.WithField("uid", uid).WarnE(err, "get user member detail failed")
		return
	}

	// 获取机器信息, 补全信息
	machine := new(mm.Machine)
	machine, err = bcs.getMachine(priceInfo.MachineID)
	if err != nil {
		bcs.log.WithField("machineID", priceInfo.MachineID).Warn("get machine failed")
		return
	}

	var memberDiscount int64
	memberDiscount, err = machine.GetDiscountRate(priceInfo.ChargeType, userMember.MemberLevel)
	if err != nil {
		bcs.log.WithField("priceInfo", priceInfo).ErrorE(err, "get discount rate failed")
		return
	}

	// 获取价格, 补全信息
	bcs.getPriceOfDataDisk(priceInfo, machine, memberDiscount, false)

	ss, err = bcs.getMachineSnapshot(priceInfo.MachineID, machine, int64(priceInfo.Num))
	if err != nil {
		bcs.log.Warn("get machine snapshot failed")
	}
	return
}

// checkInstanceRunning 检查实例是否在运行
func (bcs *BCServer) checkInstanceRunning(instanceUUID string) (bool, error) {
	var (
		instance im.Instance
		err      error
	)

	instance, err = bcs.instance.GetInstance(constant.NewInstanceUUIDType(instanceUUID))
	if err != nil {
		bcs.log.WithError(err).WithField("instanceUUID", instanceUUID).Error("get instance failed")
		return false, nil
	}

	if instance.Status.ShowSnapshotPaygPrice() {
		return true, nil
	}
	return false, nil
}

// changeChargeTypeCheck 转计费方式前的校验
func (bcs *BCServer) changeChargeTypeCheck(uid int, instanceUUID constant.InstanceUUIDType) (err error) {
	var (
		exist    bool
		count    int64
		instance im.Instance
	)

	// 只有稳定状态下的实例才可以转计费方式：运行中，已关机
	instance, err = bcs.instance.GetInstance(instanceUUID)
	if err != nil {
		bcs.log.WithError(err).WithField("uuid", instanceUUID).Error("get instance failed")
		return
	}

	if !instance.Status.CanChangeChargeType() {
		err = businesserror.ErrOrderChangeChargeTypeInstanceStatusError
		return
	}

	if instance.UID != uid {
		return businesserror.ErrInstanceSubUserAuthFailed
	}

	// 先判断是否有待支付订单
	exist, err = bcs.bc.ExistUnpaidChangeTypeOrder(instanceUUID.String())
	if err != nil {
		bcs.log.WithError(err).WithField("instanceUUID", instanceUUID).Error("check unpaid order failed")
		return
	}
	if exist {
		err = businesserror.ErrOrderChangeChargeTypeUnpaidCanNotMoreThanOne
		return
	}

	count, err = bcs.bc.CountUserChangeChargeTypeOrder(uid)
	if err != nil {
		return
	}

	if count >= constant.ChangeChargeTypeTimesLimit {
		return businesserror.ErrOrderUserChangeChargeTypeCanNotMoreThanTenToday
	}

	exist, err = bcs.bc.ExistUnpaidRenewalOrder(instanceUUID.String())
	if err != nil {
		bcs.log.WithError(err).WithField("instanceUUID", instanceUUID).Error("check unpaid order failed")
		return
	}
	if exist {
		err = businesserror.ErrOrderExistUnpaidRenewal
	}
	return
}

// changeInstanceCheck 升降配置前的校验
func (bcs *BCServer) changeInstanceCheck(instanceUUID constant.InstanceUUIDType) (err error) {
	var (
		exist bool
	)

	// 先判断是否有待支付订单
	exist, err = bcs.bc.ExistUnpaidChangeTypeOrder(instanceUUID.String())
	if err != nil {
		bcs.log.WithError(err).WithField("instanceUUID", instanceUUID).Error("check unpaid order failed")
		return
	}
	if exist {
		err = businesserror.ErrOrderChangeChargeTypeUnpaidCanNotMoreThanOne
		return
	}

	exist, err = bcs.bc.ExistUnpaidRenewalOrder(instanceUUID.String())
	if err != nil {
		bcs.log.WithError(err).WithField("instanceUUID", instanceUUID).Error("check unpaid order failed")
		return
	}
	if exist {
		err = businesserror.ErrOrderExistUnpaidRenewal
	}
	return
}

// checkMachineRentDeadLine 包卡检查机器最久租用至
func (bcs *BCServer) checkMachineRentDeadLine(machineID string, machine *mm.Machine, rentTo *time.Time) (err error) {
	if machine == nil {
		machine, err = bcs.machine.Get(machineID)
		if err != nil {
			bcs.log.WithField("machineID", machineID).Warn("get machine failed")
			return
		}
	}

	if machine.RentDeadline != nil {
		if time.Now().After(*machine.RentDeadline) {
			return businesserror.ErrOrderMachineRentDeadLineExpired
		}
		if rentTo != nil {
			if rentTo.After(*machine.RentDeadline) {
				return businesserror.ErrOrderMachineRentDeadLineLimit
			}
		}
	}

	return nil
}

// netDiskOperatePayCheck 校验余额
func (bcs *BCServer) netDiskOperatePayCheck(uid int, costPrice int64) error {
	// 网盘不能用代金券
	wallet, err := bcs.bc.GetWallet(uid, constant.ProductTypeAllFake, "", "")
	if err != nil {
		return err
	}

	if wallet.Assets < costPrice {
		return businesserror.ErrWalletInsufficientBalance
	}

	return nil
}

func (bcs *BCServer) ucCheck(uid int, uc *bcm.UserCoupon, machine *mm.Machine, needJudgeUsed bool, priceInfo *bcm.PriceInfo) error {
	now := time.Now()

	if uid != uc.UID {
		return businesserror.ErrUserCouponAuthFailed
	}

	// 是否已使用
	if uc.UsedStatus == constant.UCUsedStatusUsed && needJudgeUsed {
		return businesserror.ErrUserCouponAlreadyUsed
	}
	// 有效期是否符合
	if now.After(uc.ValidEnd) || now.Before(uc.CouponSnapshotEntity.ValidBegin) {
		return businesserror.ErrUserCouponAlreadyEndValid
	}
	// 使用条件是否符合
	if uc.CouponSnapshotEntity.UseConditionType == constant.UseConditionTypeBillAsset {
		if uc.CouponSnapshotEntity.UseConditionEntity.BillAmountLimit > priceInfo.OriginPrice {
			return businesserror.ErrUserCouponUseConditionLimit
		}
	}

	if uc.CouponSnapshotEntity.UseChargeType != constant.CouponUseChargeTypeAll {
		couponChargeType := constant.ChangeChargeTypeToCouponChargeType(priceInfo.ChargeType)
		if couponChargeType != uc.CouponSnapshotEntity.UseChargeType {
			return businesserror.ErrUserCouponChargeTypeError
		}
	}

	if uc.CouponSnapshotEntity.UseConditionEntity.MaxDeductionDays < 0 {
		bcs.log.WithField("couponID", uc.CouponID).Error("coupon MaxDeductionDays < 0")
		return businesserror.ErrInternalError
	}
	// 通用适用范围是否符合，暂时都只有一种，先不做校验

	// 可选适用范围是否符合
	switch uc.CouponSnapshotEntity.ScopeOfApplicationType {
	case constant.ScopeOfApplicationTypeRegionLimit:
		if !goset.NewSet[constant.RegionSignType](uc.CouponSnapshotEntity.ScopeOfApplicationEntity.UseRegionList...).ContainsOne(machine.RegionSign) {
			return businesserror.ErrUserCouponOutOfScopeOfApplication
		}

	case constant.ScopeOfApplicationTypeMachineLimit:
		if !goset.NewSet[string](uc.CouponSnapshotEntity.ScopeOfApplicationEntity.UseMachineList...).ContainsOne(machine.MachineID) {
			return businesserror.ErrUserCouponOutOfScopeOfApplication
		}

	case constant.ScopeOfApplicationTypeGpuTypeLimit:
		gpuInfo, err := bcs.gpuType.GetGpu(machine.GPUTypeID)
		if err != nil {
			bcs.log.WithField("gpu_type_id", machine.GPUTypeID).WithField("err", err).Error("get gpu info failed")
			return err
		}

		if !goset.NewSet[string](uc.CouponSnapshotEntity.ScopeOfApplicationEntity.UseGpuType...).ContainsOne(gpuInfo.GpuName) {
			return businesserror.ErrUserCouponOutOfScopeOfApplication
		}
	}

	return nil
}

func (bcs *BCServer) getMachine(machineID string) (machine *mm.Machine, err error) {
	var (
		dds *ddsModel.DataDiskStock
	)

	machine, err = bcs.machine.Get(machineID)
	if err != nil {
		bcs.log.WithError(err).Error("get machine failed")
		return
	}

	dds, err = bcs.dds.Get(nil, machineID)
	if err != nil {
		bcs.log.WithError(err).Info("get dds failed")
		return nil, err
	}

	if machine.SurplusMaxDataDiskExpandSize != dds.GetAvailable() {
		err = machine.MachineUpdateByMap(nil, &db_helper.QueryFilters{EqualFilters: map[string]interface{}{
			"machine_id": machineID,
		}}, map[string]interface{}{
			"surplus_max_data_disk_expand_size": dds.GetAvailable(),
		})
		if err != nil {
			bcs.log.WithError(err).Error("update machine SurplusMaxDataDiskExpandSize  failed")
			return machine, err
		}
	}
	machine.DataDiskExpandPrice = &constant.DataDiskChargeInfo{
		DailyPrice:   dds.DailyPrice,
		WeeklyPrice:  dds.WeeklyPrice,
		MonthlyPrice: dds.MonthlyPrice,
		YearlyPrice:  dds.YearlyPrice,
	}
	return
}

// [lock] 包卡转按量锁
func (bcs *BCServer) setLockForPrepayToPayg(instanceUUID string) (lock *redis.RedisMutex, err error) {
	lock = bcs.mutex.NewRedisMutex(redis.MutexPrepayToPayg, instanceUUID)
	var locked bool
	locked, err = lock.LockWithNoRetry(constant.PrepayToPaygLock)
	if err != nil {
		bcs.log.WithError(err).WithField("is locked", locked).Warn("get redis lock failed.")
		err = businesserror.ErrServerBusy
		return
	}

	if !locked {
		bcs.log.WithError(err).WithField("is locked", locked).Warn("get redis lock failed.")
		err = businesserror.ErrOrderPrepayToPaygIsRunning
		return
	}
	return
}
