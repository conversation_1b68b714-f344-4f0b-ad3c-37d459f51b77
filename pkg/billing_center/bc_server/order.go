package bc_server

import (
	"fmt"
	"server/pkg-agent/agent_constant"
	"server/pkg-agent/messenger"
	"server/pkg-core/api/coreapi"
	coreGpuStocModel "server/pkg-core/gpu_stock/model"
	bcm "server/pkg/billing_center/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	ddsModel "server/pkg/data_disk_stock/model"
	"server/pkg/db_helper"
	deploymentModel "server/pkg/deployment/model"
	gs "server/pkg/gpu_stock/model"
	im "server/pkg/instance/model"
	"server/pkg/libs"
	"server/pkg/logger"
	machineModel "server/pkg/machine/model"
	mm "server/pkg/machine/model"
	regionModel "server/pkg/region/model"
	rm "server/pkg/region/model"
	um "server/pkg/user/model"
	"server/plugin/kv_plugin"
	"server/plugin/queue_interface"
	redis "server/plugin/redis_plugin"
	tsc "server/plugin/redis_plugin/time_service_center"
	"strings"
	"time"

	goset "github.com/deckarep/golang-set/v2"

	"github.com/pkg/errors"

	log "github.com/sirupsen/logrus"

	"gorm.io/gorm"
)

// ----------------------- API -------------------------

func (bcs *BCServer) CreateInstance(params *bcm.CreateInstanceRequest) (newOrder *bcm.Order, err error) {
	return bcs.createInstance(params)
}

func (bcs *BCServer) CreateDeploymentContainer(tx *gorm.DB, params *bcm.CreateInstanceRequest) (newOrder *bcm.Order, gpuDeferFunc func(error), err error) {
	return bcs.createDeploymentContainer(tx, params)
}

func (bcs *BCServer) RenewalInstance(params *bcm.RenewalInstanceRequest) (renewalOrder *bcm.Order, err error) {
	return bcs.renewalInstance(params)
}

func (bcs *BCServer) ChangeInstance(params *bcm.ChangeInstanceParams) (err error) {
	return bcs.changeInstance(params)
}

func (bcs *BCServer) CloneInstanceCheck(instanceUUID, dstMachineID string, isAdmin bool) (err error) {
	return bcs.cloneInstanceCheck(instanceUUID, dstMachineID, isAdmin)
}

func (bcs *BCServer) PaygToPrepay(params *bcm.PaygToPrepayCreateOrderParams) (newOrder *bcm.Order, err error) {
	return bcs.paygToPrepay(params)
}

func (bcs *BCServer) PrepayToPayg(params *bcm.PrepayToPaygParams) (preview *bcm.CreateOrderForPrepayToPaygResponse, err error) {
	return bcs.prepayToPayg(params)
}

func (bcs *BCServer) OrderPay(params *bcm.OrderPayCancelParams) (order *bcm.Order, productUUID string, err error) {
	return bcs.orderPay(params)
}

func (bcs *BCServer) CancelOrder(params *bcm.OrderPayCancelParams) (err error) {
	return bcs.cancelOrder(params)
}

func (bcs *BCServer) NetDiskExpand(uid, expandCapacityInGB, durationInMonth int, sign constant.RegionSignType) (err error) {
	return bcs.netDiskExpand(uid, expandCapacityInGB, durationInMonth, sign)
}

func (bcs *BCServer) NetDiskRenewal(uid, recordID, durationInMonth int) (err error) {
	return bcs.netDiskRenewal(uid, recordID, durationInMonth)
}

func (bcs *BCServer) GetPricePreview(params *bcm.GetPricePreviewParams) (err error) {
	return bcs.getPricePreview(params)
}

func (bcs *BCServer) DataDiskChangeSize(params *bcm.ChangeDataDiskSizeParams) (newOrder *bcm.Order, err error) {
	return bcs.changeDataDiskSize(params)
}

func (bcs *BCServer) ChangeDataDiskSizePreview(params *bcm.ChangeDataDiskSizeParams) (res *bcm.ChangeDataSizePreviewRes, err error) {
	return bcs.changeDataDiskSizePreview(params)
}

func (bcs *BCServer) ChangeChargeTypeCheck(uid int, instanceUUID constant.InstanceUUIDType) (err error) {
	return bcs.changeChargeTypeCheck(uid, instanceUUID)
}

func (bcs *BCServer) MostWithdraw(uid int) (most int64, err error) {
	return bcs.mostWithdraw(uid)
}

func (bcs *BCServer) ChangeProtocol(params *bcm.ChangeProtocolReq) (err error) {
	return bcs.changeProtocol(params)
}

// --------------------- private -----------------------

// createInstance 创建实例
func (bcs *BCServer) createInstance(params *bcm.CreateInstanceRequest) (newOrder *bcm.Order, err error) {
	// 各种校验
	if params == nil {
		bcs.log.WithField("params", params).Error("Params is nil")
		err = businesserror.ErrInternalError
		return
	}
	if err = params.Check(); err != nil {
		bcs.log.WarnE(err, "create instance check params failed. params:%+v", params)
		return
	}

	if err = bcs.beforeCreateOrderCheck(params, params.CloneSrcInstanceUUID); err != nil {
		return
	}

	var (
		ok           bool
		instanceUUID = libs.GenInstanceUUID(params.InstanceInfo.MachineID)
		runTimeUUID  = libs.GenContainerRuntimeUUID(instanceUUID.String())
		orderUUID    = libs.RandNumberString()
		user         *um.User
		ss           *bcm.MachineSnapShot
		txUUID       string
	)

	// fill info
	user, ss, params.InstanceInfo.TakeEffectAt, params.InstanceInfo.ExpiredAt, err = bcs.beforeCreateOrderFillInformation(nil, params.InstanceInfo.UID, &params.PriceInfo)
	if err != nil {
		bcs.log.WithError(err).Warn("beforeCreateOrderFillInformation failed")
		return nil, err
	}

	if user.OpenId == "" {
		bcs.log.WithField("user", user).Error("this user is not bound to wechat")
		err = businesserror.ErrBindWeXinPlease
		return nil, err
	}

	params.InstanceInfo.OrderUUID = orderUUID
	params.InstanceInfo.PaygPrice = params.PriceInfo.PaygPrice
	params.InstanceInfo.RuntimeUUID = runTimeUUID
	params.InstanceInfo.RegionSign = ss.RegionSign
	if params.SubName != "" {
		params.InstanceInfo.Name = params.SubName[:strings.Index(params.SubName, "@")] + "创建"
	}

	// 预留gpu
	ok, err = bcs.gpuStock.Reserve(&coreGpuStocModel.GpuReserve{
		UUID:        instanceUUID.String(),
		RuntimeUUID: runTimeUUID.String(),
		MachineID:   params.InstanceInfo.MachineID,
		GpuNum:      params.InstanceInfo.ReqGPUAmount,
		Priority:    constant.InstanceLevel,
		Timestamp:   tsc.Timestamp(),
	})
	if err != nil {
		bcs.log.WithError(err).Warn("check machine failed|")
		return
	}
	if !ok {
		err = businesserror.ErrMachineGpuNumUpdateFailed
		return
	}
	defer func() {
		if err != nil {
			errE := bcs.gpuStock.Release(&coreGpuStocModel.GpuRelease{
				RuntimeUUID: runTimeUUID.String(),
				MachineID:   params.InstanceInfo.MachineID,
				DebugMsg:    "create instance",
				Timestamp:   tsc.Timestamp(),
			})
			if errE != nil {
				bcs.log.WithField("instanceUUID", instanceUUID).WithError(errE).Error("create order for create instance: release machine gpu failed.")
			}
		} else {
			errE := bcs.gpuStock.ReserveCommit(coreapi.GpuReserveCommitReq{
				RuntimeUUID: runTimeUUID.String(),
				MachineID:   params.InstanceInfo.MachineID,
				Timestamp:   tsc.Timestamp(),
			})
			if errE != nil {
				bcs.log.WithField("instanceUUID", instanceUUID).WithError(errE).Error("reserve commit gpu failed.")
			}
		}
	}()

	// 预留数据盘
	if params.PriceInfo.ExpandDataDisk != 0 {
		txUUID, err = bcs.dds.Reserve(&ddsModel.ExpandParams{
			MachineID:      params.InstanceInfo.MachineID,
			ProductUUID:    instanceUUID.String(),
			ExpandDiskSize: params.InstanceInfo.ExpandDataDisk,
		})
		if err != nil {
			bcs.log.WithError(err).Error("reserve dds failed")
			return nil, err
		}
		defer func() {
			if err != nil && txUUID != "" {
				errE := bcs.dds.TxRollback(txUUID)
				if errE != nil {
					bcs.log.WithField("instanceUUID", instanceUUID).WithError(errE).Error("create order for create instance: core api:TxRollback dds failed.")
					return
				}
			} else if err == nil && txUUID != "" {
				errE := bcs.dds.TxCommit(txUUID)
				if errE != nil {
					bcs.log.WithField("instanceUUID", instanceUUID).WithError(errE).Error("create order for create instance: core api:TxCommit dds failed.")
					return
				}
			}
		}()
	}

	newOrder = &bcm.Order{
		UID:                 user.ID,
		SubName:             params.SubName,
		Username:            user.Username,
		UserPhone:           user.Phone,
		UUID:                orderUUID,
		ProductUUID:         instanceUUID.String(),
		RuntimeUUID:         runTimeUUID,
		RuntimeType:         constant.ContainerRuntimeOfInstance,
		MachineID:           params.InstanceInfo.MachineID,
		Status:              constant.OrderStatusUnpaid,
		OrderType:           params.OperateType,
		ChargeType:          params.PriceInfo.ChargeType,
		DealPrice:           params.PriceInfo.ToFinallyDealPrice(),
		PriceEntity:         &params.PriceInfo,
		RuntimeEntity:       &params.InstanceInfo,
		MachineEntity:       ss,
		MigrateInstanceUUID: constant.InstanceUUIDType(params.CloneSrcInstanceUUID),
	}

	newOrder, err = bcs.bc.CreateOrder(newOrder)
	if err != nil {
		bcs.log.WithError(err).Warn("create order failed")
		return
	}

	return
}

// createDeploymentContainer 创建弹性部署
func (bcs *BCServer) createDeploymentContainer(tx *gorm.DB, params *bcm.CreateInstanceRequest) (newOrder *bcm.Order, gpuDeferFunc func(error), err error) {
	if err = params.Check(); err != nil {
		return
	}

	var (
		ok                      bool
		deploymentContainerUUID = libs.GenDeploymentContainerUUID(params.DeploymentUUID, params.InstanceInfo.MachineID)
		runTimeUUID             = libs.GenContainerRuntimeUUID(deploymentContainerUUID)
		orderUUID               = libs.RandNumberString()
		user                    *um.User
		ss                      *bcm.MachineSnapShot
	)

	if params.DccUUID != "" {
		deploymentContainerUUID = libs.GenDeploymentContainerUUIDFromDCC(params.DccUUID)
		runTimeUUID = params.DccRuntimeUUID
	}

	// 预留gpu
	ok, err = bcs.gpuStock.Reserve(&coreGpuStocModel.GpuReserve{
		UUID:        deploymentContainerUUID,
		RuntimeUUID: runTimeUUID.String(),
		MachineID:   params.InstanceInfo.MachineID,
		GpuNum:      params.InstanceInfo.ReqGPUAmount,
		Priority:    params.InstanceInfo.GetDeploymentContainerPriority(),
		ProductType: constant.ProductTypeDeployment,
		Timestamp:   tsc.Timestamp(),
	})
	if err != nil {
		bcs.log.WithError(err).Warn("reserve gpu failed|")
		return
	}
	if !ok {
		err = businesserror.ErrMachineGpuNumUpdateFailed
		return
	}

	gpuDeferFunc = func(errDefer error) {
		if errDefer != nil {
			errE := bcs.gpuStock.Release(&coreGpuStocModel.GpuRelease{
				RuntimeUUID: runTimeUUID.String(),
				MachineID:   params.InstanceInfo.MachineID,
				DebugMsg:    "createDeploymentContainer",
				Timestamp:   tsc.Timestamp(),
			})
			if errE != nil {
				bcs.log.WithField("deploymentContainerUUID", deploymentContainerUUID).WithError(errE).Error("create order for create instance: release machine gpu failed.")
			}
		} else {
			errE := bcs.gpuStock.ReserveCommit(coreapi.GpuReserveCommitReq{
				RuntimeUUID: runTimeUUID.String(),
				MachineID:   params.InstanceInfo.MachineID,
				Timestamp:   tsc.Timestamp(),
			})
			if errE != nil {
				bcs.log.WithField("deploymentContainerUUID", deploymentContainerUUID).ErrorE(errE, "reserve commit gpu failed.")
			}
		}
	}

	defer func() {
		if err != nil {
			gpuDeferFunc(err)
		}
	}()

	// fill info
	user, ss, params.InstanceInfo.TakeEffectAt, params.InstanceInfo.ExpiredAt, err = bcs.beforeCreateOrderFillInformation(nil, params.InstanceInfo.UID, &params.PriceInfo)
	if err != nil {
		bcs.log.WithError(err).Warn("beforeCreateOrderFillInformation failed")
		return
	}
	params.InstanceInfo.OrderUUID = orderUUID
	params.InstanceInfo.PaygPrice = params.PriceInfo.PaygPrice
	params.InstanceInfo.RuntimeUUID = runTimeUUID
	params.InstanceInfo.RegionSign = ss.RegionSign

	now := time.Now()
	newOrder = &bcm.Order{
		UID:                 user.ID,
		SubName:             params.SubName,
		Username:            user.Username,
		UserPhone:           user.Phone,
		UUID:                orderUUID,
		ProductUUID:         deploymentContainerUUID,
		RuntimeUUID:         runTimeUUID,
		RuntimeType:         constant.ContainerRuntimeOfDeployment,
		MachineID:           params.InstanceInfo.MachineID,
		Status:              constant.OrderStatusSuccess,
		OrderType:           constant.OrderTypeCreateDeploymentContainer,
		ChargeType:          params.PriceInfo.ChargeType,
		DealPrice:           params.PriceInfo.ToFinallyDealPrice(),
		PriceEntity:         &params.PriceInfo,
		RuntimeEntity:       &params.InstanceInfo,
		MachineEntity:       ss,
		MigrateInstanceUUID: constant.InstanceUUIDType(params.CloneSrcInstanceUUID),
		PayAt:               &now,
	}

	newOrder, err = bcs.bc.CreateOrderWithTx(tx, newOrder)
	if err != nil {
		bcs.log.WithError(err).Warn("create order failed")
		return
	}

	return
}

// renewalInstance 实例续费
func (bcs *BCServer) renewalInstance(params *bcm.RenewalInstanceRequest) (renewalOrder *bcm.Order, err error) {
	if params == nil {
		bcs.log.WithField("params", params).Error("Params is nil")
		err = businesserror.ErrInternalError
		return
	}

	if err = params.Check(); err != nil {
		bcs.log.WarnE(err, "renewal instance check params failed. params:%+v", params)
		return
	}

	var (
		l                  = bcs.log.WithField("params", params)
		ins                im.Instance
		ok                 bool
		order              *bcm.Order
		user               *um.User
		priceInfo          *bcm.PriceInfo
		ss                 *bcm.MachineSnapShot
		orderUUID          = libs.RandNumberString()
		txUUID             string
		dda                *ddsModel.DataDiskAllocate
		expandDataDiskSize int64
	)

	ins, err = bcs.instance.GetInstance(params.InstanceUUID)
	if err != nil {
		l.WithError(err).Warn("get instance failed")
		return
	}
	if ins.ChargeType.IsPayg() {
		err = businesserror.ErrInvalidRequestParams
		return
	}

	if ins.UID != params.Uid {
		return nil, businesserror.ErrInstanceSubUserAuthFailed
	}

	order, err = bcs.bc.GetOrder(ins.OrderUUID)
	if err != nil {
		bcs.log.WithField("orderUUID", ins.OrderUUID).WithError(err).Warn("get order failed")
		return
	}

	// 不能存在未付款的扩容订单
	exist, err := bcs.bc.ExistUnpaidPrepayExpandDataDiskOrder(order.ProductUUID)
	if err != nil {
		l.ErrorE(err, "call ExistUnpaidPrepayExpandDataDiskOrder failed")
		return nil, err
	}
	if exist {
		return nil, businesserror.ErrOrderForPrepayExpandDataDiskUnpaidLimit
	}

	// 续费创建订单校验
	err = bcs.renewalCheck(params.Uid, ins.MachineID, nil, params.TargetChargeType)
	if err != nil {
		bcs.log.WithError(err).Warn("check order info failed")
		return
	}

	// 加锁, 在续费时阻止实例释放资源
	lock := new(redis.RedisMutex)
	lock, err = bcs.setLockForRenewalInstance(ins.InstanceUUID.String())
	if err != nil {
		bcs.log.WithError(err).Warn("renewal: get lock failed.")
		return
	}
	defer func() {
		if err != nil {
			_ = lock.UnLock()
		}
	}()

	// 数据盘扩容
	dda, err = bcs.dds.GetDDA(order.MachineID, order.ProductUUID)
	if err != nil {
		l.ErrorE(err, "get dda failed")
		return
	}
	if dda != nil {
		expandDataDiskSize = dda.DiskAllocate
	}

	// 在实例已经过期后再次预留gpu和数据盘
	if ins.ExpiredAt.Valid && time.Now().After(ins.ExpiredAt.Time) {
		// 实例到期判断机器上架状态是否为上架不可开机
		machine, err := bcs.machine.Get(ins.MachineID)
		if err != nil {
			bcs.log.WithError(err).Warn("get machine failed")
			return nil, err
		}
		ok = machine.Online.IsMachineOnLineOk(constant.OrderTypeRenewalInstance, "")
		if !ok {
			return nil, businesserror.ErrGpuStockNotEnough
		}

		// 预留gpu
		ok, err = bcs.gpuStock.Reserve(&coreGpuStocModel.GpuReserve{
			UUID:        ins.InstanceUUID.String(),
			RuntimeUUID: ins.RuntimeUUID.String(),
			MachineID:   ins.MachineID,
			GpuNum:      ins.ReqGPUAmount,
			Priority:    constant.InstanceLevel,
			Timestamp:   tsc.Timestamp(),
		})
		if err != nil {
			bcs.log.WithError(err).Warn("check machine failed|")
			return nil, err
		}
		if !ok {
			err = businesserror.ErrMachineGpuNumUpdateFailed // 为释放前面的锁, 给err赋值
			return nil, err
		}

		defer func() {
			if err != nil {
				errE := bcs.gpuStock.Release(&coreGpuStocModel.GpuRelease{
					RuntimeUUID: ins.RuntimeUUID.String(),
					MachineID:   ins.MachineID,
					DebugMsg:    "renewalInstance",
					Timestamp:   tsc.Timestamp(),
				})
				if errE != nil {
					bcs.log.WithField("instanceUUID", ins.InstanceUUID.String()).WithError(errE).Error("create order for create instance: release machine gpu failed.")
				}
			} else {
				errE := bcs.gpuStock.ReserveCommit(coreapi.GpuReserveCommitReq{
					RuntimeUUID: ins.RuntimeUUID.String(),
					MachineID:   ins.MachineID,
					Timestamp:   tsc.Timestamp(),
				})
				if errE != nil {
					bcs.log.WithField("instanceUUID", ins.InstanceUUID.String()).WithError(errE).Error("reserve commit gpu failed.")
				}
			}
		}()

		// 预留数据盘
		if expandDataDiskSize != 0 {
			txUUID, err = bcs.dds.Reserve(&ddsModel.ExpandParams{
				MachineID:      order.PriceEntity.MachineID,
				ProductUUID:    order.ProductUUID,
				ExpandDiskSize: expandDataDiskSize,
			})
			if err != nil {
				bcs.log.WithError(err).Error("reserve dds failed")
				return nil, err
			}
			defer func() {
				if err != nil && txUUID != "" {
					errE := bcs.dds.TxRollback(txUUID)
					if errE != nil {
						bcs.log.WithField("instanceUUID", ins.InstanceUUID).WithError(errE).Error("create order for create instance: core api:TxRollback dds failed.")
						return
					}
				} else if err == nil && txUUID != "" {
					errE := bcs.dds.TxCommit(txUUID)
					if errE != nil {
						bcs.log.WithField("instanceUUID", ins.InstanceUUID).WithError(errE).Error("create order for create instance: core api:TxCommit dds failed.")
						return
					}
				}
			}()
		}
	}

	/*
		预付费实例可扩容后, 数据盘扩容大小信息将分散在多个订单中, 不能简单的从order中获取
		续费时, 数据盘扩容信息可以直接从dda/ddc中获取
		续费时的数据盘大小已经包含所有扩容记录
	*/

	// fill info
	priceInfo = &bcm.PriceInfo{
		MachineID:      order.MachineID,
		ChargeType:     params.TargetChargeType,
		Duration:       params.Duration,
		Num:            order.PriceEntity.Num,
		CouponIDList:   params.CouponIDList,
		ExpandDataDisk: expandDataDiskSize,
	}
	user, ss, order.RuntimeEntity.TakeEffectAt, order.RuntimeEntity.ExpiredAt, err = bcs.beforeCreateOrderFillInformation(&ins, params.Uid, priceInfo)
	if err != nil {
		bcs.log.WithError(err).Error("fill information failed")
		return
	}
	order.RuntimeEntity.OrderUUID = orderUUID
	order.RuntimeEntity.PaygPrice = priceInfo.PaygPrice
	order.MachineEntity.MachineSku = ss.MachineSku

	renewalOrder = &bcm.Order{
		UID:           user.ID,
		SubName:       params.SubName,
		Username:      user.Username,
		UserPhone:     user.Phone,
		UUID:          orderUUID,
		ProductUUID:   params.InstanceUUID.String(),
		RuntimeUUID:   order.RuntimeUUID,
		RuntimeType:   order.RuntimeType,
		MachineID:     order.MachineID,
		Status:        constant.OrderStatusUnpaid,
		OrderType:     constant.OrderTypeRenewalInstance,
		ChargeType:    priceInfo.ChargeType,
		DealPrice:     priceInfo.ToFinallyDealPrice(),
		PriceEntity:   priceInfo,
		RuntimeEntity: order.RuntimeEntity,
		MachineEntity: order.MachineEntity, // 续费时机器信息保持一致
	}

	renewalOrder, err = bcs.bc.CreateOrder(renewalOrder)
	if err != nil {
		bcs.log.WithError(err).Warn("create order failed")
	}
	return
}

// changeInstance 实例升降配置
func (bcs *BCServer) changeInstance(params *bcm.ChangeInstanceParams) (err error) {
	if params == nil {
		bcs.log.WithField("params", params).Error("Params is nil")
		err = businesserror.ErrInternalError
		return
	}

	if err = params.Check(); err != nil {
		bcs.log.WarnE(err, "change instance check params failed. params:%+v", params)
		return
	}

	var (
		instance   im.Instance
		userMember *um.UserMember
		user       *um.User
		now        = time.Now()
		priceInfo  *bcm.PriceInfo
		oldOrder   *bcm.Order
	)

	err = bcs.changeInstanceCheck(constant.InstanceUUIDType(params.InstanceUUID))
	if err != nil {
		return
	}

	instance, err = bcs.instance.GetInstance(constant.InstanceUUIDType(params.InstanceUUID))
	if err != nil {
		bcs.log.WithError(err).WithField("instanceUUID", params.InstanceUUID).Warn("get instance failed.")
		return
	}

	if instance.UID != params.Uid {
		return businesserror.ErrInstanceSubUserAuthFailed
	}

	machine, err := bcs.machine.Get(instance.MachineID)
	if err != nil {
		bcs.log.WithError(err).WithField("instanceUUID", params.InstanceUUID).WithField("machine_id", instance.MachineID).Warn("get machine failed.")
		return
	}

	// 实例到期判断机器上架状态是否为上架不可开机
	ok := machine.Online.IsMachineOnLineOk(params.OperateType, "")
	if !ok {
		if params.GpuNum > instance.ReqGPUAmount {
			bcs.log.Warn("machine online is not start up, required gpu num greater than instance binged gpu num.")
			err = businesserror.ErrGpuStockNotEnough
		}
	}

	if !instance.IsInstanceCanUpdateConfig() {
		err = businesserror.ErrInstanceConfigNotMeetCondition
		return
	}

	oldOrder, err = bcs.bc.GetOrder(instance.OrderUUID)
	if err != nil {
		bcs.log.WithError(err).Error("get order failed")
		return
	}

	user, err = bcs.user.FindByUserId(instance.UID)
	if err != nil {
		return
	}

	// 获取价格, 补全信息
	priceInfo = &bcm.PriceInfo{
		Num:            params.GpuNum,
		MachineID:      instance.MachineID,
		ChargeType:     constant.ChargeTypePayg,
		Duration:       1,
		ExpandDataDisk: oldOrder.PriceEntity.ExpandDataDisk,
		CouponIDList:   params.CouponIDList,
	}

	userMember, err = bcs.user.GetUserMemberInfoByUid(user.ID)
	if err != nil {
		return
	}

	err = bcs.getPrice(&getPriceParams{
		uid:         instance.UID,
		priceInfo:   priceInfo,
		levelName:   userMember.MemberLevel,
		productType: constant.ProductTypeInstance,
	})
	if err != nil {
		bcs.log.WithError(err).Warn("get real price failed")
		return
	}

	// 获取snapshot,补全image信息
	snapshot, err := bcs.instance.SnapshotWithRightPaygPriceForFrontend(constant.InstanceUUIDType(params.InstanceUUID))
	if err != nil {
		return
	}

	var instanceInfo constant.CreateContainerTaskRequest

	orderUUID := libs.RandNumberString()
	instanceInfo = constant.CreateContainerTaskRequest{
		Name:         instance.Name,
		Description:  instance.Description,
		RuntimeType:  constant.ContainerRuntimeOfInstance,
		Image:        snapshot.Image.ImageName,
		MachineID:    instance.MachineID,
		UID:          instance.UID,
		OrderUUID:    orderUUID,
		ProductUUID:  instance.InstanceUUID.String(),
		RuntimeUUID:  instance.RuntimeUUID,
		ReqGPUAmount: params.GpuNum,
		ChargeType:   constant.ChargeTypePayg,
		PaygPrice:    priceInfo.PaygPrice,
	}

	ss := new(bcm.MachineSnapShot)
	ss, err = bcs.getMachineSnapshot(instanceInfo.MachineID, nil, int64(params.GpuNum))
	if err != nil {
		bcs.log.Warn("get machine snapshot failed")
		return
	}

	newOrder := &bcm.Order{
		UID:           user.ID,
		SubName:       params.SubName,
		Username:      user.Username,
		UserPhone:     user.Phone,
		UUID:          orderUUID,
		ProductUUID:   params.InstanceUUID,
		RuntimeUUID:   instance.RuntimeUUID,
		RuntimeType:   constant.ContainerRuntimeOfInstance,
		MachineID:     instance.MachineID,
		Status:        constant.OrderStatusSuccess,
		OrderType:     params.OperateType,
		ChargeType:    instance.ChargeType,
		DealPrice:     priceInfo.ToFinallyDealPrice(),
		PriceEntity:   priceInfo,
		RuntimeEntity: &instanceInfo,
		MachineEntity: ss,
		CreatedAt:     now,
		UpdatedAt:     now,
		PayAt:         &now,
	}

	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) error {
		newOrder, err = bcs.bc.CreateOrderNeedTx(tx, newOrder)
		if err != nil {
			bcs.log.WithError(err).Warn("instance update config create order failed")
			return err
		}

		if len(priceInfo.CouponIDList) != 0 {
			err = bcs.bc.UserCouponUse(tx, priceInfo.CouponIDList[0])
			if err != nil {
				bcs.log.WithError(err).Warn("instance update config create order failed")
				return err
			}
		}

		err = bcs.bc.DataDiskChargingChange(tx, newOrder.ToDataDiskChargingChangeParams())
		if err != nil {
			bcs.log.WithError(err).Warn("ddc change failed")
			return err
		}

		return nil
	})
	if err != nil {
		return
	}

	// 通知container更改相应参数
	opt := constant.OptInstanceReq{
		InstanceUUID: params.InstanceUUID,
		OptType:      constant.InstanceChangeConfigByOrderOpt,
		Caller:       constant.NewOptCallerUser(user.ID),
		Payload:      orderUUID,
	}
	err = bcs.instance.OperateInstance(opt)
	if err != nil {
		bcs.log.WarnE(err, "instance change config by order opt call operate instance failed.")
	}
	return
}

// cloneInstanceCheck 克隆实例校验
func (bcs *BCServer) cloneInstanceCheck(instanceUUID, dstMachineID string, isAdmin bool) (err error) {
	var (
		srcInstance im.Instance
		dstMachine  *mm.Machine
	)

	// 源实例
	// 先判断实例是否在保存镜像，若是正在saving，则返回错误
	ok, err := bcs.instance.InstanceCheckImageIsSaving(constant.NewInstanceUUIDType(instanceUUID))
	if err != nil {
		return err
	}
	if ok {
		return businesserror.ErrPrivateImageStatusIsSaving
	}
	srcInstance, err = bcs.instance.GetInstance(constant.InstanceUUIDType(instanceUUID))
	if err != nil {
		return
	}

	// 本机
	srcMachine, err := bcs.machine.Get(srcInstance.MachineID)
	if err != nil {
		bcs.log.WithField("machineID", srcInstance.MachineID).ErrorE(err, "srcInstance migrate: get src dstMachine failed")
		return err
	}
	if !srcMachine.IsIntranetOK() || (!srcMachine.Online.IsMachineOnLineOk("", constant.InstanceCloneOpt) && !isAdmin) {
		return businesserror.ErrMachineCanNotCloneFormat.New().Format("源")
	}

	// 目标机器
	dstMachine, err = bcs.machine.Get(dstMachineID)
	if err != nil {
		bcs.log.WithFields(logger.Fields{"instance_uuid": instanceUUID, "machine_id": dstMachineID}).WarnE(err, "get dstMachine by dstMachine id failed")
		return
	}
	if !dstMachine.IsIntranetOK() || !dstMachine.Online.IsMachineOnLineOk(constant.OrderTypeCloneInstance, constant.InstanceCloneOpt) {
		return businesserror.ErrMachineCanNotCloneFormat.New().Format("目标")
	}
	checkArch := func(srcMachine, dstMachine *machineModel.Machine) error {
		if srcMachine.CpuArch == dstMachine.CpuArch {
			// 如果 CPU 架构相同且是 x86，则不检查 ChipCorp 直接返回成功
			if srcMachine.CpuArch == string(constant.CpuArchX86) {
				return nil
			}
			// 如果 CPU 架构相同并且 ChipCorp 也相同，则不报错
			if srcMachine.ChipCorp == dstMachine.ChipCorp {
				return nil
			}
			// CPU 架构相同但不是 x86 并且 ChipCorp 不同，报错
			return businesserror.ErrMachineChipCorpCpuArch
		}
		// CPU 架构不同，直接报错
		return businesserror.ErrMachineChipCorpCpuArch
	}
	if err = checkArch(srcMachine, dstMachine); err != nil {
		return err
	}
	// 数据中心
	sameDataCenter, err := regionModel.CheckRegionIsInSameDataCenter(srcInstance.RegionSign, dstMachine.RegionSign)
	if err != nil {
		return businesserror.ErrInstanceNotMeetCloneCondition
	}

	// 迁移条件
	if !sameDataCenter {
		return businesserror.ErrInstanceRegionDifferent
	}
	if srcInstance.Status != constant.InstanceShutdown {
		return businesserror.ErrCloneInstanceNotShutdown
	}
	containerUsageInfo, _ := bcs.container.GetContainerUsageInfo(srcInstance.RuntimeUUID)
	if containerUsageInfo.DiskHealthStatus != constant.DiskHealthNormal {
		return businesserror.ErrCloneInstanceDiskAlerts
	}

	exist, err := bcs.bc.ExistUnpaidChangeTypeOrder(instanceUUID)
	if err != nil {
		bcs.log.WithError(err).WithField("instanceUUID", instanceUUID).Error("check unpaid order failed")
		return
	}
	if exist {
		err = businesserror.ErrOrderChangeChargeTypeUnpaidCanNotMoreThanOne
		return
	}

	// operate times limit
	if !isAdmin {
		user, err := bcs.user.FindByUserId(srcInstance.UID)
		if err != nil {
			return err
		}

		ftKey := kv_plugin.FileTransferCloneContainerUserLimitGenerateUUID(user.UUID, time.Now().Day())
		val, err := bcs.kv.InternalGet(ftKey)
		if err != nil {
			bcs.log.WithField("key", ftKey).ErrorE(err, "bcs.kv.InternalGet failed")
			return businesserror.ErrServerBusy
		}
		times := kv_plugin.FileTransferUserLimitGetValue(val)
		times++
		if times > user.Setting.GetMaxClonePerDay() {
			return businesserror.ErrUserCloneInstanceAtMostTimesPerDay.New().Format(user.Setting.GetMaxClonePerDay())
		}
	}

	return
}

// paygToPrepay 按量计费转包卡 - 创建订单
func (bcs *BCServer) paygToPrepay(params *bcm.PaygToPrepayCreateOrderParams) (newOrder *bcm.Order, err error) {
	if params == nil {
		bcs.log.WithField("params", params).Error("Params is nil")
		err = businesserror.ErrInternalError
		return
	}

	if err = params.Check(); err != nil {
		bcs.log.WarnE(err, "change instance payg to prepay check params failed. params:%+v", params)
		return
	}

	var (
		ins          im.Instance
		wallet       *bcm.UserWallet
		order        *bcm.Order
		ss           *bcm.MachineSnapShot
		charging     *bcm.Charging
		newOrderUUID = libs.RandNumberString()
		priceInfo    *bcm.PriceInfo
	)

	// instance, user
	ins, err = bcs.instance.GetInstance(params.InstanceUUID)
	if err != nil {
		bcs.log.WithError(err).Error("get instance failed")
		return
	}

	if ins.UID != params.Uid {
		return nil, businesserror.ErrInstanceSubUserAuthFailed
	}

	machine, err := bcs.machine.Get(ins.MachineID)
	if err != nil {
		bcs.log.WithField("err", err).WithField("machine_id", ins.MachineID).Warn("get machine failed")
		return
	}

	// 包卡判断机器上架状态是否为上架不可开机
	ok := machine.Online.IsMachineOnLineOk(params.TargetChargeType.ToOrderType(), "")
	if !ok {
		err = businesserror.ErrMachineGpuNumNotEnough
		return
	}
	err = bcs.changeChargeTypeCheck(ins.UID, params.InstanceUUID)
	if err != nil {
		bcs.log.WithError(err).Trace("changeChargeTypeCheck failed")
		return
	}

	// 获取原始订单
	order, err = bcs.bc.GetOrder(ins.OrderUUID)
	if err != nil {
		bcs.log.WithError(err).Error("get order failed")
		return
	}
	if !order.ChargeType.IsPayg() {
		err = businesserror.ErrOrderChangeToPrepaySourceTypeNotPayg
		return
	}

	// 获取价格
	priceInfo = &bcm.PriceInfo{
		MachineID:      ins.MachineID,
		ChargeType:     params.TargetChargeType,
		Duration:       params.Duration,
		Num:            ins.ReqGPUAmount,
		CouponIDList:   params.CouponIDList,
		ExpandDataDisk: order.PriceEntity.ExpandDataDisk,
	}

	// 替换、补全新订单信息
	order.RuntimeEntity.OrderUUID = newOrderUUID
	order.RuntimeEntity.ChargeType = priceInfo.ChargeType
	order.RuntimeEntity.PaygPrice = priceInfo.PaygPrice
	_, ss, order.RuntimeEntity.TakeEffectAt, order.RuntimeEntity.ExpiredAt, err = bcs.beforeCreateOrderFillInformation(nil, order.UID, priceInfo)
	if err != nil {
		bcs.log.Warn("beforeCreateOrderFillInformation failed")
		return
	}

	// 检查是否符合价格
	wallet, err = bcs.bc.GetWalletForCreateOrder(ins.UID, constant.ProductTypeInstance, params.TargetChargeType, machine.MachineID, nil, "")
	if err != nil {
		bcs.log.WithError(err).Error("get wallet failed")
		return
	}
	if priceInfo.ToFinallyDealPrice() > wallet.Assets+wallet.VoucherBalance {
		// 根据需求，只检查余额是否满足接下来的包卡订单。至于如果有正在计费的按量付费，允许最终钱包金额为负数
		err = businesserror.ErrBillBalanceNotEnough
		return
	}

	// todo： 关于gpu预留。 如果实例按量付费正在运行， 那么此处不触发预留操作。可以创建订单，但是在订单待支付的5分钟期间，如果用户操作关机，按照现在的逻辑，
	// 会直接释放掉，付款后会导致没有gpu。需要一个机制来保证在有转包卡的订单的时候，按量关机不会释放gpu

	// gpu预留，gpu模块已经支持重复预留
	charging, err = bcs.bc.ChargingGet(ins.RuntimeUUID, ins.OrderUUID)
	if err != nil {
		bcs.log.WithError(err).Error("get charging ingo failed")
		return
	}

	if charging == nil || (ins.Status == constant.InstanceRunning && !ins.HaveGpuResources) {

		var ok bool
		ok, err = bcs.gpuStock.Reserve(&coreGpuStocModel.GpuReserve{
			UUID:        ins.InstanceUUID.String(),
			RuntimeUUID: ins.RuntimeUUID.String(),
			MachineID:   ins.MachineID,
			GpuNum:      ins.ReqGPUAmount,
			Priority:    constant.InstanceLevel,
			Timestamp:   tsc.Timestamp(),
		})
		if err != nil {
			bcs.log.WithError(err).Warn("check machine failed|")
			return
		}
		if !ok {
			return
		}

		defer func() {
			if err != nil {
				errE := bcs.gpuStock.Release(&coreGpuStocModel.GpuRelease{
					RuntimeUUID: ins.RuntimeUUID.String(),
					MachineID:   ins.MachineID,
					DebugMsg:    "paygToPrepay",
					Timestamp:   tsc.Timestamp(),
				})
				if errE != nil {
					bcs.log.WithField("instanceUUID", ins.InstanceUUID).WithError(errE).Error("create order for payg to other: release machine gpu failed.")
				}
			} else {
				errE := bcs.gpuStock.ReserveCommit(coreapi.GpuReserveCommitReq{
					RuntimeUUID: ins.RuntimeUUID.String(),
					MachineID:   ins.MachineID,
					Timestamp:   tsc.Timestamp(),
				})
				if errE != nil {
					bcs.log.WithField("instanceUUID", ins.InstanceUUID.String()).WithError(errE).Error("reserve commit gpu failed.")
				}
			}
		}()
	}

	newOrder = &bcm.Order{
		UID:           order.UID,
		SubName:       ins.SubName,
		Username:      order.Username,
		UserPhone:     order.UserPhone,
		UUID:          newOrderUUID,
		ProductUUID:   order.ProductUUID,
		RuntimeUUID:   order.RuntimeUUID,
		RuntimeType:   order.RuntimeType,
		MachineID:     order.MachineID,
		Status:        constant.OrderStatusUnpaid,
		OrderType:     priceInfo.ChargeType.ToOrderType(),
		ChargeType:    priceInfo.ChargeType,
		DealPrice:     priceInfo.ToFinallyDealPrice(),
		PriceEntity:   priceInfo,
		RuntimeEntity: order.RuntimeEntity,
		MachineEntity: ss,
	}

	newOrder, err = bcs.bc.CreateOrder(newOrder)
	if err != nil {
		bcs.log.WithError(err).Warn("create order failed")
		return
	}
	return
}

// prepayToPayg 包卡转按量计费
func (bcs *BCServer) prepayToPayg(params *bcm.PrepayToPaygParams) (preview *bcm.CreateOrderForPrepayToPaygResponse, err error) {
	var (
		l = bcs.log.WithField("params", params)
		//usedDuration                    int64
		ins                             im.Instance
		user                            *um.User
		userMember                      *um.UserMember
		ss                              *bcm.MachineSnapShot
		now                             = time.Now()
		newOrderUUIDRefund              = libs.RandNumberString()
		newOrderUUIDCreatePayg          = libs.RandNumberString()
		order                           *bcm.Order // 生效中订单
		bill                            *bcm.Bill  //
		runtimeEntityRefund             constant.CreateContainerTaskRequest
		runtimeEntityCreatePayg         constant.CreateContainerTaskRequest
		lastExpiredAt                   *time.Time
		orderCostAsset                  int64                       // 应扣除金额
		orderCostAssetInstance          int64                       // 应扣除金额 - 实例
		orderCostAssetDataDisk          int64                       // 应扣除金额 - 数据盘
		orderRefundAsset                int64                       // 生效中订单应退回金额
		unUsedOrderList                 []bcm.Order                 // 未生效的续费订单
		unUsedOrderUUIDList             []string                    // 未生效的续费订单的uuid
		unUsedOrderUUIDMap              = make(map[string]struct{}) // 未生效的续费订单的uuid的map，为了不出错，多加几层校验
		unUsedRefundAsset               int64                       // 未生效续费订单应退回的余额
		prepayExpandOrderList           []bcm.Order
		prepayDataDiskExpandRefundAsset int64     // 预付费扩容数据盘退款
		refundAsset                     int64     // 总的应退回金额， = orderRefundAsset + unUsedRefundAsset
		expiredAt                       time.Time // 退款的结束时间 退款的周期是 now -- 最后一个订单的到期时间，也是实例到期时间
		comeToExpired                   bool      // 是否到期
		existDDA                        bool
		machine                         *mm.Machine
		refundList                      = make([]bcm.PrepayToPaygPrepayRefundInfo, 0)
	)

	// 获取原始实例相关信息
	ins, err = bcs.instance.GetInstance(params.InstanceUUID)
	if err != nil {
		bcs.log.WithError(err).WithField("instanceUUID", params.InstanceUUID).Error("get instance failed")
		return
	}

	if params.Uid != 0 {
		if ins.UID != params.Uid {
			return nil, businesserror.ErrInstanceSubUserAuthFailed
		}
	}

	// 当前mysql事物执行时间过长，会有数据不一致的情况, 入口加锁，防止重复执行
	lock, err := bcs.setLockForPrepayToPayg(params.InstanceUUID.String())
	if err != nil {
		return
	}
	defer lock.UnLock()

	if ins.ExpiredAt.Valid {
		expiredAt = ins.ExpiredAt.Time
		if ins.ExpiredAt.Time.Before(now) {
			comeToExpired = true
		}
	}

	// 机器信息 -> 最新sku信息
	machine, err = bcs.getMachine(ins.MachineID)
	if err != nil {
		bcs.log.WithError(err).WithField("machineID", order.MachineEntity).Error("get machine failed")
		return
	}

	order, err = bcs.bc.GetOrder(ins.OrderUUID)
	if err != nil {
		bcs.log.WithError(err).WithField("orderUUID", ins.OrderUUID).Error("get order failed")
		return
	}

	bill, err = bcs.bc.GetBillFirstByOrderUUID(order.UID, ins.OrderUUID)
	if err != nil {
		bcs.log.WithError(err).Error("get bill list failed")
		return
	}

	user, err = bcs.user.FindByUserId(ins.UID)
	if err != nil {
		bcs.log.WithError(err).WithField("uid", ins.UID).Error("get user failed")
		return
	}

	userMember, err = bcs.user.GetUserMemberInfoByUid(user.ID)
	if err != nil {
		return
	}

	// 时间计算
	if order.RuntimeEntity.ExpiredAt == nil {
		bcs.log.WithField("orderUUID", ins.OrderUUID).Error("expiredAt time should not nil")
		err = businesserror.ErrInternalError
		return
	}
	if order.RuntimeEntity.TakeEffectAt != nil {
		lastExpiredAt = order.RuntimeEntity.TakeEffectAt
	} else {
		lastExpiredAt, err = libs.GetInstanceLastExpireTime(order.RuntimeEntity.ExpiredAt, order.ChargeType, order.PriceEntity.Duration)
		if err != nil {
			bcs.log.WithError(err).Error("get instance last expiredAt time failed")
			return
		}
	}

	// 实例到期后
	if comeToExpired {
		orderCostAsset = order.DealPrice
		refundList = append(refundList, bcm.PrepayToPaygPrepayRefundInfo{
			OrderUUID:    order.UUID,
			OrderType:    order.OrderType,
			ProductType:  constant.ProductTypeInstance,
			StartAt:      *lastExpiredAt,
			EndAt:        expiredAt,
			ExpiredAt:    expiredAt,
			Duration:     expiredAt.Unix() - lastExpiredAt.Unix(),
			Asset:        order.DealPrice,
			PayPyVoucher: bill.PayByVoucher,
			PayByBalance: bill.PayByBalance,
			UsedAsset:    order.DealPrice,
			RefundAsset:  0,
		})
	} else {
		// 1.生效中订单  获取生效中订单的实际花费
		orderCostAsset, orderCostAssetInstance, orderCostAssetDataDisk, err = bcs.prepayToPaygGetCostAsset(machine, *lastExpiredAt, now, order, userMember.MemberLevel)
		if err != nil {
			bcs.log.WithError(err).Error("change charge type to payg: get active order's cost failed")
			return
		}

		// 计算生效中订单要退的钱
		if orderCostAsset > bill.Asset {
			orderCostAsset = bill.Asset
			orderRefundAsset = 0
		} else if orderCostAsset <= bill.PayByVoucher {
			orderRefundAsset = bill.PayByBalance
		} else {
			orderRefundAsset = bill.Asset - orderCostAsset
		}
		if orderRefundAsset < 0 {
			orderRefundAsset = 0
		}
		refundList = append(refundList, bcm.PrepayToPaygPrepayRefundInfo{
			OrderUUID:    order.UUID,
			OrderType:    order.OrderType,
			ProductType:  constant.ProductTypeInstance,
			StartAt:      *lastExpiredAt,
			EndAt:        now,
			ExpiredAt:    *order.RuntimeEntity.ExpiredAt,
			Duration:     now.Unix() - lastExpiredAt.Unix(),
			Asset:        order.DealPrice,
			PayPyVoucher: bill.PayByVoucher,
			PayByBalance: bill.PayByBalance,
			UsedAsset:    orderCostAsset,
			UsedInstance: orderCostAssetInstance,
			UsedDataDisk: orderCostAssetDataDisk,
			RefundAsset:  orderRefundAsset,
		})

		// 2.处理未生效订单。 统计退款金额，标记为已生效或者已退款
		unUsedOrderList, err = bcs.bc.GetOrderUnused(ins.RuntimeUUID)
		if err != nil {
			bcs.log.WithError(err).WithField("runtimeUUID", ins.RuntimeUUID).Error("get unused order failed")
			return
		}
		for _, v := range unUsedOrderList {
			unUsedOrderUUIDList = append(unUsedOrderUUIDList, v.UUID)
			unUsedOrderUUIDMap[v.UUID] = struct{}{}

			var renewalBill *bcm.Bill
			renewalBill, err = bcs.bc.GetBillFirstByOrderUUID(v.UID, v.UUID)
			if err != nil || renewalBill == nil {
				bcs.log.WithField("orderUUID", v.UUID).ErrorE(err, "get bill failed")
				return nil, err
			}

			refundList = append(refundList, bcm.PrepayToPaygPrepayRefundInfo{
				OrderUUID:    v.UUID,
				OrderType:    v.OrderType,
				ProductType:  constant.ProductTypeInstance,
				StartAt:      *v.RuntimeEntity.TakeEffectAt,
				EndAt:        now,
				ExpiredAt:    *v.RuntimeEntity.ExpiredAt,
				Duration:     0,
				Asset:        v.DealPrice,
				PayPyVoucher: renewalBill.PayByVoucher,
				PayByBalance: renewalBill.PayByBalance,
				UsedAsset:    0,
				UsedInstance: 0,
				UsedDataDisk: 0,
				RefundAsset:  renewalBill.PayByBalance,
			})
			unUsedRefundAsset += v.PayByBalance
		}

		// 3.处理预付费数据盘扩容订单
		if len(ins.AdditionalEntity.PrepayExpandDataDiskOrderUUID) != 0 {
			prepayExpandOrderList, err = bcs.bc.GetOrderByUUIDList(ins.AdditionalEntity.PrepayExpandDataDiskOrderUUID)
			if err != nil {
				l.ErrorE(err, "get prepayExpandOrderList by uuid list failed")
				return
			}

			for _, v := range prepayExpandOrderList {
				if v.PriceEntity == nil || !v.PriceEntity.PrepayExpandDataDisk ||
					v.PriceEntity.DataDiskInfo == nil || !v.PriceEntity.DataDiskInfo.ExpiredAt.After(now) {
					continue
				}

				// 订单还在生效中
				var prepayExpandRefund int64
				var prepayExpandBill *bcm.Bill
				var prepayExpandCost int64
				prepayExpandBill, err = bcs.bc.GetBillFirstByOrderUUID(v.UID, v.UUID)
				if err != nil {
					l.WithField("orderUUID", v.UUID).ErrorE(err, "get bill failed")
					continue
				}

				// 当前订单消费
				prepayExpandCost, err = bcs.prepayToPaygGetCostAssetForPrepayExpandDataDiskOrder(machine, now, &v, userMember.MemberLevel)
				if err != nil {
					l.ErrorE(err, "prepayToPaygGetCostAssetForPrepayExpandDataDiskOrder failed")
					return
				}

				// 当前订单退款
				if prepayExpandCost > prepayExpandBill.Asset {
					prepayExpandCost = prepayExpandBill.Asset
					prepayExpandRefund = 0
				} else if prepayExpandCost <= prepayExpandBill.PayByVoucher {
					prepayExpandRefund = prepayExpandBill.PayByBalance
				} else {
					prepayExpandRefund = prepayExpandBill.Asset - prepayExpandCost
				}
				if prepayExpandRefund < 0 {
					prepayExpandRefund = 0
				}

				refundList = append(refundList, bcm.PrepayToPaygPrepayRefundInfo{
					OrderUUID:    v.UUID,
					OrderType:    v.OrderType,
					ProductType:  constant.ProductTypeDataDisk,
					StartAt:      *v.PriceEntity.DataDiskInfo.TakeEffectAt,
					EndAt:        now,
					ExpiredAt:    *v.PriceEntity.DataDiskInfo.ExpiredAt,
					Duration:     now.Unix() - v.PriceEntity.DataDiskInfo.TakeEffectAt.Unix(),
					Asset:        v.DealPrice,
					PayPyVoucher: prepayExpandBill.PayByVoucher,
					PayByBalance: prepayExpandBill.PayByBalance,
					UsedAsset:    prepayExpandCost,
					UsedInstance: 0,
					UsedDataDisk: prepayExpandCost,
					RefundAsset:  prepayExpandRefund,
				})
				prepayDataDiskExpandRefundAsset += prepayExpandRefund
			}
		}

		// 需要退的总金额
		refundAsset = orderRefundAsset + unUsedRefundAsset + prepayDataDiskExpandRefundAsset
	}

	err = bcs.changeChargeTypeCheck(ins.UID, params.InstanceUUID)
	if err != nil {
		bcs.log.WithError(err).Trace("changeChargeTypeCheck failed")
		return
	}

	var (
		isRunning          bool
		newOrderRefund     *bcm.Order
		priceInfo          *bcm.PriceInfo
		newOrderCreatePayg *bcm.Order
		newBillRefund      *bcm.Bill
		dda                *ddsModel.DataDiskAllocate
		expandDataDiskSize int64
	)

	dda, err = bcs.dds.GetDDA(order.MachineID, order.ProductUUID)
	if err != nil {
		l.ErrorE(err, "get dda failed")
		return
	}
	if dda != nil {
		expandDataDiskSize = dda.DiskAllocate
	}

	// 获取价格, 补全信息
	priceInfo = &bcm.PriceInfo{
		MachineID:      order.MachineID,
		ChargeType:     constant.ChargeTypePayg,
		Duration:       1,
		Num:            order.PriceEntity.Num,
		ExpandDataDisk: expandDataDiskSize,
	}

	// 补全runtime信息
	runtimeEntityRefund = *order.RuntimeEntity
	runtimeEntityRefund.OrderUUID = newOrderUUIDRefund
	user, ss, runtimeEntityRefund.TakeEffectAt, runtimeEntityRefund.ExpiredAt, err = bcs.beforeCreateOrderFillInformation(&ins, order.UID, priceInfo)
	if err != nil {
		bcs.log.WithError(err).Warn("beforeCreateOrderFillInformation failed")
		return nil, err
	}
	runtimeEntityRefund.ChargeType = order.ChargeType
	runtimeEntityRefund.PaygPrice = priceInfo.PaygPrice

	runtimeEntityCreatePayg = *order.RuntimeEntity
	runtimeEntityCreatePayg.OrderUUID = newOrderUUIDCreatePayg
	runtimeEntityCreatePayg.TakeEffectAt = &now
	runtimeEntityCreatePayg.ChargeType = priceInfo.ChargeType
	runtimeEntityCreatePayg.PaygPrice = priceInfo.PaygPrice

	if params.IsPreview {
		preview = &bcm.CreateOrderForPrepayToPaygResponse{
			InstanceName: ins.Name,
			InstanceUUID: ins.InstanceUUID,
			ChargeType:   order.ChargeType,

			UnusedOrderAsset: unUsedRefundAsset,
			RefundAsset:      refundAsset,
			PriceInfo:        *priceInfo,
			RefundList:       refundList,
		}

		for _, v := range refundList {
			preview.TotalPayByBalance += v.PayByBalance
			preview.TotalPayByVoucher += v.PayPyVoucher
			preview.TotalUsedAsset += v.UsedAsset
		}
		return
	}

	// 真正操作
	/*
		执行退款操作
		生成订单
		生成账单，操作钱包
		查看是否正在运行
		如果正在运行，开始按量计费
		返回
	*/

	if !comeToExpired {
		newOrderRefund = &bcm.Order{
			UID:         order.UID,
			SubName:     ins.SubName,
			Username:    user.Username,
			UserPhone:   user.Phone,
			UUID:        newOrderUUIDRefund,
			ProductUUID: order.ProductUUID,
			RuntimeUUID: order.RuntimeUUID,
			RuntimeType: order.RuntimeType,
			Status:      constant.OrderStatusSuccess,
			OrderType:   constant.OrderTypeRefund,
			ChargeType:  order.ChargeType,
			MachineID:   order.MachineID,
			DealPrice:   refundAsset,
			PriceEntity: priceInfo,
			RefundEntity: &bcm.RefundInfo{
				RefundAsset: refundAsset,
				RefundType:  constant.OrderTypeChangePayg,
				OrderUUID:   order.UUID,
				BeginAt:     now,
				EndAt:       expiredAt,
			},
			MachineEntity: ss,
			RuntimeEntity: &runtimeEntityRefund,
			CreatedAt:     now,
			UpdatedAt:     now,
			PayAt:         &now,
		}
	}

	newOrderCreatePayg = &bcm.Order{
		UID:           order.UID,
		SubName:       ins.SubName,
		Username:      user.Username,
		UserPhone:     user.Phone,
		UUID:          newOrderUUIDCreatePayg,
		ProductUUID:   order.ProductUUID,
		RuntimeUUID:   order.RuntimeUUID,
		RuntimeType:   order.RuntimeType,
		Status:        constant.OrderStatusSuccess,
		OrderType:     constant.OrderTypeChangePayg,
		ChargeType:    constant.ChargeTypePayg,
		MachineID:     order.MachineID,
		PriceEntity:   priceInfo,
		MachineEntity: ss,
		RuntimeEntity: &runtimeEntityCreatePayg,
		CreatedAt:     now,
		UpdatedAt:     now,
		PayAt:         &now,
	}

	// 校验机器计费方式
	err = bcs.checkMachineChargeType(newOrderCreatePayg.ChargeType, newOrderCreatePayg.RuntimeEntity.MachineID)
	if err != nil {
		return
	}

	newBillRefund = &bcm.Bill{
		UID:           newOrderCreatePayg.UID,
		UserPhone:     newOrderCreatePayg.UserPhone,
		OrderUUID:     newOrderUUIDRefund,
		ProductUUID:   newOrderCreatePayg.ProductUUID,
		RuntimeUUID:   newOrderCreatePayg.RuntimeUUID,
		Type:          constant.BillTypeRefund,
		SubType:       constant.BillSubTypeRefundToPayg,
		DetailsEntity: &bcm.BillDetail{},
		Asset:         refundAsset,
		PayByBalance:  refundAsset,
		ChargeType:    order.ChargeType,
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	// 检查实例是否正在运行
	isRunning, err = bcs.checkInstanceRunning(order.ProductUUID)
	if err != nil {
		bcs.log.WithError(err).Error("error")
		return
	}

	existDDA, err = bcs.dds.ExistDDA(order.MachineID, order.ProductUUID)
	if err != nil {
		bcs.log.WithError(err).Error("error")
		return
	}

	// 为保证数据库一致性，包卡转按量将走特殊方法进行付费
	afterHook, afterFunc := bcs.getAfterHook(newOrderCreatePayg)
	err = bcs.bc.PrepayToPayg(bcm.PrepayToPaygInnerParams{
		OldOrder:            order,
		OrderRefund:         newOrderRefund,
		OrderCreatePayg:     newOrderCreatePayg,
		OldBill:             bill,
		BillRefund:          newBillRefund,
		UnUsedOrderUUIDList: unUsedOrderUUIDList,
		IsRunning:           isRunning,
		AfterHook:           afterHook,
		AfterFunc:           afterFunc,
		ExistDDA:            existDDA,
	})
	if err != nil {
		bcs.log.WithError(err).Error("bc PrepayToPayg failed")
	}
	return
}

// orderPay 付款
func (bcs *BCServer) orderPay(params *bcm.OrderPayCancelParams) (order *bcm.Order, productUUID string, err error) {
	order, err = bcs.bc.GetOrder(params.OrderUUID)
	if err != nil {
		bcs.log.WithError(err).Warn("get order detail failed")
		return
	}

	err = bcs.orderPayBeforeHandler(params, order)
	if err != nil {
		return
	}

	// 因为一些操作要保证一致性，部分类型订单付款流程，将走特殊方法
	switch order.OrderType {
	case constant.OrderTypeChangeDaily,
		constant.OrderTypeChangeWeekly,
		constant.OrderTypeChangeMonthly,
		constant.OrderTypeChangeYearly:
		defer func() {
			// todo:此处关于gpu的release与commit，有一些问题。对于按量付费正在运行转包卡的情况，如果付款失败，此处会release，会不会错误释放已经占用的gpu记录
			if err != nil {
				bcs.log.WithError(err).Error("orderPayForPaygToPrepay failed")
				err = bcs.gpuStock.Release(&coreGpuStocModel.GpuRelease{
					RuntimeUUID: order.RuntimeUUID.String(),
					MachineID:   order.MachineID,
					DebugMsg:    "order pay",
					Timestamp:   tsc.Timestamp(),
				})
				if err != nil {
					bcs.log.WithError(err).Error("release gpuStock failed")
				}
			} else {
				err = bcs.gpuStock.ReserveCommit(coreapi.GpuReserveCommitReq{
					RuntimeUUID: order.RuntimeUUID.String(),
					MachineID:   order.MachineID,
					Timestamp:   tsc.Timestamp(),
				})
				if err != nil {
					bcs.log.WithError(err).Error("reserve gpuStock failed")
				}
			}
		}()

		err = bcs.orderPayForPaygToPrepay(order)
		if err != nil {
			bcs.log.WithError(err).Error("orderPayForPaygToPrepay failed")
		}
		return
	}

	// 默认付款方式
	newBill := &bcm.Bill{
		UID:            order.UID,
		UserPhone:      order.UserPhone,
		OrderUUID:      order.UUID,
		RuntimeUUID:    order.RuntimeUUID,
		ProductUUID:    order.ProductUUID,
		Type:           order.OrderType.ToBillType(),
		SubType:        order.OrderType.ToBillSubtype(),
		Asset:          order.PriceEntity.ToFinallyDealPrice(),
		ChargeType:     order.ChargeType,
		DetailsEntity:  &bcm.BillDetail{SubName: order.SubName},
		DiscountEntity: order.SetDiscountInfoForBill(),
	}
	if order.OrderType == constant.OrderTypeDataDiskExpandOfPrepay {
		newBill.DetailsEntity.ChargeFrom = order.PriceEntity.DataDiskInfo.TakeEffectAt.Format(constant.FormatTimeString)
		newBill.DetailsEntity.ChargeTo = order.PriceEntity.DataDiskInfo.ExpiredAt.Format(constant.FormatTimeString)
		newBill.DetailsEntity.ExpandSize = order.PriceEntity.ExpandDataDisk
		newBill.DetailsEntity.ExpandChangeSize = order.PriceEntity.DataDiskInfo.ChangeSize
	}

	afterHook, afterFunc := bcs.getAfterHook(order)
	_, _, err = bcs.bc.CreateBillAndUpdateWallet(&bcm.AfterUpdateWalletParams{
		Bill:             newBill,
		AfterOperateType: order.OrderType.ToUpdateWalletOperateType(),
		AfterHook:        afterHook,
		AfterFunc:        afterFunc,
	})
	if err != nil {
		bcs.log.WithError(err).Warn("crete bill failed")
		return
	}
	productUUID = newBill.ProductUUID
	if len(order.MigrateInstanceUUID) != 0 && params.SrcCloneUid == 0 {
		instance, err := bcs.instance.GetInstance(order.MigrateInstanceUUID)
		if err != nil {
			bcs.log.WithField("migrateInstanceUUID", order.MigrateInstanceUUID).WithError(err).Warn("get src instance failed")
			return order, productUUID, err
		}
		params.SrcCloneUid = instance.UID
	}

	err = bcs.orderPayAfterHandler(order, params.IsAdminClone, params.SrcCloneUid)
	return
}

// orderPayForPaygToPrepay 按量转包卡的付款方式
func (bcs *BCServer) orderPayForPaygToPrepay(order *bcm.Order) (err error) {
	var (
		now          = time.Now()
		charging     *bcm.Charging
		instance     im.Instance
		prepayBill   *bcm.Bill
		chargingBill *bcm.Bill
		isCharging   bool
		existDDA     bool
		reserveGpu   = &gs.GpuReserve{
			UUID:        order.ProductUUID,
			RuntimeUUID: order.RuntimeUUID.String(),
			MachineID:   order.MachineID,
			GpuNum:      order.PriceEntity.Num,
			Priority:    constant.InstanceLevel,
		}
	)

	// 正常bill
	prepayBill = &bcm.Bill{
		UID:            order.UID,
		UserPhone:      order.UserPhone,
		OrderUUID:      order.UUID,
		RuntimeUUID:    order.RuntimeUUID,
		ProductUUID:    order.ProductUUID,
		Type:           order.OrderType.ToBillType(),
		SubType:        order.OrderType.ToBillSubtype(),
		Asset:          order.PriceEntity.ToFinallyDealPrice(),
		ChargeType:     order.ChargeType,
		DetailsEntity:  &bcm.BillDetail{SubName: order.SubName},
		DiscountEntity: order.SetDiscountInfoForBill(),
	}

	instance, err = bcs.instance.GetInstance(constant.NewInstanceUUIDType(order.ProductUUID))
	if err != nil {
		return
	}

	// 按量计费情况，如果按量计费正在运行，停止计费
	charging, err = bcs.bc.ChargingGet(order.RuntimeUUID, instance.OrderUUID)
	if err != nil {
		return
	}
	if charging != nil {
		isCharging = true
		chargingBill = &bcm.Bill{
			UID:           charging.UID,
			UserPhone:     charging.UserPhone,
			OrderUUID:     charging.OrderUUID,
			ProductUUID:   charging.ProductUUID,
			RuntimeUUID:   charging.RuntimeUUID,
			Type:          constant.BillTypeCharge,
			SubType:       constant.BillSubTypeChangeChargeType,
			Asset:         libs.AssetRound(charging.GetCostFloating(now), true),
			ChargeType:    charging.Type,
			DetailsEntity: &bcm.BillDetail{SubName: charging.SubName},
			CreatedAt:     now,
			UpdatedAt:     now,
		}
		chargingBill.SetChargeDuration(&charging.LastSettledAt, &now)
		chargingBill.SetDiscountInfo(charging.GetOriginCostFloating(now))
	}

	existDDA, err = bcs.dds.ExistDDA(order.MachineID, order.ProductUUID)
	if err != nil {
		bcs.log.WithError(err).Error("error")
		return
	}

	afterHook, afterFunc := bcs.getAfterHook(order)
	err = bcs.bc.PaygToPrepay(bcm.PaygToPrepayParams{
		PrepayBill:   prepayBill,
		ChargingBill: chargingBill,
		IsCharging:   isCharging,
		GpuReserve:   reserveGpu,
		AfterHook:    afterHook,
		AfterFunc:    afterFunc,
		ExpiredAt:    *order.RuntimeEntity.ExpiredAt,
		ExistDDA:     existDDA,
	})
	if err != nil {
		bcs.log.WithError(err).WithField("orderUUID", order.UUID).Error("PaygToPrepay failed")
	}
	return
}

func (bcs *BCServer) cancelOrder(params *bcm.OrderPayCancelParams) (err error) {
	var (
		order       *bcm.Order
		releaseGpu  bool
		releaseDDA  bool
		releaseSize int64
		isRunning   bool
		lock        *redis.RedisMutex
	)

	order, err = bcs.bc.GetOrder(params.OrderUUID)
	if err != nil {
		bcs.log.WithError(err).Warn("get order detail failed")
		return
	}

	if (params.UID != order.UID) || (params.SubName != "" && params.SubName != order.SubName) {
		return businesserror.ErrResourceAccessAuthFailed
	}

	if !order.CanCancel() {
		return
	}

	// 加锁：更新order锁
	lock, err = bcs.bc.SetLockForUpdateOrder(params.OrderUUID)
	if err != nil {
		return
	}
	defer func() {
		_ = lock.UnLock()
	}()

	switch order.OrderType {
	case constant.OrderTypeCreateInstance, constant.OrderTypeCloneInstance:
		releaseGpu = true
		releaseDDA = true
		releaseSize = order.PriceEntity.ExpandDataDisk
	case constant.OrderTypeDataDiskExpandOfPrepay:
		releaseDDA = true
		releaseSize = order.PriceEntity.DataDiskInfo.ChangeSize
	case constant.OrderTypeRenewalInstance:
		if order.RuntimeEntity.TakeEffectAt.Before(time.Now()) {
			releaseGpu = true
		}
	case constant.OrderTypeChangeDaily,
		constant.OrderTypeChangeWeekly,
		constant.OrderTypeChangeMonthly,
		constant.OrderTypeChangeYearly:

		// 检查实例是否正在运行
		isRunning, err = bcs.checkInstanceRunning(order.ProductUUID)
		if err != nil {
			return
		}
		if !isRunning {
			releaseGpu = true
		}
	case constant.OrderTypeCreateDeploymentContainer:
		releaseGpu = true
	}

	if releaseGpu {
		err = bcs.gpuStock.Release(&coreGpuStocModel.GpuRelease{
			RuntimeUUID: string(order.RuntimeUUID),
			MachineID:   order.RuntimeEntity.MachineID,
			DebugMsg:    "cancel order",
			Timestamp:   tsc.Timestamp(),
		})
		if err != nil {
			bcs.log.WithField("orderUUID", params.OrderUUID).WithError(err).Warn("release machine gpu failed.")
			return
		}
	}

	if releaseDDA {
		var txUUID string
		txUUID, err = bcs.dds.Release(nil, &ddsModel.ReleaseParams{
			MachineID:   order.RuntimeEntity.MachineID,
			ProductUUID: order.ProductUUID,
			ReleaseSize: releaseSize,
		})
		if err != nil {
			bcs.log.WithField("orderUUID", params.OrderUUID).WithError(err).Warn("release machine gpu failed.")
			return
		}
		defer func() {
			if err != nil && txUUID != "" {
				err = bcs.dds.TxRollback(txUUID)
				if err != nil {
					bcs.log.WithField("orderUUID", params.OrderUUID).WithError(err).Warn("release data disk stock rollback failed.")
					return
				}
			} else if err == nil && txUUID != "" {
				err = bcs.dds.TxCommit(txUUID)
				if err != nil {
					bcs.log.WithField("orderUUID", params.OrderUUID).WithError(err).Warn("release data disk stock commit failed.")
					return
				}
			}
		}()
	}

	err = bcs.bc.CancelOrder(params.OrderUUID)
	if err != nil {
		bcs.log.WithError(err).Warn("cancel order failed")
		return
	}

	// todo: zt 这里似乎还有逻辑问题. 关于取消订单后的gpu释放
	if order.OrderType == constant.OrderTypeRenewalInstance {
		err = bcs.unLockForRenewalInstance(order.ProductUUID)
		if err != nil {
			bcs.log.WithError(err).Info("pay order for renewal: unlock failed")
		}
	}
	return
}

// 网盘扩容
func (bcs *BCServer) netDiskExpand(uid, expandCapacityInGB, durationInMonth int, sign constant.RegionSignType) (err error) {
	if expandCapacityInGB > constant.MaxExpandCapacityInGB {
		return businesserror.ErrRegionNetDiskMaxCapacityLimit
	}

	var (
		now                  = time.Now()
		region               *regionModel.Region
		expansionRecord      *rm.NetDiskExpansion // 已经存在的扩容
		newExpansionRecord   *rm.NetDiskExpansion
		originalCapacityInKB int64 // 扩容前的原始容量
		totalExpansionInKB   int64 // 总扩展容量
		totalCapacityInKB    int64 // 总容量 = 总扩展容量 + 地区默认容量
		expiredAt            = libs.GetExceptTime(now, durationInMonth*30)
		costPrice            = constant.NetDiskPricePerGB * int64(expandCapacityInGB) * int64(durationInMonth) // 初始价格
		newOrder             *bcm.Order
		newBill              *bcm.Bill
		user                 *um.User
		newOrderUUID         = libs.RandNumberString()
		msg                  *queue_interface.NewQueueForStorageAgentOnMachine
	)
	/*
		originalCapacityInKB：第一次时，默认容量。 多次时，扩容记录的原始容量+扩容容量。
		totalExpansionInKB：第一次时，本次扩容容量。 多次时，
		totalCapacityInKB：

	*/

	// 校验用户状态，网盘挂载权限
	user, err = bcs.user.FindByUserId(uid)
	if err != nil {
		bcs.log.WithError(err).WithField("uid", uid).Error("get user failed")
		return
	}
	if user.Status == um.Disable || !user.MountNetDiskAuthority {
		return businesserror.ErrAuthorizeFailed
	}

	// 检查region状态，nfs状态
	region, err = bcs.region.GetRegionDetail(sign)
	if err != nil {
		bcs.log.WithError(err).WithField("sign", sign).Error("get region failed")
		return
	}
	if !region.IsNFSAvailable {
		return businesserror.ErrRegionNetDiskUnavailable
	}

	// 第一次扩容，扩容容量的计算
	originalCapacityInKB = region.DefaultUserQuotaInByte                   // 扩容前原始容量
	totalExpansionInKB = int64(expandCapacityInGB) * constant.GInKB        // 总扩容容量
	totalCapacityInKB = totalExpansionInKB + region.DefaultUserQuotaInByte // 扩容后总容量

	// 检查是否有生效中的扩容
	expansionRecord, err = bcs.region.GetExpansionRecordActive(uid, sign)
	if err != nil {
		bcs.log.WithError(err).WithField("uid", uid).WithField("sign", sign).Error("get expansionRecord failed")
		return
	}
	if expansionRecord != nil {
		// 当有生效中的扩容时，因为要对齐时间，到期时间，价格，容量等全部要重新算
		originalCapacityInKB = expansionRecord.OriginalCapacity + expansionRecord.ExpandCapacity // 本次原始 = 上次原始 + 上次扩容
		totalExpansionInKB += expansionRecord.TotalExpandCapacity                                // 本次总扩容 = 本次扩容 + 上次总扩容
		totalCapacityInKB = totalExpansionInKB + region.DefaultUserQuotaInByte                   // 总容量 = 本次总扩容 + 默认容量
		if totalExpansionInKB > constant.MaxExpandCapacityInKB {
			return businesserror.ErrRegionNetDiskMaxCapacityLimit
		}

		expiredAt = expansionRecord.ExpiredAt
		costPrice = libs.AssetRound(libs.ExpandGetCost(now, expiredAt, expandCapacityInGB), false)
	}

	// 校验余额
	err = bcs.netDiskOperatePayCheck(uid, costPrice)
	if err != nil {
		return
	}

	// 创建订单，账单，付款
	newOrder = &bcm.Order{
		UID:           uid,
		Username:      user.Username,
		UserPhone:     user.Phone,
		UUID:          newOrderUUID,
		Status:        constant.OrderStatusSuccess,
		OrderType:     constant.OrderTypeNetDiskExpand,
		ChargeType:    constant.ChargeTypeMonthly,
		PriceEntity:   &bcm.PriceInfo{OriginPrice: costPrice, DealPrice: costPrice},
		PayByBalance:  costPrice,
		DealPrice:     costPrice,
		DetailsEntity: map[string]interface{}{"region_sign": region.Sign.String()},
		CreatedAt:     now,
		UpdatedAt:     now,
		PayAt:         &now,
	}
	newBill = &bcm.Bill{
		UID:           uid,
		UserPhone:     user.Phone,
		OrderUUID:     newOrderUUID,
		Type:          constant.BillTypeCharge,
		SubType:       constant.BillSubTypeNetDiskExpand,
		Asset:         costPrice,
		PayByBalance:  costPrice,
		ChargeType:    constant.ChargeTypeMonthly,
		DetailsEntity: &bcm.BillDetail{RegionSign: region.Sign, RegionName: region.Name, DateCenter: region.DataCenter},
		CreatedAt:     now,
		UpdatedAt:     now,
		ConfirmAt:     &now,
	}

	msg, err = bcs.region.GetSetQuotaMessage(sign, uid, totalCapacityInKB, messenger.NetDiskAdminSetQuotaType)

	err = bcs.bc.CreateOrderBillAndUpdateWallet(bcm.CreateOrderBillAndUpdateWalletParams{
		NewOrder:  newOrder,
		NewBill:   newBill,
		OpType:    constant.UpdateWalletSkip,
		AfterHook: msg,
	})
	if err != nil {
		bcs.log.WithError(err).Warn("create order bill failed")
		return
	}

	// 创建新扩容记录，删除老扩容记录（如果有）
	newExpansionRecord = &rm.NetDiskExpansion{
		UID:                 uid,
		RegionSign:          region.Sign,
		RegionName:          region.Name,
		OrderUUID:           newOrderUUID,
		OriginalCapacity:    originalCapacityInKB,
		ExpandCapacity:      int64(expandCapacityInGB) * constant.GInKB,
		TotalExpandCapacity: totalExpansionInKB,
		ExpiredAt:           expiredAt,
		Active:              true,
		CreatedAt:           now,
		UpdatedAt:           now,
	}

	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
		err = bcs.region.CreateExpansionRecord(tx, newExpansionRecord)
		if err != nil {
			bcs.log.WithField("record", newExpansionRecord).WithError(err).Warn("create record failed")
			return err
		}

		if expansionRecord != nil {
			err = bcs.region.DeleteExpansionRecordByID(tx, expansionRecord.ID)
			if err != nil {
				bcs.log.WithField("id", expansionRecord.ID).WithError(err).Error("delete failed")
			}
		}
		return err
	})
	if err != nil {
		bcs.log.WithError(err).WithField("newRecord", newExpansionRecord).Error("!!! --- serious mistake --- !!! transaction failed. You must fix it by hand")
	}

	// 更新net_disk表的总容量会在回调之后更新

	return
}

// 网盘续费
func (bcs *BCServer) netDiskRenewal(uid, recordID, durationInMonth int) (err error) {
	if recordID <= 0 || durationInMonth <= 0 {
		return businesserror.ErrInvalidRequestParams
	}

	var (
		now                = time.Now()
		expansionRecord    *rm.NetDiskExpansion
		newExpansionRecord *rm.NetDiskExpansion
		newOrderUUID       = libs.RandNumberString()
		newOrder           *bcm.Order
		newBill            *bcm.Bill
		user               *um.User
		costPrice          int64
	)

	// 校验用户状态，网盘挂载权限
	user, err = bcs.user.FindByUserId(uid)
	if err != nil {
		bcs.log.WithError(err).WithField("uid", uid).Error("get user failed")
		return
	}
	if user.Status == um.Disable || !user.MountNetDiskAuthority {
		return businesserror.ErrAuthorizeFailed
	}

	expansionRecord, err = bcs.region.GetExpansionRecordByID(recordID)
	if err != nil {
		bcs.log.WithError(err).WithField("id", recordID).Warn("get expansion record failed")
		return
	}
	if expansionRecord.UID != uid {
		return businesserror.ErrAuthorizeFailed
	}

	costPrice = constant.NetDiskPricePerGB * int64(durationInMonth) * expansionRecord.TotalExpandCapacity / constant.GInKB

	// 校验余额
	err = bcs.netDiskOperatePayCheck(uid, costPrice)
	if err != nil {
		return
	}

	newOrder = &bcm.Order{
		UID:           uid,
		Username:      user.Username,
		UserPhone:     user.Phone,
		UUID:          newOrderUUID,
		Status:        constant.OrderStatusSuccess,
		OrderType:     constant.OrderTypeNetDiskRenewal,
		ChargeType:    constant.ChargeTypeMonthly,
		PriceEntity:   &bcm.PriceInfo{OriginPrice: costPrice, DealPrice: costPrice},
		PayByBalance:  costPrice,
		DealPrice:     costPrice,
		DetailsEntity: map[string]interface{}{"region_sign": expansionRecord.RegionSign.String()},
		CreatedAt:     now,
		UpdatedAt:     now,
		PayAt:         &now,
	}

	dateCenter := ""
	region, err := bcs.region.GetRegionDetail(expansionRecord.RegionSign)
	if err != nil {
		bcs.log.WithError(err).WithField("region_sign", expansionRecord.RegionSign).Error("get region record failed")
	} else {
		dateCenter = region.DataCenter
	}

	newBill = &bcm.Bill{
		UID:           uid,
		UserPhone:     user.Phone,
		OrderUUID:     newOrderUUID,
		Type:          constant.BillTypeCharge,
		SubType:       constant.BillSubTypeNetDiskRenewal,
		Asset:         costPrice,
		PayByBalance:  costPrice,
		DetailsEntity: &bcm.BillDetail{RegionSign: expansionRecord.RegionSign, RegionName: expansionRecord.RegionName, DateCenter: dateCenter},
		ChargeType:    constant.ChargeTypeMonthly,
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	err = bcs.bc.CreateOrderBillAndUpdateWallet(bcm.CreateOrderBillAndUpdateWalletParams{
		NewOrder: newOrder,
		NewBill:  newBill,
		OpType:   constant.UpdateWalletSkip,
	})
	if err != nil {
		bcs.log.WithError(err).Warn("create order bill failed")
		return
	}

	newExpansionRecord = &rm.NetDiskExpansion{
		UID:                 uid,
		RegionSign:          expansionRecord.RegionSign,
		RegionName:          expansionRecord.RegionName,
		OrderUUID:           newOrderUUID,
		OriginalCapacity:    expansionRecord.OriginalCapacity + expansionRecord.ExpandCapacity,
		ExpandCapacity:      0,
		TotalExpandCapacity: expansionRecord.TotalExpandCapacity,
		ExpiredAt:           libs.GetExceptTime(expansionRecord.ExpiredAt, durationInMonth*30),
		Active:              true,
		CreatedAt:           now,
		UpdatedAt:           now,
	}

	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
		err = bcs.region.CreateExpansionRecord(tx, newExpansionRecord)
		if err != nil {
			bcs.log.WithField("record", newExpansionRecord).WithError(err).Warn("create record failed")
			return err
		}

		if expansionRecord != nil {
			err = bcs.region.DeleteExpansionRecordByID(tx, expansionRecord.ID)
			if err != nil {
				bcs.log.WithField("id", expansionRecord.ID).WithError(err).Error("delete failed")
			}
		}
		return err
	})
	if err != nil {
		bcs.log.WithError(err).WithField("newRecord", newExpansionRecord).Error("!!! --- serious mistake --- !!! transaction failed. You must fix it by hand")
	}
	return
}

// 扩容缩容预览接口
func (bcs *BCServer) changeDataDiskSizePreview(params *bcm.ChangeDataDiskSizeParams) (res *bcm.ChangeDataSizePreviewRes, err error) {
	if params == nil {
		bcs.log.WithField("params", params).Error("Params is nil")
		err = businesserror.ErrInternalError
		return
	}

	if err = params.Check(); err != nil {
		bcs.log.WarnE(err, "change data disk size preview check params failed. params:%+v", params)
		return
	}

	var (
		now                = time.Now()
		userMember         *um.UserMember
		machine            *mm.Machine
		memberDiscount     int64
		containerUsageInfo constant.ContainerUsageInfo
		expandSizeAlready  int64 // 当前已经扩容大小
		dda                *ddsModel.DataDiskAllocate
		ins                im.Instance
		order              *bcm.Order
		freeDisk           int64
		priceInfo          = &bcm.PriceInfo{
			ExpandDataDisk: params.DataDiskExpandSize,
		}
	)

	res = &bcm.ChangeDataSizePreviewRes{
		DataDiskExpandSize: params.DataDiskExpandSize,
		ChangeSize:         params.ChangeSize,
	}

	// 用户 - 会员等级
	userMember, err = bcs.user.GetUserMemberInfoByUid(params.UID)
	if err != nil {
		log.WithError(err).Error("get userMember failed")
		return
	}

	// 机器
	machine, err = bcs.getMachine(params.MachineID)
	if err != nil {
		bcs.log.WithError(err).Error("bcs: get machine failed")
		return
	}

	// 已扩容
	dda, err = bcs.dds.GetDDA(params.MachineID, params.InstanceUUID)
	if err != nil {
		bcs.log.WithError(err).Error("get dda failed")
		return nil, err
	}
	if dda != nil {
		expandSizeAlready = dda.DiskAllocate
	}

	ins, err = bcs.instance.GetInstance(constant.NewInstanceUUIDType(params.InstanceUUID))
	if err != nil {
		bcs.log.WithError(err).Error("get ins failed")
		return nil, err
	}

	order, err = bcs.bc.GetOrder(ins.OrderUUID)
	if err != nil {
		bcs.log.WithError(err).Error("get order failed")
		return nil, err
	}

	if order.MachineEntity != nil {
		freeDisk = order.MachineEntity.MaxInstanceDiskSize
	} else {
		freeDisk = machine.MaxInstanceDiskSize
	}

	memberDiscount, err = machine.GetDiscountRate(order.ChargeType, userMember.MemberLevel)
	if err != nil {
		bcs.log.WithField("chargeType", order.ChargeType).ErrorE(err, "get discount rate failed")
		return
	}

	// 数据盘已使用，最大缩容
	containerUsageInfo, _ = bcs.container.GetContainerUsageInfo(libs.GenContainerRuntimeUUID(params.InstanceUUID))
	res.DataDiskUsed = int64(containerUsageInfo.DataDiskUsedSize)
	if freeDisk > res.DataDiskUsed { // 未超过免费容量
		res.MaxReduceSize = expandSizeAlready
	} else { // 使用量超过免费容量
		res.MaxReduceSize = freeDisk + expandSizeAlready - res.DataDiskUsed
	}
	// 最大扩容
	res.MaxExpandSize = machine.SurplusMaxDataDiskExpandSize

	// 价格
	priceInfo.ChargeType = order.ChargeType
	if !priceInfo.ChargeType.IsPayg() {
		res.PrepayExpandDataDisk = true
		priceInfo.PrepayExpandDataDisk = true
		priceInfo.DataDiskInfo = &bcm.DataDiskInfo{ChangeSize: params.ChangeSize}
		priceInfo.DataDiskInfo.ExpiredAt = &ins.ExpiredAt.Time
		priceInfo.DataDiskInfo.TakeEffectAt = &now
	}

	bcs.getPriceOfDataDisk(priceInfo, machine, memberDiscount, false)
	if priceInfo.DataDiskInfo == nil {
		bcs.log.Error("get data disk price info failed")
		err = businesserror.ErrInternalError
		return
	}
	res.DataDiskInfo = *priceInfo.DataDiskInfo

	return
}

// 数据盘扩缩容
func (bcs *BCServer) changeDataDiskSize(params *bcm.ChangeDataDiskSizeParams) (newOrder *bcm.Order, err error) {
	if params == nil {
		bcs.log.WithField("params", params).Error("Params is nil")
		err = businesserror.ErrInternalError
		return
	}

	if err = params.Check(); err != nil {
		bcs.log.WarnE(err, "change data disk size params failed. params:%+v", params)
		return
	}

	// 缩容，要看实际使用量。
	var (
		log                    = bcs.log.WithField("params", params)
		ddcRecord              *bcm.DataDiskCharging
		finallyExpandSize      int64
		order                  *bcm.Order
		ins                    im.Instance
		user                   *um.User
		machine                *mm.Machine
		usedDiskSize           int64
		priceInfo              *bcm.PriceInfo
		ss                     *bcm.MachineSnapShot
		orderUUID              = libs.RandNumberString()
		now                    = time.Now()
		alreadyExistExpandSize int64
		freeDisk               int64
		txUUID                 string
	)

	// 获取数据
	ins, err = bcs.instance.GetInstance(constant.NewInstanceUUIDType(params.InstanceUUID))
	if err != nil {
		log.WithError(err).Error("get instance failed")
		return
	}

	if ins.UID != params.UID {
		err = businesserror.ErrResourceAccessAuthFailed
		return
	}

	order, err = bcs.bc.GetOrder(ins.OrderUUID)
	if err != nil {
		log.WithError(err).Error("get order failed")
		return
	}

	machine, err = bcs.getMachine(ins.MachineID)
	if err != nil {
		return
	}

	if order.MachineEntity != nil {
		freeDisk = order.MachineEntity.MaxInstanceDiskSize
	} else {
		freeDisk = machine.MaxInstanceDiskSize
	}

	ddcRecord, err = bcs.bc.DataDiskChargingGet(params.InstanceUUID)
	if err != nil {
		log.WithError(err).Error("get ddc record failed")
		return
	}
	if ddcRecord != nil {
		alreadyExistExpandSize = ddcRecord.ExpandSize
	}

	containerUsageInfo, _ := bcs.container.GetContainerUsageInfo(ins.RuntimeUUID)
	usedDiskSize = int64(containerUsageInfo.DataDiskUsedSize)

	// 计算容量变化
	switch params.OptType {
	case constant.OrderTypeDataDiskExpand:
		if params.ChangeSize > machine.SurplusMaxDataDiskExpandSize {
			err = businesserror.ErrDataDiskMaxExpandSizeLimit
			return
		}
		finallyExpandSize = alreadyExistExpandSize + params.ChangeSize
	case constant.OrderTypeDataDiskReduce:
		if !order.ChargeType.IsPayg() {
			err = businesserror.ErrDataDiskReduceOnlyPayg
			return
		}
		if alreadyExistExpandSize == 0 {
			err = businesserror.ErrDataDiskReduceFreeSizeLimit
			return
		}
		finallyExpandSize = libs.NaturalNumber(alreadyExistExpandSize - params.ChangeSize)

		if finallyExpandSize+freeDisk < usedDiskSize {
			err = businesserror.ErrDataDiskReduceUsedSizeLimit
			return
		}
	default:
		err = businesserror.ErrInvalidRequestParams
		return
	}

	// core 占用, 更新数据盘库存
	txUUID, err = bcs.dds.ChangeSize(&ddsModel.ChangeSizeParams{
		MachineID:   ins.MachineID,
		ProductUUID: params.InstanceUUID,
		OptType:     params.OptType,
		ChangeSize:  params.ChangeSize,
		FinallySize: finallyExpandSize,
	})
	if err != nil {
		log.WithError(err).Error("dds change size failed.")
		return
	}
	defer func() {
		if err != nil {
			errTx := bcs.dds.TxRollback(txUUID)
			if errTx != nil {
				bcs.log.WithError(err).Error("core api: dds rollback failed.")
			}
			return
		}
	}()

	// fill info
	priceInfo = &bcm.PriceInfo{
		MachineID:      order.MachineID,
		ChargeType:     order.ChargeType,
		Duration:       1,
		Num:            order.PriceEntity.Num,
		ExpandDataDisk: finallyExpandSize,
		DataDiskInfo: &bcm.DataDiskInfo{
			ChangeSize: params.ChangeSize,
		},
		SpecialCoupon: make(map[int]bool),
	}
	if len(order.PriceEntity.CouponIDList) != 0 {
		uc, err := bcs.bc.UserCouponGetForFrontEnd(order.PriceEntity.CouponIDList[0])
		if err != nil {
			bcs.log.WithError(err).Trace("get user coupon failed")
			return nil, err
		}
		if now.Before(uc.ValidEnd) && now.After(uc.CouponSnapshotEntity.ValidBegin) {
			priceInfo.CouponIDList = order.PriceEntity.CouponIDList
			priceInfo.SpecialCoupon[order.PriceEntity.CouponIDList[0]] = true
		}

	}

	// 根据不同的计费类型, 区分是普通扩容还是预付费扩容
	if order.ChargeType.IsPayg() {
		user, ss, order.RuntimeEntity.TakeEffectAt, order.RuntimeEntity.ExpiredAt, err = bcs.beforeCreateOrderFillInformation(&ins, order.UID, priceInfo)
	} else {
		if !ins.ExpiredAt.Valid {
			log.Error("prepay instance expiredAt field invalid")
			err = businesserror.ErrInternalError
			return
		}

		if ins.ExpiredAt.Time.Before(now) {
			err = businesserror.ErrOrderForPrepayExpandDataDiskInsExpired
			return
		}

		// 需要保证, 预付费扩容的未付款订单只能有一个
		exist, err := bcs.bc.ExistUnpaidPrepayExpandDataDiskOrder(order.ProductUUID)
		if err != nil {
			log.ErrorE(err, "call ExistUnpaidPrepayExpandDataDiskOrder failed")
			return nil, err
		}
		if exist {
			return nil, businesserror.ErrOrderForPrepayExpandDataDiskUnpaidLimit
		}

		exist, err = bcs.bc.ExistUnpaidRenewalOrder(order.ProductUUID)
		if err != nil {
			log.ErrorE(err, "call ExistUnpaidRenewalOrder failed")
			return nil, err
		}

		if exist {
			return nil, businesserror.ErrOrderExistUnpaidRenewal
		}

		priceInfo.PrepayExpandDataDisk = true
		priceInfo.DataDiskInfo.TakeEffectAt = &now
		priceInfo.DataDiskInfo.ExpiredAt = &ins.ExpiredAt.Time
		params.OptType = constant.OrderTypeDataDiskExpandOfPrepay
		user, ss, err = bcs.beforeCreateOrderFillInformationForPrepayExpandDataDisk(order.UID, priceInfo)
	}
	if err != nil {
		bcs.log.WithError(err).Error("fill information failed")
		return
	}

	// 整理order
	order.RuntimeEntity.OrderUUID = orderUUID
	order.RuntimeEntity.PaygPrice = priceInfo.PaygPrice
	order.MachineEntity.MachineSku = ss.MachineSku

	newOrder = &bcm.Order{
		UID:           user.ID,
		SubName:       ins.SubName,
		Username:      user.Username,
		UserPhone:     user.Phone,
		UUID:          orderUUID,
		ProductUUID:   params.InstanceUUID,
		RuntimeUUID:   ins.RuntimeUUID,
		RuntimeType:   constant.ContainerRuntimeOfInstance,
		Status:        constant.OrderStatusSuccess,
		OrderType:     params.OptType,
		DealPrice:     priceInfo.ToFinallyDealPrice(),
		ChargeType:    ins.ChargeType,
		PriceEntity:   priceInfo,
		RuntimeEntity: order.RuntimeEntity,
		MachineID:     params.MachineID,
		MachineEntity: order.MachineEntity,
		PayAt:         &now,
	}
	// 包卡实例判断用户余额
	if !ins.ChargeType.IsPayg() {
		wallet, err := bcs.bc.GetWalletForCreateOrder(ins.UID, constant.ProductTypeInstance, ins.ChargeType, ins.MachineID, nil, "")
		if err != nil {
			bcs.log.WithError(err).Error("get wallet failed")
			return newOrder, err
		}
		if newOrder.PriceEntity.ToFinallyDealPrice() > wallet.Assets+wallet.VoucherBalance {
			return newOrder, businesserror.ErrBillBalanceNotEnough
		}
	}

	// 整理Bill, 只有预付费数据盘扩容需要生成bill, 普通扩容在事物内部拦截掉, 不生成订单
	newBill := &bcm.Bill{
		UID:            newOrder.UID,
		UserPhone:      newOrder.UserPhone,
		OrderUUID:      newOrder.UUID,
		RuntimeUUID:    newOrder.RuntimeUUID,
		ProductUUID:    newOrder.ProductUUID,
		Type:           newOrder.OrderType.ToBillType(),
		SubType:        newOrder.OrderType.ToBillSubtype(),
		Asset:          newOrder.PriceEntity.ToFinallyDealPrice(),
		ChargeType:     newOrder.ChargeType,
		DetailsEntity:  &bcm.BillDetail{SubName: newOrder.SubName},
		DiscountEntity: newOrder.SetDiscountInfoForBill(),
	}
	if newOrder.OrderType == constant.OrderTypeDataDiskExpandOfPrepay {
		newBill.DetailsEntity.ChargeFrom = newOrder.PriceEntity.DataDiskInfo.TakeEffectAt.Format(constant.FormatTimeString)
		newBill.DetailsEntity.ChargeTo = newOrder.PriceEntity.DataDiskInfo.ExpiredAt.Format(constant.FormatTimeString)
		newBill.DetailsEntity.ExpandSize = newOrder.PriceEntity.ExpandDataDisk
		newBill.DetailsEntity.ExpandChangeSize = newOrder.PriceEntity.DataDiskInfo.ChangeSize
	}

	afterHook, afterFunc := bcs.getAfterHook(newOrder)
	err = bcs.bc.CreateOrderBillAndUpdateWallet(bcm.CreateOrderBillAndUpdateWalletParams{
		NewOrder:  newOrder,
		NewBill:   newBill,
		OpType:    newOrder.OrderType.ToUpdateWalletOperateType(),
		AfterHook: afterHook,
		AfterFunc: afterFunc,
	})

	if err != nil {
		bcs.log.WithField("newOrder", newOrder).ErrorE(err, "CreateOrderBillAndUpdateWallet failed")
		return nil, err
	}

	if txUUID != "" {
		err = bcs.dds.TxCommit(txUUID)
		if err != nil {
			bcs.log.WithError(err).Error("core api: dds commit failed.")
			return
		}
	}

	return
}

// 获取价格预览。目前只有部分操作从这里获取
func (bcs *BCServer) getPricePreview(params *bcm.GetPricePreviewParams) (err error) {
	if params == nil {
		return businesserror.ErrInvalidRequestParams
	}

	var (
		user *um.User
		log  = bcs.log.WithField("params", params)
	)

	user, err = bcs.user.FindByUserId(params.UID)
	if err != nil {
		log.WithError(err).Error("get user failed")
		return
	}

	/*
		实际需要算钱的场景：
		1.创建实例 2.实例续费 3.实例升降配置 4.转包卡: 使用priceInfo
		5.转按量 6.网盘扩容 7.网盘续费 8.数据盘扩容 9.数据盘缩容: 暂时使用各自路由加上/preview
	*/

	switch params.OptType {
	case constant.OrderTypeCreateInstance,
		constant.OrderTypeRenewalInstance,
		constant.OrderTypeUpdateInstance,
		constant.OrderTypeCloneInstance,
		constant.OrderTypeImproveConfiguration,
		constant.OrderTypeReduceConfiguration,
		constant.OrderTypeChangeWeekly,
		constant.OrderTypeChangeMonthly,
		constant.OrderTypeChangeYearly:
		// 直接使用priceInfo
	default:
		return
	}

	userMember, err := bcs.user.GetUserMemberInfoByUid(user.ID)
	if err != nil {
		return
	}

	// 获取价格, 补全信息
	err = bcs.getPrice(&getPriceParams{
		uid:         user.ID,
		priceInfo:   params.PriceInfo,
		levelName:   userMember.MemberLevel,
		productType: params.OptType.ToProductType(),
	})
	if err != nil {
		bcs.log.WithError(err).Warn("get real price failed")
		return
	}
	return
}

func (bcs *BCServer) mostWithdraw(uid int) (most int64, err error) {
	invoice, err := bcs.invoice.GetUserTotalRealInvoiceAmount(uid)
	if err != nil {
		return
	}

	balance, err := bcs.bc.GetBalance(uid)
	if err != nil {
		return
	}

	most = balance
	if invoice < balance {
		most = invoice
	}
	if most < 0 {
		most = 0
	}
	return
}

func (bcs *BCServer) changeProtocol(params *bcm.ChangeProtocolReq) (err error) {
	var (
		log = bcs.log.WithField("params", params)
		ins im.Instance
	)
	// 获取数据
	ins, err = bcs.instance.GetInstance(constant.NewInstanceUUIDType(params.InstanceUUID))
	if err != nil {
		log.WithError(err).Error("get instance failed")
		return
	}
	if ins.UID != params.UID {
		err = businesserror.ErrResourceAccessAuthFailed
		return
	}
	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) error {
		// 更新容器协议
		req := constant.OptContainerReq{
			RuntimeUUID: ins.RuntimeUUID,
			Opt:         constant.ContainerUpdateProtocol,
			Caller:      constant.NewOptCallerUser(ins.UID),
			Payload:     params.ServicePortProtocol,
			OptAt:       time.Now(),
		}
		err = bcs.container.OperateContainer(req, constant.OptContainerAuxParams{})
		if err != nil {
			log.WarnE(err, "Operate container failed. opts: %+v, runtimeUUID: [%s]", req, ins.RuntimeUUID)
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}

	return nil
}

func (bcs *BCServer) UserNetDiskRenewalByAdmin(phone string, uid, durationInMonth int, sign constant.RegionSignType) (err error) {
	if durationInMonth <= 0 {
		return businesserror.ErrInvalidRequestParams
	}

	// 检查是否有生效中的扩容
	var expansionRecord *rm.NetDiskExpansion // 已经存在的扩容
	// 根据 params 的 uid 和 region 判断该用户是否有扩容记录
	expansionRecord, err = bcs.region.GetExpansionRecordActive(uid, sign)
	if err != nil {
		bcs.log.WithField("uid", uid).WithField("sign", sign).ErrorE(err, "get expansionRecord failed")
		return
	}

	// 如果有生效中的扩容记录，则执行续费的逻辑
	if expansionRecord == nil {
		return businesserror.ErrEmptyBox.New().Format(fmt.Sprintf("用户[%s][uid:%d]在地区[%s]无生效中网盘扩容", phone, uid, sign))
	}

	return bcs.netDiskRenewal(uid, expansionRecord.ID, durationInMonth)
}

func (bcs *BCServer) CreateOrderForDeploymentDuration(params *bcm.CreateOrderForDeploymentDurationParams) (res *bcm.CreateOrderForDeploymentDurationResp, err error) {
	l := bcs.log.WithFields(logger.Fields{"params": *params, "sec": "CreateOrderForDeploymentDuration"})
	res = &bcm.CreateOrderForDeploymentDurationResp{}
	deployment, err := bcs.deployment.DeploymentGet(params.DeploymentUUID)
	if err != nil {
		return
	}

	regionSignList := make([]string, 0)
	regionList, err := bcs.region.GetRegionListByDatacenter(params.DCList, "")
	if err != nil {
		return
	}
	for _, region := range regionList {
		regionSignList = append(regionSignList, string(region.Sign))
	}
	params.RegionSignList = regionSignList

	if deployment.UID != params.UID {
		return res, businesserror.ErrResourceAccessAuthFailed
	}

	if deployment.Status.IsDown() {
		return nil, businesserror.ErrDeploymentStoppedWhenCreateDDP
	}

	if !params.Valid() {
		return nil, businesserror.ErrInvalidRequestParams
	}

	if params.SubName != "" {
		subUser, err := bcs.user.SubUserGet(params.SubName)
		if err != nil {
			l.Error("get sub user failed")
			return nil, err
		}

		if subUser.Roles.Deployment != constant.SubUserAllData {
			// deployment的子账号权限和传入子账号不匹配
			if deployment.SubName != "" && deployment.SubName != params.SubName {
				return res, businesserror.ErrResourceAccessAuthFailed
			}

			// deployment无子账号权限的情况
			if deployment.SubName == "" {
				return res, businesserror.ErrResourceAccessAuthFailed
			}

		}
	}

	gpuTypeList := []string{}
	m := machineModel.Machine{}
	err = m.MachineGetAllWithSelect("distinct "+m.KeyGpuType(), &db_helper.QueryFilters{
		InFilters: []db_helper.In{{m.KeyRegionSign(), params.RegionSignList}},
		NullField: []string{"deleted_at"},
	}, &gpuTypeList)
	if err != nil {
		l.ErrorE(err, "get gpuIDList failed")
		return
	}

	gpuSet := goset.NewSet[string](gpuTypeList...)
	if !gpuSet.Contains(params.GpuTypeList...) {
		return nil, businesserror.ErrInvalidRequestParams
	}

	user, err := bcs.user.FindByUserId(params.UID)
	if err != nil {
		return
	}

	userMember, err := bcs.user.GetUserMemberInfoByUid(params.UID)
	if err != nil {
		l.WarnE(err, "get user member detail failed")
		return
	}

	for _, gpuType := range params.GpuTypeList {
		m := &machineModel.Machine{}
		err = m.MachineGet(&db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				m.KeyGpuType():                       gpuType,
				m.ChargeTypeToKey(params.ChargeType): true,
			},
			InFilters: []db_helper.In{
				{m.KeyRegionSign(), params.RegionSignList},
				{"status", []agent_constant.MachineHealthStatus{agent_constant.Normal, agent_constant.StorageMildException, agent_constant.StorageSeriousException}},
				{"on_line", []constant.OnOffLine{constant.Online, constant.OnlineNotStartUp, constant.OnlineNotCreate, constant.OnlineNotStartUpButSchedule, constant.OnlineNotCreateButSchedule}},
			},
			Orders:    []string{m.KeyPaygPrice() + " desc"},
			NullField: []string{"deleted_at"},
		})
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, businesserror.ErrDeploymentDDPNotFoundMachine
			}
			bcs.log.ErrorE(err, "get machine with select failed")
			return
		}

		priceInfo := &bcm.PriceInfo{
			MachineID:  m.MachineID,
			ChargeType: params.ChargeType,
			Duration:   params.Duration,
			Num:        1,
		}

		err = bcs.getPrice(&getPriceParams{
			uid:         user.ID,
			priceInfo:   priceInfo,
			levelName:   userMember.MemberLevel,
			productType: constant.ProductTypeDeploymentDurationPkg,
		})
		if err != nil {
			l.ErrorE(err, "get price info failed")
			return
		}

		ss, err := bcs.getMachineSnapshot(m.MachineID, m, int64(priceInfo.Num))
		if err != nil {
			return nil, err
		}

		dcListStr := ""
		if len(params.DCList) != 0 {
			dcListStr = strings.Join(params.DCList, ",")
		}
		res.OrderList = append(res.OrderList, bcm.Order{
			UID:           params.UID,
			SubName:       params.SubName,
			Username:      user.Username,
			UserPhone:     user.Phone,
			UUID:          libs.RandNumberString(),
			ProductUUID:   params.DeploymentUUID,
			RuntimeType:   constant.ContainerRuntimeOfDeployment,
			Status:        constant.OrderStatusUnpaid,
			OrderType:     constant.OrderTypeCreateDeploymentDurationPkg,
			ChargeType:    params.ChargeType,
			DealPrice:     priceInfo.DealPrice,
			PriceEntity:   priceInfo,
			MachineID:     m.MachineID,
			MachineEntity: ss,
			DetailsEntity: map[string]interface{}{
				"ddp_subname":              deployment.SubName,
				"ddp_data_center_list_str": dcListStr,
			},
		})
		res.Assets += priceInfo.DealPrice
	}

	if params.Preview {
		return
	}

	errList := []string{}
	now := time.Now()

	// 加锁, 每个用户一个锁, 加了代金券后， 也使用同一个锁
	lock := new(redis.RedisMutex)
	lock, err = bcs.bc.SetLockForUpdateWallet(params.UID)
	if err != nil {
		return
	}
	defer func() {
		_ = lock.UnLock()
	}()

	err = bcs.ddpBeforeCreateOrderCheck(params.UID, params.SubName, params.ChargeType, res.Assets)
	if err != nil {
		return
	}

	for _, order := range res.OrderList {
		dcListStr := ""
		dcStr, ok := order.DetailsEntity["ddp_data_center_list_str"]
		if ok {
			dcListStr = dcStr.(string)
		}
		newBill := &bcm.Bill{
			UID:         order.UID,
			UserPhone:   order.UserPhone,
			UUID:        libs.RandNumberString(),
			OrderUUID:   order.UUID,
			ProductUUID: order.ProductUUID,
			Type:        constant.BillTypeCharge,
			SubType:     constant.BillSubTypeChargeCreateDeploymentDurationPkg,
			Asset:       order.DealPrice,
			ChargeType:  order.ChargeType,
			DetailsEntity: &bcm.BillDetail{
				SubName:              order.SubName,
				DeploymentUUID:       order.ProductUUID,
				DDPDataCenterListStr: dcListStr,
			},
			DiscountEntity: order.SetDiscountInfoForBill(),
			ConfirmAt:      &now,
		}
		(&order).Status = constant.OrderStatusSuccess
		(&order).PayAt = &now

		err = bcs.bc.BatchCreateOrderBillAndUpdateWalletWithOuterLock(&order, newBill, constant.UpdateWalletOperateByOrderType, nil)
		if err != nil {
			l.ErrorE(err, "CreateOrderBillAndUpdateWallet failed")
			errList = append(errList, order.ProductUUID)
		}
	}

	if len(errList) == 0 {
		return
	}

	return res, businesserror.ErrDeploymentCreateDDPFailed
}

func (bcs *BCServer) CreateOrderForCancelDeploymentDurationRefund(params *bcm.CreateOrderForCancelDeploymentDurationParams) (resp *bcm.CreateOrderForCancelDeploymentDurationResp, err error) {
	resp = &bcm.CreateOrderForCancelDeploymentDurationResp{}
	ddp := &deploymentModel.DeploymentDurationPkg{}
	filter := db_helper.QueryFilters{EqualFilters: map[string]interface{}{
		"uid":             params.UID,
		"deployment_uuid": params.DeploymentUUID,
		"status":          constant.DDPInEffect,
	}, Orders: []string{"id desc"}}
	if len(params.OrderUUIDList) != 0 {
		filter.InFilters = []db_helper.In{{"order_uuid", params.OrderUUIDList}}
	}

	user, err := bcs.user.FindByUserId(params.UID)
	if err != nil {
		bcs.log.WithField("uid", params.UID).ErrorE(err, "get user failed")
		return nil, err
	}

	userMember, err := bcs.user.GetUserMemberInfoByUid(params.UID)
	if err != nil {
		return nil, err
	}

	// 真实退款
	if len(params.OrderUUIDList) != 0 && !params.Preview {
		// 加锁, 每个用户一个锁, 加了代金券后， 也使用同一个锁
		lock := new(redis.RedisMutex)
		lock, err = bcs.bc.SetLockForUpdateWallet(params.UID)
		if err != nil {
			return
		}
		defer func() {
			_ = lock.UnLock()
		}()
	}

	ddpList, err := ddp.DDPList(nil, filter)
	if err != nil {
		bcs.log.WithField("filter", filter).ErrorE(err, "get ddp list failed")
		return nil, err
	}

	for _, ddp := range ddpList {
		order, err := bcs.bc.GetOrder(ddp.OrderUUID)
		if err != nil {
			bcs.log.WithField("orderUUID", ddp.OrderUUID).ErrorE(err, "CreateOrderForCancelDeploymentDurationRefund get order failed")
			return nil, err
		}

		bill, err := bcs.bc.GetBillFirstByOrderUUID(order.UID, ddp.OrderUUID)
		if err != nil {
			bcs.log.WithField("order_uuid", ddp.OrderUUID).ErrorE(err, "get bill list failed")
			return nil, err
		}
		var cost int64
		var refundAsset int64 = 0
		if ddp.Total == ddp.Balance {
			// 未使用，全额退款
			refundAsset = bill.PayByBalance
		} else {
			// 机器信息 -> 最新sku信息, 允许machine不存在
			machineSku := []*machineModel.SkuInfo{}
			machine, err := bcs.machine.Find(order.MachineID)
			if err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					machineSku = order.MachineEntity.MachineSku
				}
				bcs.log.WithField("machineID", order.MachineID).ErrorE(err, "get machine failed")
				return nil, err
			} else {
				machineSku = machine.NewMachineSkuContent
			}

			cost, _, err = bcs.ddpRefundGetCostAsset(machineSku, ddp.Total-ddp.Balance, order, userMember.MemberLevel)
			if err != nil {
				return nil, err
			}

			if cost > bill.Asset {
				cost = bill.Asset
			} else if cost <= bill.PayByVoucher {
				refundAsset = bill.PayByBalance
			} else {
				refundAsset = bill.Asset - cost
			}

		}

		resp.TotalRefundAsset += refundAsset
		dcListStr := ""
		dcStr, ok := order.DetailsEntity["ddp_data_center_list_str"]
		if ok {
			dcListStr = dcStr.(string)
		}

		refundOrder := &bcm.Order{
			UID:           order.UID,
			SubName:       order.SubName,
			Username:      user.Username,
			UserPhone:     user.Phone,
			UUID:          libs.RandNumberString(),
			ProductUUID:   order.ProductUUID,
			RuntimeType:   order.RuntimeType,
			Status:        constant.OrderStatusUnpaid,
			OrderType:     constant.OrderTypeDeploymentDurationPkgRefund,
			ChargeType:    order.ChargeType,
			DealPrice:     refundAsset,
			MachineEntity: order.MachineEntity,
			DetailsEntity: map[string]interface{}{
				"ddp_data_center_list_str": dcListStr,
			},
			RefundEntity: &bcm.RefundInfo{
				RefundAsset:        refundAsset,
				CostAsset:          cost,
				RefundType:         constant.OrderTypeDeploymentDurationPkgRefund,
				OrderUUID:          order.UUID,
				BeginAt:            order.CreatedAt,
				EndAt:              time.Now(),
				SourceAsset:        bill.Asset,
				SourcePayByVoucher: bill.PayByVoucher,
				SourcePayByBalance: bill.PayByBalance,
				DDPGpuTypeID:       order.MachineEntity.GpuTypeID,
				DDPGpuType:         order.MachineEntity.GpuType.Name,
				DDPBalance:         ddp.Balance,
				DDPChargeType:      order.ChargeType,
				DDPChargeDuration:  order.PriceEntity.Duration,
			},
		}
		resp.OrderList = append(resp.OrderList, *refundOrder)
	}

	if params.Preview || len(params.OrderUUIDList) == 0 ||
		len(resp.OrderList) == 0 {
		return
	}

	errList := []string{}
	now := time.Now()

	for _, order := range resp.OrderList {
		dcListStr := ""
		dcStr, ok := order.DetailsEntity["ddp_data_center_list_str"]
		if ok {
			dcListStr = dcStr.(string)
		}
		newBill := &bcm.Bill{
			UID:         order.UID,
			UserPhone:   order.UserPhone,
			UUID:        libs.RandNumberString(),
			OrderUUID:   order.UUID,
			ProductUUID: order.ProductUUID,
			Type:        constant.BillTypeRefund,
			SubType:     constant.BillSubTypeRefundDeploymentDurationPkgBillSubType,
			Asset:       order.DealPrice,
			ChargeType:  order.ChargeType,
			DetailsEntity: &bcm.BillDetail{
				SubName:              order.SubName,
				DeploymentUUID:       order.ProductUUID,
				DDPDataCenterListStr: dcListStr,
			},
			DiscountEntity: order.SetDiscountInfoForBill(),
			ConfirmAt:      &now,
		}
		(&order).Status = constant.OrderStatusSuccess
		(&order).PayAt = &now

		err = bcs.bc.BatchCreateOrderBillAndUpdateWalletWithOuterLock(&order, newBill, constant.UpdateWalletOperateByOrderType, nil)
		if err != nil {
			bcs.log.WithField("order_uuid", order.RefundEntity.OrderUUID).ErrorE(err, "ddp refund BatchCreateOrderBillAndUpdateWalletWithOuterLock failed")
			errList = append(errList, order.ProductUUID)
		}
	}

	if len(errList) == 0 {
		return
	}

	return resp, businesserror.ErrDeploymentRefundDDPFailed
}

func (bcs *BCServer) CreateOrderForCancelDDRefundSingle(params *bcm.CreateOrderForCancelDDSingleParams) (resp *bcm.CreateOrderForCancelDDSingleResp, err error) {
	resp = &bcm.CreateOrderForCancelDDSingleResp{}
	ddp := &deploymentModel.DeploymentDurationPkg{}
	user, err := bcs.user.FindByUserId(params.UID)
	if err != nil {
		bcs.log.WithField("uid", params.UID).ErrorE(err, "get user failed")
		return nil, err
	}

	userMember, err := bcs.user.GetUserMemberInfoByUid(params.UID)
	if err != nil {
		return nil, err
	}
	// 真实退款
	if params.OrderUUID != "" && !params.Preview {
		// 加锁, 每个用户一个锁, 加了代金券后， 也使用同一个锁
		lock := new(redis.RedisMutex)
		lock, err = bcs.bc.SetLockForUpdateWallet(params.UID)
		if err != nil {
			return
		}
		defer func() {
			_ = lock.UnLock()
		}()
	}

	err = ddp.DDPGet(nil, db_helper.QueryFilters{EqualFilters: map[string]interface{}{
		"uid":    params.UID,
		"id":     params.DurationID,
		"status": constant.DDPInEffect,
	}})
	if err != nil {
		bcs.log.WithField("id", params.DurationID).ErrorE(err, "get ddp failed")
		return nil, err
	}

	if params.OrderUUID != "" && ddp.OrderUUID != params.OrderUUID {
		return nil, businesserror.ErrInvalidRequestParams
	}

	order, err := bcs.bc.GetOrder(ddp.OrderUUID)
	if err != nil {
		bcs.log.WithField("orderUUID", ddp.OrderUUID).ErrorE(err, "CreateOrderForCancelDeploymentDurationRefund get order failed")
		return nil, err
	}

	bill, err := bcs.bc.GetBillFirstByOrderUUID(order.UID, ddp.OrderUUID)
	if err != nil {
		bcs.log.WithField("order_uuid", ddp.OrderUUID).ErrorE(err, "get bill list failed")
		return nil, err
	}
	var cost int64
	var refundAsset int64 = 0
	if ddp.Total == ddp.Balance {
		// 未使用，全额退款
		refundAsset = bill.PayByBalance
	} else {
		// 机器信息 -> 最新sku信息, 允许machine不存在
		var machineSku []*machineModel.SkuInfo
		machine, err := bcs.machine.Find(order.MachineID)
		if err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				bcs.log.WithField("machineID", order.MachineID).ErrorE(err, "get machine failed")
				return nil, err
			}
			machineSku = order.MachineEntity.MachineSku
		} else {
			machineSku = machine.NewMachineSkuContent
		}

		cost, _, err = bcs.ddpRefundGetCostAsset(machineSku, ddp.Total-ddp.Balance, order, userMember.MemberLevel)
		if err != nil {
			return nil, err
		}

		if cost > bill.Asset {
			cost = bill.Asset
		} else if cost <= bill.PayByVoucher {
			refundAsset = bill.PayByBalance
		} else {
			refundAsset = bill.Asset - cost
		}

	}

	dcListStr := ""
	dcStr, ok := order.DetailsEntity["ddp_data_center_list_str"]
	if ok {
		dcListStr = dcStr.(string)
	}
	refundOrder := &bcm.Order{
		UID:           order.UID,
		SubName:       ddp.SubName,
		Username:      user.Username,
		UserPhone:     user.Phone,
		UUID:          libs.RandNumberString(),
		ProductUUID:   order.ProductUUID,
		RuntimeType:   order.RuntimeType,
		Status:        constant.OrderStatusUnpaid,
		OrderType:     constant.OrderTypeDeploymentDurationPkgRefund,
		ChargeType:    order.ChargeType,
		DealPrice:     refundAsset,
		MachineEntity: order.MachineEntity,
		DetailsEntity: map[string]interface{}{
			"ddp_data_center_list_str": dcListStr,
		},
		RefundEntity: &bcm.RefundInfo{
			RefundAsset:        refundAsset,
			CostAsset:          cost,
			RefundType:         constant.OrderTypeDeploymentDurationPkgRefund,
			OrderUUID:          order.UUID,
			BeginAt:            order.CreatedAt,
			EndAt:              time.Now(),
			SourceAsset:        bill.Asset,
			SourcePayByVoucher: bill.PayByVoucher,
			SourcePayByBalance: bill.PayByBalance,
			DDPGpuTypeID:       order.MachineEntity.GpuTypeID,
			DDPGpuType:         order.MachineEntity.GpuType.Name,
			DDPBalance:         ddp.Balance,
			DDPChargeType:      order.ChargeType,
			DDPChargeDuration:  order.PriceEntity.Duration,
		},
	}
	resp.Order = *refundOrder

	if params.Preview || params.OrderUUID == "" {
		return
	}

	now := time.Now()

	newBill := &bcm.Bill{
		UID:         refundOrder.UID,
		UserPhone:   refundOrder.UserPhone,
		UUID:        libs.RandNumberString(),
		OrderUUID:   refundOrder.UUID,
		ProductUUID: refundOrder.ProductUUID,
		Type:        constant.BillTypeRefund,
		SubType:     constant.BillSubTypeRefundDeploymentDurationPkgBillSubType,
		Asset:       refundOrder.DealPrice,
		ChargeType:  refundOrder.ChargeType,
		DetailsEntity: &bcm.BillDetail{
			DeploymentUUID:       refundOrder.ProductUUID,
			DDPDataCenterListStr: dcListStr,
		},

		DiscountEntity: refundOrder.SetDiscountInfoForBill(),
		ConfirmAt:      &now,
	}
	refundOrder.Status = constant.OrderStatusSuccess
	refundOrder.PayAt = &now

	err = bcs.bc.BatchCreateOrderBillAndUpdateWalletWithOuterLock(refundOrder, newBill, constant.UpdateWalletOperateByOrderType, nil)
	if err != nil {
		bcs.log.WithField("order_uuid", ddp.OrderUUID).ErrorE(err, "ddp refund BatchCreateOrderBillAndUpdateWalletWithOuterLock failed")
		return nil, businesserror.ErrDeploymentRefundDDPFailed
	}

	return

}
