package controller

import (
	"server/conf"
	bcm "server/pkg/billing_center/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
)

// wallet
type AdminRechargeReqBody struct {
	UID   int   `form:"id" json:"id" binding:"required"`
	Asset int64 `form:"asset" json:"asset" binding:"required"`
}

type WalletAdminWithdrawReqBody struct {
	UID   int   `form:"id" json:"id" binding:"required"`
	Asset int64 `form:"asset" json:"asset" binding:"required"`
}

func (a *AdminRechargeReqBody) Check() error {
	if a.UID == 0 {
		return businesserror.ErrInvalidRequestParams
	}
	if !conf.GetGlobalGsConfig().App.DebugApi && a.Asset < 1000 {
		return businesserror.ErrWalletRechargeOneYuan
	}
	return nil
}

type RechargeCreateRes struct {
	CodeUrl      string `json:"code_url"`
	RechargeUUID string `json:"recharge_uuid"`
	Asset        int64  `json:"asset"`
}

type AppletRechargeCreateRes struct {
	PrepayId     string `json:"prepay_id"`
	RechargeUUID string `json:"recharge_uuid"`
	Asset        int64  `json:"asset"`
}

// order
type OrderGetListRequest struct {
	InstanceUUID string               `json:"instance_uuid"`
	OrderUUID    string               `json:"order_uuid"`
	Phone        string               `json:"phone"`
	Status       constant.OrderStatus `json:"status"`
	OrderType    constant.OrderType   `json:"order_type"`
	ProductType  constant.ProductType `json:"product_type"`
	ChargeType   constant.ChargeType  `json:"charge_type"`
	db_helper.GetPagedRangeRequest
}

type CreateOrderForCreateInstanceRequest struct {
	InstanceInfo constant.CreateContainerTaskRequest `json:"instance_info"`
	PriceInfo    bcm.PriceInfo                       `json:"price_info"`
}

func (c *CreateOrderForCreateInstanceRequest) Check() (err error) {
	if len(c.InstanceInfo.Image) == 0 ||
		len(c.InstanceInfo.ChargeType) == 0 ||
		len(c.InstanceInfo.MachineID) == 0 ||
		c.InstanceInfo.ReqGPUAmount == 0 {
		err = businesserror.ErrInvalidRequestParams
		return
	}

	if c.InstanceInfo.ChargeType != c.PriceInfo.ChargeType ||
		c.InstanceInfo.ReqGPUAmount != c.PriceInfo.Num ||
		c.InstanceInfo.MachineID != c.PriceInfo.MachineID {
		err = businesserror.ErrInvalidRequestParams
	}
	return
}

type CreateOrderForRenewalInstanceRequest struct {
	// 实例续费只能在旧实例的计费方式的基础上续费, 不能直接改变
	InstanceUUID string              `form:"instance_uuid" json:"instance_uuid" binding:"required"`
	Duration     int                 `form:"duration" json:"duration" binding:"required"`
	ChargeType   constant.ChargeType `form:"charge_type" json:"charge_type" binding:"required"`
	CouponIDList []int               `form:"coupon_id_list" json:"coupon_id_list"`
}

type CreateOrderForChangeInstanceRequest struct {
	OperateType  constant.OrderType `json:"operate_type"` // 操作类型
	InstanceUUID string             `json:"instance_uuid"`
	GpuNum       int                `json:"gpu_num"`
}

type CreateOrderForInstanceMigrateRequest struct {
	InstanceUUID string `json:"instance_uuid"`
	bcm.CreateInstanceRequest
}

func (c *CreateOrderForInstanceMigrateRequest) Check() (err error) {
	if len(c.InstanceUUID) == 0 {
		return businesserror.ErrInvalidRequestParams
	}

	if err = c.CreateInstanceRequest.Check(); err != nil {
		return
	}
	return nil
}

// bill
type BillGetListRequest struct {
	InstanceUUID string            ` json:"instance_uuid"`
	UserPhone    string            `json:"user_phone"`
	OrderUUID    string            `json:"order_uuid"`
	BillType     constant.BillType `json:"bill_type"`
	BillUUID     string            `json:"bill_uuid"` ////
	SubName      string            `json:"sub_name"`
	db_helper.GetPagedRangeRequest
}

// voucher
type VoucherGetListRequest struct {
	bcm.VoucherGetListParams
	db_helper.GetPagedRangeRequest
}

type VoucherIssueAppointmentTicketRequest struct {
	UID       int `json:"uid"`
	VoucherID int `json:"voucher_id"`
}

type VoucherIssueExchangeTicketRequest struct {
	ExchangeCode string `json:"exchange_code"`
}

type VoucherUserAdminGetListRequest struct {
	bcm.VoucherUserGetListParams
	db_helper.GetPagedRangeRequest
}

type VoucherUserGetListRequest struct {
	Validity bool `json:"validity"`
	db_helper.GetPagedRangeRequest
}

type VoucherUserGetListResponse struct {
	Asset int64 `json:"asset"`
	*db_helper.PagedData
}

type VoucherCloseRequest struct {
	VoucherID int `json:"voucher_id"`
}

type UserVoucherUsageListRes struct {
	Phone       string                `json:"phone"`
	UsageStatus constant.UVUsedStatus `json:"usage_status"`
	db_helper.GetPagedRangeRequest
}

type CreateOrderForPrepayToPaygParams struct {
	InstanceUUID constant.InstanceUUIDType `form:"instance_uuid" json:"instance_uuid" binging:"required"` // 实例uuid
}

func (c *CreateOrderForPrepayToPaygParams) check() error {
	if len(c.InstanceUUID) == 0 {
		return businesserror.ErrInvalidRequestParams
	}
	return nil
}

type CreateOrderForNetDiskExpandRequest struct {
	ExpandCapacity int                     `json:"expand_capacity"` // 接收单位：GB
	Duration       int                     `json:"duration"`        // 扩容时长. 如果需要对齐到期时间，传1就行
	RegionSign     constant.RegionSignType `json:"region_sign"`
}

type CreateOrderForNetDiskOperateByAdmin struct {
	Phone string `json:"phone"`
	//UID            int                     `json:"uid"`
	ExpandCapacity int                     `json:"expand_capacity"` // 接收单位：GB
	Duration       int                     `json:"duration"`        // 扩容时长. 如果需要对齐到期时间，传1就行
	RegionSign     constant.RegionSignType `json:"region_sign"`
}

type CreateOrderForNetDiskRenewalRequest struct {
	ID       int `json:"id"` // 扩容记录ID
	Duration int `json:"duration"`
}

type CouponListParams struct {
	bcm.CouponGetListParams
	db_helper.GetPagedRangeRequest
}

type CouponReceiveRecordListParams struct {
	bcm.CouponGetReceiveRecordListParams
	db_helper.GetPagedRangeRequest
}

type CouponIssueByAdminParams struct {
	CouponID int `json:"coupon_id"`
	UID      int `json:"uid"`
}

type UserCouponGetListPagedForFrontEndParams struct {
	bcm.UserCouponGetListParams
	db_helper.GetPagedRangeRequest
}

type CreditWalletCreateParams struct {
	Phone       string `json:"phone"`
	CreditLimit int64  `json:"credit_limit"`
}

type CreditWalletGetListParams struct {
	bcm.CreditWalletGetListParams
	db_helper.GetPagedRangeRequest
}

type CreditWalletHistoryGetListParams struct {
	bcm.CreditWalletHistoryGetListParams
	db_helper.GetPagedRangeRequest
}

type VoucherIssueRechargeCashbackTicketParams struct {
	UID    int   `json:"uid"`
	Assets int64 `json:"assets"`
}

type UserSetVoucherIsAutoUseReq struct {
	VoucherId int  `json:"voucher_id"`  // 代金券id
	IsAutoUse bool `json:"is_auto_use"` // 是否自动使用
}
