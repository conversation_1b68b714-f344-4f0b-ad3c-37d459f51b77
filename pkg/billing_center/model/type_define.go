package model

import (
	"server/conf"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	gs "server/pkg/gpu_stock/model"
	"server/plugin/queue_interface"
	"time"

	"gorm.io/gorm"
)

// wallet
type AdminRechargeReqBody struct {
	UID   int   `form:"id" json:"id" binding:"required"`
	Asset int64 `form:"asset" json:"asset" binding:"required"`
}

func (a *AdminRechargeReqBody) Check() error {
	if a.UID == 0 {
		return businesserror.ErrInvalidRequestParams
	}
	if !conf.GetGlobalGsConfig().App.DebugApi && a.Asset < 1000 {
		return businesserror.ErrWalletRechargeOneYuan
	}
	return nil
}

type RechargeCreateRes struct {
	CodeUrl      string `json:"code_url"`
	RechargeUUID string `json:"recharge_uuid"`
	Asset        int64  `json:"asset"`
}

// order
type OrderGetListRequest struct {
	InstanceUUID string               `json:"instance_uuid"`
	OrderUUID    string               `json:"order_uuid"`
	Phone        string               `json:"phone"`
	Status       constant.OrderStatus `json:"status"`
	OrderType    constant.OrderType   `json:"order_type"`
	ChargeType   constant.ChargeType  `json:"charge_type"`
	db_helper.GetPagedRangeRequest
}

type CreateInstanceRequest struct {
	InstanceInfo         constant.CreateContainerTaskRequest `json:"instance_info"`
	PriceInfo            PriceInfo                           `json:"price_info"`
	OperateType          constant.OrderType                  `json:"-"`
	CloneSrcInstanceUUID string                              `json:"-"` // 克隆实例用
	SubName              string                              `json:"-"` // 子账号用

	// 弹性部署用
	DeploymentUUID string                        `json:"-"` //
	DccUUID        string                        `json:"-"` // 容器复用
	DccRuntimeUUID constant.ContainerRuntimeUUID `json:"-"` // 容器复用

	CloneCode int `json:"clone_code"` //克隆码
}

type RenewalInstanceRequest struct {
	Uid              int                       `json:"uid"`
	InstanceUUID     constant.InstanceUUIDType `json:"instance_uuid"`
	TargetChargeType constant.ChargeType       `json:"target_charge_type"`
	Duration         int                       `json:"duration"`
	CouponIDList     []int                     `json:"coupon_id_list"`
	SubName          string                    `json:"sub_name"`
}

func (r *RenewalInstanceRequest) Check() (err error) {
	if r.Duration < 0 {
		err = businesserror.ErrInvalidRequestParams
		return
	}

	if r.TargetChargeType != constant.ChargeTypePayg && r.Duration == 0 {
		err = businesserror.ErrInvalidRequestParams
		return
	}

	return
}

func (c *CreateInstanceRequest) Check() (err error) {
	if len(c.InstanceInfo.Image) == 0 ||
		len(c.InstanceInfo.ChargeType) == 0 ||
		len(c.InstanceInfo.MachineID) == 0 ||
		c.InstanceInfo.ReqGPUAmount <= 0 ||
		c.PriceInfo.Duration < 0 ||
		c.PriceInfo.ExpandDataDisk < 0 ||
		c.PriceInfo.Num < 0 {
		err = businesserror.ErrInvalidRequestParams
		return
	}

	if c.InstanceInfo.ChargeType != constant.ChargeTypePayg && c.PriceInfo.Duration == 0 {
		err = businesserror.ErrInvalidRequestParams
		return
	}

	if c.InstanceInfo.ChargeType != c.PriceInfo.ChargeType ||
		c.InstanceInfo.ReqGPUAmount != c.PriceInfo.Num ||
		c.InstanceInfo.MachineID != c.PriceInfo.MachineID {
		err = businesserror.ErrInvalidRequestParams
	}
	return
}

type CreateOrderForRenewalInstanceRequest struct {
	// 实例续费只能在旧实例的计费方式的基础上续费, 不能直接改变
	InstanceUUID   string   `form:"instance_uuid" json:"instance_uuid" binding:"required"`
	Duration       int      `form:"duration" json:"duration" binding:"required"`
	CouponUUIDList []string `json:"coupon_uuid_list"`
}

type CreateOrderForRenewalBMInstanceRequest struct {
	// 实例续费只能在旧实例的计费方式的基础上续费, 不能直接改变
	InstanceID   string              `form:"instance_id" json:"instance_id" binding:"required"`
	Duration     int                 `form:"duration" json:"duration" binding:"required"`
	ChargeType   constant.ChargeType `form:"charge_type" json:"charge_type" binding:"required"`
	SubName      string              `json:"sub_name"`
	CouponIDList []int               `form:"coupon_id_list" json:"coupon_id_list"`
}

type AdminOffsetBalanceParams struct {
	Phone string `json:"phone"`
	Asset int64  `json:"asset"`
}

type CreateOrderForChangeInstanceRequest struct {
	OperateType  constant.OrderType `json:"operate_type"` // 操作类型
	InstanceUUID string             `json:"instance_uuid"`
	GpuNum       int                `json:"gpu_num"`
}

func (c *CreateOrderForChangeInstanceRequest) Check() (err error) {
	if len(c.InstanceUUID) == 0 {
		err = businesserror.ErrInvalidRequestParams
		return
	}

	if c.OperateType != constant.OrderTypeUpdateInstance || c.GpuNum == 0 {
		err = businesserror.ErrInvalidRequestParams
		return
	}
	return
}

// bill
type BillGetListRequest struct {
	InstanceUUID string ` json:"instance_uuid"`
	UserPhone    string `json:"user_phone"`
	OrderUUID    string `json:"order_uuid"`
	db_helper.GetPagedRangeRequest
}

// voucher
type VoucherGetListRequest struct {
	VoucherGetListParams
	db_helper.GetPagedRangeRequest
}

type VoucherIssueAppointmentTicketRequest struct {
	UID       int `json:"uid"`
	VoucherID int `json:"voucher_id"`
}

type VoucherIssueExchangeTicketRequest struct {
	ExchangeCode string `json:"exchange_code"`
}

type VoucherUserAdminGetListRequest struct {
	VoucherUserGetListParams
	db_helper.GetPagedRangeRequest
}

type VoucherUserGetListRequest struct {
	Validity bool `json:"validity"`
	db_helper.GetPagedRangeRequest
}

type VoucherUserGetListResponse struct {
	Asset int64 `json:"asset"`
	*db_helper.PagedData
}

type VoucherCloseRequest struct {
	VoucherID int `json:"voucher_id"`
}

type CreateOrderForPrepayToPaygResponse struct {
	InstanceName      string                         `json:"instance_name"`
	InstanceUUID      constant.InstanceUUIDType      `json:"instance_uuid"`
	ChargeType        constant.ChargeType            `json:"charge_type"`
	UnusedOrderAsset  int64                          `json:"unused_order_asset"`
	RefundAsset       int64                          `json:"refund_asset"` // 总的应退回金额， = orderRefundAsset + unUsedRefundAsset
	PriceInfo         PriceInfo                      `json:"price_info"`
	RefundList        []PrepayToPaygPrepayRefundInfo `json:"refund_list"`
	TotalPayByBalance int64                          `json:"total_pay_by_balance"`
	TotalPayByVoucher int64                          `json:"total_pay_by_voucher"`
	TotalUsedAsset    int64                          `json:"total_used_asset"`
}

type PrepayToPaygPrepayRefundInfo struct {
	OrderUUID    string               `json:"order_uuid"`
	OrderType    constant.OrderType   `json:"order_type"`
	ProductType  constant.ProductType `json:"product_type"`
	StartAt      time.Time            `json:"start_at"`       // 计费开始时间
	EndAt        time.Time            `json:"end_at"`         // 计费结束时间
	ExpiredAt    time.Time            `json:"expired_at"`     // 期望的计费结束时间
	Duration     int64                `json:"duration"`       // 实际使用时长
	Asset        int64                `json:"asset"`          // 订单金额
	PayPyVoucher int64                `json:"pay_py_voucher"` // 代金券支付
	PayByBalance int64                `json:"pay_by_balance"` // 余额支付
	UsedAsset    int64                `json:"used_asset"`     // 已消费
	UsedInstance int64                `json:"used_instance"`  // 已消费
	UsedDataDisk int64                `json:"used_data_disk"` // 已消费
	RefundAsset  int64                `json:"refund_asset"`   // 应退回
}

type PaygToPrepayCreateOrderParams struct {
	Uid              int                       `json:"-"`
	InstanceUUID     constant.InstanceUUIDType `json:"instance_uuid" binging:"required"`      // 实例uuid
	TargetChargeType constant.ChargeType       `json:"target_charge_type" binging:"required"` // 目标计费类型
	Duration         int                       `json:"duration" binging:"required"`           // 购买时长
	CouponIDList     []int                     `json:"coupon_id_list"`
	SubName          string                    `json:"-"`
}

func (c *PaygToPrepayCreateOrderParams) Check() error {
	if len(c.InstanceUUID) == 0 || len(c.TargetChargeType) == 0 || c.Duration <= 0 {
		return businesserror.ErrInvalidRequestParams
	}
	return nil
}

type InvoiceOrderListParams struct {
	UID         int                    `json:"uid"`
	Status      []constant.OrderStatus `json:"status"`
	ChargeType  []constant.ChargeType  `json:"charge_type"`
	InvoiceUUID string                 `json:"invoice_uuid"`
}

type AfterUpdateWalletParams struct {
	Order            *Order                                `json:"order"`
	Bill             *Bill                                 `json:"bill"`
	AfterOperateType constant.UpdateWalletOperateType      `json:"operate_type"`
	AfterHook        queue_interface.ElementPayloadContent `json:"after_hook"`
	AfterFunc        func(tx *gorm.DB) error

	DataDiskUpdateMap map[string]interface{} `json:"data_disk_update_map"`

	// auxParams
	UpdateWalletInstanceCallShutdownChargingStoppedAt *time.Time `json:"update_wallet_instance_call_shutdown_charging_stopped_at"`
}

type GetPricePreviewParams struct {
	UID       int                `json:"uid"`
	OptType   constant.OrderType `json:"opt_type"`
	PriceInfo *PriceInfo         `json:"price_info"`
}

// -----------------------------------
type OrderGetListParams struct {
	UID         int                           `json:"uid"`
	Status      constant.OrderStatus          `json:"status"`
	OrderType   constant.OrderType            `json:"order_type"`
	ChargeType  constant.ChargeType           `json:"charge_type"`
	RuntimeType constant.ContainerRuntimeType `json:"runtime_type"`
	ProductType constant.ProductType          `json:"product_type"`

	UUID        string `json:"uuid"`
	Phone       string `json:"phone"`
	ProductUUID string `json:"instance_uuid"`
	RuntimeUUID string `json:"runtime_uuid"`
}

// ChangeDataDiskSizeParams 数据盘扩容/缩容参数
type ChangeDataDiskSizeParams struct {
	UID                int                `json:"uid"`
	OptType            constant.OrderType `json:"opt_type"`
	MachineID          string             `json:"machine_id"`
	InstanceUUID       string             `json:"instance_uuid"`
	ChangeSize         int64              `json:"change_size"`
	DataDiskExpandSize int64              `json:"data_disk_expand_size"`
	SubName            string             `json:"-"`
}

func (c *ChangeDataDiskSizeParams) Check() (err error) {
	if c.ChangeSize < 0 || c.DataDiskExpandSize < 0 {
		err = businesserror.ErrInvalidRequestParams
		return
	}

	return
}

type ChangeDataSizePreviewRes struct {
	DataDiskExpandSize   int64 `json:"data_disk_expand_size"` // 扩/缩容后数据盘大小
	ChangeSize           int64 `json:"change_size"`           // 改变大小
	DataDiskUsed         int64 `json:"data_disk_used"`        // 数据盘已使用
	MaxExpandSize        int64 `json:"max_expand_size"`       // 最大扩容容量
	MaxReduceSize        int64 `json:"max_reduce_size"`       // 最大缩容容量
	PrepayExpandDataDisk bool  `json:"prepay_expand_data_disk"`
	DataDiskInfo
}

type CreditWalletGetListParams struct {
	Phone string `json:"phone"`
	UID   int    `json:"-"`
}

type CreditWalletHistoryGetListParams struct {
	UID   int    `json:"-"`
	Phone string `json:"phone"`
}

type CreditWalletAdjustmentQuotaParams struct {
	UID         int   `json:"uid"`
	CreditLimit int64 `json:"credit_limit"`
}

type CreditWalletRepayParams struct {
	UID    int    `json:"uid"`
	Asset  int64  `json:"asset"`
	Remark string `json:"remark"`
}

type CreditWalletLoanParams struct {
	UID   int   `json:"-"`
	Asset int64 `json:"asset"`
}

type ChangeInstanceParams struct {
	Uid          int                `json:"uid"`
	InstanceUUID string             `json:"instance_uuid"`
	OperateType  constant.OrderType `json:"operate_type"`
	GpuNum       int                `json:"gpu_num"`
	SubName      string             `json:"-"`
	CouponIDList []int              `json:"coupon_id_list"`
}

func (c *ChangeInstanceParams) Check() (err error) {
	if c.GpuNum < 0 {
		err = businesserror.ErrInvalidRequestParams
		return
	}

	return
}

type OrderPayCancelParams struct {
	TX           *gorm.DB
	UID          int
	OrderUUID    string
	SubName      string
	IsAdminClone bool
	SrcCloneUid  int //克隆码克隆给他人的源用户id
}

type PrepayToPaygParams struct {
	Uid          int                       `json:"uid"`
	InstanceUUID constant.InstanceUUIDType `json:"instance_uuid"`
	IsPreview    bool                      `json:"is_preview"`
	SubName      string                    `json:"sub_name"`
}

type SubUserPaymentMethodChangeParams struct {
	SubName       string                   `json:"sub_name"`
	PaymentMethod constant.SUPaymentMethod `json:"payment_method"`
}

func (u *SubUserPaymentMethodChangeParams) Check() bool {
	if u.SubName == "" {
		return false
	}

	if u.PaymentMethod != constant.SUPaymentMethodQuota &&
		u.PaymentMethod != constant.SUPaymentMethodShare {
		return false
	}
	return true
}

type SubUserQuotaRechargeParams struct {
	SubName string `json:"sub_name"`
	Quota   int64  `json:"quota"`
}

type SubUserQuotaWithdrawParams struct {
	SubName string `json:"sub_name"`
	Quota   int64  `json:"quota"`
}

func (u *SubUserQuotaWithdrawParams) Check() bool {
	if u.SubName == "" || u.Quota <= 0 {
		return false
	}
	return true
}
func (u *SubUserQuotaRechargeParams) Check() bool {
	if u.SubName == "" || u.Quota <= 0 {
		return false
	}
	return true
}

type BillInfoFromShardingTable struct {
	Bill
	Suffix string `json:"suffix"` // 分表后缀
}

type VoucherIssueRegisterTicketParams struct {
	UID          int
	UserPhone    string
	InviterUID   int
	InviterPhone string
}

type GetBalanceStatementListParams struct {
	db_helper.GetPagedRangeRequest // 分页和交易时间

	BalanceStatementType constant.BalanceStatementType `json:"balance_statement_type"` // 收支明细类型
	BillType             constant.BillType             `json:"bill_type"`              // 交易类型
}

type BalanceStatementInfo struct {
	Bill

	BalanceStatementType constant.BalanceStatementType `json:"balance_statement_type"`
}

type GetDailyBillStatementListParams struct {
	db_helper.GetPagedRangeRequest

	UID      int               `json:"uid"`
	BillType constant.BillType `json:"bill_type"` // 交易类型
	Phone    string            `json:"phone"`     // 电话
}

type DailyBillStatementExportParams struct {
	db_helper.GetPagedRangeRequest
	BillType constant.BillType `form:"bill_type" json:"bill_type"`
	UID      int               `json:"uid"`
}

type BtListParams struct {
	AccountName    string              `form:"account_name" json:"account_name"`
	Status         []constant.BTStatus `form:"status" json:"status"`
	ConfirmedPhone string              `form:"confirmed_phone" json:"confirmed_phone"`
	db_helper.GetPagedRangeRequest
}

type BTUpdateParams struct {
	SerialNum        string                     `json:"serial_num"`
	ConfirmResult    constant.BTConfirmedResult `json:"confirm_result"`
	Phone            string                     `json:"phone"`
	AcceptanceRemark string                     `json:"acceptance_remark"`
}

func (b *BTUpdateParams) Valid() bool {
	if b.SerialNum == "" {
		return false
	}

	if b.ConfirmResult != constant.BTConfirmedRecharge &&
		b.ConfirmResult != constant.BTConfirmedConfirm &&
		b.ConfirmResult != constant.BTConfirmedIgnore {
		return false
	}
	return true
}

type CreateRefundRecordReq struct {
	Uid        int       `json:"-"`
	AuditorUid int       `json:"-"`
	BillUuid   string    `json:"bill_uuid"` // bill_uuid
	Phone      string    `json:"phone"`     // 手机号
	Assets     int64     `json:"assets"`    // 提现金额
	RefundTime time.Time `json:"-"`         // 提现时间
	IsDelay    bool      `json:"-"`
}

type GetRefundRecordListFilter struct {
	Uid         int
	RefundUuids []string
	Status      string
}

type GetRefundListReq struct {
	Uid         int      `form:"-" json:"-"`
	Phone       string   `form:"phone" json:"phone"`
	RefundUuids []string `form:"refund_uuids" json:"refund_uuids"`
	Status      string   `form:"status" json:"status"`
}

type GetRefundListRes struct {
	Phone      string                   `json:"phone"`
	Pathway    constant.RechargePathway `json:"pathway"`
	BillUuid   string                   `json:"bill_uuid"`
	PayAccount string                   `json:"pay_account"`
	Asset      int64                    `json:"asset"`
	CreatedAt  time.Time                `json:"created_at"`
	Auditor    string                   `json:"auditor"`
	Status     constant.RefundStatus    `json:"status"`
	RefundTime *time.Time               `json:"refund_time"`
	Reason     string                   `json:"reason"`
}

type CreateRefundDelayReq struct {
	Uid        int       `json:"-"`
	AuditorUid int       `json:"-"`
	BillUuid   string    `json:"bill_uuid"`   // bill_uuid
	Phone      string    `json:"phone"`       // 手机号
	Assets     int64     `json:"assets"`      // 提现金额
	RefundTime time.Time `json:"refund_time"` // 提现时间
}

type RefundDelay struct {
	Assets     int64     `json:"assets"`      // 提现金额
	RefundTime time.Time `json:"refund_time"` // 提现时间
	BillUuid   string    `json:"bill_uuid"`
}

type CreateOrderForDeploymentDurationResp struct {
	OrderList []Order `json:"order_list"`
	Assets    int64   `json:"assets"`
}
type CreateOrderForDeploymentDurationParams struct {
	UID            int                 `json:"-"`
	SubName        string              `json:"-"` // 操作者 - order所属
	Preview        bool                `json:"-"`
	DeploymentUUID string              `json:"deployment_uuid"`
	Duration       int                 `json:"duration"`
	ChargeType     constant.ChargeType `json:"charge_type"`
	GpuTypeList    []string            `json:"gpu_type_list"`
	RegionSignList []string            `json:"region_sign"`
	DCList         []string            `json:"dc_list"`
}

func (c *CreateOrderForDeploymentDurationParams) Valid() bool {
	switch c.ChargeType {
	case constant.ChargeTypeDaily, constant.ChargeTypeWeekly, constant.ChargeTypeMonthly:
	default:
		return false
	}

	return true
}

type CreateOrderForCancelDeploymentDurationParams struct {
	UID            int      `json:"-"`
	SubName        string   `json:"-"`
	Preview        bool     `json:"-"`
	DeploymentUUID string   `json:"deployment_uuid"`
	OrderUUIDList  []string `json:"order_uuid"`
}

type CreateOrderForCancelDDSingleParams struct {
	UID        int    `json:"-"`
	SubName    string `json:"-"`
	Preview    bool   `json:"-"`
	DurationID int    `json:"duration_id"`
	OrderUUID  string `json:"order_uuid"`
}

type CreateOrderForCancelDeploymentDurationResp struct {
	OrderList        []Order `json:"order_list"`
	TotalRefundAsset int64   `json:"total_refund_asset"`
}

type CreateOrderForCancelDDSingleResp struct {
	Order Order `json:"order_list"`
}

type GetDeploymentDurationListParams struct {
	UID     int    `json:"-"`
	Status  string `json:"status"`
	SubName string `json:"sub_name"`
	db_helper.GetPagedRangeRequest
}

type GetDeploymentDurationInfo struct {
	ID             int                `json:"id"`
	UID            int                `json:"uid"` // 创建人, 详细信息从 /pkg/user 模块获取.
	DeploymentUUID string             `json:"deployment_uuid"`
	OrderUUID      string             `json:"order_uuid"`
	GpuType        string             `json:"gpu_type"`
	Total          int64              `json:"total"`   // 总时长, 单位秒
	Balance        int64              `json:"balance"` // 剩余时长
	Status         constant.DDPStatus `json:"status"`
	CreatedAt      time.Time          `json:"created_at"`

	DDPChargeType     constant.ChargeType `json:"ddp_charge_type"`
	DDPChargeDuration int                 `json:"ddp_charge_duration"`
	DDPList           string              `json:"ddp_list"`
}

type PrepayToPaygInnerParams struct {
	OldOrder, OrderRefund, OrderCreatePayg *Order
	OldBill, BillRefund                    *Bill
	UnUsedOrderUUIDList                    []string
	IsRunning                              bool
	AfterHook                              queue_interface.ElementPayloadContent
	AfterFunc                              func(tx *gorm.DB) error
	ExistDDA                               bool
}

type PaygToPrepayParams struct {
	PrepayBill, ChargingBill *Bill
	IsCharging               bool
	GpuReserve               *gs.GpuReserve
	AfterHook                queue_interface.ElementPayloadContent
	AfterFunc                func(tx *gorm.DB) error
	ExpiredAt                time.Time
	ExistDDA                 bool
}

type CreateOrderBillAndUpdateWalletParams struct {
	NewOrder  *Order
	NewBill   *Bill
	OpType    constant.UpdateWalletOperateType
	AfterHook queue_interface.ElementPayloadContent
	AfterFunc func(tx *gorm.DB) error
}

type AdminFixPreChargeReq struct {
	BillUuid string `json:"bill_uuid"`
}

type AdminGetPreChargeListReq struct {
	BillUuid string `json:"bill_uuid" form:"bill_uuid"`
	Phone    string `json:"phone" form:"phone"`
}

type GetRechargeRecordListFilter struct {
	PreRecharge bool   `json:"pre_recharge"`
	BillUuid    string `json:"bill_uuid"`
	Phone       string `json:"phone"` // 手机
}
type AdminGetPreChargeListItem struct {
	Asset    int64  `json:"asset"`    // 金额
	Phone    string `json:"phone"`    // 手机
	Username string `json:"username"` // 用户名
	Balance  int64  `json:"balance"`  // 余额
	BillUuid string `json:"bill_uuid"`
}

type ChangeProtocolReq struct {
	InstanceUUID        string `json:"instance_uuid"`         // 实例uuid
	ServicePortProtocol string `json:"service_port_protocol"` // 协议，http、tcp
	UID                 int    `json:"-"`
}

func (c *ChangeProtocolReq) Check() error {
	if len(c.InstanceUUID) == 0 || (c.ServicePortProtocol != constant.ProtocolTcp && c.ServicePortProtocol != constant.ProtocolHTTP) {
		return businesserror.ErrInvalidRequestParams
	}
	return nil
}

type AdminTransInsPrePayReq struct {
	OldInstanceUuid string `json:"old_instance_uuid"`
	NewInstanceUuid string `json:"new_instance_uuid"`
}

type GetPriceParams struct {
	Uid         int
	PriceInfo   *PriceInfo
	LevelName   constant.MemberLevelName
	ProductType constant.ProductType
}

type AdminRevokeUserVoucherReq struct {
	UserVoucherId int `json:"user_voucher_id"`
}

type CouponCreateUnReceiveParams struct {
	CouponID int `json:"coupon_id"`
	Num      int `json:"num"`
}

type CouponIssueByExchangeCode struct {
	ExchangeCode string `json:"exchange_code"`
}
