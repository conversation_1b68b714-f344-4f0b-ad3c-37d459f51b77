package model

import (
	"database/sql"
	"encoding/json"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/libs"
	mm "server/pkg/machine/model"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const TableNameOrder = "orders"

type Order struct {
	ID          int                           `gorm:"column:id;" json:"id"`
	UID         int                           `gorm:"column:uid;" json:"uid"` // 用户id
	SubName     string                        `gorm:"column:sub_name;type:varchar(255);default ''" json:"sub_name"`
	Username    string                        `gorm:"type:varchar(255);column:username" json:"username"`                // 用户名
	UserPhone   string                        `gorm:"type:varchar(255);column:user_phone" json:"user_phone"`            // 手机号
	UUID        string                        `gorm:"type:varchar(255);column:uuid;index:order_uuid" json:"uuid"`       // 订单编号 生成uuid的随机数
	ProductUUID string                        `gorm:"type:varchar(255);column:product_uuid;" json:"product_uuid"`       // 实例/闲时 uuid
	RuntimeUUID constant.ContainerRuntimeUUID `gorm:"type:varchar(255);column:runtime_uuid;" json:"runtime_uuid"`       // 容器 uuid
	RuntimeType constant.ContainerRuntimeType `gorm:"type:varchar(255);column:runtime_type;" json:"runtime_type"`       // instance / idle job
	Status      constant.OrderStatus          `gorm:"type:varchar(255);column:status;index:order_status" json:"status"` // 订单状态	(Success, Closed, Unpaid, timeout)
	OrderType   constant.OrderType            `gorm:"type:varchar(255);column:order_type;" json:"order_type"`           // 订单类型	(create_instance，change_weekly,change_monthly,change_yearly)
	ChargeType  constant.ChargeType           `gorm:"type:varchar(255);column:charge_type;" json:"charge_type"`         // 计费方式	按量付费,按周,月,年

	MigrateInstanceUUID constant.InstanceUUIDType `gorm:"type:varchar(255);column:migrate_instance_uuid" json:"migrate_instance_uuid"`

	// 价格信息
	DealPrice    int64          `gorm:"column:deal_price;" json:"deal_price"`        // 交易金额
	PayByBalance int64          `gorm:"column:pay_by_balance" json:"pay_by_balance"` // 余额支付金额
	PriceInfo    datatypes.JSON `gorm:"type:json;column:price_info" json:"-"`
	PriceEntity  *PriceInfo     `gorm:"-" json:"price_info,omitempty"`

	// 实例信息
	RuntimeInfo   datatypes.JSON                       `gorm:"type:json;column:runtime_info;" json:"-"`
	RuntimeEntity *constant.CreateContainerTaskRequest `gorm:"-" json:"runtime_info"`

	// 机器信息
	MachineID     string           `gorm:"type:varchar(255);column:machine_id;" json:"machine_id"`
	MachineInfo   datatypes.JSON   `gorm:"type:json;column:machine_info" json:"-"`
	MachineEntity *MachineSnapShot `gorm:"-" json:"machine_info"`

	// 退款信息
	RefundInfo   datatypes.JSON `gorm:"type:json;column:refund_info;" json:"-"`
	RefundEntity *RefundInfo    `gorm:"-" json:"refund_info"`

	// 发票信息
	InvoiceUUID  string `gorm:"type:varchar(255);column:invoice_uuid;" json:"invoice_uuid"` // 发票uuid
	RefundAmount int64  `gorm:"type:int; column:refund_amount;" json:"refund_amount"`       // 退款金额

	// 详情
	DetailsJson   datatypes.JSON         `gorm:"type:json;column:details" json:"-"`
	DetailsEntity map[string]interface{} `gorm:"-" json:"details"`

	CreatedAt time.Time  `gorm:"type:datetime;column:created_at" json:"created_at"` // 订单创建时间
	UpdatedAt time.Time  `gorm:"type:datetime;column:updated_at" json:"updated_at"` // 账单更新时间
	PayAt     *time.Time `gorm:"type:datetime;column:pay_at" json:"pay_at"`         // 付款时间

	// 仅对前端有效
	NewPriceInfo *NewPriceInfo `gorm:"-"  json:"new_price_info"`
	AdditionInfo interface{}   `gorm:"-" json:"addition_info"`
}

func (o *Order) TableName() string {
	return TableNameOrder
}

func (o *Order) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&Order{})
}

func (o *Order) GetUID() int {
	return o.UID
}

func (o *Order) FillPhone(p string) {
	o.UserPhone = p
}

// ------------------------------------------------

func (o *Order) BeforeCreate(db *gorm.DB) (err error) {
	if o.RuntimeEntity != nil {
		if o.RuntimeEntity.TakeEffectAt == nil {
			if o.OrderType == constant.OrderTypeRenewalInstance {
				return businesserror.ErrInternalError
			} else {
				now := time.Now()
				o.RuntimeEntity.TakeEffectAt = &now
			}
		}

		o.RuntimeInfo, err = json.Marshal(o.RuntimeEntity)
		if err != nil {
			return
		}
	}

	if o.PriceEntity != nil {
		o.PriceInfo, err = json.Marshal(o.PriceEntity)
		if err != nil {
			return
		}
	}

	if o.Status == constant.OrderStatusRefund {
		if o.RefundEntity == nil {
			return businesserror.ErrInternalError
		}
		o.RefundInfo, err = json.Marshal(o.RefundEntity)
		if err != nil {
			return
		}
	}

	if o.MachineEntity != nil {
		o.MachineInfo, err = json.Marshal(o.MachineEntity)
	}

	if o.RefundEntity != nil {
		o.RefundInfo, err = json.Marshal(o.RefundEntity)
		if err != nil {
			return
		}
	}

	if o.DetailsEntity != nil {
		o.DetailsJson, err = json.Marshal(o.DetailsEntity)
		if err != nil {
			return
		}
	}
	return
}

func (o *Order) AfterFind(db *gorm.DB) (err error) {
	if len(o.RuntimeInfo) != 0 {
		o.RuntimeEntity = new(constant.CreateContainerTaskRequest)
		err = json.Unmarshal(o.RuntimeInfo, o.RuntimeEntity)
		if err != nil {
			return
		}
	}
	if len(o.PriceInfo) != 0 {
		o.PriceEntity = new(PriceInfo)
		err = json.Unmarshal(o.PriceInfo, o.PriceEntity)
		if err != nil {
			return
		}
	}
	if len(o.RefundInfo) != 0 {
		o.RefundEntity = new(RefundInfo)
		err = json.Unmarshal(o.RefundInfo, o.RefundEntity)
		if err != nil {
			return
		}
	}

	if len(o.MachineInfo) != 0 {
		o.MachineEntity = new(MachineSnapShot)
		err = json.Unmarshal(o.MachineInfo, o.MachineEntity)
		if err != nil {
			return
		}
	}

	if len(o.DetailsJson) != 0 {
		o.DetailsEntity = make(map[string]interface{})
		err = json.Unmarshal(o.DetailsJson, &o.DetailsEntity)
	}
	return
}

func (o *Order) SetDiscountInfoForBill() *DiscountDetail {
	if !o.ChargeType.IsPayg() && o.PriceEntity != nil {
		detail := &DiscountDetail{
			OriginPrice: o.PriceEntity.ToFinallyOriginPrice(),
			DiscountSum: o.PriceEntity.ToFinallyOriginPrice() - o.PriceEntity.ToFinallyDealPrice(),
		}

		for _, v := range o.PriceEntity.DiscountList {
			detail.DiscountList = append(detail.DiscountList, BillDiscountList{
				ProductType: v.ProductType,
				Type:        v.Type,
				OriginPrice: v.OriginPrice,
				TotalReduce: v.TotalReduction,
			})
		}
		return detail
	}
	return nil
}

func (o *Order) RenewalShouldTakeEffect() bool {
	if o.RuntimeEntity == nil ||
		o.RuntimeEntity.TakeEffectAt == nil ||
		o.RuntimeEntity.TakeEffectAt.Before(time.Now()) {
		return true
	}
	return false
}

func (o *Order) ShouldGetNetDiskInfo() bool {
	if o.OrderType == constant.OrderTypeNetDiskExpand || o.OrderType == constant.OrderTypeNetDiskRenewal {
		return true
	}
	return false
}

func (o *Order) CanCancel() bool {
	if o.Status == constant.OrderStatusUnpaid {
		return true
	}
	return false
}

func (o *Order) ToDataDiskCharging() *DataDiskCharging {
	if o.PriceEntity.ExpandDataDisk == 0 {
		return nil
	}

	now := time.Now()
	return &DataDiskCharging{
		UID:                  o.UID,
		SubName:              o.SubName,
		UserPhone:            o.UserPhone,
		MachineID:            o.MachineID,
		ProductUUID:          o.ProductUUID,
		OrderUUID:            o.UUID,
		ExpandSize:           o.PriceEntity.ExpandDataDisk,
		DailyPrice:           o.PriceEntity.DataDiskInfo.DataDiskDealDailyPrice,
		OriginDailyPrice:     o.PriceEntity.DataDiskInfo.DataDiskOriginDailyPrice,
		DailyPeakExpandSize:  o.PriceEntity.ExpandDataDisk,
		DailyPeakPrice:       o.PriceEntity.DataDiskInfo.DataDiskDealDailyPrice,
		OriginDailyPeakPrice: o.PriceEntity.DataDiskInfo.DataDiskOriginDailyPrice,
		ChargeType:           o.ChargeType,
		PrepayExpiredAt:      o.RuntimeEntity.ExpiredAt,
		Charging:             true,
		CreatedAt:            now,
		LastSettledAt:        now,
	}
}

func (o *Order) ToDataDiskChargingChangeParams() *DataDiskChargingChangeParams {
	switch o.OrderType {
	case constant.OrderTypeRenewalInstance:
		return &DataDiskChargingChangeParams{
			ProductUUID:     o.ProductUUID,
			OptType:         o.OrderType,
			PrepayExpiredAt: o.RuntimeEntity.ExpiredAt,
			OrderUUID:       o.UUID,
		}
	case constant.OrderTypeDataDiskExpand, constant.OrderTypeDataDiskReduce, constant.OrderTypeDataDiskExpandOfPrepay:
		return &DataDiskChargingChangeParams{
			ProductUUID:      o.ProductUUID,
			OptType:          o.OrderType,
			ChangeSize:       o.PriceEntity.DataDiskInfo.ChangeSize,
			FinallySize:      o.PriceEntity.ExpandDataDisk,
			DailyPrice:       o.PriceEntity.DataDiskInfo.DataDiskDealDailyPrice,
			OriginDailyPrice: o.PriceEntity.DataDiskInfo.DataDiskOriginDailyPrice,
			OrderUUID:        o.UUID,
		}
	case constant.OrderTypeImproveConfiguration, constant.OrderTypeReduceConfiguration:
		return &DataDiskChargingChangeParams{
			ProductUUID: o.ProductUUID,
			OptType:     o.OrderType,
			OrderUUID:   o.UUID,
		}
	}

	return nil

}

func (o *Order) ToUpdateDDCParams() (params *DataDiskCharging) {
	params = &DataDiskCharging{
		MachineID:        o.MachineID,
		ProductUUID:      o.ProductUUID,
		OrderUUID:        o.UUID,
		ExpandSize:       o.PriceEntity.ExpandDataDisk,
		DailyPrice:       o.PriceEntity.DataDiskInfo.DataDiskDealDailyPrice,
		OriginDailyPrice: o.PriceEntity.DataDiskInfo.DataDiskOriginDailyPrice,
	}

	if !o.ChargeType.IsPayg() {
		params.PrepayExpiredAt = o.RuntimeEntity.ExpiredAt
		params.ChargeType = o.ChargeType
		params.LastSettledAt = time.Now()
		params.ForceSettleOnce = true
	}

	return
}

type PriceInfo struct {
	// 计费配置信息
	MachineID      string              `json:"machine_id"`
	ChargeType     constant.ChargeType `json:"charge_type"`
	Duration       int                 `json:"duration"`         // 时长, 如5周, 3个月, 2年这种
	Num            int                 `json:"num"`              // 这个基本就是卡的数量
	ExpandDataDisk int64               `json:"expand_data_disk"` // 最终数据盘扩容大小，不论扩容缩容，都表示计算后的大小, 统一单位，byte。

	// 实例价格信息，网盘订单时，部分字段是网盘的价格信息
	OriginPrice  int64 `json:"origin_price"`             // 初始价格
	DealPrice    int64 `json:"deal_price"`               // 最终价格
	PaygPrice    int64 `json:"payg_price"`               // 按量付费的单价
	DDPPaygPrice int64 `json:"ddp_payg_price,omitempty"` // ddp折合成按量付费的单价

	// 优惠信息
	CouponIDList  []int          `json:"coupon_id_list,omitempty"` // 用户优惠券
	DiscountList  []DiscountInfo `json:"discount_list,omitempty"`  // 折扣记录
	SpecialCoupon map[int]bool   `json:"special_coupon,omitempty"`

	// 数据盘配置信息
	DataDiskInfo *DataDiskInfo `json:"data_disk_info,omitempty"` // 数据盘扩容信息

	// 预付费数据盘扩容, 只在预付费扩容时为true，其他如转包卡，直接创建包卡实例顺带数据盘扩容时，都是false
	PrepayExpandDataDisk bool `json:"prepay_expand_data_disk"`

	RegionSign constant.RegionSignType `json:"region_sign"`
	ChargeSize int64                   `json:"charge_size"`
	ChargeFrom time.Time               `json:"charge_from"`
	ChargeTo   time.Time               `json:"charge_to"`
}

// DataDiskInfo 数据盘扩容价格信息
type DataDiskInfo struct {
	ChangeSize               int64 `json:"change_size"`
	DataDiskOriginPrice      int64 `json:"data_disk_origin_price"`       // 扩容原始价格
	DataDiskDealPrice        int64 `json:"data_disk_deal_price"`         // 扩容成交价格
	DataDiskOriginDailyPrice int64 `json:"data_disk_origin_daily_price"` // 按日付费原始价格
	DataDiskDealDailyPrice   int64 `json:"data_disk_deal_daily_price"`   // 按日付费折后价格

	// 预付费扩容数据盘,开始时间是操作时间,结束时间从instance中取,包含未生效的续费订单的时间
	FinallyChargeType constant.ChargeType // 经过阶梯计价后, 最终计费时使用的计费方式
	Duration          float64             // 经过阶梯计价后, 由最终的计费方式决定的计费时长, 只用于记录
	TakeEffectAt      *time.Time          `json:"take_effect_at"` // 扩容时刻
	ExpiredAt         *time.Time          `json:"expired_at"`     // 相应的实例的到期时间(包含未生效续费订单的部分时间)
}

func (p *PriceInfo) Invalid() (err error) {
	if len(p.MachineID) == 0 || len(p.ChargeType) == 0 || p.Num <= 0 {
		err = businesserror.ErrInvalidRequestParams
	}
	return
}

func (p *PriceInfo) ToFinallyDealPrice() int64 {
	if p.DataDiskInfo != nil {
		return p.DealPrice + p.DataDiskInfo.DataDiskDealPrice
	}
	return p.DealPrice
}

func (p *PriceInfo) ToFinallyOriginPrice() int64 {
	if p.DataDiskInfo != nil {
		return p.OriginPrice + p.DataDiskInfo.DataDiskOriginPrice
	}
	return p.OriginPrice
}

type MachineSnapShot struct {
	mm.BaseMachineInfo
	CpuUsed             int64                   `json:"cpu_used"`
	MemUsed             int64                   `json:"mem_used"`
	MaxInstanceDiskSize int64                   `json:"max_instance_disk_size"` // 单位为byte
	GpuTypeID           int                     `json:"gpu_type_id,omitempty"`
	GpuType             MachineGpuType          `json:"gpu_type"`
	MachineSku          []*mm.SkuInfo           `json:"machine_sku"`
	RegionSign          constant.RegionSignType `json:"region_sign"`
	RegionName          string                  `json:"region_name"`
}

type MachineGpuType struct {
	Name   string `json:"name"`
	Memory int64  `json:"memory"`
}

type RefundInfo struct {
	RefundAsset int64              `json:"refund_asset,omitempty"`
	CostAsset   int64              `json:"cost_asset,omitempty"`
	RefundType  constant.OrderType `json:"refund_type,omitempty"`
	OrderUUID   string             `json:"order_uuid,omitempty"` // 记录退款订单的uuid
	BeginAt     time.Time          `json:"begin_at,omitempty"`
	EndAt       time.Time          `json:"end_at,omitempty"`
	//FinallyChargeType constant.ChargeType `json:"finally_charge_type,omitempty"`

	// ddp
	SourceAsset        int64               `json:"source_asset,omitempty"`
	SourcePayByVoucher int64               `json:"source_pay_by_voucher,omitempty"`
	SourcePayByBalance int64               `json:"source_pay_by_balance,omitempty"`
	DDPGpuTypeID       int                 `json:"ddp_gpu_type_id,omitempty"`
	DDPGpuType         string              `json:"ddp_gpu_type,omitempty"`
	DDPBalance         int64               `json:"ddp_balance,omitempty"`
	DDPChargeType      constant.ChargeType `json:"ddp_charge_type,omitempty"`
	DDPChargeDuration  int                 `json:"ddp_charge_duration,omitempty"`

	/*
		是否要记录更多信息？
		如：详细的退款明细
	*/
}

// type
type GetPriceResponse struct {
	OriginPrice  int64          `json:"origin_price"` // 若是按量付费，代表非会员的原始价格,若是包日包周等，代表非会员的原始价格
	DealPrice    int64          `json:"deal_price"`   // 包日包周等的交易金额
	DiscountList []DiscountInfo `json:"discount_list"`
	PaygPrice    int64          `json:"payg_price"` // 按量付费的单价
}

type DiscountInfo struct {
	ProductType                constant.ProductType  `json:"product_type"`
	Type                       constant.DiscountType `json:"type"`
	OriginPrice                int64                 `json:"origin_price"`
	TotalReduction             int64                 `json:"total_reduction"`
	DiscountRate               int64                 `json:"discount_rate"`
	CouponDiscount             *CouponDiscountInfo   `json:"coupon_discount,omitempty"`
	ExceptCouponOriginPrice    int64                 `json:"except_coupon_origin_price,omitempty"`    // 不使用优惠券的会员折扣原始金额
	ExceptCouponTotalReduction int64                 `json:"except_coupon_total_reduction,omitempty"` // 不使用优惠券的会员折扣优惠金额
	CouponValidEnd             sql.NullTime          `json:"coupon_valid_end,omitempty"`              // 优惠券的有效截至时间
}

type CouponDiscountInfo struct {
	CouponID         int                 `json:"coupon_id"`
	CouponType       constant.CouponType `json:"coupon_type"`
	Discount         int64               `json:"discount"`
	CashBack         int64               `json:"cash_back"`
	UseConditionInfo UseCondition        `json:"use_condition_info"`
}

type NewPriceInfo struct {
	OriginCostAmount  int64 `json:"origin_cost_amount"`  // 原始包日包月等金额
	CurrentCostAmount int64 `json:"current_cost_amount"` // 会员的包日包周等的交易金额
	PaygPrice         int64 `json:"payg_price"`          // 会员的按量付费的单价
	OriginPaygPrice   int64 `json:"origin_payg_price"`   // 实际按量付费的单价
}

type OrderDetailInfo struct {
	Order *Order `json:"order"`
}

func (o *Order) OrderGet(tx *gorm.DB, filter db_helper.QueryFilters) error {
	return db_helper.GetFirst(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         o,
		Filters:                 filter,
	}, &o).GetError()
}

func (o *Order) OrderUpdate(tx *gorm.DB, filter db_helper.QueryFilters, um map[string]interface{}) (err error) {
	return db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         o,
		Filters:                 filter,
	}, um).GetError()
}

func (o *Order) Mask() {
	o.UserPhone = libs.Mask(o.UserPhone)
}

var productToOrderMap = map[constant.ProductType][]constant.OrderType{
	constant.ProductTypeInstance: {
		constant.OrderTypeCreateInstance,
		constant.OrderTypeRenewalInstance,
		constant.OrderTypeUpdateInstance,
		constant.OrderTypeCloneInstance,
		constant.OrderTypeImproveConfiguration,
		constant.OrderTypeReduceConfiguration,
		constant.OrderTypeChangePayg,
		constant.OrderTypeChangeYearly,
		constant.OrderTypeChangeMonthly,
		constant.OrderTypeChangeWeekly,
		constant.OrderTypeChangeDaily,
		constant.OrderTypeRefund,
	},
	constant.ProductTypeNetDisk:               {constant.OrderTypeNetDiskExpand, constant.OrderTypeNetDiskRenewal},
	constant.ProductTypeDataDisk:              {constant.OrderTypeDataDiskExpand, constant.OrderTypeDataDiskExpandOfPrepay, constant.OrderTypeDataDiskReduce},
	constant.ProductTypeFileStorage:           {constant.OrderTypeFileStorage, constant.OrderTypeAutoFsInit},
	constant.ProductTypeHSFS:                  {constant.OrderTypeHSFSInit, constant.OrderTypeHSFSReinit, constant.OrderTypeHSFSRenewal, constant.OrderTypeHSFSExpand},
	constant.ProductTypeDeployment:            {constant.OrderTypeCreateDeploymentContainer},
	constant.ProductTypeDeploymentDurationPkg: {constant.OrderTypeCreateDeploymentDurationPkg, constant.OrderTypeDeploymentDurationPkgRefund},
	constant.ProductTypePrivateImage:          {constant.OrderTypePrivateImage},
}

func GetOrderTypesForProduct(product constant.ProductType) []constant.OrderType {
	return productToOrderMap[product]
}
