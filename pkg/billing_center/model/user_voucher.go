package model

import (
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/libs"
	"time"

	"gorm.io/gorm"
)

const TableNameUserVoucher = "user_voucher"

type UserVoucher struct {
	ID           int                   `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	Uid          int                   `gorm:"column:uid" json:"uid"`
	UserPhone    string                `gorm:"type:varchar(30);column:user_phone" json:"user_phone"`
	IssuerID     int                   `gorm:"column:issuer_id" json:"issuer_id"`
	Type         constant.VoucherType  `gorm:"type:varchar(255);column:voucher_type" json:"voucher_type"`
	VoucherID    int                   `gorm:"column:voucher_id" json:"voucher_id"`
	VoucherTitle string                `gorm:"type:varchar(255);column:voucher_title" json:"voucher_title"` // 快照，不随voucher变化
	Asset        int64                 `gorm:"column:asset" json:"asset"`
	Balance      int64                 `gorm:"column:balance" json:"balance"`
	UsedStatus   constant.UVUsedStatus `gorm:"type:varchar(255);column:used_status" json:"used_status"`
	//UseSku       constant.VoucherUseSku `gorm:"type:varchar(255);column:use_sku" json:"use_sku"`
	UseProduct    constant.ProductType         `gorm:"column:use_product;type:varchar(255)" json:"use_product"`                             // 适用产品类型
	UseChargeType constant.CouponUseChargeType `gorm:"column:use_charge_type;type:varchar(255);NOT NULL;default:''" json:"use_charge_type"` // 适用计费方式
	// 适用范围
	ScopeOfApplicationType   constant.VoucherScopeOfApplicationType `gorm:"column:scope_of_application_type;type:varchar(255)" json:"scope_of_application_type"`
	ScopeOfApplicationEntity VoucherScopeOfApplication              `gorm:"serializer:json;type:json;column:scope_info" json:"scope_info"`
	ConditionalScore         int                                    `gorm:"column:conditional_score;default 0" json:"conditional_score"` // 条件得分，分数越高条件越苛刻

	// 是否自动抵扣消费 默认为true
	IsAutoUse bool `gorm:"column:is_auto_use;type:tinyint(1);default:1;" json:"is_auto_use"`

	CreatedAt time.Time  `gorm:"type:datetime;column:created_at" json:"created_at"` // 创建时间, 领取时间
	UpdatedAt time.Time  `gorm:"type:datetime;column:updated_at" json:"updated_at"`
	InvalidAt time.Time  `gorm:"type:datetime;column:invalid_at" json:"invalid_at"` // 有效期截止
	UseAt     *time.Time `gorm:"type:datetime;column:use_at" json:"use_at"`         // 使用时间，最后一次使用的时间
}

func (v *UserVoucher) TableName() string {
	return TableNameUserVoucher
}

func (v *UserVoucher) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&UserVoucher{})
}

func (v *UserVoucher) UserVoucherGet(filter db_helper.QueryFilters) error {
	return db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: v,
		Filters:         filter,
	}, &v).GetError()
}

func (v *UserVoucher) UserVoucherUpdate(tx *gorm.DB, filters *db_helper.QueryFilters, um map[string]interface{}) error {
	um["updated_at"] = time.Now()
	return db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         &UserVoucher{},
		Filters:                 *filters,
	}, um).GetError()
}

func (v *UserVoucher) GetUID() int {
	return v.Uid
}

func (v *UserVoucher) FillPhone(phone string) {
	v.UserPhone = phone
}

func (v *UserVoucher) Mask() {
	v.UserPhone = libs.Mask(v.UserPhone)
}

// ----------------------------------------------------

type VoucherUserGetListParams struct {
	VoucherID  int                   `json:"voucher_id"` // 代金券id
	UsedStatus constant.UVUsedStatus `json:"used_status"`
	UserPhone  string                `json:"user_phone"` // 领取用户

	UID int `json:"-"`
}

func (v *VoucherUserGetListParams) Check() error {
	if v.VoucherID == 0 {
		return businesserror.ErrInvalidRequestParams
	}
	return nil
}

type SumVoucherUsedNum struct {
	VoucherID int `gorm:"column:voucher_id" json:"voucher_id"`
	UsedNum   int `gorm:"column:used_num" json:"used_num"`
}
