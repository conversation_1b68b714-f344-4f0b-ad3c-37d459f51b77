package model

import (
	"encoding/json"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/libs"
	"time"

	log "github.com/sirupsen/logrus"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const TableNameBill = "bill"

type Bill struct {
	ID          int                           `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	UID         int                           `gorm:"column:uid" json:"uid"`
	UserPhone   string                        `gorm:"type:varchar(30);column:user_phone" json:"user_phone"`
	UUID        string                        `gorm:"type:varchar(255);column:uuid;index:bill_uuid" json:"uuid"`
	OrderUUID   string                        `gorm:"type:varchar(255);column:order_uuid" json:"order_uuid"`      // 所属订单
	RuntimeUUID constant.ContainerRuntimeUUID `gorm:"type:varchar(255);column:runtime_uuid" json:"runtime_uuid"`  // 所属容器
	ProductUUID string                        `gorm:"type:varchar(255);column:product_uuid;" json:"product_uuid"` // 实例/闲时 uuid

	Type         constant.BillType    `gorm:"type:varchar(255);column:bill_type" json:"bill_type"`     // 账单类型.充值,退款,扣费
	SubType      constant.BillSubType `gorm:"column:bill_sub_type" json:"bill_sub_type"`               // 子类行或者叫操作类型
	Asset        int64                `gorm:"column:asset" json:"asset"`                               // 交易金额,1000倍
	PayByBalance int64                `gorm:"column:pay_by_balance;default 0" json:"pay_by_balance"`   // 余额支付
	PayByVoucher int64                `gorm:"column:pay_by_voucher" json:"pay_by_voucher"`             // 代金券支付
	ChargeType   constant.ChargeType  `gorm:"type:varchar(255);column:charge_type" json:"charge_type"` // 计费方式	按量付费,按周,月,年
	Balance      int64                `gorm:"column:balance" json:"balance"`                           // 此时用户钱包余额

	DetailsJson    datatypes.JSON  `gorm:"type:json;column:details" json:"-"`
	DetailsEntity  *BillDetail     `gorm:"-" json:"details"` // 详情
	DiscountJson   []byte          `gorm:"type:json;column:discount" json:"-"`
	DiscountEntity *DiscountDetail `gorm:"-" json:"discount_detail"` // 折扣详情
	CreatedAt      time.Time       `gorm:"type:datetime;column:created_at" json:"created_at"`
	UpdatedAt      time.Time       `gorm:"type:datetime;column:updated_at" json:"updated_at"`
	ConfirmAt      *time.Time      `gorm:"type:datetime;column:confirm_at" json:"confirm_at"`
}

type BillDetail struct {
	// 计费周期，因为历史代码原因，from和to无法直接unmarshal到time.Time类型上
	// 又因为这两个字段都是展示用的，代码里基本不会用到，所以此处继续使用string类型
	ChargeFrom     string `json:"charge_from,omitempty"`
	ChargeTo       string `json:"charge_to,omitempty"`
	ChargeDuration int64  `json:"charge_duration,omitempty"`

	ExpandSize       int64                   `json:"expand_size,omitempty"`
	ExpandChangeSize int64                   `json:"expand_change_size,omitempty"`
	RegionSign       constant.RegionSignType `json:"region_sign,omitempty"` // 不是date_center
	DateCenter       string                  `json:"date_center,omitempty"`
	RegionName       string                  `json:"region_name,omitempty"`

	SubName string `json:"sub_name,omitempty"` // 如果这个账单是子用户的实例产生的，那么非空

	FileStorageCostSize int64 `json:"file_storage_cost_size,omitempty"` // 文件存储计算容量

	PrivateImageCostSize int64 `json:"private_image_cost_size,omitempty"` // 私有镜像计算容量 计算容量没有使用统一的字段，使用含有明确意义的前缀

	RechargePathway constant.RechargePathway `json:"recharge_pathway,omitempty"`

	// deployment相关
	DeploymentUUID            string              `json:"deployment_uuid,omitempty"`
	DeploymentContainerUUID   string              `json:"deployment_container_uuid,omitempty"`
	BasicPrice                int64               `json:"basic_price,omitempty"`
	PriceFloatingRate         int64               `json:"price_floating_rate,omitempty"`
	GpuNum                    int                 `json:"gpu_num,omitempty"`
	GpuTypeID                 int                 `json:"gpu_type_id,omitempty"`
	GpuType                   string              `json:"gpu_type,omitempty"`
	DDPUsedList               []BillDetailDDPUsed `json:"ddp_used_list,omitempty"`
	DDPConvertedIncome        int64               `json:"ddp_converted_income,omitempty"`         // ddp折算的收入
	DDPConvertedVoucherIncome int64               `json:"ddp_converted_voucher_income,omitempty"` // ddp折算的收入,代金券
	DDPDataCenterListStr      string              `json:"ddp_data_center_list_str,omitempty"`     // ddp data_center
	// 经过ddp扣减后，剩余的计费时长，单位 卡*时。用于计算应使用钱包付款的金额，bill.asset = 剩余卡时/总卡时*bill.asset
	AfterDDPChargeResidualDuration int64 `json:"after_ddp_charge_residual_duration,omitempty"`

	BankTransferSerialNumber string `json:"bank_transfer_serial_number,omitempty"`

	RefundUuid string `json:"refund_uuid,omitempty"`
}

type BillDetailDDPUsed struct {
	OrderUUID     string `json:"order_uuid,omitempty"`
	SourceBalance int64  `json:"source_balance,omitempty"`
	// 计费时长，单位：卡*时
	DDPChargeDuration int64 `json:"ddp_charge_duration,omitempty"`
	Balance           int64 `json:"balance,omitempty"`
}

func (b *Bill) TableName() string {
	return TableNameBill
}

func (b *Bill) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&Bill{})
}

func (b *Bill) BeforeCreate(db *gorm.DB) (err error) {
	if len(b.UUID) == 0 {
		b.UUID = libs.RandNumberString()
	}

	if b.DetailsEntity != nil {
		b.DetailsJson, err = json.Marshal(b.DetailsEntity)
		if err != nil {
			return
		}
	}

	if b.DiscountEntity != nil {
		b.DiscountJson, err = json.Marshal(b.DiscountEntity)
	}
	return
}

func (b *Bill) AfterFind(db *gorm.DB) (err error) {
	if len(b.DetailsJson) != 0 {
		b.DetailsEntity = &BillDetail{}
		err = json.Unmarshal(b.DetailsJson, &b.DetailsEntity)
		if err != nil {
			return
		}
	}

	// if len(b.DiscountJson) != 0 {
	//	b.DiscountEntity = &DiscountDetail{}
	//	err = json.Unmarshal(b.DiscountJson, &b.DiscountEntity)
	// }
	return
}

func (b *Bill) GetUID() int {
	return b.UID
}

func (b *Bill) FillPhone(p string) {
	b.UserPhone = p
}

func (b *Bill) BeforeCreateCheck() (err error) {
	if b.UID == 0 {
		log.Info("create bill params error: uid is 0")
		return businesserror.ErrInvalidRequestParams
	}

	// ...

	return
}

func (b *Bill) SetChargeDuration(from, to *time.Time) {
	if b.Type != constant.BillTypeRecharge {
		if b.Type == constant.BillTypeCharge && from != nil && to != nil {
			if b.DetailsEntity == nil {
				b.DetailsEntity = &BillDetail{}
			}
			b.DetailsEntity.ChargeFrom = from.Format(constant.FormatTimeString)
			b.DetailsEntity.ChargeTo = to.Format(constant.FormatTimeString)
			b.DetailsEntity.ChargeDuration = to.Unix() - from.Unix()

		}
	}
}

func (b *Bill) SetDiscountInfo(originCost int64) {
	// 计算折扣信息
	originPrice := libs.AssetRound(originCost, b.ChargeType.To001Limit())
	discountSum := originPrice - b.Asset
	b.DiscountEntity = &DiscountDetail{OriginPrice: originPrice, DiscountSum: discountSum}
}

func (b *Bill) LoadDiscount() (err error) {
	b.DiscountEntity = new(DiscountDetail)
	if len(b.DiscountJson) > 0 {
		err = json.Unmarshal(b.DiscountJson, &b.DiscountEntity)
		if err != nil {
			return err
		}
	}
	return
}

// 带正负号的金额, 英语叫啥
func (b *Bill) A() int64 {
	if b.Type == constant.BillTypeCharge {
		return b.Asset * -1
	}
	return b.Asset
}

// --------------------------------------------------------------

// SettleOnceRequest payg定时结算和关闭实例结算请求体
type SettleOnceRequest struct {
	SettleTime     time.Time                     `json:"settle_time"` // 本次结算时间. 定时结算与结束结算使用同一个接口
	InstanceUIDMap map[int][]*SettleInstanceList `json:"instance_uid_map"`
}

// SettleInstanceList 用于将这个结构体供其他包访问.
type SettleInstanceList struct {
	ID              int                              `json:"id"`
	UID             int                              `json:"uid"`
	SubName         string                           `json:"sub_name"`
	UserPhone       string                           `json:"user_phone"`
	MachineID       string                           `json:"machine_id"`   // 机器id
	OrderUUID       string                           `json:"order_uuid"`   // 订单uuid
	ProductUUID     string                           `json:"product_uuid"` // 上层UUID
	RuntimeUUID     constant.ContainerRuntimeUUID    `json:"runtime_uuid"` // 实例uuid
	RuntimeType     constant.ContainerRuntimeType    `json:"runtime_type"`
	LastSettledAt   time.Time                        `json:"last_settled_at"`   // 上次结算时间
	PaygPriceBasic  int64                            `json:"payg_price_basic"`  // 按量付费原价
	PaygPrice       int64                            `json:"payg_price"`        // 按量付费价格，浮动后的价格
	OriginPaygPrice int64                            `json:"origin_payg_price"` // 原始的按量付费价格
	OperateType     constant.UpdateWalletOperateType `json:"operate_type"`      // 操作类型, 定时结算, 关机结算
	ChargeType      constant.ChargeType              `json:"charge_type"`       // 计费类型，主要用于无卡启动
	Note            ChargingNote                     `json:"note"`
}

type SettleOnceAsyncRequest struct {
	SettleTime time.Time `json:"settle_time"`
	SettleInstanceList
}

type BillGetListParam struct {
	UID         int               `json:"uid"`
	UserPhone   string            `json:"user_phone"`
	BillUUID    string            `json:"bill_uuid"`
	OrderUUID   string            `json:"order_uuid"`
	ProductUUID string            `json:"instance_uuid"`
	RuntimeUUID string            `json:"runtime_uuid"`
	SubName     string            `json:"sub_name"`
	BillType    constant.BillType `json:"bill_type"`
}

type DiscountDetail struct {
	OriginPrice  int64              `json:"origin_price"`
	DiscountSum  int64              `json:"discount_sum"`
	DiscountList []BillDiscountList `json:"discount_list,omitempty"`
}

type BillDiscountList struct {
	ProductType constant.ProductType  `json:"product_type"`
	Type        constant.DiscountType `json:"type"`
	OriginPrice int64                 `json:"origin_price"`
	TotalReduce int64                 `json:"total_reduce"`
}

func (b *Bill) UpdateWalletNotNeedCreateBill() bool {
	switch b.SubType {
	case constant.BillSubTypeCreateContainer:
		// 创建按量计费的容器时付款0元, 不生成账单
		if b.ChargeType.IsPayg() {
			return true
		}
	case constant.BillSubTypeDataDiskSettle, constant.BillSubTypeDataDiskExpandForPrepay:
		// 数据盘定时扣费, 如果扣费金额为0, 则不生成账单.(只为了update ddc)
		if b.Asset == 0 {
			return true
		}
	case constant.BillSubTypeDataDiskChange:
		return true
	}

	return false
}

func (b *Bill) BillCanUseVoucher() bool {
	switch b.SubType {
	case constant.BillSubTypeNetDiskExpand, constant.BillSubTypeNetDiskRenewal:
		return false
	}

	return true
}

func (b *Bill) Mask() {
	b.UserPhone = libs.Mask(b.UserPhone)
}
