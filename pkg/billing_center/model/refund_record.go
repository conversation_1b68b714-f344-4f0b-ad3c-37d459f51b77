package model

import (
	"log"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"

	"gorm.io/gorm"
)

const TableNameRefundRecord = "refund_record"

type RefundRecord struct {
	ID         int                      `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	UID        int                      `gorm:"column:uid;index" json:"uid"`
	BillUUID   string                   `gorm:"column:bill_uuid;type:varchar(255)" json:"bill_uuid"` // 原充值BillUuid
	RefundUUID string                   `gorm:"column:refund_uuid;type:varchar(255)" json:"refund_uuid"`
	Pathway    constant.RechargePathway `gorm:"column:pathway" json:"pathway"`                 // 计费方式
	Assets     int64                    `gorm:"column:asset" json:"asset"`                     // 本次计费金额
	Status     constant.RefundStatus    `gorm:"column:status;type:varchar(255)" json:"status"` // 退款状态

	Reason     string     `gorm:"column:reason;type:varchar(255)" json:"reason"`           // 原因
	CreatedAt  time.Time  `gorm:"type:datetime;column:created_at" json:"created_at"`       // 创建时间
	UpdatedAt  time.Time  `gorm:"type:datetime;column:updated_at" json:"updated_at"`       // 更新时间
	CallbackAt *time.Time `gorm:"type:datetime;column:callback_at" json:"callback_at"`     // 回调时间
	RefundTime *time.Time `gorm:"type:datetime;column:refund_time" json:"refund_time"`     // 退款时间
	PayAccount string     `gorm:"type:varchar(255);column:pay_account" json:"pay_account"` // 支付平台账号
	// 提现操作人
	AuditorUid int `gorm:"type:varchar(255);column:auditor_uid" json:"auditor_uid"`

	IsDelayRefund   bool `gorm:"type:tinyint(1);default 0;column:is_delay_refund" json:"is_delay_refund"`       // 是否延迟执行，默认立即退款
	IsPassRefundApi bool `gorm:"type:tinyint(1);default 0;column:is_pass_refund_api" json:"is_pass_refund_api"` // 只对延迟退款有效，用来标记当前记录已经调用过退款api,为true才能主动查询更新状态
}

func (r *RefundRecord) TableName() string {
	return TableNameRefundRecord
}

func (r *RefundRecord) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&RefundRecord{})
}

func (r *RefundRecord) RRCreate(tx *gorm.DB) error {
	return db_helper.InsertOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         r,
		InsertPayload:           r,
	}).GetError()
}

func (r *RefundRecord) RRCount(filters *db_helper.QueryFilters) (int64, error) {
	var count int64
	err := db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: r,
		Filters:         *filters,
	}, &count).GetError()
	return count, err
}

func (r *RefundRecord) RRGetFirst(filters *db_helper.QueryFilters) error {
	return db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: r,
		Filters:         *filters,
	}, &r).GetError()
}

func (r *RefundRecord) RRGetWithSelect(s string, filter *db_helper.QueryFilters, result interface{}) (err error) {
	return db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: r,
		Select:          s,
		Filters:         *filter,
		NoLimit:         true,
	}, result).GetError()
}

func (r *RefundRecord) RRGetAll(filters db_helper.QueryFilters) ([]RefundRecord, error) {
	list := make([]RefundRecord, 0)
	err := db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: r,
		Filters:         filters,
		NoLimit:         true,
	}, &list).GetError()

	return list, err
}

func (r *RefundRecord) RRUpdate(tx *gorm.DB, filters *db_helper.QueryFilters, um map[string]interface{}) error {
	um["updated_at"] = time.Now()
	return db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         &RefundRecord{},
		Filters:                 *filters,
	}, um).GetError()
}

func (r *RefundRecord) RRGetPagedList(filter GetRefundRecordListFilter, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []*RefundRecord, err error) {
	db := db_helper.GlobalDBConn().Table(TableNameRefundRecord)

	if filter.Uid != 0 {
		db = db.Where("uid = ?", filter.Uid)
	}

	if len(filter.RefundUuids) != 0 {
		db = db.Where("refund_uuid in (?)", filter.RefundUuids)
	}

	if filter.Status != "" {
		db = db.Where("status = ?", constant.RefundStatus(filter.Status))
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		log.Printf("Count failed: %v\n", err)
		return
	}

	paged = db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, int(count), 0)

	// 按照创建时间进行排序
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("id desc").
		Find(&list).
		Error
	if err != nil {
		log.Printf("get refund record list failed: %v\n", err)
		return
	}

	return
}
