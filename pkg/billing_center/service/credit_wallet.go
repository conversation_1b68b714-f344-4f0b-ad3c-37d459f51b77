package service

import (
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"server/conf"
	"server/pkg/billing_center/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/libs"
	"server/plugin/queue_interface"
	redis "server/plugin/redis_plugin"
	"time"
)

// 查询授信信息
func (svc *BCService) creditWalletGet(uid int) (cw *model.CreditWallet, err error) {
	err = db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: &model.CreditWallet{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uid": uid}},
	}, &cw).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		svc.log.WithError(err).WithField("uid", uid).Error("get cw failed")
		err = businesserror.ErrDatabaseError
	}
	return
}

// CreditWalletGetList 查询授信信息列表
func (svc *BCService) CreditWalletGetList(db *gorm.DB, params *model.CreditWalletGetListParams, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []model.CreditWallet, err error) {
	if params == nil {
		params = &model.CreditWalletGetListParams{}
	}
	if pageReq == nil {
		pageReq = &db_helper.GetPagedRangeRequest{}
	}

	list = make([]model.CreditWallet, 0)

	if db == nil {
		db = db_helper.GlobalDBConn()
	}

	db = db.Table(model.TableNameCreditWallet)

	if params.UID != 0 {
		db = db.Where("uid = ?", params.UID)
	}

	if pageReq.DateFrom != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') >= ?", pageReq.DateFrom)
	}
	if pageReq.DateTo != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') <= ?", pageReq.DateTo)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Warn("Count failed.")
		err = businesserror.ErrDatabaseError
		return
	}

	paged = db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, int(count), 0)

	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("id desc").
		Find(&list).
		Error
	if err != nil {
		svc.log.WithError(err).Warn("get cw list failed.")
		err = businesserror.ErrDatabaseError
	}
	paged.List = list

	return
}

// CreditWalletHistoryGetList 查询授信历史
func (svc *BCService) CreditWalletHistoryGetList(db *gorm.DB, params *model.CreditWalletHistoryGetListParams, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []model.CreditWalletHistory, err error) {
	if params == nil {
		params = &model.CreditWalletHistoryGetListParams{}
	}
	if pageReq == nil {
		pageReq = &db_helper.GetPagedRangeRequest{}
	}

	list = make([]model.CreditWalletHistory, 0)

	if db == nil {
		db = db_helper.GlobalDBConn()
	}

	db = db.Table(model.TableNameCreditWalletHistory).
		Where("operate_type in (?)", []constant.CreditWalletOperateType{constant.CreditWalletOperateLoan, constant.CreditWalletOperateRepay})

	if params.UID != 0 {
		db = db.Where("uid = ?", params.UID)
	}

	if pageReq.DateFrom != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') >= ?", pageReq.DateFrom)
	}
	if pageReq.DateTo != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') <= ?", pageReq.DateTo)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Warn("Count failed.")
		err = businesserror.ErrDatabaseError
		return
	}

	paged = db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, int(count), 0)

	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("id desc").
		Find(&list).
		Error
	if err != nil {
		svc.log.WithError(err).Warn("get cw history list failed.")
		err = businesserror.ErrDatabaseError
	}
	paged.List = list

	return
}

// CreditWalletCreate 开通
func (svc *BCService) CreditWalletCreate(newCW *model.CreditWallet) (err error) {
	if newCW == nil {
		err = businesserror.ErrInvalidRequestParams
		return err
	}
	err = newCW.BeforeCreateCheck()
	if err != nil {
		return err
	}

	var (
		cw *model.CreditWallet
	)

	cw, err = svc.creditWalletGet(newCW.UID)
	if err != nil {
		svc.log.WithError(err).WithField("uid", newCW.UID).Error("get cw failed")
		return err
	}

	if cw != nil {
		return businesserror.ErrCreditWalletExist
	}

	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) error {
		err = db_helper.InsertOne(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.CreditWallet{},
			InsertPayload:           newCW,
		}).GetError()
		if err != nil {
			svc.log.WithError(err).Error("create cw failed")
			err = businesserror.ErrDatabaseError
			return err
		}

		err = svc.creditWalletHistoryInsert(tx, &model.CreditWalletHistory{
			UID:             newCW.UID,
			UserPhone:       newCW.UserPhone,
			OperateType:     constant.CreditWalletOperateCreate,
			OperateAmount:   newCW.CreditLimit,
			CreditLimit:     newCW.CreditLimit,
			CreditLimitUsed: 0,
		})
		if err != nil {
			svc.log.WithField("newCW", newCW).Error("create cw operate history failed")
			return err
		}
		return nil
	})
	if err != nil {
		svc.log.WithError(err).Error("transaction failed")
	}

	return
}

// 更新cw，带乐观锁
func (svc *BCService) creditWalletUpdate(tx *gorm.DB, uid int, lock int64, updateMap map[string]interface{}) (err error) {
	if uid == 0 || updateMap == nil {
		return businesserror.ErrInvalidRequestParams
	}

	affectRows, errDB := db_helper.UpdateOneAffectRows(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         &model.CreditWallet{},
		Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uid": uid, "credit_lock": lock}},
	}, updateMap)
	if errDB.IsNotNil() {
		svc.log.WithError(err).Error("update cw failed")
		err = businesserror.ErrDatabaseError
		return
	}

	if affectRows == 0 {
		err = businesserror.ErrServerBusy
	}
	return err
}

// 插入操作记录
func (svc *BCService) creditWalletHistoryInsert(tx *gorm.DB, newHistory *model.CreditWalletHistory) (err error) {
	if newHistory == nil {
		err = businesserror.ErrInvalidRequestParams
		return
	}
	err = newHistory.BeforeCreateCheck()
	if err != nil {
		return err
	}

	err = db_helper.InsertOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         &model.CreditWalletHistory{},
		InsertPayload:           newHistory,
	}).GetError()
	if err != nil {
		svc.log.WithError(err).Error("create cw operate history failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

// CreditWalletAdjustmentQuota 调额
func (svc *BCService) CreditWalletAdjustmentQuota(params *model.CreditWalletAdjustmentQuotaParams) (err error) {
	if params == nil {
		err = businesserror.ErrInvalidRequestParams
		return
	}

	var (
		cw              *model.CreditWallet
		operate         = constant.CreditWalletOperateIncreaseLimit
		adjustmentQuota int64
		log             = svc.log.WithField("params", params)
	)

	cw, err = svc.creditWalletGet(params.UID)
	if err != nil {
		log.Error("get cw failed")
		return
	}
	if cw == nil {
		err = businesserror.ErrCreditWalletUserNotFound
		return
	}

	if params.CreditLimit > cw.CreditLimit {
		adjustmentQuota = params.CreditLimit - cw.CreditLimit
	} else if params.CreditLimit < cw.CreditLimit {
		if params.CreditLimit < cw.CreditLimitUsed {
			err = businesserror.ErrCreditWalletReduceCreditLimitLessThanUsed
			return err
		}
		operate = constant.CreditWalletOperateReduceLimit
		adjustmentQuota = cw.CreditLimit - params.CreditLimit
	} else {
		return nil
	}

	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) error {
		// 更新额度
		err = svc.creditWalletUpdate(tx, params.UID, cw.CreditLock, map[string]interface{}{
			"credit_limit": params.CreditLimit,
			"credit_lock":  cw.CreditLock + 1})
		if err != nil {
			log.WithError(err).Error("update cw failed")
			return businesserror.ErrDatabaseError
		}

		// 插入操作记录
		err = svc.creditWalletHistoryInsert(tx, &model.CreditWalletHistory{
			UID:             params.UID,
			UserPhone:       cw.UserPhone,
			OperateType:     operate,
			OperateAmount:   adjustmentQuota,
			CreditLimit:     params.CreditLimit,
			CreditLimitUsed: cw.CreditLimitUsed,
		})
		if err != nil {
			log.WithError(err).Error("insert cw operate history failed")
			return err
		}
		return nil
	})
	if err != nil {
		svc.log.WithError(err).Error("transaction failed")
	}

	return
}

// CreditWalletLoan 借款
func (svc *BCService) CreditWalletLoan(params *model.CreditWalletLoanParams) (err error) {
	var (
		cw                 *model.CreditWallet
		log                = svc.log.WithField("uid", params.UID).WithField("asset", params.Asset)
		lock               *redis.RedisMutex
		now                = time.Now()
		queueTxMsgUUIDList []string
		afterHookAsset     = params.Asset
	)
	if conf.GetGlobalGsConfig().App.DebugApi {
		afterHookAsset /= 1000
	}

	// 加锁
	lock, err = svc.setLockForUpdateWallet(params.UID)
	if err != nil {
		return err
	}
	defer func() {
		_ = lock.UnLock()
	}()

	// 获取信用钱包
	cw, err = svc.creditWalletGet(params.UID)
	if err != nil {
		log.WithError(err).Error("get cw failed")
		return err
	}
	if cw == nil {
		return businesserror.ErrCreditWalletNotCreated
	}
	if params.Asset > cw.CreditLimitAvailable { // 查看借款额度
		return businesserror.ErrCreditWalletCreditLimitNotEnough
	}

	// afterHook
	afterHook := &queue_interface.NewQueueForUserRecharge{
		UserId:    params.UID,
		UserPhone: cw.UserPhone,
		Asset:     afterHookAsset,
		SubType:   queue_interface.AfterUserRecharge,
	}

	// 账单
	bill := &model.Bill{
		UID:          params.UID,
		UserPhone:    cw.UserPhone,
		UUID:         libs.RandNumberString(),
		Type:         constant.BillTypeRecharge,
		SubType:      constant.BillSubTypeRechargeCreditWallet,
		Asset:        params.Asset,
		PayByBalance: params.Asset,
		ConfirmAt:    &now,
	}

	// 充值记录
	rr := &model.RechargeRecord{
		UID:        params.UID,
		UserPhone:  cw.UserPhone,
		BillUUID:   bill.UUID,
		Pathway:    constant.CreditWalletPay,
		Assets:     params.Asset,
		ExpireAt:   &now,
		CallbackAt: &now,
	}

	// 信用钱包 cw updateMap
	updateMap := map[string]interface{}{
		"credit_lock":       cw.CreditLock + 1,
		"credit_limit_used": cw.CreditLimitUsed + params.Asset,
	}

	// 使用历史 cw history
	cwHistory := &model.CreditWalletHistory{
		UID:             params.UID,
		UserPhone:       cw.UserPhone,
		OperateType:     constant.CreditWalletOperateLoan,
		OperateAmount:   params.Asset,
		CreditLimit:     cw.CreditLimit,
		CreditLimitUsed: cw.CreditLimitUsed,
	}

	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) error {
		// 更新钱包
		err = svc.updateWallet(tx, bill)
		if err != nil {
			svc.log.WithError(err).Error("update wallet failed")
			return err
		}

		// 发送消息
		queueTxMsgUUIDList, err = svc.afterUpdateWallet(tx, &model.AfterUpdateWalletParams{
			Bill:             bill,
			AfterOperateType: constant.UpdateWalletSkip,
			AfterHook:        afterHook,
		})
		if err != nil {
			svc.log.WithError(err).Error("after update wallet failed")
			return err
		}

		// 创建bill
		err = svc.createBill(tx, bill)
		if err != nil {
			svc.log.WithError(err).Error("insert into bill failed")
			return err
		}

		// 创建rr
		err = svc.rechargeRecordCreate(tx, rr)
		if err != nil {
			svc.log.WithError(err).Error("create rr failed")
			return err
		}

		// 更新cw
		err = svc.creditWalletUpdate(tx, params.UID, cw.CreditLock, updateMap)
		if err != nil {
			log.WithError(err).Error("update cw failed")
			return err
		}

		// 插入history
		err = svc.creditWalletHistoryInsert(tx, cwHistory)
		if err != nil {
			log.WithError(err).Error("insert cw operate history failed")
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}

	if len(queueTxMsgUUIDList) != 0 {
		errMsg := svc.q.Pub(queueTxMsgUUIDList...)
		if len(errMsg) != 0 {
			svc.log.Warn("Pub tx msg failed [%s]. msg_uuid_list: %+v", errMsg, queueTxMsgUUIDList)
			err = businesserror.ErrInternalError
			return
		}
	}

	return
}

// CreditWalletRepay 还款
func (svc *BCService) CreditWalletRepay(params *model.CreditWalletRepayParams) (err error) {
	var (
		cw  *model.CreditWallet
		log = svc.log.WithField("uid", params.UID).WithField("asset", params.Asset)
	)

	// 获取信用钱包
	cw, err = svc.creditWalletGet(params.UID)
	if err != nil {
		log.WithError(err).Error("get cw failed")
		return err
	}
	if cw == nil {
		return businesserror.ErrCreditWalletNotCreated
	}

	// 查看还款额度
	if params.Asset > cw.CreditLimitUsed {
		return businesserror.ErrCreditWalletRepayCreditLimitUsedLimit
	}

	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) error {
		// 更新cw
		err = svc.creditWalletUpdate(tx, params.UID, cw.CreditLock, map[string]interface{}{
			"credit_lock":       cw.CreditLock + 1,
			"credit_limit_used": cw.CreditLimitUsed - params.Asset,
		})
		if err != nil {
			log.WithError(err).Error("update cw failed")
			return err
		}

		// 插入history
		err = svc.creditWalletHistoryInsert(tx, &model.CreditWalletHistory{
			UID:             params.UID,
			UserPhone:       cw.UserPhone,
			OperateType:     constant.CreditWalletOperateRepay,
			OperateAmount:   params.Asset,
			CreditLimit:     cw.CreditLimit,
			CreditLimitUsed: cw.CreditLimitUsed,
			Remark:          params.Remark,
		})
		if err != nil {
			log.WithError(err).Error("insert cw operate history failed")
			return err
		}
		return nil
	})
	if err != nil {
		svc.log.WithError(err).Error("transaction failed")
	}

	return
}
