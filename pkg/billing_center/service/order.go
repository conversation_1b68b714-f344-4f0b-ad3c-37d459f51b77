package service

import (
	"encoding/json"
	coreGpuStocModel "server/pkg-core/gpu_stock/model"
	"server/pkg/billing_center/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	instanceModel "server/pkg/instance/model"
	"server/pkg/libs"
	userModel "server/pkg/user/model"
	"server/plugin/queue_interface"
	redis "server/plugin/redis_plugin"
	tsc "server/plugin/redis_plugin/time_service_center"
	"time"

	log "github.com/sirupsen/logrus"

	"github.com/pkg/errors"
	"gorm.io/gorm"
)

// -------------------- API ------------------------

func (svc *BCService) CreateOrder(params *model.Order) (detail *model.Order, err error) {
	return svc.createOrder(params)
}

func (svc *BCService) CreateOrderWithTx(tx *gorm.DB, params *model.Order) (detail *model.Order, err error) {
	return svc.createOrderWithTx(tx, params)
}

func (svc *BCService) CancelOrder(orderUUID string) (err error) {
	return svc.cancelOrder(orderUUID)
}

func (svc *BCService) GetOrderList(params *model.OrderGetListParams, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []*model.Order, err error) {
	return svc.getOrderList(db_helper.GlobalDBConn(), params, pageReq)
}

func (svc *BCService) GetOrderListFromRO(params *model.OrderGetListParams, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []*model.Order, err error) {
	return svc.getOrderList(db_helper.GlobalDBConnForRead(), params, pageReq)
}

func (svc *BCService) GetOrder(uuid string) (res *model.Order, err error) {
	return svc.getOrder(uuid)
}

func (svc *BCService) GetOrderByUUIDList(uuidList []string) (res []model.Order, err error) {
	return svc.getOrderByUUIDList(uuidList)
}

func (svc *BCService) GetOrderDetail(uuid string) (res *model.OrderDetailInfo, err error) {
	return svc.getOrderDetail(uuid)
}

func (svc *BCService) GetSnapshot(uuid string) (*model.MachineSnapShot, error) {
	return svc.getSnapshot(uuid)
}

func (svc *BCService) GetOrderUnused(runtimeUUID constant.ContainerRuntimeUUID) (res []model.Order, err error) {
	return svc.getOrderUnused(runtimeUUID)
}

func (svc *BCService) ExistUnpaidChangeTypeOrder(productUUID string) (exist bool, err error) {
	return svc.existUnpaidChangeTypeOrder(productUUID)
}

func (svc *BCService) CountUserChangeChargeTypeOrder(uid int) (count int64, err error) {
	return svc.countUserChangeChargeTypeOrder(uid)
}

func (svc *BCService) PrepayToPayg(params model.PrepayToPaygInnerParams) (error error) {
	return svc.prepayToPayg(params)
}

func (svc *BCService) ExistUnpaidRenewalOrder(productUUID string) (exist bool, err error) {
	return svc.existUnpaidRenewalOrder(productUUID)
}

func (svc *BCService) PaygToPrepay(params model.PaygToPrepayParams) (err error) {
	return svc.paygToPrepay(params)
}

func (svc *BCService) CreateOrderBillAndUpdateWallet(params model.CreateOrderBillAndUpdateWalletParams) (err error) {
	return svc.createOrderBillAndUpdateWallet(params)
}

func (svc *BCService) CreateOrderNeedTx(tx *gorm.DB, params *model.Order) (detail *model.Order, err error) {
	return svc.createOrderNeedTx(tx, params)
}

// -------------------- private -----------------------
// [order] 创建订单
func (svc *BCService) createOrder(params *model.Order) (detail *model.Order, err error) {
	err = db_helper.NewDBConn().Transaction(func(tx *gorm.DB) (errDB db_helper.ModelError) {
		errDB = db_helper.InsertOne(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.Order{},
			InsertPayload:           params,
		})
		if errDB.IsNotNil() {
			svc.log.WithError(errDB.GetError()).Error("create order failed")
			return
		}

		if params.PriceEntity != nil && len(params.PriceEntity.CouponIDList) != 0 {
			err = svc.userCouponUse(tx, params.PriceEntity.CouponIDList[0])
			if err != nil {
				return
			}
		}

		if params.Status == constant.OrderStatusUnpaid {
			err = svc.countdown.HSet(params.UUID, params.CreatedAt.Add(constant.OrderTimeoutDuration).Format(constant.FormatTimeString))
			if err != nil {
				svc.log.WithField("instanceUUID", params.RuntimeEntity).WithError(err).Error("set order timeout time failed.")
				errDB = db_helper.SetError("set order timeout failed")
				return
			}
		}
		return
	}).GetError()
	if err != nil {
		svc.log.WithError(err).Error("Transaction failed")
		return nil, err
	}

	return params, nil
}

// [order] 创建订单
func (svc *BCService) createOrderWithTx(tx *gorm.DB, params *model.Order) (detail *model.Order, err error) {
	err = db_helper.InsertOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         &model.Order{},
		InsertPayload:           params,
	}).GetError()
	if err != nil {
		svc.log.WithField("params", params).ErrorE(err, "create order failed")
		return
	}

	if params.PriceEntity != nil && len(params.PriceEntity.CouponIDList) != 0 {
		err = svc.userCouponUse(tx, params.PriceEntity.CouponIDList[0])
		if err != nil {
			return
		}
	}

	if params.Status == constant.OrderStatusUnpaid {
		err = svc.countdown.HSet(params.UUID, params.CreatedAt.Add(constant.OrderTimeoutDuration).Format(constant.FormatTimeString))
		if err != nil {
			svc.log.WithField("instanceUUID", params.RuntimeEntity).WithError(err).Error("set order timeout time failed.")
			return
		}
	}
	return params, nil
}

// 创建订单
func (svc *BCService) createOrderNeedTx(tx *gorm.DB, params *model.Order) (detail *model.Order, err error) {
	err = db_helper.InsertOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         &model.Order{},
		InsertPayload:           params,
	}).GetError()
	if err != nil {
		svc.log.WithError(err).Error("create order failed")
		return
	}

	if params.Status == constant.OrderStatusUnpaid {
		err = svc.countdown.HSet(params.UUID, params.CreatedAt.Add(constant.OrderTimeoutDuration).Format(constant.FormatTimeString))
		if err != nil {
			svc.log.WithField("instanceUUID", params.RuntimeEntity).WithError(err).Error("set order timeout time failed.")
			err = businesserror.ErrInternalError
			return
		}
	}
	return params, nil
}

// 创建订单、账单、付款
func (svc *BCService) createOrderBillAndUpdateWallet(params model.CreateOrderBillAndUpdateWalletParams) (err error) {
	if params.NewOrder == nil || params.NewBill == nil {
		return businesserror.ErrInternalError
	}

	var (
		msgUUID []string
	)

	// 加锁, 每个用户一个锁, 加了代金券后， 也使用同一个锁
	lock := new(redis.RedisMutex)
	lock, err = svc.setLockForUpdateWallet(params.NewBill.UID)
	if err != nil {
		return
	}
	defer func() {
		_ = lock.UnLock()
	}()

	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
		if params.NewBill.BillCanUseVoucher() {
			err = svc.voucherUpdateForUpdateWallet(tx, params.NewBill)
			if err != nil {
				svc.log.ErrorE(err, "update voucher failed")
				return err
			}
		}

		// 更新wallet
		err = svc.updateWallet(tx, params.NewBill)
		if err != nil {
			svc.log.WithField("bill", params.NewBill).ErrorE(err, "update wallet failed")
			return
		}

		// 发送hook
		msgUUID, err = svc.afterUpdateWallet(tx, &model.AfterUpdateWalletParams{
			Order:            params.NewOrder,
			Bill:             params.NewBill,
			AfterOperateType: params.OpType,
			AfterHook:        params.AfterHook,
			AfterFunc:        params.AfterFunc,
		})
		if err != nil {
			svc.log.WarnE(err, "after update wallet failed")
			return
		}

		// 创建订单
		params.NewOrder.PayByBalance = params.NewBill.PayByBalance
		_, err = svc.createOrderNeedTx(tx, params.NewOrder)
		if err != nil {
			svc.log.WithError(err).Warn("create order failed")
			return
		}

		if params.NewBill.UpdateWalletNotNeedCreateBill() {
			return
		}

		// 创建账单
		err = svc.createBill(tx, params.NewBill)
		if err != nil {
			svc.log.WithError(err).WithField("bill", params.NewBill).Warn("create bill failed")
		}
		return
	})
	if err != nil && err != businesserror.ErrOptimisticLockingRollbackButNotReturnError {
		svc.log.WithError(err).Warn("transaction failed")
		return
	}

	err = nil
	if len(msgUUID) != 0 {
		errMsg := svc.q.Pub(msgUUID...)
		if len(errMsg) != 0 {
			svc.log.Warn("Pub tx msg failed [%s]. msg_uuid_list: %+v", errMsg, msgUUID)
			return
		}
	}

	return
}

// BatchCreateOrderBillAndUpdateWalletWithOuterLock 创建订单、账单、付款
func (svc *BCService) BatchCreateOrderBillAndUpdateWalletWithOuterLock(newOrder *model.Order, newBill *model.Bill, opType constant.UpdateWalletOperateType, afterHook queue_interface.ElementPayloadContent) (err error) {
	if newOrder == nil || newBill == nil {
		return businesserror.ErrInternalError
	}

	var (
		msgUUID []string
	)

	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
		if newBill.BillCanUseVoucher() {
			err = svc.voucherUpdateForUpdateWallet(tx, newBill)
			if err != nil {
				log.WithError(err).Error("update voucher failed")
				return err
			}
		}

		// 更新wallet
		err = svc.updateWallet(tx, newBill)
		if err != nil {
			svc.log.WithError(err).WithField("bill", newBill).Error("update wallet failed")
			return
		}

		// 发送hook
		msgUUID, err = svc.afterUpdateWallet(tx, &model.AfterUpdateWalletParams{
			Order:            newOrder,
			Bill:             newBill,
			AfterOperateType: opType,
			AfterHook:        afterHook,
		})
		if err != nil {
			svc.log.WithError(err).Warn("after update wallet failed")
			return
		}

		// 创建订单
		if newBill.Type != constant.BillTypeRefund {
			newOrder.PayByBalance = newBill.PayByBalance
		}

		if newBill.Type == constant.BillTypeRefund {
			order := &model.Order{}
			err = order.OrderUpdate(tx, db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"uuid": newOrder.RefundEntity.OrderUUID,
				}},
				map[string]interface{}{
					"refund_amount": newBill.PayByBalance,
				},
			)
			if err != nil {
				svc.log.WithField("order_uuid", newOrder.RefundEntity.OrderUUID).ErrorE(err, "update order refund amount failed")
				return err
			}
		}
		_, err = svc.createOrderNeedTx(tx, newOrder)
		if err != nil {
			svc.log.WithError(err).Warn("create order failed")
			return
		}

		if newBill.UpdateWalletNotNeedCreateBill() {
			return
		}

		// 创建账单
		err = svc.createBill(tx, newBill)
		if err != nil {
			svc.log.WithError(err).WithField("bill", newBill).Warn("create bill failed")
		}
		return
	})
	if err != nil && err != businesserror.ErrOptimisticLockingRollbackButNotReturnError {
		svc.log.WithError(err).Warn("transaction failed")
		return
	}

	err = nil
	if len(msgUUID) != 0 {
		errMsg := svc.q.Pub(msgUUID...)
		if len(errMsg) != 0 {
			svc.log.Warn("Pub tx msg failed [%s]. msg_uuid_list: %+v", errMsg, msgUUID)
			return
		}
	}

	return
}

// [order] 取消订单
func (svc *BCService) cancelOrder(orderUUID string) (err error) {
	order := &model.Order{}
	order, err = svc.getOrder(orderUUID)
	if err != nil {
		svc.log.WithError(err).Warn("Get order failed in cancelOrder().")
		return err
	}

	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) error {
		err = db_helper.UpdateOne(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.Order{},
			Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uuid": orderUUID}},
		}, map[string]interface{}{
			"status":       constant.OrderStatusClosed,
			"updated_at":   time.Now(),
			"product_uuid": "",
			"runtime_uuid": "",
		}).GetError()
		if err != nil {
			svc.log.WithError(err).Error("cancel order: update order status failed")
			err = businesserror.ErrDatabaseError
			return err
		}

		if order.PriceEntity != nil && len(order.PriceEntity.CouponIDList) != 0 {
			err = svc.userCouponUseCancel(tx, order.PriceEntity.CouponIDList[0])
			if err != nil {
				svc.log.WithError(err).Error("cancel coupon used status failed")
				return err
			}
		}
		return nil
	})
	if err != nil {
		svc.log.WithError(err).Info("cancel order failed")
		return err
	}
	err = svc.countdown.HDel(order.UUID)
	if err != nil {
		svc.log.WithField("instanceUUID", order.ProductUUID).WithError(err).Info("cancel order timeout time failed.")
	}

	return
}

// [order] 获取订单列表
func (svc *BCService) getOrderList(db *gorm.DB, params *model.OrderGetListParams, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []*model.Order, err error) {
	if params == nil {
		params = &model.OrderGetListParams{}
	}
	if pageReq == nil {
		pageReq = &db_helper.GetPagedRangeRequest{}
	}

	list = make([]*model.Order, 0)

	if db == nil {
		db = db_helper.GlobalDBConn()
	}

	db = db.Table(model.TableNameOrder)

	if params.UID != 0 {
		db = db.Where("uid = ?", params.UID)
	}
	if params.Status != "" {
		db = db.Where("status = ?", params.Status)
	}
	if params.ChargeType != "" {
		db = db.Where("charge_type = ?", params.ChargeType)
	}
	if params.OrderType != "" {
		db = db.Where("order_type = ?", params.OrderType)
	}
	if params.RuntimeType != "" {
		db = db.Where("runtime_type = ?", params.RuntimeType)
	}

	if params.ProductType != "" {
		orderTypeList := model.GetOrderTypesForProduct(params.ProductType)
		db = db.Where("order_type in (?)", orderTypeList)
	}

	if params.UUID != "" {
		db = db.Where("uuid = ?", params.UUID)
	}
	if params.ProductUUID != "" {
		db = db.Where("product_uuid = ?", params.ProductUUID)
	}
	if params.RuntimeUUID != "" {
		db = db.Where("runtime_uuid = ?", params.RuntimeUUID)
	}

	if pageReq.DateFrom != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') >= ?", pageReq.DateFrom)
	}
	if pageReq.DateTo != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') <= ?", pageReq.DateTo)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Warn("Count failed.")
		err = businesserror.ErrDatabaseError
		return
	}

	paged = db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, int(count), 0)

	paged.List = &list

	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("id desc").
		Find(&list).
		Error
	if err != nil {
		svc.log.WithError(err).Error("get orderList failed.")
		err = businesserror.ErrDatabaseError
	}

	for k := range list {
		svc.user.FillListPhone(list[k])
	}
	return
}

// [order] 获取订单详细信息
func (svc *BCService) getOrder(uuid string) (res *model.Order, err error) {
	res = new(model.Order)
	err = db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: &model.Order{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uuid": uuid}},
	}, res).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = businesserror.ErrOrderNotFound
			return
		}
		svc.log.WithError(err).Error("get order detail failed")
		err = businesserror.ErrDatabaseError
	}
	return
}

// [order] 获取订单详细信息
func (svc *BCService) getOrderByUUIDList(uuidList []string) (res []model.Order, err error) {
	res = []model.Order{}
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.Order{},
		Filters: db_helper.QueryFilters{
			InFilters: []db_helper.In{{Key: "uuid", InSet: uuidList}},
		},
	}, &res).GetError()
	if err != nil {
		svc.log.WithError(err).Error("get order detail failed")
		err = businesserror.ErrDatabaseError
	}
	return
}

// [order] 返回给前端的订单详细信息
func (svc *BCService) getOrderDetail(uuid string) (newOrder *model.OrderDetailInfo, err error) {
	newOrder = &model.OrderDetailInfo{}
	order, err := svc.getOrder(uuid)
	if err != nil {
		svc.log.WithField("err", err).Error("get order by uuid failed")
		return
	}
	newPriceInfo := &model.NewPriceInfo{}
	switch order.OrderType {
	case constant.OrderTypeFileStorage,
		constant.OrderTypePrivateImage,
		constant.OrderTypeDeploymentDurationPkgRefund:
	// 这几个不用处理
	default:
		if order.ChargeType.IsPayg() {
			newPriceInfo.PaygPrice = order.RuntimeEntity.PaygPrice
			newPriceInfo.OriginPaygPrice = order.PriceEntity.OriginPrice
		} else {
			newPriceInfo.OriginCostAmount = order.PriceEntity.OriginPrice
			newPriceInfo.CurrentCostAmount = order.PriceEntity.DealPrice
		}
	}
	order.NewPriceInfo = newPriceInfo
	newOrder.Order = order

	return
}

// [order] 更新状态
func (svc *BCService) updateOrderStatus(tx *gorm.DB, uuid string, status constant.OrderStatus, clearInstanceUUID bool) (err error) {
	c := false
	n := time.Now()
	m := make(map[string]interface{})
	m["status"] = status
	m["updated_at"] = n
	if status == constant.OrderStatusSuccess {
		m["pay_at"] = &n
	} else if status == constant.OrderStatusTimeout || status == constant.OrderStatusClosed {
		if clearInstanceUUID {
			m["runtime_uuid"] = ""
			m["product_uuid"] = ""
		}
		c = true
	}

	// 增加实例续费的判断
	var order *model.Order
	order, err = svc.GetOrder(uuid)
	if err != nil {
		return
	}
	if status == constant.OrderStatusSuccess && order.OrderType == constant.OrderTypeRenewalInstance {
		if !order.RenewalShouldTakeEffect() {
			m["status"] = constant.OrderStatusUnused
		}
	}

	err = db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         &model.Order{},
		Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uuid": uuid}},
	}, m).GetError()
	if err != nil {
		svc.log.WithError(err).Error("update order status failed")
		err = businesserror.ErrDatabaseError
	}

	if c {
		err = svc.countdown.HDel(uuid)
		if err != nil {
			svc.log.WithError(err).Info("cancel order timeout time failed.")
		}
	}

	return
}

// getSnapshot 获取order保存的机器快照
func (svc *BCService) getSnapshot(orderUUID string) (*model.MachineSnapShot, error) {
	order, err := svc.getOrder(orderUUID)
	if err != nil {
		return nil, err
	}

	return order.MachineEntity, nil
}

// getOrderUnused 获取未生效订单
func (svc *BCService) getOrderUnused(runtimeUUID constant.ContainerRuntimeUUID) (res []model.Order, err error) {
	filter := map[string]interface{}{"status": constant.OrderStatusUnused}
	if runtimeUUID != "" {
		filter["runtime_uuid"] = runtimeUUID
	}

	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.Order{},
		Filters:         db_helper.QueryFilters{EqualFilters: filter},
		NoLimit:         true,
	}, &res).GetError()
	if err != nil {
		svc.log.WithError(err).Error("get order unused failed")
		err = businesserror.ErrDatabaseError
	}
	return
}

// batchUpdateOrderStatus 批量更新订单状态
func (svc *BCService) batchUpdateOrderStatus(tx *gorm.DB, orderUUIDList []string, status constant.OrderStatus) (err error) {
	err = db_helper.UpdateAll(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         &model.Order{},
		Filters:                 db_helper.QueryFilters{InFilters: []db_helper.In{{Key: "uuid", InSet: orderUUIDList}}},
		UpdatePayload:           map[string]interface{}{"status": status},
	}).GetError()
	if err != nil {
		svc.log.WithField("orderUUIDList", orderUUIDList).WithField("status", status).WithError(err).Error("update order status faield")
		err = businesserror.ErrDatabaseError
	}
	return
}

// existUnpaidChangeTypeOrder 是否存在未付款的转实例计费类型的订单
func (svc *BCService) existUnpaidChangeTypeOrder(productUUID string) (exist bool, err error) {
	var count int64
	inSet := []constant.OrderType{
		constant.OrderTypeChangeYearly,
		constant.OrderTypeChangeMonthly,
		constant.OrderTypeChangeWeekly,
		constant.OrderTypeChangeDaily,
		constant.OrderTypeChangePayg,
		constant.OrderTypeRenewalInstance,
		constant.OrderTypeCloneInstance,
	}

	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: &model.Order{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{"product_uuid": productUUID, "status": constant.OrderStatusUnpaid},
			InFilters:    []db_helper.In{{Key: "order_type", InSet: inSet}},
		},
	}, &count).GetError()
	if err != nil {
		svc.log.WithError(err).Error("count UnpaidChangeTypeOrder failed")
		err = businesserror.ErrDatabaseError
		return
	}

	if count != 0 {
		exist = true
		return
	}

	// migrate_instance_uuid
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: &model.Order{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{"migrate_instance_uuid": productUUID, "status": constant.OrderStatusUnpaid},
			InFilters:    []db_helper.In{{Key: "order_type", InSet: inSet}},
		},
	}, &count).GetError()
	if err != nil {
		svc.log.WithError(err).Error("count UnpaidChangeTypeOrder failed")
		err = businesserror.ErrDatabaseError
		return
	}

	if count != 0 {
		exist = true
		return
	}

	return false, nil
}

// countUserChangeChargeTypeOrder 获取用户当日转实例计费方式的次数
func (svc *BCService) countUserChangeChargeTypeOrder(uid int) (count int64, err error) {
	var (
		now   = time.Now().Local()
		inSet = []constant.OrderType{
			constant.OrderTypeChangeDaily,
			constant.OrderTypeChangeWeekly,
			constant.OrderTypeChangeMonthly,
			constant.OrderTypeChangeYearly,
			constant.OrderTypeChangePayg,
		}
	)
	err = db_helper.GlobalDBConn().Table(model.TableNameOrder).
		Where("uid = ?", uid).
		Where("status = ?", constant.OrderStatusSuccess).
		Where("order_type in (?)", inSet).
		Where("created_at > ?", now.Format(constant.FormatDateString)).
		Count(&count).Error
	if err != nil {
		svc.log.WithError(err).WithFields(map[string]interface{}{
			"uid":           uid,
			"created_at > ": now.Format(constant.FormatDateString),
		}).Error("count failed.")
		err = businesserror.ErrDatabaseError
	}

	return
}

// existUnpaidRenewalOrder 是否存在未付款的续费的订单
func (svc *BCService) existUnpaidRenewalOrder(productUUID string) (exist bool, err error) {
	var count int64
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: &model.Order{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"product_uuid": productUUID,
				"status":       constant.OrderStatusUnpaid,
				"order_type":   constant.OrderTypeRenewalInstance},
		},
	}, &count).GetError()
	if err != nil {
		svc.log.WithError(err).Error("count UnpaidChangeTypeOrder failed")
		err = businesserror.ErrDatabaseError
		return
	}

	if count != 0 {
		exist = true
		return
	}

	return false, nil
}

// ---------------------- complex --------------------------------
// 为保证一致性，将对一些复杂操作写专门的接口

// prepayToPayg 包卡转按量
func (svc *BCService) prepayToPayg(params model.PrepayToPaygInnerParams) (err error) {
	if params.OrderCreatePayg == nil || params.BillRefund == nil {
		return businesserror.ErrInternalError
	}
	var (
		charging *model.Charging
		now      = time.Now()
		msgUUID  []string
	)

	if params.IsRunning {
		charging = &model.Charging{
			UID:             params.OrderCreatePayg.UID,
			SubName:         params.OrderCreatePayg.SubName,
			UserPhone:       params.OrderCreatePayg.UserPhone,
			MachineID:       params.OrderCreatePayg.RuntimeEntity.MachineID,
			ProductUUID:     params.OrderCreatePayg.ProductUUID,
			RuntimeUUID:     params.OrderCreatePayg.RuntimeUUID,
			RuntimeType:     constant.ContainerRuntimeOfInstance,
			OrderUUID:       params.OrderCreatePayg.UUID,
			Type:            params.OrderCreatePayg.PriceEntity.ChargeType,
			PaygPrice:       params.OrderCreatePayg.PriceEntity.PaygPrice,
			OriginPaygPrice: params.OrderCreatePayg.PriceEntity.OriginPrice,
			Charging:        true,
			CreatedAt:       now,
			StartedAt:       now,
			LastSettledAt:   now,
			UpdatedAt:       now,
		}
	}

	// 加锁, 每个用户一个锁, 加了代金券后， 也使用同一个锁
	lock := new(redis.RedisMutex)
	lock, err = svc.setLockForUpdateWallet(params.BillRefund.UID)
	if err != nil {
		return
	}
	defer func() {
		_ = lock.UnLock()
	}()

	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
		// 退费时校验原来的订单是否已经开票，若是开了票，需要生成欠票记录，若是没开，需要更新原来订单实际能开多少金额(refund_amount字段)
		invoiceParams, err := svc.RefundUpdateOrders(tx, params.OldOrder.ProductUUID, params.BillRefund.PayByBalance)
		if err != nil {
			svc.log.WithField("order_uuid", params.OldOrder.UUID).ErrorE(err, "update order invoice amount failed")
			return err
		}
		err = svc.createArrearInvoice(tx, invoiceParams)
		if err != nil {
			svc.log.WithField("err", err).WithField("order_uuid", params.OldOrder.UUID).Error("create order arrer invoice failed")
			return err
		}

		// 创建两个订单
		if params.OrderRefund != nil {
			// 允许 orderRefund 为nil，在实例到期后转的，就不会生成退款订单
			_, err = svc.createOrderNeedTx(tx, params.OrderRefund)
			if err != nil {
				svc.log.WithField("refundOrder", params.OrderRefund).ErrorE(err, "tx create order failed")
				return
			}
		}

		_, err = svc.createOrderNeedTx(tx, params.OrderCreatePayg)
		if err != nil {
			svc.log.WithField("paygOrder", params.OrderCreatePayg).ErrorE(err, "tx create order failed")
			return
		}

		// 更新未生效的订单为生效
		if len(params.UnUsedOrderUUIDList) != 0 {
			err = svc.batchUpdateOrderStatus(tx, params.UnUsedOrderUUIDList, constant.OrderStatusSuccess)
			if err != nil {
				svc.log.WithField("uuidList", params.UnUsedOrderUUIDList).ErrorE(err, "batchUpdateOrderStatus failed")
				return
			}
		}

		// 实例运行添加charging记录
		if params.IsRunning {
			err = svc.chargingStart(tx, charging)
			if err != nil {
				svc.log.WithField("charging", charging).ErrorE(err, "chargingStart failed")
				return
			}
		}

		// 更新wallet
		err = svc.updateWallet(tx, params.BillRefund)
		if err != nil {
			svc.log.WithField("bill", params.BillRefund).Error("update wallet failed")
			return
		}

		// 发送hook
		msgUUID, err = svc.afterUpdateWallet(tx, &model.AfterUpdateWalletParams{
			Bill:             params.BillRefund,
			AfterOperateType: constant.UpdateWalletSkip,
			AfterHook:        params.AfterHook,
			AfterFunc:        params.AfterFunc,
		})
		if err != nil {
			svc.log.WithError(err).Error("afterUpdateWallet failed")
			return
		}

		// 更改数据盘计费信息
		if params.ExistDDA {
			err = svc.dataDiskChargingChange(tx, &model.DataDiskChargingChangeParams{
				ProductUUID: params.OrderCreatePayg.ProductUUID,
				OptType:     constant.OrderTypeChangePayg,
				OrderUUID:   params.OrderCreatePayg.UUID,
			})
			if err != nil {
				svc.log.ErrorE(err, "change payg update ddc failed")
				return err
			}
		}

		// 创建账单
		if params.OrderRefund != nil {
			err = svc.createBill(tx, params.BillRefund)
			if err != nil {
				svc.log.WithField("bill", params.BillRefund).ErrorE(err, "create bill failed")
				return
			}
		}

		return
	})
	if err != nil && err != businesserror.ErrOptimisticLockingRollbackButNotReturnError {
		svc.log.WithError(err).Error("prepay to payg transaction failed")
		return
	}

	err = nil
	if len(msgUUID) != 0 {
		errMsg := svc.q.Pub(msgUUID...)
		if len(errMsg) != 0 {
			svc.log.Warn("Pub tx msg failed [%s]. msg_uuid_list: %+v", errMsg, msgUUID)
			return
		}
	}

	return
}

// paygToPrepay 按量转包卡
func (svc *BCService) paygToPrepay(params model.PaygToPrepayParams) (err error) {
	if params.PrepayBill == nil || params.GpuReserve == nil {
		return businesserror.ErrInternalError
	}
	if params.IsCharging && params.ChargingBill == nil {
		return businesserror.ErrInternalError
	}

	var (
		lockWallet *redis.RedisMutex
		ok         bool
	)

	// 加锁, 每个用户一个锁, 加了代金券后， 也使用同一个锁
	lockWallet, err = svc.setLockForUpdateWallet(params.PrepayBill.UID)
	if err != nil {
		return
	}
	defer func() {
		_ = lockWallet.UnLock()
	}()

	// 无论是否运行，都申请一遍gpu, 兼容无卡模式.
	ok, err = svc.gpuStockInterference.Reserve(&coreGpuStocModel.GpuReserve{
		UUID:        params.GpuReserve.UUID,
		RuntimeUUID: params.GpuReserve.RuntimeUUID,
		MachineID:   params.GpuReserve.MachineID,
		GpuNum:      params.GpuReserve.GpuNum,
		Priority:    params.GpuReserve.Priority,
		Timestamp:   tsc.Timestamp(),
	})
	if err != nil {
		svc.log.WithError(err).Warn("check machine failed.")
		return
	}
	if !ok {
		svc.log.WithField("orderUUID", params.PrepayBill.OrderUUID).Info("reserve gpu failed")
		err = businesserror.ErrGpuStockNotEnough
		return
	}

	// 事务
	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
		// 按量计费扣代金券，余额，停止计费
		if params.IsCharging {
			err = svc.voucherUpdateForUpdateWallet(tx, params.ChargingBill)
			if err != nil {
				svc.log.WithError(err).Error("update voucher failed")
				return err
			}

			err = svc.updateWallet(tx, params.ChargingBill)
			if err != nil {
				svc.log.WithError(err).Error("update wallet failed")
				return err
			}

			_, err = svc.afterUpdateWallet(tx, &model.AfterUpdateWalletParams{
				Bill:             params.ChargingBill,
				AfterOperateType: constant.UpdateWalletChargeTypeToPrepayStopCharging,
				AfterHook:        nil,
			})
			if err != nil {
				svc.log.WithError(err).Error("after update wallet failed")
				return
			}
		}

		// 包年包月扣代金券，余额
		err = svc.voucherUpdateForUpdateWallet(tx, params.PrepayBill)
		if err != nil {
			svc.log.WithError(err).Error("update voucher failed")
			return err
		}

		err = svc.updateWallet(tx, params.PrepayBill)
		if err != nil {
			svc.log.WithError(err).Error("update wallet failed")
			return err
		}

		// 创建bill
		now := time.Now()
		params.PrepayBill.ConfirmAt = &now

		if params.IsCharging {
			err = svc.createBill(tx, params.ChargingBill)
			if err != nil {
				svc.log.WithError(err).WithField("bill", params.ChargingBill).Error("create bill failed")
				return
			}
		}

		err = svc.createBill(tx, params.PrepayBill)
		if err != nil {
			svc.log.WithError(err).WithField("bill", params.PrepayBill).Error("create bill failed")
			return
		}
		if params.PrepayBill.ChargeType.IsRentType() {
			err = svc.updateOrderInfo(tx, params.PrepayBill.OrderUUID, map[string]interface{}{"pay_by_balance": params.PrepayBill.PayByBalance})
			if err != nil {
				svc.log.WithError(err).WithField("order_uuid", params.PrepayBill.OrderUUID).Error(" update order pay_by_balance failed")
				return
			}
		}

		// 涉及给instance发消息，最后做
		_, err = svc.afterUpdateWallet(tx, &model.AfterUpdateWalletParams{
			Bill:             params.PrepayBill,
			AfterOperateType: constant.UpdateWalletCharge,
			AfterHook:        params.AfterHook,
			AfterFunc:        params.AfterFunc,
		})
		if err != nil {
			svc.log.WithError(err).Error("after update wallet failed")
			return
		}

		// 更改数据盘计费信息
		if params.ExistDDA {
			err = svc.dataDiskChargingChange(tx, &model.DataDiskChargingChangeParams{
				ProductUUID:     params.PrepayBill.ProductUUID,
				OptType:         constant.OrderTypeChangeDaily,
				OrderUUID:       params.PrepayBill.OrderUUID,
				PrepayExpiredAt: &params.ExpiredAt,
			})
			if err != nil {
				svc.log.WithError(err).Error("change prepay update ddc failed")
				return err
			}
		}

		return
	})
	if err != nil && err != businesserror.ErrOptimisticLockingRollbackButNotReturnError {
		svc.log.WithError(err).Error("transaction failed, rollback")
		return
	}
	//if len(queueTxMsgUUIDList) > 0 {
	//	errMsg := svc.q.Pub(queueTxMsgUUIDList...)
	//	if len(errMsg) != 0 {
	//		logger.NewLogger("TxNowPubMessage").WithField("queueTxMsgUUIDList", queueTxMsgUUIDList).Warn("SimplePubMessage pub msg failed [%s], Pub instance options request failed.", errMsg)
	//	}
	//}
	return nil
}

func (svc *BCService) updateOrderInfo(tx *gorm.DB, orderUUID string, data map[string]interface{}) (err error) {
	_, err = svc.GetOrder(orderUUID)
	if err != nil {
		svc.log.WithField("err", err).WithField("order_uuid", orderUUID).Error("get order failed")
		return
	}
	err = db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         &model.Order{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uuid": orderUUID,
			},
		},
	}, data).GetError()
	if err != nil {
		svc.log.WithField("err", err).WithField("order_uuid", orderUUID).Error("update order field failed")
		err = businesserror.ErrDatabaseError
		return err
	}
	return
}

func (svc *BCService) GetInvoiceOrderList(params *model.InvoiceOrderListParams, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []*model.Order, err error) {
	if params == nil {
		params = &model.InvoiceOrderListParams{}
	}
	if pageReq == nil {
		pageReq = &db_helper.GetPagedRangeRequest{}
	}

	list = make([]*model.Order, 0)
	db := db_helper.GlobalDBConn().Table(model.TableNameOrder).
		Where("pay_by_balance-refund_amount>0")

	if params.UID != 0 {
		db = db.Where("uid = ?", params.UID)
	}
	if len(params.Status) > 0 {
		db = db.Where("status in (?)", params.Status)
	}
	if len(params.ChargeType) > 0 {
		db = db.Where("charge_type in (?)", params.ChargeType)
	}
	if params.InvoiceUUID != "" {
		db = db.Where("invoice_uuid = ?", params.InvoiceUUID)
	} else {
		db = db.Where("invoice_uuid = '' or isnull(invoice_uuid)=1")
	}

	if pageReq.DateFrom != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') >= ?", pageReq.DateFrom)
	}
	if pageReq.DateTo != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') <= ?", pageReq.DateTo)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Warn("Count failed.")
		err = businesserror.ErrDatabaseError
		return
	}

	paged = db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, int(count), 0)

	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("id desc").
		Find(&list).
		Error
	if err != nil {
		svc.log.WithError(err).Error("get orderList failed.")
		err = businesserror.ErrDatabaseError
	}

	paged.List = &list
	return
}

func (svc *BCService) UpdateOrderInvoiceUUID(tx *gorm.DB, invoiceUUID string, orderUUIDs []string) (err error) {
	err = db_helper.UpdateAll(db_helper.QueryDefinition{ModelDefinition: &model.Order{},
		DBTransactionConnection: tx,
		Filters: db_helper.QueryFilters{
			InFilters: []db_helper.In{{Key: "uuid", InSet: orderUUIDs}},
		},
		UpdatePayload: map[string]interface{}{"invoice_uuid": invoiceUUID},
	}).GetError()
	if err != nil {
		svc.log.WithField("err", err).WithField("order_uuid", orderUUIDs).Error("update order invoice_uuid failed")
		return
	}
	return
}

func (svc *BCService) SetOrderInvoiceUUIDNull(tx *gorm.DB, invoiceUUID string) (err error) {
	err = db_helper.UpdateAll(db_helper.QueryDefinition{ModelDefinition: &model.Order{},
		DBTransactionConnection: tx,
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{"invoice_uuid": invoiceUUID},
		},
		UpdatePayload: map[string]interface{}{"invoice_uuid": ""},
	}).GetError()
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil
		}
		svc.log.WithField("err", err).WithField("invoice_uuid", invoiceUUID).Error("set order invoice_uuid null failed")
		return
	}
	return
}

func (svc *BCService) RefundUpdateOrders(tx *gorm.DB, productUUID string, refundAmount int64) (params []*model.CreateArrearInvoiceParam, err error) {
	orders := make([]*model.Order, 0)
	params = make([]*model.CreateArrearInvoiceParam, 0)

	// 筛选出所有不是按量计费的，且付费成功或续费成功，且不是退款的订单
	err = db_helper.GlobalDBConn().Table(model.TableNameOrder).Where("product_uuid", productUUID).
		Where("order_type not in (?)", []constant.OrderType{constant.OrderTypeRefund}).
		Where("status in (?)", []constant.OrderStatus{constant.OrderStatusSuccess, constant.OrderStatusUnused}).
		Where("charge_type not in (?)", []constant.ChargeType{constant.ChargeTypePayg}).
		// Where("DATE_FORMAT(created_at,'%Y-%m-%d %H:%i:%s') <= ?", time.Now()). //针对一个实例被多次退款的情况
		Order("id desc").Find(&orders).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	for _, o := range orders {
		// 添加创建欠票的参数
		var param *model.CreateArrearInvoiceParam
		if o.InvoiceUUID != "" {
			param = &model.CreateArrearInvoiceParam{
				UID:         o.UID,
				OrderUUID:   o.UUID,
				ProductUUID: o.ProductUUID,
				InvoiceUUID: o.InvoiceUUID,
			}
		}
		// 一次更新所有订单中refund_amount字段值
		refundAmount -= o.PayByBalance
		if refundAmount > 0 {
			if param != nil && o.InvoiceUUID != "" {
				param.ArrearAmount = o.PayByBalance
				params = append(params, param)
			}
			err = db_helper.UpdateOne(db_helper.QueryDefinition{ModelDefinition: &model.Order{},
				DBTransactionConnection: tx,
				Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uuid": o.UUID}}},
				map[string]interface{}{"refund_amount": o.PayByBalance}).GetError()
			if err != nil {
				return nil, err
			}
		} else {
			if param != nil && o.InvoiceUUID != "" {
				param.ArrearAmount = refundAmount + o.PayByBalance
				params = append(params, param)
			}
			err = db_helper.UpdateOne(db_helper.QueryDefinition{ModelDefinition: &model.Order{},
				DBTransactionConnection: tx,
				Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uuid": o.UUID}}},
				map[string]interface{}{"refund_amount": refundAmount + o.PayByBalance}).GetError()
			if err != nil {
				return nil, err
			}
			break
		}
	}
	return
}

func (svc *BCService) GetUserPrivateImageOrder(uid int) (model.Order, error) {
	order := model.Order{}

	err := db_helper.GlobalDBConn().
		Table(model.TableNameOrder).
		Where("uid = ?", uid).
		Where("order_type = ?", constant.OrderTypePrivateImage).
		First(&order).
		Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			user, err := svc.user.FindByUserId(uid)
			if err != nil {
				return model.Order{}, err
			}

			now := time.Now()
			newOrder := &model.Order{
				UID:          user.ID,
				Username:     user.Username,
				UserPhone:    user.Phone,
				UUID:         libs.RandNumberString(),
				Status:       constant.OrderStatusSuccess,
				OrderType:    constant.OrderTypePrivateImage,
				ChargeType:   constant.ChargeTypePayg,
				PriceEntity:  &model.PriceInfo{OriginPrice: 0, DealPrice: 0},
				PayByBalance: 0,
				DealPrice:    0,
				CreatedAt:    now,
				UpdatedAt:    now,
				PayAt:        &now,
			}
			newOrder, err = svc.CreateOrder(newOrder)
			if err != nil {
				log.WithError(err).Error("CreateOrder failed")
				return model.Order{}, err
			}
			return *newOrder, nil
		}
		svc.log.WithError(err).Error("get order failed.")
		return model.Order{}, businesserror.ErrDatabaseError
	}

	return order, nil
}

func (svc *BCService) ExistUnpaidPrepayExpandDataDiskOrder(productUUID string) (exist bool, err error) {
	var count int64
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: &model.Order{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{
			"product_uuid": productUUID,
			"order_type":   constant.OrderTypeDataDiskExpandOfPrepay,
			"status":       constant.OrderStatusUnpaid,
		}},
	}, &count).GetError()
	if err != nil {
		svc.log.WithField("productUUID", productUUID).ErrorE(err, "count order failed")
		err = businesserror.ErrDatabaseError
		return

	}
	if count > 0 {
		exist = true
	}
	return
}

func (svc *BCService) AdminTransInsPrePay(operateUid int, oldInstanceUuid, newInstanceUuid constant.InstanceUUIDType) (err error) {
	// 1. 实例 check
	// ---1-1. 原实例必须为包卡实例且未到期
	oldInstanceInfo, err := svc.instance.GetInstance(oldInstanceUuid)
	if err != nil {
		svc.log.WithField("old_instance_uuid", oldInstanceUuid).ErrorE(err, "get old instance info by uuid failed")
		return
	}
	if oldInstanceInfo.ChargeType == constant.ChargeTypePayg || oldInstanceInfo.ExpiredAt.Time.Before(time.Now()) {
		err = businesserror.ErrOldInstanceTransIsNotValid
		return
	}
	// ---1-2. 新实例必须为按量计费且处于运行中状态
	newInstanceInfo, err := svc.instance.GetInstance(newInstanceUuid)
	if err != nil {
		svc.log.WithField("new_instance_uuid", newInstanceUuid).ErrorE(err, "get new instance info by uuid failed")
		return
	}
	if newInstanceInfo.ChargeType != constant.ChargeTypePayg || newInstanceInfo.Status != constant.InstanceRunning || !newInstanceInfo.HaveGpuResources {
		err = businesserror.ErrNewInstanceTransIsNotValid
		return
	}
	// ---1-3. 原实例和新实例必须属于同一个用户
	if oldInstanceInfo.UID != newInstanceInfo.UID {
		err = businesserror.ErrInstanceBelongToDifferentUser
		return
	}

	// 2. 钱包加锁
	lock, err := svc.SetLockForUpdateWallet(oldInstanceInfo.UID)
	if err != nil {
		return
	}
	defer func() {
		_ = lock.UnLock()
	}()

	// 3. 获取原实例生效中订单信息
	oldInstanceUsedOrder, err := svc.getOrder(oldInstanceInfo.OrderUUID)
	if err != nil {
		svc.log.WithField("old_instance_order_uuid", oldInstanceInfo.OrderUUID).ErrorE(err, "get old instance used order info by uuid failed")
		return
	}

	// 4. 获取原实例生效中订单的账单信息
	usedBill, err := svc.GetBillFirstByOrderUUID(oldInstanceInfo.UID, oldInstanceInfo.OrderUUID)
	if err != nil || usedBill == nil {
		svc.log.WithField("oldInstanceUsedOrderUUID", oldInstanceInfo.OrderUUID).ErrorE(err, "get oldInstanceUsedOrder bill failed")
		return err
	}

	// 5. 获取原实例未生效订单信息
	oldInstanceUnusedOrders, err := svc.getOrderUnused(oldInstanceInfo.RuntimeUUID)
	if err != nil {
		svc.log.WithField("old_instance_runtime_uuid", oldInstanceInfo.RuntimeUUID).ErrorE(err, "get old instance unused order list by uuid failed")
		return
	}

	// 6. 原实例所有订单退款总金额计算
	var oldInstanceOrderTotalRefundAsset int64
	// ---6-1. 原实例生效中订单退款总金额计算
	oldInstanceOrderTotalRefundAsset += oldInstanceUsedOrder.PayByBalance
	// ---6-2. 原实例未生效订单退款总金额计算
	unusedBillMap := make(map[string]*model.Bill)
	for _, v := range oldInstanceUnusedOrders {
		// 获取原实例未生效订单的账单信息
		unusedBill, err := svc.GetBillFirstByOrderUUID(v.UID, v.UUID)
		if err != nil || unusedBill == nil {
			svc.log.WithField("oldInstanceUnusedOrderUUID", v.UUID).ErrorE(err, "get oldInstanceUnusedOrder bill failed")
			return err
		}

		unusedBillMap[v.UUID] = unusedBill

		oldInstanceOrderTotalRefundAsset += v.PayByBalance
	}

	userMember, err := svc.user.GetUserMemberInfoByUid(oldInstanceInfo.UID)
	if err != nil {
		svc.log.WithField("uid", oldInstanceInfo.UID).WarnE(err, "get user member detail failed")
		return
	}

	// 7. 原实例dds处理
	oldInstanceExistDDA, err := svc.dds.ExistDDA(oldInstanceInfo.MachineID, string(oldInstanceInfo.InstanceUUID))
	if err != nil {
		svc.log.WithError(err).Error("error")
		return
	}
	if oldInstanceExistDDA {
		oldInstanceExpandDisk, err := svc.dds.GetDDA(oldInstanceInfo.MachineID, string(oldInstanceInfo.InstanceUUID))
		if err != nil {
			return err
		}
		if oldInstanceExpandDisk != nil {
			oldInstanceUsedOrder.PriceEntity.ExpandDataDisk = oldInstanceExpandDisk.DiskAllocate
		}
	}

	oldInstancePaygPriceInfo := &model.PriceInfo{
		MachineID:      oldInstanceInfo.MachineID,
		ChargeType:     constant.ChargeTypePayg,
		Duration:       1,
		Num:            oldInstanceUsedOrder.PriceEntity.Num,
		ExpandDataDisk: oldInstanceUsedOrder.PriceEntity.ExpandDataDisk,
	}
	err = svc.bcServer.GetPrice(&model.GetPriceParams{
		Uid:         oldInstanceInfo.UID,
		PriceInfo:   oldInstancePaygPriceInfo,
		LevelName:   userMember.MemberLevel,
		ProductType: constant.ProductTypeInstance,
	})
	if err != nil {
		svc.log.WithError(err).Warn("get old instance real payg price failed")
		return
	}

	now := time.Now()

	// 8. 原实例扩容disk退款总金额计算
	var oldInstanceExpandDiskRefundAsset int64
	var prepayExpandOrderList []model.Order
	if len(oldInstanceInfo.AdditionalEntity.PrepayExpandDataDiskOrderUUID) != 0 {
		prepayExpandOrderList, err = svc.GetOrderByUUIDList(oldInstanceInfo.AdditionalEntity.PrepayExpandDataDiskOrderUUID)
		if err != nil {
			svc.log.ErrorE(err, "get prepayExpandOrderList by uuid list failed")
			return err
		}

		for _, v := range prepayExpandOrderList {
			if v.PriceEntity == nil || !v.PriceEntity.PrepayExpandDataDisk ||
				v.PriceEntity.DataDiskInfo == nil || !v.PriceEntity.DataDiskInfo.ExpiredAt.After(now) {
				continue
			}

			oldInstanceExpandDiskRefundAsset += v.PayByBalance
		}
	}

	oldInstanceOrderTotalRefundAsset += oldInstanceExpandDiskRefundAsset

	// 10. 获取新实例订单信息
	newInstanceUsedOrder, err := svc.getOrder(newInstanceInfo.OrderUUID)
	if err != nil {
		svc.log.WithField("new_instance_order_uuid", newInstanceInfo.OrderUUID).ErrorE(err, "get new instance used order info by uuid failed")
		return
	}

	// 新实例dds处理
	newInstanceExistDDA, err := svc.dds.ExistDDA(newInstanceInfo.MachineID, string(newInstanceUuid))
	if err != nil {
		svc.log.ErrorE(err, "check new instance exist dda error")
		return
	}
	if newInstanceExistDDA {
		oldInstanceExpandDisk, err := svc.dds.GetDDA(oldInstanceInfo.MachineID, string(oldInstanceInfo.InstanceUUID))
		if err != nil {
			return err
		}
		if oldInstanceExpandDisk != nil {
			oldInstanceUsedOrder.PriceEntity.ExpandDataDisk = oldInstanceExpandDisk.DiskAllocate
		}
	}

	newInstancePrepayPriceInfo := &model.PriceInfo{
		MachineID:      newInstanceInfo.MachineID,
		ChargeType:     oldInstanceUsedOrder.ChargeType,
		Duration:       1,
		Num:            newInstanceUsedOrder.PriceEntity.Num,
		ExpandDataDisk: newInstanceUsedOrder.PriceEntity.ExpandDataDisk,
	}
	err = svc.bcServer.GetPrice(&model.GetPriceParams{
		Uid:         newInstanceInfo.UID,
		PriceInfo:   newInstancePrepayPriceInfo,
		LevelName:   userMember.MemberLevel,
		ProductType: constant.ProductTypeInstance,
	})
	if err != nil {
		svc.log.WithError(err).Warn("get new instance real prepay price failed")
		return
	}

	// 9. 获取用户余额
	userBalance, err := svc.GetBalance(oldInstanceUsedOrder.UID)
	if err != nil {
		svc.log.WithField("uid", oldInstanceUsedOrder.UID).ErrorE(err, "get user balance failed")
		return
	}

	// 11. 原实例和新实例之间转移包卡订单相关处理
	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
		// 1. 原实例生成退款订单
		oldInstanceRefundOrderUuid := libs.RandNumberString()
		oldInstanceRefundOrder := &model.Order{
			UID:         oldInstanceUsedOrder.UID,
			SubName:     oldInstanceInfo.SubName,
			Username:    oldInstanceUsedOrder.Username,
			UserPhone:   oldInstanceUsedOrder.UserPhone,
			UUID:        oldInstanceRefundOrderUuid,
			ProductUUID: oldInstanceUsedOrder.ProductUUID,
			RuntimeUUID: oldInstanceUsedOrder.RuntimeUUID,
			RuntimeType: oldInstanceUsedOrder.RuntimeType,
			Status:      constant.OrderStatusSuccess,
			OrderType:   constant.OrderTypeRefund,
			ChargeType:  oldInstanceUsedOrder.ChargeType,
			MachineID:   oldInstanceUsedOrder.MachineID,
			DealPrice:   oldInstanceOrderTotalRefundAsset,
			PriceEntity: oldInstancePaygPriceInfo,
			RefundEntity: &model.RefundInfo{
				RefundAsset: oldInstanceOrderTotalRefundAsset,
				RefundType:  constant.OrderTypeChangePayg,
				OrderUUID:   oldInstanceUsedOrder.UUID,
				BeginAt:     oldInstanceInfo.CreatedAt,
				EndAt:       oldInstanceInfo.CreatedAt,
			},
			MachineEntity: oldInstanceUsedOrder.MachineEntity,
			RuntimeEntity: oldInstanceUsedOrder.RuntimeEntity,
			CreatedAt:     now,
			UpdatedAt:     now,
			PayAt:         &now,
		}
		_, err = svc.createOrderNeedTx(tx, oldInstanceRefundOrder)
		if err != nil {
			svc.log.WithField("oldInstanceRefundOrder", oldInstanceRefundOrder).ErrorE(err, "tx create oldInstanceRefundOrder failed")
			return
		}

		// 2. 原实例创建退款订单对应的账单
		userBalance += oldInstanceOrderTotalRefundAsset
		oldInstanceRefundBill := &model.Bill{
			UID:         oldInstanceUsedOrder.UID,
			UserPhone:   oldInstanceUsedOrder.UserPhone,
			UUID:        libs.RandNumberString(),
			OrderUUID:   oldInstanceRefundOrderUuid,
			RuntimeUUID: oldInstanceRefundOrder.RuntimeUUID,
			ProductUUID: oldInstanceRefundOrder.ProductUUID,
			Type:        constant.BillTypeRefund,
			SubType:     constant.BillSubTypeTransInstancePrepay,
			Asset:       oldInstanceOrderTotalRefundAsset,
			ChargeType:  oldInstanceRefundOrder.ChargeType,
			Balance:     userBalance,
			CreatedAt:   now,
			UpdatedAt:   now,
		}
		err = svc.createBill(tx, oldInstanceRefundBill)
		if err != nil {
			svc.log.WithField("oldInstanceRefundBill", oldInstanceRefundBill).ErrorE(err, "create oldInstanceRefundBill failed")
			return
		}

		// 3. 原实例生成按量订单
		oldInstancePaygOrderUuid := libs.RandNumberString()
		oldInstancePaygOrder := &model.Order{
			UID:           oldInstanceUsedOrder.UID,
			SubName:       oldInstanceInfo.SubName,
			Username:      oldInstanceUsedOrder.Username,
			UserPhone:     oldInstanceUsedOrder.UserPhone,
			UUID:          oldInstancePaygOrderUuid,
			ProductUUID:   oldInstanceUsedOrder.ProductUUID,
			RuntimeUUID:   oldInstanceUsedOrder.RuntimeUUID,
			RuntimeType:   oldInstanceUsedOrder.RuntimeType,
			Status:        constant.OrderStatusSuccess,
			OrderType:     constant.OrderTypeChangePayg,
			ChargeType:    constant.ChargeTypePayg,
			MachineID:     oldInstanceUsedOrder.MachineID,
			PriceEntity:   oldInstancePaygPriceInfo,
			MachineEntity: oldInstanceUsedOrder.MachineEntity,
			RuntimeEntity: oldInstanceUsedOrder.RuntimeEntity,
			CreatedAt:     now,
			UpdatedAt:     now,
		}
		_, err = svc.createOrderNeedTx(tx, oldInstancePaygOrder)
		if err != nil {
			svc.log.WithField("oldInstancePaygOrder", oldInstancePaygOrder).ErrorE(err, "tx create oldInstancePaygOrder failed")
			return
		}

		// 4. 原实例由包卡转为按量
		oldInstanceInfo.AdditionalEntity = instanceModel.AdditionalInfo{}
		oldInstanceInfo.AdditionalInfo, _ = json.Marshal(oldInstanceInfo.AdditionalEntity)
		err = oldInstanceInfo.InstanceUpdate(tx, &db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uuid": oldInstanceInfo.InstanceUUID,
			},
		}, map[string]interface{}{
			"order_uuid":        oldInstancePaygOrderUuid,
			"charge_type":       constant.ChargeTypePayg,
			"additional_info":   oldInstanceInfo.AdditionalInfo,
			"payg_price":        oldInstancePaygPriceInfo.PaygPrice,
			"origin_payg_price": oldInstancePaygPriceInfo.OriginPrice,
		})
		if err != nil {
			svc.log.WithField("oldInstanceInfo", oldInstanceInfo).ErrorE(err, "update oldInstanceInfo failed")
			return err
		}

		// 5. 判断原实例是否在运行中
		if oldInstanceInfo.Status == constant.InstanceStarting || oldInstanceInfo.Status == constant.InstanceRunning {
			// 5-2. 如果实例处于运行中，开始实例的按量计费
			charging := &model.Charging{
				UID:             oldInstancePaygOrder.UID,
				SubName:         oldInstancePaygOrder.SubName,
				UserPhone:       oldInstancePaygOrder.UserPhone,
				MachineID:       oldInstancePaygOrder.RuntimeEntity.MachineID,
				ProductUUID:     oldInstancePaygOrder.ProductUUID,
				RuntimeUUID:     oldInstancePaygOrder.RuntimeUUID,
				RuntimeType:     constant.ContainerRuntimeOfInstance,
				OrderUUID:       oldInstancePaygOrderUuid,
				Type:            constant.ChargeTypePayg,
				PaygPrice:       oldInstancePaygPriceInfo.PaygPrice,
				OriginPaygPrice: oldInstancePaygPriceInfo.OriginPrice,
				Charging:        true,
				CreatedAt:       now,
				StartedAt:       now,
				LastSettledAt:   now,
				UpdatedAt:       now,
			}
			err = svc.chargingStart(tx, charging)
			if err != nil {
				svc.log.WithField("charging", charging).ErrorE(err, "chargingStart failed")
				return
			}
		} else {
			// 5-2. 如果实例未处于运行中，释放原实例Gpu占用
			err = svc.gpuStockInterference.Release(&coreGpuStocModel.GpuRelease{
				RuntimeUUID: oldInstanceInfo.RuntimeUUID.String(),
				MachineID:   oldInstanceInfo.MachineID,
				DebugMsg:    "AdminTransInsPrePay",
				Timestamp:   tsc.Timestamp(),
			})
			if err != nil {
				svc.log.WithField("oldInstanceUuid", oldInstanceUuid).ErrorE(err, "create order for payg to other: release machine gpu failed.")
				return
			}
		}

		// 6. 原实例 disk 计费转按量
		if oldInstanceExistDDA {
			err = svc.dataDiskChargingChange(tx, &model.DataDiskChargingChangeParams{
				ProductUUID: string(oldInstanceInfo.InstanceUUID),
				OptType:     constant.OrderTypeChangePayg,
				OrderUUID:   oldInstanceUsedOrder.UUID,
			})
			if err != nil {
				svc.log.ErrorE(err, "old instance change payg update ddc failed")
				return err
			}
		}

		// 7. 更新原实例扩容disk订单refund_amount
		if len(oldInstanceInfo.AdditionalEntity.PrepayExpandDataDiskOrderUUID) != 0 {
			for _, v := range prepayExpandOrderList {
				if v.PriceEntity == nil || !v.PriceEntity.PrepayExpandDataDisk ||
					v.PriceEntity.DataDiskInfo == nil || !v.PriceEntity.DataDiskInfo.ExpiredAt.After(now) {
					continue
				}

				err = v.OrderUpdate(tx, db_helper.QueryFilters{
					EqualFilters: map[string]interface{}{
						"uuid": v.UUID,
					}},
					map[string]interface{}{
						"refund_amount": v.PayByBalance,
					},
				)
				if err != nil {
					svc.log.WithField("order_uuid", v.UUID).ErrorE(err, "update disk expand order refund amount failed")
					return err
				}
			}
		}

		// 8. 新实例创建生效中包卡订单：
		// 配置对齐新实例按量付费订单的配置
		// 付款对齐原实例生效中订单支付的余额
		newInstanceUsedPrePayOrderUuid := libs.RandNumberString()
		newInstanceUsedPrePayOrder := &model.Order{
			UID:           oldInstanceUsedOrder.UID,
			SubName:       oldInstanceUsedOrder.SubName,
			Username:      oldInstanceUsedOrder.Username,
			UserPhone:     oldInstanceUsedOrder.UserPhone,
			UUID:          newInstanceUsedPrePayOrderUuid,
			ProductUUID:   string(newInstanceUuid),
			RuntimeUUID:   newInstanceUsedOrder.RuntimeUUID,
			RuntimeType:   constant.ContainerRuntimeOfInstance,
			Status:        oldInstanceUsedOrder.Status,
			OrderType:     oldInstanceUsedOrder.ChargeType.ToOrderType(),
			ChargeType:    oldInstanceUsedOrder.ChargeType,
			DealPrice:     oldInstanceUsedOrder.PayByBalance + oldInstanceExpandDiskRefundAsset,
			PayByBalance:  oldInstanceUsedOrder.PayByBalance + oldInstanceExpandDiskRefundAsset,
			PriceEntity:   newInstancePrepayPriceInfo,
			RuntimeEntity: newInstanceUsedOrder.RuntimeEntity,
			MachineID:     newInstanceUsedOrder.MachineID,
			MachineEntity: newInstanceUsedOrder.MachineEntity,
			PayAt:         &now,
		}
		// ---8-1. 新实例订单中Runtime快照信息记录真实的Runtime信息
		newInstanceUsedPrePayOrder.RuntimeEntity.OrderUUID = newInstanceUsedPrePayOrderUuid
		newInstanceUsedPrePayOrder.RuntimeEntity.ChargeType = oldInstanceUsedOrder.RuntimeEntity.ChargeType
		newInstanceUsedPrePayOrder.RuntimeEntity.ExpiredAt = oldInstanceUsedOrder.RuntimeEntity.ExpiredAt
		newInstanceUsedPrePayOrder.RuntimeEntity.TakeEffectAt = oldInstanceUsedOrder.RuntimeEntity.TakeEffectAt
		_, err = svc.createOrderNeedTx(tx, newInstanceUsedPrePayOrder)
		if err != nil {
			svc.log.WithField("newInstanceUsedPrePayOrder", newInstanceUsedPrePayOrder).ErrorE(err, "create newInstanceUsedPrePayOrder failed")
			return
		}
		// ---8-2. 修改新实例信息
		err = newInstanceInfo.InstanceUpdate(tx, &db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uuid": newInstanceInfo.InstanceUUID,
			},
		}, map[string]interface{}{
			"order_uuid":  newInstanceUsedPrePayOrderUuid,
			"charge_type": oldInstanceUsedOrder.ChargeType,
			"expired_at":  oldInstanceInfo.ExpiredAt,
		})
		if err != nil {
			svc.log.WithField("newInstanceInfo", newInstanceInfo).ErrorE(err, "update newInstanceInfo failed")
			return err
		}
		opt := constant.OptInstanceReq{
			InstanceUUID: string(newInstanceUuid),
			OptType:      constant.InstanceTransPrepayOpt,
			Caller:       constant.OptCallerAdmin,
			Payload:      newInstanceUsedPrePayOrderUuid,
		}
		err = svc.instance.OperateInstance(opt)
		if err != nil {
			return err
		}

		// 9. 新实例创建生效中包卡订单对应的账单
		userBalance -= oldInstanceUsedOrder.PayByBalance
		newInstanceUsedPrePayBill := &model.Bill{
			UID:          oldInstanceUsedOrder.UID,
			UserPhone:    oldInstanceUsedOrder.UserPhone,
			UUID:         libs.RandNumberString(),
			OrderUUID:    newInstanceUsedPrePayOrderUuid,
			RuntimeUUID:  newInstanceUsedOrder.RuntimeUUID,
			ProductUUID:  newInstanceUsedOrder.ProductUUID,
			Type:         usedBill.Type,
			SubType:      usedBill.SubType,
			Asset:        oldInstanceUsedOrder.PayByBalance + oldInstanceExpandDiskRefundAsset,
			PayByBalance: oldInstanceUsedOrder.PayByBalance + oldInstanceExpandDiskRefundAsset,
			ChargeType:   oldInstanceUsedOrder.ChargeType,
			Balance:      userBalance,
		}
		err = svc.createBill(tx, newInstanceUsedPrePayBill)
		if err != nil {
			svc.log.WithField("newInstanceUsedPrePayBill", newInstanceUsedPrePayBill).ErrorE(err, "create newInstanceUsedPrePayBill failed")
			return err
		}

		// 10. 新实例创建未生效包卡订单：
		// 配置对齐新实例按量付费订单的配置
		// 付款对齐原实例未生效订单支付的余额
		for _, v := range oldInstanceUnusedOrders {
			// ---10-1. 新实例未生效订单UUID
			newInstanceUnusedPrePayOrderUuid := libs.RandNumberString()
			// ---10-2. 新实例创建未生效订单
			newInstanceUnusedPrePayOrder := &model.Order{
				UID:           v.UID,
				SubName:       v.SubName,
				Username:      v.Username,
				UserPhone:     v.UserPhone,
				UUID:          newInstanceUnusedPrePayOrderUuid,
				ProductUUID:   string(newInstanceUuid),
				RuntimeUUID:   newInstanceUsedOrder.RuntimeUUID,
				RuntimeType:   constant.ContainerRuntimeOfInstance,
				Status:        v.Status,
				OrderType:     v.OrderType,
				ChargeType:    v.ChargeType,
				DealPrice:     v.PayByBalance,
				PayByBalance:  v.PayByBalance,
				PriceEntity:   newInstancePrepayPriceInfo,
				RuntimeEntity: newInstanceUsedOrder.RuntimeEntity,
				MachineID:     newInstanceUsedOrder.MachineID,
				MachineEntity: newInstanceUsedOrder.MachineEntity,
				PayAt:         &now,
			}
			// ---10-3. 新实例未生效订单中Runtime快照信息记录真实的Runtime信息
			newInstanceUnusedPrePayOrder.RuntimeEntity.OrderUUID = newInstanceUnusedPrePayOrderUuid
			newInstanceUnusedPrePayOrder.RuntimeEntity.ChargeType = v.RuntimeEntity.ChargeType
			newInstanceUnusedPrePayOrder.RuntimeEntity.ExpiredAt = v.RuntimeEntity.ExpiredAt
			newInstanceUnusedPrePayOrder.RuntimeEntity.TakeEffectAt = v.RuntimeEntity.TakeEffectAt
			_, err = svc.createOrderNeedTx(tx, newInstanceUnusedPrePayOrder)
			if err != nil {
				svc.log.WithField("newInstanceUnusedPrePayOrder", newInstanceUnusedPrePayOrder).ErrorE(err, "tx create newInstanceUnusedPrePayOrder failed")
				return err
			}

			// ---10-4. 更新原实例未生效订单状态为交易成功
			err = v.OrderUpdate(tx, db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"uuid":   v.UUID,
					"status": constant.OrderStatusUnused,
				},
			}, map[string]interface{}{
				"status": constant.OrderStatusSuccess,
			})
			if err != nil {
				svc.log.WithField("order_uuid", v.UUID).ErrorE(err, "update oldInstanceUsedOrder status failed")
				return err
			}

			// ---10-5. 新实例创建未生效订单对应的账单
			userBalance -= v.PayByBalance
			newInstanceUnusedBill := &model.Bill{
				UID:          v.UID,
				UserPhone:    v.UserPhone,
				UUID:         libs.RandNumberString(),
				OrderUUID:    newInstanceUnusedPrePayOrderUuid,
				RuntimeUUID:  newInstanceUsedOrder.RuntimeUUID,
				ProductUUID:  newInstanceUsedOrder.ProductUUID,
				Type:         unusedBillMap[v.UUID].Type,
				SubType:      unusedBillMap[v.UUID].SubType,
				Asset:        v.PayByBalance,
				PayByBalance: v.PayByBalance,
				ChargeType:   unusedBillMap[v.UUID].ChargeType,
				Balance:      userBalance,
			}
			err = svc.createBill(tx, newInstanceUnusedBill)
			if err != nil {
				svc.log.WithField("newInstanceUnusedBill", newInstanceUnusedBill).ErrorE(err, "create newInstanceUsedBill failed")
				return err
			}
		}

		// 11. 新实例原按量计费charging停止
		settleTime := time.Now()
		err = svc.chargingStopFillStopTime(tx, newInstanceUsedOrder.UUID, &settleTime)
		if err != nil {
			svc.log.WithError(err).Warn("charging stop failed")
			return err
		}

		// 12. 新实例 disk 按量转包卡
		if newInstanceExistDDA {
			err = svc.dataDiskChargingChange(tx, &model.DataDiskChargingChangeParams{
				ProductUUID:     string(newInstanceUuid),
				OptType:         constant.OrderTypeChangeDaily,
				OrderUUID:       newInstanceUsedPrePayOrderUuid,
				PrepayExpiredAt: newInstanceUsedPrePayOrder.RuntimeEntity.ExpiredAt,
			})
			if err != nil {
				svc.log.WithError(err).Error("change ddc charge type failed")
				return err
			}
		}

		return
	})
	if err != nil {
		svc.log.WithField("old_instance_uuid", oldInstanceUuid).WithField("new_instance_uuid", newInstanceUuid).ErrorE(err, "transaction failed")
		return
	}

	// 12. 存储admin转移实例包卡订单操作记录
	user, err := svc.user.FindByUserId(oldInstanceInfo.UID)
	if err != nil {
		svc.log.WithField("uid", oldInstanceInfo.UID).ErrorE(err, "find user by uid failed")
		return
	}
	adminOperateInfo := map[string]interface{}{
		"old_instance_uuid": oldInstanceUuid,
		"new_instance_uuid": newInstanceUuid,
		"phone":             user.Phone,
	}
	adminOperateLog := userModel.AdminOperateLog{
		UID:           operateUid,
		Url:           "POST/admin/v1/tools/trans",
		PayloadEntity: adminOperateInfo,
		CreatedAt:     time.Now(),
	}
	err = adminOperateLog.AdminOperateLogCreate(nil)
	if err != nil {
		svc.log.WithField("admin_operate_log", adminOperateLog).ErrorE(err, "create admin transfer instance prepay operate log failed")
		return
	}

	return
}
