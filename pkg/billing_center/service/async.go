package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	goset "github.com/deckarep/golang-set/v2"
	"server/conf"
	coreGpuStocModel "server/pkg-core/gpu_stock/model"
	"server/pkg/billing_center/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	ddsModel "server/pkg/data_disk_stock/model"
	"server/pkg/db_helper"
	deploymentModel "server/pkg/deployment/model"
	"server/pkg/libs"
	machineModel "server/pkg/machine/model"
	message_model "server/pkg/message/model"
	userModel "server/pkg/user/model"
	"server/plugin/payment"
	"server/plugin/queue"
	"server/plugin/queue_interface"
	redis "server/plugin/redis_plugin"
	tsc "server/plugin/redis_plugin/time_service_center"
	"strconv"
	"strings"
	"sync"
	"time"

	log "github.com/sirupsen/logrus"
	"github.com/spf13/cast"

	uuid "github.com/satori/go.uuid"

	"gorm.io/gorm"
)

// ----------------- API ----------------------
func (svc *BCService) CronJobRegister(ctx context.Context, cron *libs.Cron) {
	go svc.cronDoCharging(ctx)              // 定时结算
	go svc.cronUpdateOrderTimeout(ctx)      // 定时更新订单超时
	go svc.cronCheckRenewalUnusedOrder(ctx) // 定时查询续费订单
	//go svc.syncDailyBill(ctx)
	go cron.CronAdd("45 0 0", svc.billToDailyBill) // 定时同步每日按量计费账单
	go svc.cronSettleChargingTableStopped(ctx)     // 定时结算后台中已被标志为结束计费的charging
	go svc.cronADFSCreateOrder(ctx)                // adfs计费
	go svc.cronAutoFsCharge(ctx)                   // autoFs计费
	go svc.cronSyncShardingBill(ctx)               // 同步账单分表数据
	go svc.cronPrivateImageCreateOrder(ctx)        // 镜像计费
	go svc.cronCalculateDailyBillStatement(ctx)    // 日结账单, 统计按日账单明细数据
	go svc.cmbCronSyncBankTransfer(ctx)
	go svc.cronRefundQuery(ctx)      // 主动查询更新立即退款状态
	go svc.cronRefundDelayQuery(ctx) // 主动查询更新延迟退款状态
	go svc.cornRefundDelay(ctx)      // 延迟提现
}

func (svc *BCService) MsgRegister() []queue.RegisterInfo {
	return []queue.RegisterInfo{
		{Type: queue_interface.BCCharging, Handler: svc.registerChargingHandler}, // 按量付费实例开关机
		{Type: queue_interface.DataDiskCharging, Handler: svc.registerDDSChargingHandler},
		// 可以并发执行，现在是每个用户一个消息，不会存在锁竞争的情况
		// {Type: queue_interface.AsyncChargingSettle, Handler: svc.registerAsyncChargingSettle},
	}
}

// --------------------------- handler ---------------------------

// instance控制 开始/结束 计费
func (svc *BCService) registerChargingHandler(chargeMsg queue.ElementPayload, consumerHandler queue.ConsumerHandlerInterface) {
	var (
		err     error
		payload = new(queue_interface.NewQueueForBCCharging)
		log     = svc.log.WithField("part", "worker").
			WithField("handler", "registerChargingHandler").
			WithField("type", payload.Type()).WithField("info", chargeMsg)
	)

	defer func() {
		if err != nil {
			err = consumerHandler.Nack()
			if err != nil {
				log.WithError(err).Info("reject failed")
			}
		}
	}()

	log.Info("get a BCCharging msg...")

	// parse
	err = payload.ParseFromContent(chargeMsg.Payload)
	if err != nil {
		log.WithError(err).Error("ParseFromContent failed")
		return
	}

	err = svc.doRegisterCharging(nil, payload)
	if err != nil {
		log.WithError(err).WithField("payload", payload).Error("doRegisterCharging failed")
		return
	}

	// ack
	err = consumerHandler.Ack()
	if err != nil {
		log.WithError(err).Error("ack queue failed")
		return
	}
}

func (svc *BCService) RegisterCharging(tx *gorm.DB, payload *queue_interface.NewQueueForBCCharging) (err error) {
	return svc.doRegisterCharging(tx, payload)
}

func (svc *BCService) doRegisterCharging(tx *gorm.DB, payload *queue_interface.NewQueueForBCCharging) (err error) {
	if payload == nil {
		return businesserror.ErrInternalError
	}

	var (
		lock     = new(redis.RedisMutex)
		order    = new(model.Order)
		charging = new(model.Charging)
		log      = svc.log.WithField("payload", payload)
	)

	// 加锁, 每条记录一个锁
	lock, err = svc.setLockForRegisterCharging(payload.OrderUUID)
	if err != nil {
		return err
	}
	defer func() {
		_ = lock.UnLock()
	}()

	// 获取order
	order, err = svc.getOrder(payload.OrderUUID)
	if err != nil {
		log.WithError(err).Error("Get order failed in doRegisterCharging().")
		return
	}

	// 查看计费类型是否正确
	if !order.ChargeType.IsPayg() {
		return nil
	}

	// 检查
	charging, err = svc.chargingGet(order.RuntimeUUID)
	if err != nil {
		log.WithError(err).Error("check charging info failed")
		return
	}

	// 分操作执行
	switch payload.Operate {
	case queue_interface.ChargingStart:
		if charging == nil {
			// 检查是否是错误消息
			lastCharging := new(model.Charging)
			err = db_helper.GetLast(db_helper.QueryDefinition{
				ModelDefinition: &model.Charging{},
				Filters: db_helper.QueryFilters{
					EqualFilters: map[string]interface{}{"runtime_uuid": order.RuntimeUUID, "order_uuid": order.UUID}},
			}, lastCharging).GetError()
			if err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					err = nil
				} else {
					log.WithError(err).WithField("orderUUID", order.UUID).Error("get charging failed")
					err = businesserror.ErrDatabaseError
					return
				}
			} else {
				if payload.T.Before(lastCharging.LastSettledAt) {
					log.WithField("payload", libs.IndentString(payload)).Error("charging start: time error!!!!!!!!!")
					return nil
				}
			}

			if payload.ChargeType != constant.ChargeTypeNoGpu {
				payload.ChargeType = constant.ChargeTypePayg
			}

			note := model.ChargingNote{}

			if order.RuntimeType == constant.ContainerRuntimeOfDeployment {
				dc := ""
				region, err := svc.region.GetRegionDetail(order.RuntimeEntity.RegionSign)
				if err != nil {
					svc.log.WithError(err).WithField("sign", order.RuntimeEntity.RegionSign).Error("get region failed")
				} else {
					dc = region.DataCenter
				}
				note.DeploymentUUID = order.RuntimeEntity.DeploymentUUID
				note.GpuNum = order.PriceEntity.Num
				note.GpuTypeID = order.MachineEntity.GpuTypeID
				note.GpuType = order.MachineEntity.GpuType.Name
				note.DC = dc
				note.RegionSign = order.RuntimeEntity.RegionSign
			}

			// 写charging表
			now := time.Now()
			err = svc.chargingStart(tx, &model.Charging{
				UID:             order.UID,
				SubName:         payload.SubName,
				UserPhone:       order.UserPhone,
				MachineID:       order.RuntimeEntity.MachineID,
				ProductUUID:     order.ProductUUID,
				RuntimeUUID:     order.RuntimeUUID,
				RuntimeType:     order.RuntimeType,
				OrderUUID:       order.UUID,
				Type:            payload.ChargeType,
				PaygPrice:       payload.PaygPrice,
				OriginPaygPrice: payload.OriginPrice,
				Charging:        true,
				CreatedAt:       now,
				StartedAt:       payload.T,
				LastSettledAt:   payload.T,
				UpdatedAt:       now,
				Note:            note,
			})
			if err != nil {
				log.WithError(err).Error("start charging failed")
				return
			}
		}

	case queue_interface.ChargingStop:
		if charging != nil && charging.StoppedAt == nil {
			err = svc.chargingStopFillStopTime(tx, charging.OrderUUID, &payload.T)
			// err = svc.createBillAndUpdateWalletForStopCharging(order, payload.T)
			if err != nil {
				log.WithError(err).Warn("charging stop failed")
				return
			}
		}

	default:
		log.Info("charging: operate type error")
	}
	return nil
}

func (svc *BCService) registerDDSChargingHandler(ddsMsg queue.ElementPayload, consumerHandler queue.ConsumerHandlerInterface) {
	var (
		err     error
		payload = new(queue_interface.NewQueueForDDSRecharge)
		log     = svc.log.WithField("type", payload.Type()).WithField("info", ddsMsg)
	)

	defer func() {
		if err != nil {
			err = consumerHandler.Nack()
			if err != nil {
				svc.log.WithError(err).Info("reject failed")
			}
		}
	}()
	// parse
	err = payload.ParseFromContent(ddsMsg.Payload)
	if err != nil {
		log.WithError(err).Error("ParseFromContent failed")
		return
	}
	log.WithField("payload", payload)

	txUUID, err := svc.dds.Release(nil, &ddsModel.ReleaseParams{
		MachineID:   payload.MachineID,
		ProductUUID: payload.InstanceUUID.String(),
		ReleaseDDA:  true,
	})
	if err != nil {
		log.WithError(err).Error("release data disk stock failed")
		return
	}
	defer func() {
		if err != nil && txUUID != "" {
			err = svc.dds.TxRollback(txUUID)
			if err != nil {
				log.WithError(err).Error("release data disk stock rollback failed")
				return
			}
		} else if err == nil && txUUID != "" {
			err = svc.dds.TxCommit(txUUID)
			if err != nil {
				log.WithError(err).Error("release data disk stock commit failed")
				return
			}
		}
	}()

	err = svc.dataDiskChargingRelease(nil, payload.InstanceUUID.String())
	if err != nil {
		return
	}

	// ack
	err = consumerHandler.Ack()
	if err != nil {
		log.WithError(err).Error("ack queue failed")
		return
	}
}

//// 整点计算，改成异步执行
//func (svc *BCService) registerAsyncChargingSettle(msg queue.ElementPayload, consumerHandler queue.ConsumerHandlerInterface) {
//	var (
//		err          error
//		payload      = new(queue_interface.NewQueueForBCChargingAsyncSettle)
//		log          = svc.log.WithField("type", payload.Type()).WithField("info", msg)
//		chargingList []model.Charging
//	)
//
//	defer func() {
//		if err != nil {
//			err = consumerHandler.Nack()
//			if err != nil {
//				log.WithError(err).Info("reject failed")
//			}
//		}
//	}()
//
//	// parse
//	err = payload.ParseFromContent(msg.Payload)
//	if err != nil {
//		log.WithError(err).Error("ParseFromContent failed")
//		return
//	}
//
//	chargingList, err = svc.chargingListGetByIDList(payload.ChargingIDList)
//	if err != nil {
//		svc.log.WithError(err).Error("get charging list failed")
//		return
//	}
//
//	for _, v := range chargingList {
//		if !v.Charging || payload.SettleTime.Before(v.LastSettledAt) || v.StoppedAt != nil {
//			// charging的上次结算时间比payload的待结算时间靠前或相等，说明是重复操作
//			continue
//		}
//
//		errDoing := svc.paygAsyncSettleDoing(&model.SettleOnceAsyncRequest{
//			SettleTime: payload.SettleTime,
//			SettleInstanceList: model.SettleInstanceList{
//				UID:             v.UID,
//				UserPhone:       v.UserPhone,
//				MachineID:       v.MachineID,
//				OrderUUID:       v.OrderUUID,
//				ProductUUID:     v.ProductUUID,
//				RuntimeUUID:     v.RuntimeUUID,
//				LastSettledAt:   v.LastSettledAt,
//				PaygPrice:       v.GetPaygPriceFloating(),
//				OriginPaygPrice: v.GetOriginPaygPriceFloating(),
//				OperateType:     payload.OperateType,
//				ChargeType:      v.Type,
//			},
//		})
//		if errDoing != nil {
//			svc.log.WithError(errDoing).Error("payg async settle failed")
//			err = businesserror.ErrInternalError // 用于再次消费消息
//		}
//	}
//
//	// 结束循环后，如果err ！= nil，则会再次消费消息
//}

// ----------------------------------- cron ------------------------------

// [worker] 按量付费定时结算
func (svc *BCService) cronDoCharging(ctx context.Context) {
	now := time.Now()
	times := int64(now.Minute()/10 + 1 + now.Hour()*6)
	next := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), (now.Minute()/10+1)*10, 0, 0, now.Location())
	t := time.NewTicker(next.Sub(now))
	var timer *time.Ticker

	select {
	case now := <-t.C:
		timer = time.NewTicker(constant.CronSettlePaygInstancesTimeInterval)
		t.Stop()
		svc.doChargingForDDS(times, now)
		svc.doCharging(times, now)
		// svc.doChargingFastPub(times, now)
		break
	}

	for {
		select {
		case <-ctx.Done():
			return
		case now := <-timer.C:
			now = time.Now()
			times++
			err := svc.setLockForDoCharging()
			if err == nil {
				svc.doChargingForDDS(times, now)
				svc.doCharging(times, now)
				// svc.doChargingFastPub(times, now)
			}
			err = nil
		}
	}
}

func (svc *BCService) doCharging(times int64, settleTime time.Time) {
	// 获取待扣费按量付费实例
	paygInstances, err := svc.chargingList("")
	if err != nil {
		svc.log.WithError(err).Warn("Cron get payg instances list failed.")
		return
	}
	if len(paygInstances) == 0 {
		return
	}

	// 初始化
	num := times % 6
	UIDPriceMap := make(map[int]map[string]int64)
	UIDInstanceListMap := make(map[int]map[string][]*model.SettleInstanceList)
	uidSet := libs.NewIntSet()
	subUserSet := libs.NewStringSet()
	balance := make(map[int]int64)
	voucherBalance := make(map[int]int64)
	settleReq := &model.SettleOnceRequest{
		SettleTime:     settleTime,
		InstanceUIDMap: make(map[int][]*model.SettleInstanceList),
	}

	ddp := &deploymentModel.DeploymentDurationPkg{}
	ddpSkuIDMap, ddpIDBalanceMap, err := ddp.DDPGetAllForBCCronCharge()
	if err != nil {
		svc.log.ErrorE(err, "doCharging get deployment ddp failed")
	}

	// 所有运行中的实例按uid集合在一起
	for _, v := range paygInstances {
		if v.StoppedAt != nil {
			continue
		}

		operateType := constant.UpdateWalletPaygShutdown
		currentPrice := libs.PaygGetCost(v.LastSettledAt, settleTime, v.GetPaygPriceFloating())
		if v.RuntimeType == constant.ContainerRuntimeOfDeployment && v.Note.GpuNum != 0 {
			dc := v.Note.DC
			if dc == "" {
				order, err := svc.GetOrder(v.OrderUUID)
				if err == nil {
					rs, err := svc.region.GetRegionDetail(order.RuntimeEntity.RegionSign)
					if err == nil {
						dc = rs.DataCenter
					}
				}
			}

			var ddpBalance int64 // 所有符合条件的ddp的余额
			idList, ok := ddpSkuIDMap[v.Note.DeploymentUUID+v.Note.GpuType+dc]
			if !ok {
				idList = []int{}
			}

			for _, v := range idList {
				ddpBalance += ddpIDBalanceMap[v]
			}

			if ddpBalance != 0 {
				chargeDuration := int64(v.Note.GpuNum) * (settleTime.Unix() - v.LastSettledAt.Unix())
				if ddpBalance >= chargeDuration {
					for _, v := range idList { // 分别
						balance := ddpIDBalanceMap[v]
						if balance >= chargeDuration {
							// 如果进入到这个循环, 那么最终一定会走到这里
							ddpIDBalanceMap[v] = balance - chargeDuration
							break
						} else {
							ddpIDBalanceMap[v] = 0
							chargeDuration -= balance
						}
					}

					currentPrice = 0
					operateType = constant.UpdateWalletPaygSettle

				} else {
					sourceChargeDuration := chargeDuration
					// 所有的ddp的balance和, 不够抵扣的. 直接全部置为0
					chargeDuration = chargeDuration - ddpBalance
					for _, v := range idList {
						ddpIDBalanceMap[v] = 0
					}
					currentPrice = libs.AfterDDPPrice(currentPrice, chargeDuration, sourceChargeDuration)
				}
			}
		}

		settleInstanceList := &model.SettleInstanceList{
			ID:              v.ID,
			UID:             v.UID,
			SubName:         v.SubName,
			UserPhone:       v.UserPhone,
			MachineID:       v.MachineID,
			OrderUUID:       v.OrderUUID,
			ProductUUID:     v.ProductUUID,
			RuntimeUUID:     v.RuntimeUUID,
			RuntimeType:     v.RuntimeType,
			LastSettledAt:   v.LastSettledAt,
			PaygPriceBasic:  v.PaygPrice,
			PaygPrice:       v.GetPaygPriceFloating(),
			OriginPaygPrice: v.GetOriginPaygPriceFloating(),
			OperateType:     operateType,
			ChargeType:      v.Type,
			Note:            v.Note,
		}

		// 初始化
		if _, ok := UIDPriceMap[v.UID]; !ok {
			UIDPriceMap[v.UID] = make(map[string]int64)
		}
		if _, ok := UIDInstanceListMap[v.UID]; !ok {
			UIDInstanceListMap[v.UID] = make(map[string][]*model.SettleInstanceList)
		}

		// 主账号
		uidSet.Append(v.UID)
		uidStr := strconv.Itoa(v.UID)
		UIDPriceMap[v.UID][uidStr] += currentPrice
		if _, ok := UIDInstanceListMap[v.UID][uidStr]; !ok {
			UIDInstanceListMap[v.UID][uidStr] = make([]*model.SettleInstanceList, 0)
		}

		// 子账号
		if v.SubName != "" {
			subUserSet.Append(v.SubName)
			if _, ok := UIDInstanceListMap[v.UID][v.SubName]; !ok {
				UIDInstanceListMap[v.UID][v.SubName] = make([]*model.SettleInstanceList, 0)
			}
			UIDInstanceListMap[v.UID][v.SubName] = append(UIDInstanceListMap[v.UID][v.SubName], settleInstanceList)
			UIDPriceMap[v.UID][v.SubName] += currentPrice
		} else {
			UIDInstanceListMap[v.UID][uidStr] = append(UIDInstanceListMap[v.UID][uidStr], settleInstanceList)
		}
	}

	// 根据uid获取每个用户的余额
	balance, err = svc.getBalances(uidSet.ToSlice())
	if err != nil {
		svc.log.WithField("uid_list", uidSet.ToSlice()).WarnE(err, "[bc doCharging] get balance failed")
		return
	}
	voucherBalance, err = svc.voucherGetBalance(uidSet.ToSlice())
	if err != nil {
		svc.log.WithField("uid_list", uidSet.ToSlice()).WarnE(err, "[bc doCharging] get voucher balance failed")
		return
	}

	//根据subName获取用户下子账号的wallet
	subUserListForPaygSettle, err := svc.getSubUserPaygSettle(subUserSet.ToSlice())
	if err != nil {
		svc.log.WithField("uid_sub_user_list", subUserSet.ToSlice()).WarnE(err, "[bc doCharging] get subUserListForPaygSettle failed")
		return
	}

	var instanceList []*model.SettleInstanceList
	var walletCompareAsset int64
	// 操作
	if num == 0 {
		/*
		* 整点结算
		* 结算后会欠款的, 直接shutdown
		* 不会欠款的, settle
		 */
		for uid, subUserInsList := range UIDInstanceListMap {
			uidStr := strconv.Itoa(uid)
			if balance[uid] >= 0 {
				walletCompareAsset = balance[uid] + voucherBalance[uid]
			} else {
				walletCompareAsset = voucherBalance[uid]
			}
			if walletCompareAsset-libs.AssetRound(UIDPriceMap[uid][uidStr], true) <= 0 { // 主账号欠款，直接shutdown
				// 主账号欠款，全部shutdown
				for _, insList := range subUserInsList {
					instanceList = append(instanceList, insList...)
				}
			} else {
				// 主账号不欠款
				// 判断各个子账号是否 是共享余额并且 是余额不足强制关机
				for subUser, settleInstanceList := range subUserInsList {
					// 主账号，已经不欠款，其下所有实例，settle
					// 子账号判断子账号是否 非共享余额并且 是余额不足强制关机
					if subWallet, ok := subUserListForPaygSettle[subUser]; ok && // 这个判断已经能判断出来是不是主账号了
						uidStr != subUser && // 这个子账号是不是主账号
						subWallet.PaymentMethod == constant.SUPaymentMethodQuota &&
						subWallet.InSufficientIsBalanceShutdown &&
						subWallet.Quota-libs.AssetRound(UIDPriceMap[uid][subUser], true) <= 0 {
						instanceList = append(instanceList, settleInstanceList...)
					} else {
						for _, settleInstance := range settleInstanceList {
							settleInstance.OperateType = constant.UpdateWalletPaygSettle
							instanceList = append(instanceList, settleInstance)
						}
					}
				}
			}
		}
	} else {
		/*
		* 每10分钟检验
		* 看哪些用户当前如果结算会欠款
		 */
		for uid, subUserInsList := range UIDInstanceListMap {
			uidStr := strconv.Itoa(uid)
			if UIDPriceMap[uid][uidStr] == 0 {
				continue
			}

			if balance[uid] >= 0 {
				walletCompareAsset = balance[uid] + voucherBalance[uid]
			} else {
				walletCompareAsset = voucherBalance[uid]
			}

			if walletCompareAsset-libs.AssetRound(UIDPriceMap[uid][uidStr], true) <= 0 { // 主账号欠款，直接shutdown
				// 主账号欠款，全部shutdown
				for _, insList := range subUserInsList {
					instanceList = append(instanceList, insList...)
				}
			} else {
				// 主账号不欠款
				// 判断各个子账号是否 是共享余额并且 是余额不足强制关机
				for subUser, settleInstanceList := range subUserInsList {
					// 主账号，已经不欠款，其下所有实例，settle
					// 子账号判断子账号是否 非共享余额并且 是余额不足强制关机
					if subWallet, ok := subUserListForPaygSettle[subUser]; ok && // 这个判断已经能判断出来是不是主账号了
						uidStr != subUser && // 这个子账号是不是主账号
						subWallet.PaymentMethod == constant.SUPaymentMethodQuota &&
						subWallet.InSufficientIsBalanceShutdown &&
						subWallet.Quota-libs.AssetRound(UIDPriceMap[uid][subUser], true) <= 0 {
						instanceList = append(instanceList, settleInstanceList...)
					}
				}
			}
		}
	}

	for k := range instanceList {
		if _, ok := settleReq.InstanceUIDMap[instanceList[k].UID]; !ok {
			settleReq.InstanceUIDMap[instanceList[k].UID] = make([]*model.SettleInstanceList, 0)
		}
		settleReq.InstanceUIDMap[instanceList[k].UID] = append(settleReq.InstanceUIDMap[instanceList[k].UID], instanceList[k])
	}

	svc.paygSettleOnce(times, settleReq)
}

func (svc *BCService) doChargingFastPub(times int64, settleTime time.Time) {
	/*
		使用用户级别发送，如果出现结算过程中部分成功部分失败时
		对于settle的，可以根据lastSettledAt与stoppedAt字段判断是否已经结算过
		对于shutdown的，直接可以根据charging状态判断
	*/

	// 获取待扣费按量付费实例
	paygInstances, err := svc.chargingList("")
	if err != nil {
		svc.log.WithError(err).Warn("Cron get payg instances list failed.")
		return
	}
	if len(paygInstances) == 0 {
		return
	}

	// 初始化
	var (
		num                = times % 6
		UIDPriceMap        = make(map[int]int64)
		UIDInstanceListMap = make(map[int][]int)
		uidList            = make([]int, 0)
		balance            = make(map[int]int64)
		voucherBalance     = make(map[int]int64)
		fastPubList        []queue_interface.NewQueueForBCChargingAsyncSettle
	)

	// 所有运行中的实例按uid集合在一起
	for _, v := range paygInstances {
		if v.StoppedAt != nil {
			continue
		}
		UIDInstanceListMap[v.UID] = append(UIDInstanceListMap[v.UID], v.ID)
		UIDPriceMap[v.UID] += v.GetCostFloating(settleTime)
	}

	// 获取uid列表
	for uid := range UIDInstanceListMap {
		uidList = append(uidList, uid)
	}

	// 根据uid获取每个用户的余额
	balance, err = svc.getBalances(uidList)
	if err != nil {
		svc.log.WithError(err).Warn("get balance failed")
		return
	}
	voucherBalance, err = svc.voucherGetBalance(uidList)
	if err != nil {
		svc.log.WithError(err).Warn("get balance failed")
		return
	}

	// 组织列表
	if num == 0 {
		// 整点结算，结算后会欠款的, 直接shutdown，不会欠款的, settle
		for uid, list := range UIDInstanceListMap {
			operate := constant.UpdateWalletPaygSettle
			if balance[uid]+voucherBalance[uid]-libs.AssetRound(UIDPriceMap[uid], true) <= 0 {
				operate = constant.UpdateWalletPaygShutdown
			}
			fastPubList = append(fastPubList, queue_interface.NewQueueForBCChargingAsyncSettle{
				SettleTime:     settleTime,
				UID:            uid,
				OperateType:    operate,
				ChargingIDList: list,
			})
		}
	} else {
		// 每10分钟检验，看哪些用户当前如果结算会欠款
		for uid, list := range UIDInstanceListMap {
			if balance[uid]+voucherBalance[uid]-libs.AssetRound(UIDPriceMap[uid], true) > 0 {
				continue
			}
			fastPubList = append(fastPubList, queue_interface.NewQueueForBCChargingAsyncSettle{
				SettleTime:     settleTime,
				UID:            uid,
				OperateType:    constant.UpdateWalletPaygShutdown,
				ChargingIDList: list,
			})
		}
	}

	// fastPub
	for _, vv := range fastPubList {
		v := vv
		err = svc.q.FastPub(svc.log, queue.ElementPayload{
			Type:    v.Type(),
			MsgUUID: uuid.NewV4().String(),
			Payload: v.ToString(),
			ValidAt: time.Now(),
		})
		if err != nil {
			svc.log.WithField("payload", v).Error("fast pub failed.")
		}
	}

}

// [worker] 定时更新订单超时
func (svc *BCService) cronUpdateOrderTimeout(ctx context.Context) {
	svc.log.Info("CronUpdateOrderTimeout start...")
	timer := time.NewTicker(constant.CronUpdateOrderTimeoutTimeInterval) // 2 sec
	for {
		select {
		case <-ctx.Done():
			return
		case <-timer.C:
			orderList, err := svc.countdown.HGetAll()
			if err != nil {
				svc.log.WithError(err).Info("get order timeout hashtable failed")
				continue
			}

			for uuid, ts := range orderList {
				var t time.Time
				t, err = time.ParseInLocation(constant.FormatTimeString, ts, time.Local)
				if err != nil {
					svc.log.Info("parse time string failed")
					continue
				}

				if time.Now().Before(t) {
					continue
				}

				order := new(model.Order)
				order, err = svc.getOrder(uuid)
				if err != nil {
					svc.log.WithField("orderUUID", uuid).WithError(err).Warn("get order failed. hdel.")
					err = svc.countdown.HDel(uuid)
					if err != nil {
						svc.log.WithField("orderUUID", order.UUID).WithError(err).Error("Countdown.HDel failed")
					}
					continue
				}

				var ucID int
				if order.PriceEntity != nil && len(order.PriceEntity.CouponIDList) != 0 {
					ucID = order.PriceEntity.CouponIDList[0]
				}

				if order.Status == constant.OrderStatusUnpaid && order.PayAt == nil {
					if ucID != 0 {
						// 使用了优惠券，在事务内部取消优惠券的使用状态
						err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) error {
							err = svc.updateOrderStatus(tx, uuid, constant.OrderStatusTimeout, order.OrderType == constant.OrderTypeCreateInstance)
							if err != nil {
								svc.log.WithField("uuid", uuid).WithError(err).Error("update order failed")
								return err
							}

							err = svc.userCouponUseCancel(tx, ucID)
							if err != nil {
								svc.log.WithField("ucID", ucID).WithError(err).Error("cancel coupon used status failed")
								return err
							}

							return nil
						})
						if err != nil {
							svc.log.WithError(err).Error("cancel order failed")
							continue
						}

					} else {
						// 没使用优惠券
						err = svc.updateOrderStatus(nil, uuid, constant.OrderStatusTimeout, order.OrderType == constant.OrderTypeCreateInstance)
						if err != nil {
							svc.log.WithField("uuid", uuid).WithError(err).Error("update order failed")
							continue
						}
					}

					var releaseGpu bool
					var releaseDDA bool
					var releaseSize int64
					switch order.OrderType {
					case constant.OrderTypeCreateInstance, constant.OrderTypeCloneInstance:
						releaseGpu = true
						releaseDDA = true
						releaseSize = order.PriceEntity.ExpandDataDisk
					case constant.OrderTypeDataDiskExpandOfPrepay:
						releaseDDA = true
						releaseSize = order.PriceEntity.DataDiskInfo.ChangeSize
					case constant.OrderTypeRenewalInstance:
						/*
							到期了，续费，takeEffectAt == 创建时刻
							取消或超时， 释放，takeEffectAt 一定比取消时间早，可以作为判断依据

							没到期，续费。 takeEffectAt == 原到期时间
							取消或超时，两种情况
								一：取消时，还没到期，不释放， takeEffect 比取消时间靠后
								二：取消时，已经到期，释放，takeEffect 比取消时间早
						*/

						if order.RuntimeEntity.TakeEffectAt.Before(time.Now()) {
							releaseGpu = true
						}
					case constant.OrderTypeChangeDaily,
						constant.OrderTypeChangeWeekly,
						constant.OrderTypeChangeMonthly,
						constant.OrderTypeChangeYearly:

						// 检查实例是否正在运行
						charging := new(model.Charging)
						charging, err = svc.chargingGet(order.RuntimeUUID)
						if err != nil {
							svc.log.WithField("uuid", order.RuntimeUUID).WithError(err).Error("check charging info failed")
							continue
						}
						if charging == nil {
							releaseGpu = true
						}
					case constant.OrderTypeCreateDeploymentContainer:
						releaseGpu = true
					}

					if releaseGpu {
						err = svc.gpuStockInterference.Release(&coreGpuStocModel.GpuRelease{
							RuntimeUUID: string(order.RuntimeUUID),
							MachineID:   order.MachineID,
							DebugMsg:    "cronUpdateOrderTimeout",
							Timestamp:   tsc.Timestamp(),
						})
						if err != nil {
							svc.log.WithField("uuid", order.RuntimeUUID).WithError(err).Error("release machine failed")
							continue
						}
					}

					if releaseDDA {
						var txUUID string
						txUUID, err = svc.dds.Release(nil, &ddsModel.ReleaseParams{
							MachineID:   order.MachineID,
							ProductUUID: order.ProductUUID,
							ReleaseSize: releaseSize,
						})
						if err != nil {
							svc.log.WithField("productUUID", order.ProductUUID).ErrorE(err, "release dda failed")
							return
						}
						if txUUID != "" {
							err = svc.dds.TxCommit(txUUID)
							if err != nil {
								svc.log.WithField("productUUID", order.ProductUUID).ErrorE(err, "release dda commit failed")
								return
							}
						}
					}
				}

				err = svc.countdown.HDel(order.UUID)
				if err != nil {
					svc.log.WithField("orderUUID", order.UUID).WithError(err).Error("Countdown.HDel failed")
				}
			}
		}
	}
}

// 定时查询续费订单
func (svc *BCService) cronCheckRenewalUnusedOrder(ctx context.Context) {
	t := time.NewTicker(constant.CronCheckOrderUnused)

	for {
		select {
		case <-ctx.Done():
			return
		case <-t.C:
			// 获取续费未生效订单
			unusedOrder, err := svc.GetOrderUnused("")
			if err != nil {
				svc.log.WithError(err).Error("get order unused failed")
				return
			}

			/*
				按机器组织好，发给instance
				成功发过去的，标记为成功
			*/
			var (
				machineInstanceMap    = make(map[string][]string) // machine -> []instanceUUID
				instanceOrderUUIDMap  = make(map[string]string)   // instanceUUID -> orderUUID
				updateOrderStatusList []string                    // 整体更新订单状态的列表
			)

			for _, v := range unusedOrder {
				if v.RenewalShouldTakeEffect() {
					machineInstanceMap[v.MachineID] = append(machineInstanceMap[v.MachineID], v.ProductUUID)
					instanceOrderUUIDMap[v.ProductUUID] = v.UUID
				}
			}

			for mchID, insList := range machineInstanceMap {
				var (
					msg                    = queue_interface.NewQueueForInstanceOnMachine{MachineID: mchID}
					orderUUIDWaitingUpdate = make([]string, len(insList))
				)

				for k, uuid := range insList {
					msg.OptReqs = append(msg.OptReqs, constant.OptInstanceReq{
						InstanceUUID: uuid,
						OptType:      constant.InstanceChangeOrderUUIDTypeOpt,
						Caller:       constant.OptCallerBC,
						Payload:      instanceOrderUUIDMap[uuid],
					})
					orderUUIDWaitingUpdate[k] = instanceOrderUUIDMap[uuid]
				}

				err = message_model.SimplePubMessage(svc.log, svc.q, &msg)
				if err != nil {
					svc.log.WithField("orderUUID", orderUUIDWaitingUpdate).WithError(err).
						Warn("SimplePubMessage failed when pub change orderUUID msg to instance.")
					continue
				}
				updateOrderStatusList = append(updateOrderStatusList, orderUUIDWaitingUpdate...)
			}

			// 发送成功的标记成功。更新DB
			err = svc.batchUpdateOrderStatus(nil, updateOrderStatusList, constant.OrderStatusSuccess)
			if err != nil {
				svc.log.WithError(err).WithField("orderUUIDLIst", updateOrderStatusList).Error("batchUpdateOrderStatus failed.")
			}

		}
	}
}

// 同步每日按量计费账单
//func (svc *BCService) syncDailyBill(ctx context.Context) {
//	now := time.Now()
//	next := now.Add(time.Hour * 24)
//	next = time.Date(next.Year(), next.Month(), next.Day(), 0, 0, 45, 0, now.Location())
//	t := time.NewTicker(next.Sub(now))
//	var timer *time.Ticker
//	select {
//	case <-t.C:
//		timer = time.NewTicker(24 * time.Hour)
//		t.Stop()
//		err := svc.billToDailyBill(next)
//		if err != nil {
//			svc.log.WithField("err", err).Error("sync bill to daily bill failed")
//			return
//		}
//		break
//	}
//	for {
//		select {
//		case <-ctx.Done():
//			return
//		case newTime := <-timer.C:
//			err := svc.billToDailyBill(newTime)
//			if err != nil {
//				svc.log.WithField("err", err).Error("sync bill to daily bill failed")
//				return
//			}
//		}
//	}
//}

func (svc *BCService) billToDailyBill() {
	var err error
	now := time.Now()
	now = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 45, 0, now.Location())

	defer func() {
		if err != nil {
			svc.log.WithField("err", err).Error("sync bill to daily bill failed")
		}
	}()

	// 每次同步前两天的按量计费账单
	for i := 0; i < 2; i++ {
		dateTo := now.Add(-time.Hour * time.Duration(i) * 24)
		dateFrom := now.Add(-time.Hour * time.Duration(i+1) * 24)
		bills, err := svc.GetBillDayRecord(dateFrom, dateTo)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				continue
			}
			svc.log.WithField("err", err).WithField("date_from", dateFrom).WithField("date_to", dateTo).Warn("get bill failed")
			return
		}
		// 按照order_uuid分类按量计费账单, 按量计费的同一个订单可能会有很多账单，这里合并了一天的总额，其他类型只有一条账单记录
		billProductUUIDMap := make(map[string]*model.InstanceDailyBillStatistics)
		for _, bill := range bills {
			if bill.ChargeType.IsRentType() {
				continue
			}
			_, ok := billProductUUIDMap[bill.OrderUUID]
			if ok {
				billProductUUIDMap[bill.OrderUUID].PayAmount += bill.PayByBalance // 仅加余额支付的钱
			} else {
				rightTime := time.Date(dateTo.Year(), dateTo.Month(), dateTo.Day(), dateTo.Hour(), 0, 0, 0, dateTo.Location())
				ibds := &model.InstanceDailyBillStatistics{
					UID:         bill.UID,
					ProductUUID: bill.ProductUUID,
					OrderUUID:   bill.OrderUUID,
					ChargeType:  bill.ChargeType,
					BillType:    bill.Type,
					PayAmount:   bill.PayByBalance,
					PayAt:       rightTime,
					ProductType: bill.SubType.ToProductType(),
				}
				billProductUUIDMap[bill.OrderUUID] = ibds
			}
		}
		params := make([]*model.CreateOrUpdateDailyBillParams, 0)
		for _, instanceBillDailyStatistics := range billProductUUIDMap {
			if instanceBillDailyStatistics.PayAmount <= 0 {
				continue
			}
			params = append(params, &model.CreateOrUpdateDailyBillParams{
				UID:         instanceBillDailyStatistics.UID,
				OrderUUID:   instanceBillDailyStatistics.OrderUUID,
				ProductUUID: instanceBillDailyStatistics.ProductUUID,
				ChargeType:  instanceBillDailyStatistics.ChargeType,
				PayAmount:   instanceBillDailyStatistics.PayAmount,
				BillType:    instanceBillDailyStatistics.BillType,
				PayAt:       instanceBillDailyStatistics.PayAt,
				ProductType: instanceBillDailyStatistics.ProductType,
			})
		}
		err = svc.createDailyBill(params)
		if err != nil {
			svc.log.WithField("err", err).Error("sync daily bill failed")
			return
		}
	}
	return
}

func (svc *BCService) doChargingForDDS(times int64, t time.Time) {
	// 测试环境每次都执行;线上环境需要等到0点
	if !conf.GetGlobalGsConfig().App.DebugApi && times%144 != 0 {
		return
	}
	var (
		paygList []model.DataDiskCharging
	)

	svc.log.Info("doChargingForDDS: begin payg... times: %d", times)
	paygList, errExternal := svc.dataDiskChargingGetList()
	if errExternal != nil {
		svc.log.WithError(errExternal).Error("get ddc list failed")
		return
	}

	uidSet := goset.NewSet[int]()
	machineIDSet := goset.NewSet[string]()
	machineMap := make(map[string]machineModel.Machine)
	userMemberMap := make(map[int]constant.MemberLevelName)
	uidPaygMap := map[int][]*model.DataDiskCharging{}
	uidPaygMapRepeat := map[int][]*model.DataDiskCharging{}

	// 按uid聚合, 方便并发计费
	for k := range paygList {
		if _, ok := uidPaygMap[paygList[k].UID]; !ok {
			uidPaygMap[paygList[k].UID] = make([]*model.DataDiskCharging, 0)
		}
		uidPaygMap[paygList[k].UID] = append(uidPaygMap[paygList[k].UID], &paygList[k])
		uidSet.Add(paygList[k].UID)
		machineIDSet.Add(paygList[k].MachineID)
	}

	// 提前获取userMember和machine信息
	machineList, err := svc.machine.GetAllMachine(machineIDSet.ToSlice()...)
	if err != nil {
		svc.log.ErrorE(err, "get machine list failed")
		return
	}
	for k, v := range machineList {
		machineMap[v.MachineID] = machineList[k]
	}

	umList, err := svc.user.UserMemberGetAll(uidSet.ToSlice()...)
	if err != nil {
		svc.log.ErrorE(err, "get um list failed")
		return
	}
	for k, v := range umList {
		userMemberMap[v.UID] = umList[k].MemberLevel
	}

	limiter := make(chan struct{}, 5)
	loop := 0
	mu := sync.Mutex{}

	// 对于按量计费，直接生成扣费账单
	// 如果dailyPrice与dailyPeakPrice不相等，那么取二者最大值，更新时
	for len(uidPaygMap) != 0 {
		if loop > 10 {
			break
		}
		svc.log.Info("doChargingForDDS, loop: %d, charging:%d", loop, len(paygList))
		wg := sync.WaitGroup{}
		loop++
		uidPaygMapRepeat = map[int][]*model.DataDiskCharging{}
		for uid := range uidPaygMap {
			limiter <- struct{}{}
			wg.Add(1)
			go func(vList []*model.DataDiskCharging) {
				var err error
				defer func() {
					if panicErr := recover(); panicErr != nil {
						svc.log.Error("panic:err:%v", err)
					}
					<-limiter
					wg.Done()
				}()

				for k, v := range vList {
					var (
						peakSize        int64
						peakPrice       int64
						originPeakPrice int64
						chargeEndTime   = t
						updateMap       = make(map[string]interface{})
					)

					updateMap["last_settled_at"] = time.Now()
					updateMap["force_settle_once"] = false
					peakSize = v.ExpandSize
					peakPrice = v.DailyPrice
					originPeakPrice = v.OriginDailyPrice

					// 判断此时是否有会员折扣
					var machine machineModel.Machine
					var ok bool
					if machine, ok = machineMap[v.MachineID]; !ok {
						machineGet, err := svc.machine.Get(v.MachineID)
						if err != nil {
							svc.log.WithField("machine_id", v.MachineID).ErrorE(err, "get machine failed")
							return
						}
						machine = *machineGet
					}

					var memberLevel constant.MemberLevelName
					if memberLevel, ok = userMemberMap[v.UID]; !ok {
						userMember, err := svc.user.GetUserMemberInfoByUid(v.UID)
						if err != nil {
							log.WithError(err).Error("get userMember failed")
							return
						}
						memberLevel = userMember.MemberLevel
					}

					memberDiscount, err := machine.GetDiscountRate(v.ChargeType, memberLevel)
					if err != nil {
						svc.log.WithField("chargeType", v.ChargeType).ErrorE(err, "get discount rate failed")
						return
					}

					afterMemberDiscountPrice := libs.DiscountPrice(originPeakPrice, memberDiscount, false)
					if afterMemberDiscountPrice != v.DailyPrice {
						updateMap["daily_price"] = afterMemberDiscountPrice
						v.DailyPrice = afterMemberDiscountPrice
					}

					if v.DailyPeakPrice != v.DailyPrice {
						// 不相等意味着存在最大值，全部取当日最大值
						peakSize = v.DailyPeakExpandSize
						peakPrice = v.DailyPeakPrice
						originPeakPrice = v.OriginDailyPeakPrice

						// 把最大值更新成和一般值相等
						updateMap["daily_peak_expand_size"] = v.ExpandSize
						updateMap["daily_peak_price"] = v.DailyPrice
						updateMap["origin_daily_peak_price"] = v.OriginDailyPrice
					}

					// 是否是最后一次计费
					if v.ReleasedAt != nil {
						updateMap["charging"] = false
						chargeEndTime = *v.ReleasedAt
					}

					bill := &model.Bill{
						UID:           v.UID,
						UserPhone:     v.UserPhone,
						OrderUUID:     v.OrderUUID,
						RuntimeUUID:   constant.NewContainerRuntimeUUID(v.ProductUUID),
						ProductUUID:   v.ProductUUID,
						Type:          constant.BillTypeCharge,
						SubType:       constant.BillSubTypeDataDiskSettle,
						Asset:         peakPrice,
						ChargeType:    constant.ChargeTypePayg,
						DetailsEntity: &model.BillDetail{ExpandSize: peakSize, SubName: v.SubName},
						CreatedAt:     t,
						UpdatedAt:     t,
						ConfirmAt:     &t,
					}
					bill.SetDiscountInfo(originPeakPrice)
					bill.SetChargeDuration(&v.LastSettledAt, &chargeEndTime)

					_, _, err = svc.createBillAndUpdateWallet(&model.AfterUpdateWalletParams{
						Bill:              bill,
						AfterOperateType:  constant.UpdateWalletDataDiskPaygSettle,
						DataDiskUpdateMap: updateMap,
					})
					if err != nil {
						//if err == businesserror.ErrServerBusy {
						mu.Lock()
						if _, ok := uidPaygMapRepeat[v.UID]; !ok {
							uidPaygMapRepeat[v.UID] = make([]*model.DataDiskCharging, 0)
						}
						uidPaygMapRepeat[v.UID] = append(uidPaygMapRepeat[v.UID], vList[k])
						mu.Unlock()
						//} else {
						//	svc.log.WithError(err).WithField("bill", bill).WithField("product_uuid", v.ProductUUID).
						//		Error("data disk settle create bill and update wallet failed")
						//}
					}
				}

			}(uidPaygMap[uid])
		}
		wg.Wait()
		uidPaygMap = uidPaygMapRepeat
		time.Sleep(time.Second)
	}

	svc.log.Info("doChargingForDDS: update prepay to payg...")
	// 更新到期的预付费到按量
	errExternal = svc.dataDiskChargingUpdatePrepayToPayg()
	if errExternal != nil {
		svc.log.WithError(errExternal).Error("update ddc prepay to payg failed")
	}
}

// 定时结算后台中已被标志为结束计费的charging
func (svc *BCService) cronSettleChargingTableStopped(ctx context.Context) {
	t := time.NewTicker(constant.CronSyncCharging)

	for {
		select {
		case <-ctx.Done():
			t.Stop()
			return
		case <-t.C:
			var (
				chargingList []model.Charging
				err          error
			)

			chargingList, err = svc.chargingListStopped()
			if err != nil {
				svc.log.WithError(err).Error("get stopped charging list failed")
				continue
			}

			for _, charging := range chargingList {
				if charging.StoppedAt == nil {
					continue
				}
				if charging.LastSettledAt.After(*charging.StoppedAt) {
					// 因为意外原因，导致已经被计费了，此处就直接更新为停止计费
					err = svc.chargingStopForAsyncJob(nil, charging.OrderUUID, &charging.LastSettledAt, charging.LastSettledAt.Format(constant.FormatTimeString))
					if err != nil {
						svc.log.WithError(err).WithField("charging", charging).Error("chargingStopForAsyncJob failed failed.")
					}
				} else {
					// 计费
					err = svc.createBillAndUpdateWalletForStopCharging(&charging)
					if err != nil {
						svc.log.WithError(err).WithField("charging", charging).Error("cron settle charging failed.")
					}
				}
			}
		}
	}
}

func (svc *BCService) cronADFSCreateOrder(ctx context.Context) {
	log := svc.log.WithField("method", "cronADFSCreateOrder")
	for {
		log.Info("cronADFSCreateOrder will begin at next time")
		now := time.Now()               //获取当前时间，放到now里面，要给next用
		next := now.Add(time.Hour * 24) //通过now偏移24小时
		//next := now.Add(time.Minute * 2)
		next = time.Date(next.Year(), next.Month(), next.Day(), 0, 10, 0, 0, next.Location()) //获取下一个00:10的日期(00:05的时候会同步前一天的峰值数据到KV)
		t := time.NewTimer(next.Sub(now))                                                     //计算当前时间到下一个00:10的时间间隔，设置一个定时器
		<-t.C
		current := time.Date(next.Year(), next.Month(), next.Day(), 0, 0, 0, 0, next.Location())

		// 现在now是前一天时间，next是当前天00:10，current是当前天00:00

		// 获取所有地区信息
		regions, err := svc.region.GetRegionList()
		if err != nil {
			log.ErrorE(err, "get region list failed.")
			continue
		}

		// 这里没用协程，因为并发会造成数据库写入压力
		for _, region := range regions {
			if !region.IsADFSAvailable {
				continue
			}
			log = log.WithField("region", region.Sign)
			key := fmt.Sprintf(constant.ADFSKVAllUserDayMaxUsageKey, region.Sign, now.Year(), now.Month(), now.Day()) // 取前一天的用户使用量
			log.Info("key:%+v", key)
			val, err := svc.kv.Get(key)
			if err != nil {
				log.WithField("region", region.Sign).ErrorE(err, "kv get failed.")
				continue
			}
			if val == "" {
				continue
			}

			kvArr := make([]constant.ADFSKV, 0)
			err = json.Unmarshal([]byte(val), &kvArr)
			if err != nil {
				svc.log.WithField("region", region.Sign).ErrorE(err, "unmarshal %+v failed.", val)
				continue
			}

			// 获取这个地区所有开通网盘的记录
			fs, err := svc.region.GetFileStorageForCharge(region.Sign, constant.ADFS, time.Now())
			if err != nil {
				log.WithField("region", region.Sign).ErrorE(err, "GetFileStorageForCharge failed.")
				continue
			}

			uidOrderUUIDMap := make(map[int]string)
			uids := make([]int, 0)
			for _, f := range fs.List {
				uids = append(uids, f.UID)
				uidOrderUUIDMap[f.UID] = f.OrderUUID
			}

			if len(uids) == 0 {
				continue
			}

			users, err := svc.user.FindByUserIds(uids)
			if err != nil {
				log.ErrorE(err, "get user info failed.")
				continue
			}

			uidM := make(map[int]*userModel.User)
			for _, user := range users {
				uidM[user.ID] = user
			}

			// 处理每个用户
			for _, kv := range kvArr {
				uid := kv.UID
				if _, ok := uidM[uid]; !ok {
					continue
				}
				if _, ok := uidOrderUUIDMap[uid]; !ok {
					continue
				}
				if uidOrderUUIDMap[uid] == "" {
					continue
				}
				user := uidM[uid]

				cost := constant.ADFSCost(kv.Usage)

				if cost <= 0 {
					continue
				}

				detail := &model.BillDetail{
					// 因为now的时分秒跟worker启动时间相关所以需要使用current来计算计费周期
					ChargeFrom:          current.Add(-time.Hour * 24).Format("2006-01-02 15:04:05"),
					ChargeTo:            current.Format("2006-01-02 15:04:05"),
					FileStorageCostSize: kv.Usage - constant.ADFSFreeSize,
					RegionSign:          region.Sign,
					DateCenter:          region.DataCenter,
					RegionName:          region.Name,
				}
				bill := &model.Bill{
					UID:           uid,
					UserPhone:     user.Phone,
					OrderUUID:     uidOrderUUIDMap[uid],
					Type:          constant.BillTypeCharge,
					SubType:       constant.BillSubTypeFileStorage,
					ChargeType:    constant.ChargeTypePayg,
					Asset:         cost,
					CreatedAt:     current,
					UpdatedAt:     current,
					ConfirmAt:     &current,
					DetailsEntity: detail,
				}

				params := model.AfterUpdateWalletParams{
					Bill:             bill,
					AfterOperateType: constant.UpdateWalletSkip,
					AfterHook:        nil,
				}
				_, _, err = svc.createBillAndUpdateWallet(&params)
				if err != nil {
					log.WithField("uid", uid).ErrorE(err, "createBillAndUpdateWallet failed.")
					continue
				}

				// 如果余额支付的数为0（全部用代金券支付的）就不生成日结账单了
				if params.Bill.PayByBalance != 0 {
					var dailyBill = []*model.CreateOrUpdateDailyBillParams{
						&model.CreateOrUpdateDailyBillParams{
							UID:         uid,
							OrderUUID:   uidOrderUUIDMap[uid],
							ProductUUID: uidOrderUUIDMap[uid],
							ChargeType:  constant.ChargeTypePayg,
							PayAmount:   cost,
							BillType:    constant.BillTypeCharge,
							ProductType: constant.ProductTypeFileStorage,
							PayAt:       current,
							Detail:      detail,
						},
					}

					err = svc.createDailyBill(dailyBill)
					if err != nil {
						log.WithField("uid", uid).WithField("order_uuid", uidOrderUUIDMap[uid]).ErrorE(err, "createDailyBill failed.")
						continue
					}
				}
			}
		}
	}
}

func (svc *BCService) cronSyncShardingBill(ctx context.Context) {
	svc.syncShardingBill()

	ticker := time.NewTicker(time.Minute * 5)

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			svc.syncShardingBill()
		}
	}
}

func (svc *BCService) syncShardingBill() {
	log := svc.log.WithField("method", "syncShardingBill")
	log.Info("syncShardingBill start...")

	db := db_helper.GlobalDBConn()

	now := time.Now()
	bills := make([]model.Bill, 0)
	lastSyncID := 0

	fn := func(tx *gorm.DB, batch int) error {
		if len(bills) == 0 {
			return nil
		}
		createBills := make([]model.Bill, 0)
		for _, bill := range bills {
			if bill.ConfirmAt == nil {
				continue
			}
			if now.Sub(*bill.ConfirmAt).Hours() > 24*30 { // 超过30天的丢弃
				continue
			}
			lastSyncID = bill.ID
			bill.ID = 0
			createBills = append(createBills, bill)
		}
		if len(createBills) != 0 {
			err := db.Table(model.TableNameBillShardingSync).Create(&createBills).Error
			if err != nil {
				return err
			}
		}
		return nil
	}

	for i := 0; i < constant.BillShardingCount; i++ {
		tableID := constant.GetBillShardingTableID(i)
		lastSyncID = svc.cache.LastSyncShardingBillIDGet(tableID)
		table := constant.GetBillShardingTable(i)
		err := db.Table(table).
			Where("id > ?", lastSyncID).
			FindInBatches(&bills, 2000, fn).
			Error
		if err != nil {
			log.WithError(err).Error("FindInBatches failed")
			return
		}
		svc.cache.LastSyncShardingBillIDSet(tableID, lastSyncID)
	}
}

func (svc *BCService) cronCalculateDailyBillStatement(ctx context.Context) {
	l := svc.log.WithField("method", "cronCalculateDailyBillStatement")
	for {
		l.Info("cronCalculateDailyBillStatement will begin at next time")

		//now := time.Now() //获取当前时间，放到now里面，要给next用
		////next := now.Add(time.Hour * 24) //通过now偏移24小时
		//next := now.Add(time.Hour)
		//next = time.Date(next.Year(), next.Month(), next.Day(), next.Hour(), 0, 0, 0, next.Location()) //获取下一个00:20的日期
		//t := time.NewTimer(next.Sub(now))                                                              //计算当前时间到下一个00:20的时间间隔，设置一个定时器
		//<-t.C
		//dateTo := next
		//dateFrom := dateTo.Add(-1 * time.Hour)

		now := time.Now()                                                                     //获取当前时间，放到now里面，要给next用
		next := now.Add(time.Hour * 24)                                                       //通过now偏移24小时
		next = time.Date(next.Year(), next.Month(), next.Day(), 0, 20, 0, 0, next.Location()) //获取下一个00:20的日期
		t := time.NewTimer(next.Sub(now))                                                     //计算当前时间到下一个00:20的时间间隔，设置一个定时器
		select {
		case <-ctx.Done():
			return
		case <-t.C:
		}
		dateTo := time.Date(next.Year(), next.Month(), next.Day(), 0, 0, 0, 0, next.Location())
		dateFrom := dateTo.Add(-time.Hour * 24)

		db := db_helper.GlobalDBConn()

		var bills []model.Bill
		var box map[int]map[constant.BillType]model.DailyBillStatement

		fn := func(tx *gorm.DB, batch int) error {
			if len(bills) == 0 {
				return nil
			}
			for _, bill := range bills {
				if _, ok := box[bill.UID]; !ok {
					box[bill.UID] = make(map[constant.BillType]model.DailyBillStatement)
				}
				if bill.Type == constant.BillTypeCharge {
					if _, ok := box[bill.UID][constant.BillTypeCharge]; !ok {
						box[bill.UID][constant.BillTypeCharge] = model.DailyBillStatement{}
					}
					chargeStatement := box[bill.UID][constant.BillTypeCharge]
					discount := make(map[string]interface{})
					json.Unmarshal(bill.DiscountJson, &discount)
					var originPrice int64
					var discountSum int64
					if _, ok := discount["origin_price"]; ok {
						originPrice = cast.ToInt64(discount["origin_price"])
					}
					if _, ok := discount["discount_sum"]; ok {
						discountSum = cast.ToInt64(discount["discount_sum"])
					}
					chargeStatement.UID = bill.UID
					chargeStatement.UserPhone = bill.UserPhone
					chargeStatement.Type = constant.BillTypeCharge
					chargeStatement.Date = dateFrom
					chargeStatement.OriginPriceAmount += originPrice
					chargeStatement.DiscountAmount += discountSum
					chargeStatement.AssetAmount += bill.Asset
					chargeStatement.PayByBalanceAmount += bill.PayByBalance
					chargeStatement.PayByVoucherAmount += bill.PayByVoucher
					box[bill.UID][constant.BillTypeCharge] = chargeStatement
				} else if bill.Type == constant.BillTypeRefund {
					if _, ok := box[bill.UID][constant.BillTypeRefund]; !ok {
						box[bill.UID][constant.BillTypeRefund] = model.DailyBillStatement{}
					}
					refundStatement := box[bill.UID][constant.BillTypeRefund]
					refundStatement.UID = bill.UID
					refundStatement.UserPhone = bill.UserPhone
					refundStatement.Type = constant.BillTypeRefund
					refundStatement.Date = dateFrom
					refundStatement.AssetAmount += bill.Asset
					box[bill.UID][constant.BillTypeRefund] = refundStatement
				}
			}
			return nil
		}

		for i := 0; i < constant.BillShardingCount; i++ {
			box = make(map[int]map[constant.BillType]model.DailyBillStatement)
			table := constant.GetBillShardingTable(i)
			err := db.Table(table).
				Where("confirm_at >= ?", dateFrom.Format(constant.FormatTimeString)).
				Where("confirm_at < ?", dateTo.Format(constant.FormatTimeString)).
				FindInBatches(&bills, 2000, fn).Error
			if err != nil {
				l.WithError(err).Error("get bills failed")
				return
			}

			insertData := make([]model.DailyBillStatement, 0)

			for _, userStatements := range box {
				for _, userStatement := range userStatements {
					insertData = append(insertData, userStatement)
				}
			}

			if len(insertData) != 0 {
				err = db.Table(model.TableNameDailyBillStatement).Create(&insertData).Error
				if err != nil {
					log.WithError(err).Error("Create failed")
					return
				}
			}
		}

		l.Info("cronCalculateDailyBillStatement run succeed")
	}
}

func (svc *BCService) cronAutoFsCharge(ctx context.Context) {
	var timer *time.Ticker

	if conf.GetGlobalGsConfig().App.DebugApi {
		timer = time.NewTicker(time.Minute * 10)
	} else {
		now := time.Now()
		next := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 10, 0, 0, now.Location()) // 每天00:30计费
		t := time.NewTicker(next.Sub(now))

		select {
		case <-t.C:
			now = time.Now()
			timer = time.NewTicker(constant.CronAutoFsChargeInterval)
			t.Stop()
			svc.autoFsCharge()
			break
		}
	}

	for {
		select {
		case <-ctx.Done():
			return
		case <-timer.C:
			svc.autoFsCharge()
		}
	}
}

func (svc *BCService) autoFsCharge() {
	log := svc.log.WithField("section", "autoFsCharge")

	now := time.Now()                      // 当前时间
	settleDate := now.Add(-12 * time.Hour) // 前一天时间,主要为了获取日期,时间不重要
	settleTimeForBill := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	if conf.GetGlobalGsConfig().App.DebugApi {
		settleTimeForBill = now
	}

	reginList, err := svc.region.GetRegionList()
	if err != nil {
		log.ErrorE(err, "get region list failed")
		return
	}

	for _, region := range reginList {
		l := log.WithField("region", region.Sign)
		if !region.IsAutoFsAvailable {
			continue
		}

		autoFsData, err := svc.region.GetFileStorageForCharge(region.Sign, constant.AutoFS, settleDate)
		if err != nil {
			l.ErrorE(err, "get AutoFsGetListForCharge from core failed")
			continue
		}

		uidOrderMap := map[int]string{}
		for _, v := range autoFsData.List {
			if v.OrderUUID == "" {
				continue
			}
			uidOrderMap[v.UID] = v.OrderUUID
		}

		uidMaxUsageMap := map[int]int64{}
		uidCostMap := map[int]int64{}
		uidList := []int{}
		for uidStr, usageStr := range autoFsData.MaxUsageMap {
			uid, _ := strconv.Atoi(uidStr)
			usage, _ := strconv.ParseInt(usageStr, 10, 64)

			if _, ok := uidOrderMap[uid]; !ok {
				continue
			}
			cost := constant.ADFSCost(usage)
			if cost <= 0 {
				continue
			}

			uidMaxUsageMap[uid] = usage
			uidCostMap[uid] = cost
			uidList = append(uidList, uid)
		}

		uidPhoneMap, err := svc.user.CacheIDToPhoneMGet(uidList)
		if err != nil {
			svc.log.ErrorE(err, "autoFsCharge CacheIDToPhoneMGet failed")
			continue
		}

		for uid, cost := range uidCostMap {
			detail := &model.BillDetail{
				ChargeFrom:          settleTimeForBill.Add(-time.Hour * 24).Format(constant.FormatTimeString),
				ChargeTo:            settleTimeForBill.Format(constant.FormatTimeString),
				FileStorageCostSize: uidMaxUsageMap[uid] - constant.ADFSFreeSize,
				RegionSign:          region.Sign,
				DateCenter:          region.DataCenter,
				RegionName:          region.Name,
			}
			bill := &model.Bill{
				UID:           uid,
				UserPhone:     uidPhoneMap[uid],
				OrderUUID:     uidOrderMap[uid],
				Type:          constant.BillTypeCharge,
				SubType:       constant.BillSubTypeAutoFs,
				ChargeType:    constant.ChargeTypePayg,
				Asset:         cost,
				CreatedAt:     settleTimeForBill,
				UpdatedAt:     settleTimeForBill,
				ConfirmAt:     &settleTimeForBill,
				DetailsEntity: detail,
			}

			params := model.AfterUpdateWalletParams{
				Bill:             bill,
				AfterOperateType: constant.UpdateWalletSkip,
				AfterHook:        nil,
			}
			_, _, err = svc.createBillAndUpdateWallet(&params)
			if err != nil {
				log.WithField("uid", uid).ErrorE(err, "createBillAndUpdateWallet failed.")
				continue
			}

			// 如果余额支付的数为0（全部用代金券支付的）就不生成日结账单了
			if params.Bill.PayByBalance != 0 {
				var dailyBill = []*model.CreateOrUpdateDailyBillParams{
					&model.CreateOrUpdateDailyBillParams{
						UID:         uid,
						OrderUUID:   uidOrderMap[uid],
						ProductUUID: uidOrderMap[uid],
						ChargeType:  constant.ChargeTypePayg,
						PayAmount:   cost,
						BillType:    constant.BillTypeCharge,
						ProductType: constant.ProductTypeFileStorage,
						PayAt:       settleTimeForBill,
						Detail:      detail,
					},
				}

				err = svc.createDailyBill(dailyBill)
				if err != nil {
					log.WithField("uid", uid).WithField("order_uuid", uidOrderMap[uid]).ErrorE(err, "createDailyBill failed.")
					continue
				}
			}
		}

	}

}

func (svc *BCService) cmbCronSyncBankTransfer(ctx context.Context) {
	l := svc.log.WithField("section", "cmbCronSyncBankTransfer")
	now := time.Now()
	beginDate := now.Format("********")
	endDate := now.AddDate(1, 0, 0).Format("********")
	ti := time.NewTicker(time.Second * 5)
	beginTime, _ := time.ParseInLocation("2006-01-02 15:04:05", "2023-12-21 19:00:00", time.Local)

	req := &payment.CmbTradeListReq{
		TRANSQUERYBYBREAKPOINT_X1: []payment.TRANSQUERYBYBREAKPOINT_X1{{
			CardNbr:             svc.cmb.CardNbr,
			BeginDate:           beginDate,
			EndDate:             endDate,
			TransactionSequence: "1",
			QueryAcctNbr:        "",
			LoanCode:            constant.LoanCode_C,
		}},
	}

	for {
		select {
		case <-ctx.Done():
			return

		case <-ti.C:
			res, err := svc.cmb.CmbTradeList(req)
			if err != nil {
				l.ErrorE(err, "cmb get trade list failed")
				continue
			}

			if res.TRANSQUERYBYBREAKPOINT_Y1 != nil && len(res.TRANSQUERYBYBREAKPOINT_Y1) != 0 {
				req.TRANSQUERYBYBREAKPOINT_X1[0].QueryAcctNbr = res.TRANSQUERYBYBREAKPOINT_Y1[0].AcctNbr
				req.TRANSQUERYBYBREAKPOINT_Y1 = res.TRANSQUERYBYBREAKPOINT_Y1
			}

			for _, v := range res.TRANSQUERYBYBREAKPOINT_Z2 {
				if v.LoanCode == constant.LoanCode_D {
					continue
				}
				if strings.Contains(v.CtpBankName, "中科视拓") ||
					strings.Contains(v.CtpAcctName, "视拓云") {
					continue
				}
				now := time.Now()

				status := constant.BTUnconfirmed
				result := constant.BTConfirmedNone
				phone := ""
				confirmedUID := 0
				waitRecharge := false
				var confirmedAt *time.Time

				// virtualNbr 记账子单元自动认款
				v.VirtualNbr = strings.TrimSpace(v.VirtualNbr)
				if v.VirtualNbr != "" {
					bts, errBts := svc.BTSubunitGet(v.VirtualNbr, 0)
					if errBts != nil {
						if errors.Is(err, gorm.ErrRecordNotFound) {
							svc.log.WithField("v.VirtualNbr", v.VirtualNbr).WithField("SerialNum", v.TransSequenceIdn).
								Warn("cmb v.VirtualNbr valid, but not exist in server.")
						}
					}

					confirmedUID = bts.UID
					if bts.UID != 0 {
						phone, err = svc.user.CacheIDToPhoneGet(bts.UID)
						if err != nil {
							l.WarnE(err, "VirtualNbr get user phone by uid [%d] failed", bts.UID)
						}

						err = svc.btSubunitLastTransferUpdate(bts.Subunit, bts.UID)
						if err != nil {
							l.WarnE(err, "btSubunitLastTransferUpdate failed, subunit:%s, uid:%d", bts.Subunit, bts.UID)
						}

						status = constant.BTConfirmedAutoSubunit
						result = constant.BTConfirmedRecharge
						confirmedAt = &now
						waitRecharge = true
					}
				}

				// 识别码自动认款, 只要有子单元, 就不走自动认款
				remittanceCode, uid := libs.ParseADLRemittanceCode(v.RemarkTextClt)
				if v.VirtualNbr == "" && remittanceCode != "" && uid != 0 && v.GetTransTimeForADL().After(beginTime) {
					confirmedUID = uid
					status = constant.BTConfirmedAuto
					result = constant.BTConfirmedRecharge
					confirmedAt = &now
					waitRecharge = true
					phone, err = svc.user.CacheIDToPhoneGet(uid)
					if err != nil {
						l.WarnE(err, "remittanceCode get user phone by uid [%d] failed", uid)
					}
				}

				bt := &model.BankTransfer{
					SerialNum:      v.TransSequenceIdn,
					AccountName:    v.CtpAcctName,
					AccountNum:     v.CtpAcctNbr,
					Amount:         v.GetTransAmountForADL(),
					Subunit:        v.VirtualNbr,
					RemittanceCode: remittanceCode,
					RemarkText:     v.RemarkTextClt,
					TransTime:      v.GetTransTimeForADL(),
					Status:         status,
					ConfirmResult:  result,
					ConfirmedAt:    confirmedAt,
					ConfirmedPhone: phone,
					ConfirmedUID:   confirmedUID,
					WaitRecharge:   waitRecharge,
				}

				err = bt.BTCreate(nil)
				if err != nil {
					if !strings.Contains(err.Error(), "Error 1062 (23000)") { // 唯一索引重复
						l.WithField("new_bt", bt).ErrorE(err, "bank transfer create record failed")
					}
					continue
				}

				if result == constant.BTConfirmedRecharge {
					err = svc.rechargeForBankTransfer(bt, false, map[string]interface{}{"wait_recharge": false})
					if err != nil {
						l.ErrorE(err, "bt created, but recharge user wallet [%d] failed", uid)
					}
				}

			}

			//next := time.Second
			//if len(res.TRANSQUERYBYBREAKPOINT_Z1) == 0 || res.TRANSQUERYBYBREAKPOINT_Z1[0].CtnFlag == constant.CtnFlag_N {
			//	next = time.Second * 5
			//}
			//ti.Reset(next)
		}
	}
}

// 定时查询
func (svc *BCService) cronRefundQuery(ctx context.Context) {
	tick := time.Second * 10
	ticker := time.NewTicker(tick)
	svc.refundQuery()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			svc.refundQuery()
		}
	}
}

func (svc *BCService) refundQuery() {
	// 获取process的退款列表
	refund := &model.RefundRecord{}
	refundList, err := refund.RRGetAll(db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"status":          constant.RefundProcessing,
			"is_delay_refund": false,
		},
	})
	if err != nil {
		svc.log.ErrorE(err, "get refund list failed")
	}

	for _, v := range refundList {
		if v.Pathway == constant.WeChatPay {
			_, err = svc.RefundWxPayQuery(v.RefundUUID)
			if err != nil {
				svc.log.ErrorE(err, "query wx pay refund failed")
			}

		} else if v.Pathway == constant.Alipay {
			_, err = svc.RefundAliPayQuery(v.RefundUUID)
			if err != nil {
				svc.log.ErrorE(err, "query ali pay refund failed")
			}
		}
	}
}

func (svc *BCService) cornRefundDelay(ctx context.Context) {
	svc.log.Info("cornRefundDelay start...")

	tick := time.Second * 20
	ticker := time.NewTicker(tick)
	svc.refundDelayQuery()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			svc.refundDelayQuery()
		}
	}
}

func (svc *BCService) refundDelayQuery() {
	refundList, err := svc.refundDelay.HGetAll()
	if err != nil {
		svc.log.WithError(err).Error("get order timeout hashtable failed")
		return
	}

	for refundUuid, refundInfo := range refundList {
		var refund model.RefundDelay
		err = json.Unmarshal([]byte(refundInfo), &refund)
		if err != nil {
			svc.log.WithError(err).Error("unmarshall refundInfo  failed")
			continue
		}

		if time.Now().Before(refund.RefundTime) {
			continue
		}

		svc.log.WithField("refund", refund).Info("get refund")

		refundRecord, err := svc.GetRefundByUuid(refundUuid)
		if err != nil {
			svc.log.ErrorE(err, "get refundRecord failed")
			continue
		}

		if refundRecord.Status != constant.RefundStatus(constant.Processing) {
			err = svc.refundDelay.HDel(refundUuid)
			if err != nil {
				svc.log.WithField("refundUuid", refundUuid).Error("redis HDel refund failed")
				continue
			}
			continue
		}

		refundableInfo, err := svc.GetRefundableInfo(refund.BillUuid)
		if err != nil {
			svc.log.ErrorE(err, "get refundable info failed")
			continue
		}

		if refundableInfo.Pathway == constant.WeChatPay || refundableInfo.Pathway == constant.WechatAppletPay {
			err = svc.wxPay.Refund(context.Background(), &payment.WxRefundParams{
				BillUUID:       refund.BillUuid,
				RefundBillUUID: refundUuid,
				Total:          refundableInfo.Assets,
				Refund:         refund.Assets,
			})

			if err != nil {
				svc.log.WithError(err).WithFields(map[string]interface{}{
					"BillUUID":       refund.BillUuid,
					"RefundBillUUID": refundUuid,
					"Total":          refundableInfo.Assets,
					"Refund":         refund.Assets,
				}).Error("wxRefundCreate: create wxpay refund failed")

				err1 := refundRecord.RRUpdate(nil, &db_helper.QueryFilters{
					EqualFilters: map[string]interface{}{
						"refund_uuid": refundUuid,
					},
				}, map[string]interface{}{
					"reason":             err.Error(),
					"is_pass_refund_api": true,
				})
				if err1 != nil {
					svc.log.Error("update refund request fail, %s", err1.Error())
					continue
				}

				continue
			}

			// 针对微信返回报错：FREQUENCY_LIMITED 请求频率过高，请稍后重试
			time.Sleep(1 * time.Second)
		} else if refundableInfo.Pathway == constant.Alipay {
			err = svc.aliPay.Refund(refundableInfo.Note.AliAppID, &payment.WxRefundParams{
				BillUUID:       refund.BillUuid,
				RefundBillUUID: refundUuid,
				Total:          refundableInfo.Assets,
				Refund:         refund.Assets,
			})
			if err != nil {
				svc.log.WithError(err).WithFields(map[string]interface{}{
					"BillUUID":       refund.BillUuid,
					"RefundBillUUID": refundUuid,
					"Total":          refundableInfo.Assets,
					"Refund":         refund.Assets,
				}).Error("aliPayRefundCreate: create aliPay refund failed")

				err1 := refundRecord.RRUpdate(nil, &db_helper.QueryFilters{
					EqualFilters: map[string]interface{}{
						"refund_uuid": refundUuid,
					},
				}, map[string]interface{}{
					"reason":             err.Error(),
					"is_pass_refund_api": true,
				})
				if err1 != nil {
					svc.log.Error("update refund request fail, %s", err1.Error())
					continue
				}
				continue
			}
		}

		err = svc.refundDelay.HDel(refundUuid)
		if err != nil {
			svc.log.WithField("refundUuid", refundUuid).Error("redis HDel refund failed")
			continue
		}
		err = refundRecord.RRUpdate(nil, &db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"refund_uuid": refundUuid,
			},
		}, map[string]interface{}{
			"is_pass_refund_api": true,
		})

	}
}

// 主动查询更新延迟退款状态
func (svc *BCService) cronRefundDelayQuery(ctx context.Context) {
	tick := time.Second * 30
	ticker := time.NewTicker(tick)
	svc.refundDelayActiveQuery()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			svc.refundDelayActiveQuery()
		}
	}
}

func (svc *BCService) refundDelayActiveQuery() {
	refund := &model.RefundRecord{}
	refundList, err := refund.RRGetAll(db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"status":             constant.RefundProcessing,
			"is_delay_refund":    true,
			"is_pass_refund_api": true,
		},
		CompareFilters: []db_helper.Compare{{Key: "refund_time", Sign: db_helper.SmallerThan, CompareValue: time.Now().Add(-1 * time.Minute)}},
	})
	if err != nil {
		svc.log.ErrorE(err, "get refund list failed")
	}

	for _, v := range refundList {
		if v.Pathway == constant.WeChatPay {
			_, err = svc.RefundWxPayQuery(v.RefundUUID)
			if err != nil {
				svc.log.ErrorE(err, "query wx pay refund delay failed")
			}

		} else if v.Pathway == constant.Alipay {
			_, err = svc.RefundAliPayQuery(v.RefundUUID)
			if err != nil {
				svc.log.ErrorE(err, "query ali pay refund delay failed")
			}
		}
	}
}
