package service

import (
	"server/pkg/logger"
	"server/pkg/module_definition"
	"server/pkg/notify"
	"server/plugin/kv_plugin"
	"server/plugin/payment"
	"server/plugin/queue"
	redis "server/plugin/redis_plugin"
	"sync"
)

const ModuleName = "billing_center_service"

type BCService struct {
	log                       *logger.Logger
	q                         *queue.Q
	mutex                     *redis.MutexRedis
	countdown                 *redis.OrderTimeoutPlugin
	billWarning               *redis.BillWarningPlugin
	wxPay                     *payment.WxPayPlugin
	gpuStockInterference      module_definition.GpuStockInterface
	bcRecharge                *redis.BCRechargeQuery
	aliRechargeTimeout        *redis.AliRechargeTimeoutPlugin
	aliPay                    *payment.AliPayPlugin
	cmb                       *payment.CmbBank
	dds                       module_definition.DataDiskStockInterface
	cache                     *redis.BCCache
	refundDelay               *redis.RefundDelay
	user                      module_definition.UserInterface
	region                    module_definition.RegionInterface
	kv                        *kv_plugin.KVPlugin
	privateImage              module_definition.PrivateImageInterface
	instance                  module_definition.InstanceInterface
	t                         redis.SyncTimer
	smsNotifyChannel          notify.Channel
	bcServer                  module_definition.BCServerInterface
	notify                    module_definition.NotifyInterface
	smsCount                  *redis.SMSCountCachePlugin
	balanceNegativeLocks      map[int]*sync.Mutex
	balanceNegativeLocksMutex *sync.Mutex
	machine                   module_definition.MachineInference
	gpuType                   module_definition.GpuTypeInference
}

func NewBCServiceProvider(
	self *BCService,
	mutex *redis.MutexRedis,
	countdown *redis.OrderTimeoutPlugin,
	billWarning *redis.BillWarningPlugin,
	q *queue.Q,
	wxPay *payment.WxPayPlugin,
	gpuStockInterference module_definition.GpuStockInterface,
	bcRecharge *redis.BCRechargeQuery,
	aliRecharge *redis.AliRechargeTimeoutPlugin,
	aliPay *payment.AliPayPlugin,
	dds module_definition.DataDiskStockInterface,
	cache *redis.BCCache,
	refundDelay *redis.RefundDelay,
	user module_definition.UserInterface,
	region module_definition.RegionInterface,
	kv *kv_plugin.KVPlugin,
	privateImage module_definition.PrivateImageInterface,
	instance module_definition.InstanceInterface,
	timer redis.SyncTimer,
	cmb *payment.CmbBank,
	smsNotifyChannel notify.Channel,
	bcServer module_definition.BCServerInterface,
	notify module_definition.NotifyInterface,
	smsCount *redis.SMSCountCachePlugin,
	machine module_definition.MachineInference,
	gpuType module_definition.GpuTypeInference,
) {
	self.log = logger.NewLogger(ModuleName)
	self.q = q.New()
	self.mutex = mutex
	self.countdown = countdown
	self.billWarning = billWarning
	self.wxPay = wxPay
	self.gpuStockInterference = gpuStockInterference
	self.bcRecharge = bcRecharge
	self.aliRechargeTimeout = aliRecharge
	self.aliPay = aliPay
	self.dds = dds
	self.cache = cache
	self.refundDelay = refundDelay
	self.user = user
	self.region = region
	self.kv = kv
	self.privateImage = privateImage
	self.instance = instance
	self.t = timer
	self.cmb = cmb
	self.smsNotifyChannel = smsNotifyChannel
	self.bcServer = bcServer
	self.notify = notify
	self.smsCount = smsCount
	self.balanceNegativeLocks = make(map[int]*sync.Mutex)
	self.balanceNegativeLocksMutex = &sync.Mutex{}
	self.machine = machine
	self.gpuType = gpuType
}
