package storage_agent_server

import (
	"github.com/gin-gonic/gin"
	storageConstant "server/pkg-storage-agent/storage_agent_constant" // import for swagger
	_ "server/pkg/db_helper"                                          // import for swagger
	_ "server/pkg/http"                                               // import for swagger
)

// GetUploadProgressController
// ShowAccount godoc
// @Summary get progress
// @Description author: ytb
// @Tags storage_agent
// @Accept  mpfd
// @Produce  json
// @Param req query storageConstant.GetUploadProgressRequest true "请求体"
// @Success 200 {object} http.CommonResponse{data=float64} "Success"
// @Router /api/v1/net_disk/file/progress/:file_uuid [get]
func GetUploadProgressController(c *gin.Context) {
	var req storageConstant.GetUploadProgressRequest
	_ = req
}

// UploadFileController
// ShowAccount godoc
// @Summary upload file
// @Description author: ytb
// @Tags storage_agent
// @Accept  json
// @Produce  json
// @Param req body storageConstant.UploadRequestParams true "请求体"
// @Success 200 {object} http.CommonResponse "Success"
// @Router /api/v1/net_disk/file [post]
func UploadFileController(c *gin.Context) {
	var req storageConstant.UploadRequestParams
	_ = req
}

// BeforeUploadFileController
// ShowAccount godoc
// @Summary get offset, 断点续传用
// @Description author: ytb
// @Tags storage_agent
// @Accept  mpfd
// @Produce  json
// @Param req query storageConstant.GetUploadProgressRequest true "请求体"
// @Success 200 {object} http.CommonResponse{data=float64} "Success"
// @Router /api/v1/net_disk/file/before_upload [get]
func BeforeUploadFileController(c *gin.Context) {
	var req storageConstant.GetUploadProgressRequest
	_ = req
}

// RemoveFileController
// ShowAccount godoc
// @Summary remove file
// @Description author: ytb
// @Tags storage_agent
// @Accept  json
// @Produce  json
// @Param req body storageConstant.RemoveFileRequest true "请求体"
// @Success 200 {object} http.CommonResponse "Success"
// @Router /api/v1/net_disk/file [delete]
func RemoveFileController(c *gin.Context) {
	var req storageConstant.RemoveFileRequest
	_ = req
}

// RenameFileController
// ShowAccount godoc
// @Summary rename file
// @Description author: ytb
// @Tags storage_agent
// @Accept  json
// @Produce  json
// @Param req body storageConstant.RenameFileRequest true "请求体"
// @Success 200 {object} http.CommonResponse "Success"
// @Router /api/v1/net_disk/file/name [put]
func RenameFileController(c *gin.Context) {
	var req storageConstant.RenameFileRequest
	_ = req
}

// GetFileTokenController
// ShowAccount godoc
// @Summary 下载文件 step 1: token
// @Description author: ytb
// @Tags storage_agent
// @Accept  json
// @Produce  json
// @Param req body storageConstant.GetDownloadFileTokenRequest true "请求体"
// @Success 200 {object} http.CommonResponse{data=string} "Success"
// @Router /api/v1/net_disk/file/download_token [post]
func GetFileTokenController(c *gin.Context) {
	var req storageConstant.RenameFileRequest
	_ = req
}

// GetPagedFilesController
// ShowAccount godoc
// @Summary 获取目录中的文件列表
// @Description author: ytb
// @Tags storage_agent
// @Accept  mpfd
// @Produce  json
// @Param req query storageConstant.GetPagedFilesRequest true "请求体"
// @Success 200 {object} http.CommonResponse{data=db_helper.PagedData{list=[]storageConstant.NFSListFileInfo}} "Success"
// @Router /api/v1/net_disk/dir [get]
func GetPagedFilesController(c *gin.Context) {
	var req storageConstant.GetPagedFilesRequest
	_ = req
}

// MakeDirController
// ShowAccount godoc
// @Summary make dir
// @Description author: ytb
// @Tags storage_agent
// @Accept  json
// @Produce  json
// @Param req body storageConstant.MakeDirRequest true "请求体"
// @Success 200 {object} http.CommonResponse "Success"
// @Router /api/v1/net_disk/dir [post]
func MakeDirController(c *gin.Context) {
	var req storageConstant.MakeDirRequest
	_ = req
}

// RemoveDirController
// ShowAccount godoc
// @Summary remove dir
// @Description author: ytb
// @Tags storage_agent
// @Accept  json
// @Produce  json
// @Param req body storageConstant.RemoveDirRequest true "请求体"
// @Success 200 {object} http.CommonResponse "Success"
// @Router /api/v1/net_disk/dir [delete]
func RemoveDirController(c *gin.Context) {
	var req storageConstant.RemoveDirRequest
	_ = req
}

// RenameDirController
// ShowAccount godoc
// @Summary rename dir
// @Description author: ytb
// @Tags storage_agent
// @Accept  json
// @Produce  json
// @Param req body storageConstant.RenameDirRequest true "请求体"
// @Success 200 {object} http.CommonResponse "Success"
// @Router /api/v1/net_disk/dir [put]
func RenameDirController(c *gin.Context) {
	var req storageConstant.RenameDirRequest
	_ = req
}

// DownloadFileController
// ShowAccount godoc
// @Summary 下载文件 step 2: download
// @Description author: ytb
// @Tags storage_agent
// @Accept  mpfd
// @Produce  json
// @Param req query storageConstant.DownloadFileRequest true "请求体"
// @Success 200 {object} http.CommonResponse "Success"
// @Router /api/v1/download/file/:token [get]
func DownloadFileController(c *gin.Context) {
	var req storageConstant.RenameFileRequest
	_ = req
}
