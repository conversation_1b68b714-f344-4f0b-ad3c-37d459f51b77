package middleware

import (
	"bytes"
	"io"
	"math/rand"
	workOrderService "server/pkg-work-order/work_order/service"
	"time"

	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/http"
	"server/pkg/logger"
	"server/pkg/middleware/claim"
	"server/pkg/user/model"
	"server/pkg/user/service"
	"server/plugin/redis_plugin"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
)

const ModuleName = "middleware"

type Middleware struct {
	UserPlugin        *redis_plugin.UserPlugin
	logger            *logger.Logger
	userService       *service.UserService
	tokenBucketPlugin *redis_plugin.TokenBucket
	workOrderService  *workOrderService.WorkOrderService
}

func NewMiddlewareProvider(
	userPlugin *redis_plugin.UserPlugin,
	userService *service.UserService,
	tokenBucketPlugin *redis_plugin.TokenBucket,
	workOrderService *workOrderService.WorkOrderService,
) *Middleware {
	return &Middleware{
		userPlugin,
		logger.NewLogger(ModuleName),
		userService,
		tokenBucketPlugin,
		workOrderService,
	}
}

func (mw *Middleware) LoginRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. 先寻找 token
		token, err := claim.TryToFindToken(c)
		if err != nil { // 此时 token == ""
			mw.logger.WithError(err).Info("Cannot find token in this context.")
			http.AbortAndSendAuthErr(c, businesserror.ErrTokenMissing)
			return
		}

		// 2. 解析token获取用户信息
		claims, err := constant.ParseToken(token, &claim.UserClaims{})
		if err != nil {
			if ve, ok := err.(*jwt.ValidationError); ok {
				if ve.Errors&(jwt.ValidationErrorMalformed|jwt.ValidationErrorExpired|jwt.ValidationErrorNotValidYet) != 0 {
					mw.logger.WarnE(err, "Parse token failed.")
				} else {
					mw.logger.WithError(err).Error("Parse token failed.")
				}
			} else {
				mw.logger.WithError(err).Error("Parse token failed.")
			}

			http.AbortAndSendAuthErr(c, businesserror.ErrParseTokenFailed)
			return
		}
		userClaims, ok := claims.(*claim.UserClaims)
		if !ok {
			mw.logger.WithError(err).Error("Parse token failed.")
			http.AbortAndSendAuthErr(c, businesserror.ErrParseTokenFailed)
			return
		}

		if !mw.tokenBucketPlugin.UIDBucket(userClaims.UID, "") {
			http.AbortAndSendErr(c, businesserror.ErrAccessSpeedIsLimited.New().Format("用户"))
			return
		}

		if userClaims.SubName != "" {
			http.AbortAndSendErr(c, businesserror.ErrResourceAccessAuthFailed)
			return
		}

		// 3: 检查解析出来的token中的用户是否存在
		user, err := mw.userService.FindByUserId(userClaims.UID)
		if err != nil {
			http.AbortAndSendAuthErr(c, nil)
			return
		}
		if user.UUID != userClaims.UUID {
			mw.logger.WithFields(logger.Fields{
				"uuid": userClaims.UUID,
				"id":   userClaims.UID,
			}).Info("This user is not exist in db. Please re-login.")
			http.AbortAndSendAuthErr(c, nil)
			return
		}
		if user.Status == model.Disable {
			mw.logger.WithFields(logger.Fields{
				"uuid": userClaims.UUID,
				"id":   userClaims.UID,
			}).Info("This user is disabled by admin. Please re-login.")
			http.AbortAndSendAuthErr(c, nil)
			return
		}

		// 4. 检查用户是否在线(此时uuid已经过验证,即用户在数据库中存在).
		isOnline, err := mw.UserPlugin.CheckUserOnline(userClaims.UUID)
		if err != nil {
			mw.logger.WithError(err).Error("Check user online in redis failed.")
			http.AbortAndSendAuthErr(c, businesserror.ErrInternalError)
			return
		}
		if !isOnline {
			mw.logger.WithField("uuid", userClaims.UID).Info("This user is offline. Please re-login.")
			http.AbortAndSendAuthErr(c, businesserror.ErrAuthorizeFailed)
			return
		}

		if userClaims.UPK != user.UpdatePasswordKey {
			mw.logger.WithField("uuid", userClaims.UID).Info("User updated password before. Please re-login.")
			http.AbortAndSendAuthErr(c, businesserror.ErrAuthorizeFailed)
			return
		}

		c.Set(constant.GinKeyUserInfo, userClaims)
		c.Set(constant.GinKeyUserID, userClaims.UID)
		c.Next()
	}
}

func (mw *Middleware) SubUserLoginRequired(needRoles ...constant.RoleType) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. 先寻找 token
		token, err := claim.TryToFindToken(c)
		if err != nil { // 此时 token == ""
			mw.logger.WithError(err).Info("Cannot find token in this context.")
			http.AbortAndSendAuthErr(c, businesserror.ErrTokenMissing)
			return
		}

		// 2. 解析token获取用户信息
		claims, err := constant.ParseToken(token, &claim.UserClaims{})
		if err != nil {
			if ve, ok := err.(*jwt.ValidationError); ok {
				if ve.Errors&(jwt.ValidationErrorMalformed|jwt.ValidationErrorExpired|jwt.ValidationErrorNotValidYet) != 0 {
					mw.logger.WarnE(err, "Parse token failed.")
				} else {
					mw.logger.WithError(err).Error("Parse token failed.")
				}
			} else {
				mw.logger.WithError(err).Error("Parse token failed.")
			}

			http.AbortAndSendAuthErr(c, businesserror.ErrParseTokenFailed)
			return
		}
		userClaims, ok := claims.(*claim.UserClaims)
		if !ok {
			mw.logger.WithError(err).Error("Parse token failed.")
			http.AbortAndSendAuthErr(c, businesserror.ErrParseTokenFailed)
			return
		}

		if !mw.tokenBucketPlugin.UIDBucket(userClaims.UID, userClaims.SubName) {
			http.AbortAndSendErr(c, businesserror.ErrAccessSpeedIsLimited.New().Format("用户"))
			return
		}

		// 3: 检查解析出来的token中的用户是否存在
		su, err := mw.userService.SubUserGetWithUser(userClaims.SubName)
		if err != nil {
			http.AbortAndSendAuthErr(c, nil)
			return
		}
		if su.UID != userClaims.UID {
			mw.logger.WithFields(logger.Fields{
				"UID":     userClaims.UID,
				"subName": userClaims.SubName,
			}).Info("This user is not exist in db. Please re-login.")
			http.AbortAndSendAuthErr(c, nil)
			return
		}
		if su.User.Status == model.Disable || su.User.SubUserNum == 0 {
			mw.logger.WithFields(logger.Fields{
				"UID":     userClaims.UID,
				"subName": userClaims.SubName,
			}).Info("This user is disabled by admin. Please re-login.")
			http.AbortAndSendAuthErr(c, nil)
			return
		}

		// 4. 检查用户是否在线(此时uuid已经过验证,即用户在数据库中存在).
		isOnline, err := mw.UserPlugin.CheckUserOnline(userClaims.SubName)
		if err != nil {
			mw.logger.WithError(err).Error("Check user online in redis failed.")
			http.AbortAndSendAuthErr(c, businesserror.ErrInternalError)
			return
		}
		if !isOnline {
			mw.logger.WithField("uuid", userClaims.UID).Info("This user is offline. Please re-login.")
			http.AbortAndSendAuthErr(c, businesserror.ErrAuthorizeFailed)
			return
		}

		// 校验权限
		pass := false
		for _, needRole := range needRoles {
			switch needRole {
			case constant.RoleInstance:
				if su.Roles.Instance != constant.SubUserNoPermission {
					pass = true
					break
				}
			case constant.RoleDeployment:
				if su.Roles.Deployment != constant.SubUserNoPermission {
					pass = true
					break
				}
			case constant.RoleImage:
				if su.Roles.Image != constant.SubUserNoPermission {
					pass = true
					break
				}
			case constant.RoleFileStorage:
				if su.Roles.FileStorage != constant.SubUserNoPermission {
					pass = true
					break
				}
			case constant.RoleBillingCenter:
				if su.Roles.BillingCenter != constant.SubUserNoPermission {
					pass = true
					break
				}
			}
		}
		if len(needRoles) != 0 && !pass {
			http.AbortAndSendErr(c, businesserror.ErrResourceAccessAuthFailed)
			return
		}

		c.Set(constant.GinKeyUserInfo, userClaims)
		c.Set(constant.GinKeyUserID, userClaims.UID)
		c.Next()
	}
}

func (mw *Middleware) WOAuth(needRoles ...constant.WorkOrderUserRole) gin.HandlerFunc {
	return mw.WorkOrderTenantUserRoleRequired(needRoles...)
}

func (mw *Middleware) WorkOrderTenantUserRoleRequired(needRoles ...constant.WorkOrderUserRole) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. 先寻找 token
		token, err := claim.TryToFindToken(c)
		if err != nil { // 此时 token == ""
			mw.logger.WithError(err).Info("Cannot find token in this context.")
			http.AbortAndSendAuthErr(c, businesserror.ErrTokenMissing)
			return
		}

		// 2. 解析token获取用户信息
		claims, err := constant.ParseToken(token, &claim.TenantUserClaims{})
		if err != nil {
			if ve, ok := err.(*jwt.ValidationError); ok {
				if ve.Errors&(jwt.ValidationErrorMalformed|jwt.ValidationErrorExpired|jwt.ValidationErrorNotValidYet) != 0 {
					mw.logger.WarnE(err, "Parse token failed.")
				} else {
					mw.logger.WithError(err).Error("Parse token failed.")
				}
			} else {
				mw.logger.WithError(err).Error("Parse token failed.")
			}

			http.AbortAndSendAuthErr(c, businesserror.ErrParseTokenFailed)
			return
		}
		userClaims, ok := claims.(*claim.TenantUserClaims)
		if !ok {
			mw.logger.WithError(err).Error("Parse token failed.")
			http.AbortAndSendAuthErr(c, businesserror.ErrParseTokenFailed)
			return
		}

		if userClaims.StandardClaims.ExpiresAt == 0 {
			http.AbortAndSendAuthErr(c, businesserror.ErrParseTokenFailed)
			return
		}

		operateUser, err := mw.workOrderService.GetWorkOrderCreateUser(userClaims.UUID, userClaims.TenantId)
		if err != nil {
			http.AbortAndSendAuthErr(c, nil)
			return
		}

		pass := false
		for _, needRole := range needRoles { // 如果为空列表，则默认需要superAdmin权限
			switch needRole {
			case constant.Administrator:
				if operateUser.UserRole.IsAdmin() {
					pass = true
					break
				}
			case constant.ProductionStaff:
				if operateUser.UserRole.IsProductions() {
					pass = true
					break
				}
			case constant.AfterSales:
				if operateUser.UserRole.IsAfterSales() {
					pass = true
					break
				}
			case constant.Operation:
				if operateUser.UserRole.IsOperation() {
					pass = true
					break
				}
			case constant.CustomerService:
				// 只有autodl下的客服有该权限
				if operateUser.UserRole.IsCustomerService() && operateUser.TenantID == 1 {
					pass = true
					break
				}
			}
		}

		if !pass {
			mw.logger.Info("租户内用户角色权限不足！！！！")
			http.AbortAndSendErr(c, businesserror.ErrResourceAccessAuthFailed)
			return
		}
		c.Set(constant.GinKeyUserInfo, userClaims)
		c.Set(constant.GinKeyUserID, userClaims.UID)
		c.Next()
	}

}

func (mw *Middleware) ALR(nl bool, needRoles ...constant.BackstageRole) gin.HandlerFunc {
	// needRoles为空，默认需要权限superAdmin
	return mw.AdminLoginRequired(nl, needRoles...)
}
func (mw *Middleware) AdminLoginRequired(nl bool, needRoles ...constant.BackstageRole) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. 先寻找 token
		token, err := claim.TryToFindToken(c)
		if err != nil { // 此时 token == ""
			mw.logger.WithError(err).Info("Cannot find token in this context.")
			http.AbortAndSendAuthErr(c, businesserror.ErrTokenMissing)
			return
		}

		// 2. 解析token获取用户信息
		claims, err := constant.ParseToken(token, &claim.UserClaims{})
		if err != nil {
			if ve, ok := err.(*jwt.ValidationError); ok {
				if ve.Errors&(jwt.ValidationErrorMalformed|jwt.ValidationErrorExpired|jwt.ValidationErrorNotValidYet) != 0 {
					mw.logger.WarnE(err, "Parse token failed.")
				} else {
					mw.logger.WithError(err).Error("Parse token failed.")
				}
			} else {
				mw.logger.WithError(err).Error("Parse token failed.")
			}

			http.AbortAndSendAuthErr(c, businesserror.ErrParseTokenFailed)
			return
		}
		userClaims, ok := claims.(*claim.UserClaims)
		if !ok {
			mw.logger.WithError(err).Error("Parse token failed.")
			http.AbortAndSendAuthErr(c, businesserror.ErrParseTokenFailed)
			return
		}

		if userClaims.StandardClaims.ExpiresAt == 0 {
			http.AbortAndSendAuthErr(c, businesserror.ErrParseTokenFailed)
			return
		}

		// 3: 检查解析出来的token中的用户是否存在
		user, err := mw.userService.FindByUserId(userClaims.UID)
		if err != nil {
			http.AbortAndSendAuthErr(c, nil)
			return
		}

		if !user.IsAdmin {
			http.AbortAndSendAuthErr(c, nil)
			return
		}

		if user.UUID != userClaims.UUID {
			mw.logger.WithFields(logger.Fields{
				"uuid": userClaims.UUID,
				"id":   userClaims.UID,
			}).Info("This user is not exist in db. Please re-login.")
			http.AbortAndSendAuthErr(c, nil)
			return
		}

		// 4. 检查用户是否在线(此时uuid已经过验证,即用户在数据库中存在).
		isOnline, err := mw.UserPlugin.CheckUserOnline(userClaims.UUID)
		if err != nil {
			mw.logger.WithError(err).Error("Check user online in redis failed.")
			http.AbortAndSendAuthErr(c, businesserror.ErrInternalError)
			return
		}
		if !isOnline {
			mw.logger.WithField("uuid", userClaims.UID).Info("This user is offline. Please re-login.")
			http.AbortAndSendAuthErr(c, businesserror.ErrAuthorizeFailed)
			return
		}

		// 超管有所有权限
		if !user.BackstageRole.IsSuperAdmin() {
			// 校验权限
			pass := false
			for _, needRole := range needRoles { // 如果为空列表，则默认需要superAdmin权限
				switch needRole {
				case constant.Admin:
					if user.BackstageRole.IsAdmin() {
						pass = true
						break
					}
				case constant.Finance:
					if user.BackstageRole.IsFinance() {
						pass = true
						break
					}
				case constant.Operations:
					if user.BackstageRole.IsOperations() {
						pass = true
						break
					}
				case constant.TechnicalCustomerService:
					if user.BackstageRole.IsTechnicalCustomerService() {
						pass = true
						break
					}
				case constant.BusinessCustomerService:
					if user.BackstageRole.IsBusinessCustomerService() {
						pass = true
						break
					}
				}
			}

			if !pass {
				http.AbortAndSendErr(c, businesserror.ErrResourceAccessAuthFailed)
				return
			}
		}
		//任何人,操作后台接口,只要配置此类型,都记录操作
		if nl {
			body, err := io.ReadAll(c.Request.Body)
			if err != nil {
				mw.logger.WithFields(logger.Fields{
					"uid": userClaims.UID,
				}).WarnE(err, "get body err url:%v,method:%v", c.Request.URL.Path, c.Request.Method)
			} else {
				c.Request.Body = io.NopCloser(bytes.NewBuffer(body))
				var requestBody map[string]interface{}
				err = c.ShouldBind(&requestBody)
				if err != nil {
					mw.logger.WithFields(logger.Fields{
						"uid": userClaims.UID,
					}).WarnE(err, "ShouldBind err url:%v,method:%v body:%v", c.Request.URL.Path, c.Request.Method, string(body))
				} else {
					operateLog := model.AdminOperateLog{
						UID:           userClaims.UID,
						Method:        c.Request.Method,
						Url:           c.Request.URL.Path,
						PayloadEntity: requestBody,
					}
					err = operateLog.AdminOperateLogCreate(c)
					if err != nil {
						mw.logger.WithFields(logger.Fields{
							"uid": userClaims.UID,
						}).ErrorE(err, "insert customer_audit_log failed: url:%v,method:%v", c.Request.URL.Path, c.Request.Method)
					}
				}
			}
			// 将原始请求体复制回去，以便后续处理，避免后续解析不出来结果
			c.Request.Body = io.NopCloser(bytes.NewBuffer(body))
		}
		c.Set(constant.GinKeyUserInfo, userClaims)
		c.Set(constant.GinKeyUserID, userClaims.UID)
		c.Next()
	}
}

func (mw *Middleware) PersonalTokenCheck() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. 先寻找 token
		token, err := claim.TryToFindToken(c)
		if err != nil { // 此时 token == ""
			mw.logger.WithError(err).Info("Cannot find token in this context.")
			http.AbortAndSendAuthErr(c, businesserror.ErrTokenMissing)
			return
		}

		// 2. 解析token获取用户信息
		claims, err := constant.ParseToken(token, &claim.UserClaims{})
		if err != nil {
			if ve, ok := err.(*jwt.ValidationError); ok {
				if ve.Errors&(jwt.ValidationErrorMalformed|jwt.ValidationErrorExpired|jwt.ValidationErrorNotValidYet) != 0 {
					mw.logger.WarnE(err, "Parse token failed.")
				} else {
					mw.logger.WithError(err).Error("Parse token failed.")
				}
			} else {
				mw.logger.WithError(err).Error("Parse token failed.")
			}

			http.AbortAndSendAuthErr(c, businesserror.ErrParseTokenFailed)
			return
		}
		userClaims, ok := claims.(*claim.UserClaims)
		if !ok {
			mw.logger.WithError(err).Error("Parse token failed.")
			http.AbortAndSendAuthErr(c, businesserror.ErrParseTokenFailed)
			return
		}

		if !mw.tokenBucketPlugin.UIDBucket(userClaims.UID, "") {
			http.AbortAndSendErr(c, businesserror.ErrAccessSpeedIsLimited.New().Format("用户"))
			return
		}

		// 3: 检查解析出来的token中的用户是否存在
		user, err := mw.userService.FindByUserId(userClaims.UID)
		if err != nil {
			http.AbortAndSendAuthErr(c, nil)
			return
		}

		if user.UUID != userClaims.UUID {
			mw.logger.WithFields(logger.Fields{
				"uuid": userClaims.UUID,
				"id":   userClaims.UID,
			}).Info("This user is not exist in db. Please re-login.")
			http.AbortAndSendAuthErr(c, nil)
			return
		}

		valid := mw.userService.PersonalTokenValid(userClaims.UID, token)
		if !valid {
			http.AbortAndSendAuthErr(c, businesserror.ErrUserPersonalTokenInvalid)
			return
		}
		c.Set(constant.GinKeyUserInfo, userClaims)
		c.Set(constant.GinKeyUserID, userClaims.UID)
		c.Next()
	}
}

func (mw *Middleware) EnterpriseAuth() gin.HandlerFunc {
	return func(c *gin.Context) {

		rand.Seed(time.Now().Unix())
		s := rand.Int() % 500
		time.Sleep(time.Duration(s)*time.Millisecond + 100*time.Millisecond)

		u := http.GetUserInfo(c)
		um, err := mw.userService.GetUserMemberInfoByUid(u.UID)
		if err != nil {
			mw.logger.WithField("uid", u.UID).ErrorE(err, "get user member info failed")
			http.AbortAndSendAuthErr(c, businesserror.ErrResourceAccessAuthFailed)
			return
		}

		if !um.IsEnterprise() {
			http.AbortAndSendErr(c, businesserror.ErrResourceAccessAuthFailed)
			return
		}
		c.Next()
	}
}

func (mw *Middleware) IPAccessSpeedLimiter() gin.HandlerFunc {
	return func(c *gin.Context) {
		ip := c.ClientIP()
		if !mw.tokenBucketPlugin.IpBucket(ip) {
			http.AbortAndSendErr(c, businesserror.ErrAccessSpeedIsLimited.New().Format("IP"))
			return
		}
		c.Next()
	}
}

func (mw *Middleware) TenantLoginRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. 先寻找 token
		token, err := claim.TryToFindToken(c)
		if err != nil { // 此时 token == ""
			mw.logger.WithError(err).Info("Cannot find token in this context.")
			http.AbortAndSendAuthErr(c, businesserror.ErrTokenMissing)
			return
		}

		// 2. 解析token获取用户信息
		claims, err := constant.ParseToken(token, &claim.TenantUserClaims{})
		if err != nil {
			if ve, ok := err.(*jwt.ValidationError); ok {
				if ve.Errors&(jwt.ValidationErrorMalformed|jwt.ValidationErrorExpired|jwt.ValidationErrorNotValidYet) != 0 {
					mw.logger.WarnE(err, "Parse token failed.")
				} else {
					mw.logger.WithError(err).Error("Parse token failed.")
				}
			} else {
				mw.logger.WithError(err).Error("Parse token failed.")
			}

			c.Abort()
			http.AbortAndSendAuthErr(c, businesserror.ErrParseTokenFailed)
			return
		}

		userClaims, ok := claims.(*claim.TenantUserClaims)
		if !ok {
			c.Abort()
			mw.logger.WithError(err).Error("Parse token failed.")
			http.AbortAndSendAuthErr(c, businesserror.ErrParseTokenFailed)
			return
		}

		// 3: 检查解析出来的token中的用户是否存在
		user, err := mw.userService.FindByUserId(userClaims.UID)
		if err != nil {
			http.AbortAndSendAuthErr(c, nil)
			return
		}
		if user.UUID != userClaims.UUID {
			mw.logger.WithFields(logger.Fields{
				"uuid": userClaims.UUID,
				"id":   userClaims.UID,
			}).Info("This user is not exist in db. Please re-login.")
			http.AbortAndSendAuthErr(c, nil)
			return
		}
		if user.Status == model.Disable {
			mw.logger.WithFields(logger.Fields{
				"uuid": userClaims.UUID,
				"id":   userClaims.UID,
			}).Info("This user is disabled by admin. Please re-login.")
			http.AbortAndSendAuthErr(c, nil)
			return
		}

		// 4. 检查用户是否在线(此时uuid已经过验证,即用户在数据库中存在).
		isOnline, err := mw.UserPlugin.CheckUserOnline(userClaims.UUID)
		if err != nil {
			mw.logger.WithError(err).Error("Check user online in redis failed.")
			http.AbortAndSendAuthErr(c, businesserror.ErrInternalError)
			return
		}
		if !isOnline {
			mw.logger.WithField("uuid", userClaims.UID).Info("This user is offline. Please re-login.")
			http.AbortAndSendAuthErr(c, businesserror.ErrAuthorizeFailed)
			return
		}

		// 5. 检查租户下用户是否存在
		//_, err = mw.tenantService.GetTenantUser(userClaims.TenantUuid, userClaims.UID)
		//if err != nil {
		//	http.AbortAndSendAuthErr(c, nil)
		//	return
		//}

		c.Set(constant.GinKeyUserInfo, userClaims)
		c.Set(constant.GinKeyUserID, userClaims.UID)
		c.Next()
	}
}
