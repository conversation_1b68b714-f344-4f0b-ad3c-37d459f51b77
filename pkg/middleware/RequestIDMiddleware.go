package middleware

import (
	"server/pkg/threadlocal"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func (mw *Middleware) RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. 优先从请求头获取RequestID，若不存在则生成UUID
		requestID := c.GetHeader(threadlocal.HttpHeaderXRequestIDKey)
		if requestID == "" {
			requestID = uuid.New().String()
		}

		// 2. 使用新的SetRequestID方法存储到ThreadLocal中
		threadlocal.SetRequestID(requestID)
		// 设置到请求头
		c.Request.Header.Set(threadlocal.HttpHeaderXRequestIDKey, requestID)

		// 3. 设置到响应头
		c.Writer.Header().Set(threadlocal.HttpHeaderXRequestIDKey, requestID)

		// 4. 传递到后续处理链
		c.Next()

		// 5. 请求结束后清理ThreadLocal（避免内存泄漏）
		defer threadlocal.ClearContext()
	}
}

func NewRequestIDMiddleware() gin.HandlerFunc {
	mw := &Middleware{}
	return mw.RequestIDMiddleware()
}
