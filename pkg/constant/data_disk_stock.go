package constant

type DataDiskChargeInfo struct {
	DailyPrice   int64 `json:"daily_price"`
	WeeklyPrice  int64 `json:"weekly_price"`
	MonthlyPrice int64 `json:"monthly_price"`
	YearlyPrice  int64 `json:"yearly_price"`
}

func (d *DataDiskChargeInfo) GetPrice(chargeType ChargeType) int64 {
	switch chargeType {
	// 数据盘扩容, 按天计费
	case ChargeTypeDaily:
		return d.DailyPrice
	case ChargeTypeWeekly:
		return d.WeeklyPrice
	case ChargeTypeMonthly:
		return d.MonthlyPrice
	case ChargeTypeYearly:
		return d.YearlyPrice
	}
	return 0
}
