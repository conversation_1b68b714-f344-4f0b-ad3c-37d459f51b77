package constant

import (
	"encoding/binary"
	"encoding/json"
	"fmt"
	"path/filepath"
	"server/pkg/db_helper"
	"strconv"
	"strings"
	"unsafe"
)

type AutoFsInitParams struct {
	UID             int            `json:"uid"`
	Prefix          string         `json:"prefix"`
	RegionSign      RegionSignType `json:"region_sign"`
	Capacity        int            `json:"capacity"`         // 单位:GB
	Inodes          int            `json:"inodes"`           // 单位:个
	ConcurrentLimit int            `json:"concurrent_limit"` // 单位:Mb, 用于分布式速率限制的key大小
}

func (a *AutoFsInitParams) String() string {
	as, _ := json.Marshal(a)
	return string(as)
}

type AutoFsInitResult struct {
	UID        int            `json:"uid"`
	IsSucceed  bool           `json:"is_succeed"`
	RegionSign RegionSignType `json:"region_sign"`
	Msg        string         `json:"msg"`
}

func (a *AutoFsInitResult) String() string {
	as, _ := json.Marshal(a)
	return string(as)
}

type AutoFsSyncUsage struct {
	RegionSign RegionSignType              `json:"region_sign"`
	UsageInfo  map[int]AutoFsSyncUsageData `json:"usage_info"`
}

type AutoFsSyncUsageData struct {
	Size  int64 `json:"size"`  //容量使用大小
	Inode int64 `json:"inode"` // inode使用数量
}

func (r *AutoFsSyncUsage) String() string {
	b, _ := json.Marshal(r)
	return string(b)
}

type AutoFsUserDetail struct {
	Capacity    int64 // 总容量大小，对应 setting key 中的 Capacity
	Inodes      int64 // 总 Inodes 数，对应 setting key 中的 Inodes
	UsedSpace   int64 // 使用的容量大小
	TotalInodes int64 // 使用的 Inodes 数
}

type FsType string

func (f FsType) IsNfs() bool {
	return f == NFS
}
func (f FsType) IsAdfs() bool {
	return f == ADFS
}
func (f FsType) IsAutoFs() bool {
	return f == AutoFS
}
func (f FsType) String() string {
	return string(f)
}

const (
	NFS              FsType = "nfs"
	ADFS             FsType = "adfs"
	AutoFS           FsType = "autofs"
	AutoFsBufferSize        = 300
	AutoFsCacheSize         = 512

	// 修改时需要与上面同步
	AutoFsFreeSizeInGB      = 20                                            // 免费大小
	AutoFsDefaultSizeInGB   = 200                                           // 默认容量
	AutoFsMaxSizeInGB       = 100 * 1024                                    // 最大容量
	AutoFsFreeSizeInByte    = AutoFsFreeSizeInGB * 1024 * 1024 * 1024       // 免费大小
	AutoFsDefaultSizeInByte = AutoFsDefaultSizeInGB * 1024 * 1024 * 1024    // 默认容量
	AutoFsMaxSizeInByte     = AutoFsMaxSizeInGB * 1024 * 1024 * 1024 * 1024 // 最大容量
	AutoFsDefaultInode      = 200000                                        // 默认inode
	AutoFsMaxInode          = 10000000                                      // 最大inode
	AutoFsDefaultRateLimit  = 3277                                          // 默认速率限制 20Mb
)

func GetAutoFsCommand(uid, bufferSize, cacheSize, concurrentLimit, maxUpload int, metadataEngineAddr string, noBGJob bool, subPath string) (string, []string) {
	/*
		fmt: bash /root/mount.sh [--subdir {subPath}] {authKey} {bufferSize} {concurrentLimit} {cacheSize} {metadataAddr} {readonly}
		e.g: bash /root/mount.sh [--subdir /] autodl-commonauth-key-audb 300 1024 512 redis://192.168.64.47:6370,192.168.64.48:6370/16 --read-only=false
	*/

	if bufferSize == 0 {
		bufferSize = AutoFsBufferSize
	}
	if cacheSize == 0 {
		cacheSize = AutoFsCacheSize
	}
	if maxUpload == 0 {
		maxUpload = 1
	}
	if bufferSize == 0 {
		bufferSize = AutoFsBufferSize
	}
	if cacheSize == 0 {
		cacheSize = AutoFsCacheSize
	}
	if subPath != "" {
		subPath = filepath.Join(subPath)
	}

	metadataAddr := fmt.Sprintf("redis://%s/%d", metadataEngineAddr, uid)
	authKey := AutoFsOptionCheckSum(metadataAddr, uint64(bufferSize), uint64(cacheSize), false)

	env := []string{
		"autofs_authKey=" + authKey,
		"autofs_bufferSize=" + strconv.Itoa(bufferSize),
		"autofs_concurrentLimit=" + strconv.Itoa(concurrentLimit),
		"autofs_cacheSize=" + strconv.Itoa(cacheSize),
		"autofs_maxUpload=" + strconv.Itoa(maxUpload),
		"autofs_metadataAddr=" + metadataAddr,
		//"autofs_readonly=--read-only",
	}

	if subPath != "" {
		env = append(env, "autofs_subDir="+subPath)
	}

	if noBGJob {
		env = append(env, "autofs_bgjob=--no-bgjob")
		formatStr := "bash /root/mount.sh %s %d %d %d %s --read-only=%v %d %s %s"
		return fmt.Sprintf(formatStr, authKey, bufferSize, concurrentLimit, cacheSize, metadataAddr, false, maxUpload, "--no-bgjob", subPath), env
	}

	formatStr := "bash /root/mount.sh %s %d %d %d %s --read-only=%v %d %s"
	return fmt.Sprintf(formatStr, authKey, bufferSize, concurrentLimit, cacheSize, metadataAddr, false, maxUpload, subPath), env
}

func AutoFsOptionCheckSum(mountPath string, buffSize, cacheSize uint64, readOnly bool) string {
	mountPath = "/" + strings.TrimLeft(mountPath, "/")
	payload := fmt.Sprintf("autodl-check-sum-%s-%d-%d-%v", mountPath, buffSize, cacheSize, readOnly)
	return Md5String([]byte(payload))
}

type AutoFsSetting struct {
	Name string `json:"name"`
	//UUID         string `json:"uuid"`
	//Storage      string `json:"storage"`
	//Bucket       string `json:"bucket"`
	//AccessKey    string `json:"access_key"`
	//SecretKey    string `json:"secret_key"`
	//BlockSize    int    `json:"block_size"`
	//Compression  string `json:"compression"`
	Capacity int64 `json:"capacity"`
	Inodes   int64 `json:"inodes"`
	//KeyEncrypted bool   `json:"key_encrypted"`
	//MetaVersion  int    `json:"meta_version"`
}

// -----------------------------------
// -----------------------------------
// -----------------------------------

// Buffer is a buffer to read/write integers.
type Buffer struct {
	endian binary.ByteOrder
	off    int
	buf    []byte
}

// NewBuffer returns a buffer with sz number of bytes.
func NewBuffer(sz uint32) *Buffer {
	return FromBuffer(make([]byte, sz))
}

// ReadBuffer utility to create *Buffer from slice of bytes
func ReadBuffer(buf []byte) *Buffer {
	return FromBuffer(buf)
}

// FromBuffer utility to create *Buffer
func FromBuffer(buf []byte) *Buffer {
	return &Buffer{binary.BigEndian, 0, buf}
}

// Len returns length of buffer
func (b *Buffer) Len() int {
	return len(b.buf)
}

// HasMore checks if offset is less than length
func (b *Buffer) HasMore() bool {
	return b.off < len(b.buf)
}

// Left returns number of bytes after offset
func (b *Buffer) Left() int {
	return len(b.buf) - b.off
}

// Seek seeks or sets offset to `p`
func (b *Buffer) Seek(p int) {
	b.off = p
}

// Buffer returns
func (b *Buffer) Buffer() []byte {
	return b.buf[b.off:]
}

// Put8 appends uint8 to Buffer
func (b *Buffer) Put8(v uint8) {
	b.buf[b.off] = v
	b.off++
}

// Get8 returns uint8
func (b *Buffer) Get8() uint8 {
	v := b.buf[b.off]
	b.off++
	return v
}

// Put16 appends uint16 to Buffer
func (b *Buffer) Put16(v uint16) {
	b.endian.PutUint16(b.buf[b.off:b.off+2], v)
	b.off += 2
}

// Get16 returns uint16
func (b *Buffer) Get16() uint16 {
	v := b.endian.Uint16(b.buf[b.off : b.off+2])
	b.off += 2
	return v
}

// Put32 appends uint32 to Buffer
func (b *Buffer) Put32(v uint32) {
	b.endian.PutUint32(b.buf[b.off:b.off+4], v)
	b.off += 4
}

// Get32 returns uint32
func (b *Buffer) Get32() uint32 {
	v := b.endian.Uint32(b.buf[b.off : b.off+4])
	b.off += 4
	return v
}

// Put64 appends uint64 to Buffer
func (b *Buffer) Put64(v uint64) {
	b.endian.PutUint64(b.buf[b.off:b.off+8], v)
	b.off += 8
}

// Get64 returns uint64
func (b *Buffer) Get64() uint64 {
	v := b.endian.Uint64(b.buf[b.off : b.off+8])
	b.off += 8
	return v
}

// Put appends slice of byte to Buffer
func (b *Buffer) Put(v []byte) {
	l := len(v)
	copy(b.buf[b.off:b.off+l], v)
	b.off += l
}

// Get returns `l` bytes from offset
func (b *Buffer) Get(l int) []byte {
	b.off += l
	return b.buf[b.off-l : b.off]
}

// SetBytes initilizes the Buffer with BigEndian ordering
func (b *Buffer) SetBytes(buf []byte) {
	b.endian = binary.BigEndian
	b.off = 0
	b.buf = buf
}

// Bytes returns the bytes
func (b *Buffer) Bytes() []byte {
	return b.buf
}

var nativeEndian binary.ByteOrder

// NewNativeBuffer utility to create *Buffer of given size with nativeEndian
func NewNativeBuffer(buf []byte) *Buffer {
	return &Buffer{nativeEndian, 0, buf}
}

func init() {
	buf := [2]byte{}
	*(*uint16)(unsafe.Pointer(&buf[0])) = uint16(0xABCD)

	switch buf {
	case [2]byte{0xCD, 0xAB}:
		nativeEndian = binary.LittleEndian
	case [2]byte{0xAB, 0xCD}:
		nativeEndian = binary.BigEndian
	default:
		panic("Could not determine native endianness.")
	}
}

type AutoFsListDirRequest struct {
	Inode Ino `form:"inode" json:"inode"`
}

type AutoFsListDirRequestV2 struct {
	DirPath string `form:"dir_path" json:"dir_path"`
	db_helper.GetPagedRangeRequest
}

type Ino uint64

func (i Ino) String() string {
	return strconv.FormatUint(uint64(i), 10)
}

// AutoFsEntry is an entry inside a directory.
type AutoFsEntry struct {
	Inode Ino
	Name  []byte
	Attr  *AutoFsAttr
}

// AutoFsAttr represents attributes of a node.
type AutoFsAttr struct {
	Flags     uint8  // reserved flags
	Typ       uint8  // type of a node
	Mode      uint16 // permission mode
	Uid       uint32 // owner id
	Gid       uint32 // group id of owner
	Rdev      uint32 // device number
	Atime     int64  // last access time
	Mtime     int64  // last modified time
	Ctime     int64  // last change time for meta
	Atimensec uint32 // nanosecond part of atime
	Mtimensec uint32 // nanosecond part of mtime
	Ctimensec uint32 // nanosecond part of ctime
	Nlink     uint32 // number of links (sub-directories or hardlinks)
	Length    uint64 // length of regular file

	Parent    Ino  // inode of parent; 0 means tracked by parentKey (for hardlinks)
	Full      bool // the attributes are completed or not
	KeepCache bool // whether to keep the cached page or not
}

func (a *AutoFsEntry) ToFileInfo() *AutoFsFileInfo {
	return &AutoFsFileInfo{
		Inode:  a.Inode,
		Name:   string(a.Name),
		Typ:    a.Attr.Typ,
		Length: a.Attr.Length,
		Parent: a.Attr.Parent,
		Atime:  a.Attr.Atime,
		Mtime:  a.Attr.Mtime,
		Ctime:  a.Attr.Ctime,
	}
}

const (
	TypeFile      = 1 // type for regular file
	TypeDirectory = 2 // type for directory
	TypeSymlink   = 3 // type for symlink
	TypeFIFO      = 4 // type for FIFO node
	TypeBlockDev  = 5 // type for block device
	TypeCharDev   = 6 // type for character device
	TypeSocket    = 7 // type for socket
)

type AutoFsFileInfo struct {
	Inode  Ino
	Name   string
	Typ    uint8
	Length uint64 // length of regular file
	Parent Ino
	Atime  int64 // last access time
	Mtime  int64 // last modified time
	Ctime  int64 // last change time for meta
}
