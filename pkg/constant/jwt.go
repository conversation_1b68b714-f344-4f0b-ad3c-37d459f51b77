package constant

import (
	"github.com/dgrijalva/jwt-go"
	"github.com/pkg/errors"
	log "github.com/sirupsen/logrus"
	"reflect"
	"server/conf"
)

type UserClaims struct {
	UID     int  `json:"uid"`
	IsAdmin bool `json:"is_admin"`
	jwt.StandardClaims
}

// 是否有管理员权限
func (uri *UserClaims) HasAdminAuthority() bool {
	return uri.IsAdmin
}

// 生成token
func GenerateToken(claims jwt.Claims) (string, error) {
	tokenClaims := jwt.NewWithClaims(jwt.SigningMethodES256, claims)
	token, err := tokenClaims.SignedString(conf.GetGlobalGsConfig().Key.PrivateKey)
	if err != nil {
		log.Error("Token signed failed.")
	}
	return token, err
}

// 解析token
func ParseToken(token string, parseType jwt.Claims) (claims jwt.Claims, err error) {
	rv := reflect.ValueOf(parseType)
	if rv.Kind() != reflect.Ptr || rv.IsNil() {
		err = errors.Errorf("parseType must be pointer, %s given", reflect.TypeOf(parseType))
		return
	}
	var jwtToken *jwt.Token
	jwtToken, err = jwt.ParseWithClaims(token, parseType, func(token *jwt.Token) (interface{}, error) {
		return conf.GetGlobalGsConfig().Key.PublicKey, nil
	})
	if err != nil {
		return
	}

	if jwtToken != nil && jwtToken.Valid {
		return jwtToken.Claims, nil
	}

	if _, ok := err.(*jwt.ValidationError); ok {
		return nil, err
	}

	err = errors.Errorf("jwtToken is invalid")
	return
}
