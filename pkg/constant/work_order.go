package constant

import "strings"

type WorkOrderStatus string

const (
	WaitProcess         WorkOrderStatus = "wait_process"         // 待处理
	Processing          WorkOrderStatus = "processing"           // 进行中
	Finish              WorkOrderStatus = "finish"               // 已完成
	ShutdownMaintenance WorkOrderStatus = "shutdown_maintenance" // 可停机维护
	PressureMeasurement WorkOrderStatus = "pressure_measurement" // 压测中
)

func (w *WorkOrderStatus) IsFinished() bool {
	switch *w {
	case Finish:
		return true
	}
	return false
}

var statusMap = map[WorkOrderStatus]string{
	WaitProcess:         "待处理",
	Processing:          "处理中",
	Finish:              "已处理",
	ShutdownMaintenance: "可停机维护",
	PressureMeasurement: "压测中",
}

func WorkOrderStatusString(status WorkOrderStatus) string {
	if chineseStatus, ok := statusMap[status]; ok {
		return chineseStatus
	}
	// 如果未找到匹配的中文状态，返回原始英文状态
	return string(status)
}

type WorkOrderFaultLevel string

const (
	Observe                WorkOrderFaultLevel = "observe"                 // 观察
	MinorFault             WorkOrderFaultLevel = "minor_fault"             // 一般故障
	EmergencyFault         WorkOrderFaultLevel = "emergency_fault"         // 紧急故障
	HardwareTest           WorkOrderFaultLevel = "hardware_test"           // 硬件测试
	HardwareTransformation WorkOrderFaultLevel = "hardware_transformation" // 硬件改造
	ThirdPartyEquipment    WorkOrderFaultLevel = "third_party_equipment"   // 三方设备
)

type WorkOrderFaultType string

const (
	FaultTypeCPU         WorkOrderFaultType = "cpu"
	FaultTypeGPUCard     WorkOrderFaultType = "gpu_card"
	FaultTypeGPUWire     WorkOrderFaultType = "gpu_wire"
	FaultTypeMemory      WorkOrderFaultType = "memory"
	FaultTypePowerSource WorkOrderFaultType = "power_source"  // 电源
	FaultTypeMainBoard   WorkOrderFaultType = "main_board"    // 主板
	FaultTypeOS          WorkOrderFaultType = "os"            // 操作系统
	FaultTypeGPUDriver   WorkOrderFaultType = "gpu_driver"    // gpu驱动
	DiskAbnormal         WorkOrderFaultType = "disk_abnormal" // 硬盘损坏
	WORaidAbnormal       WorkOrderFaultType = "raid_abnormal" // RAID损坏
	FaultTypeOther       WorkOrderFaultType = "other"
)

type MachineMonitorStatus string

const (
	Unprocessed MachineMonitorStatus = "unprocessed" // 未处理
	Processed   MachineMonitorStatus = "processed"   // 已处理
)

type MachineMonitorFaultType string

const (
	GpuFanErr      MachineMonitorFaultType = "gpu_fan_err"      // GPU FAN ERR
	NvidiaSmiStuck MachineMonitorFaultType = "nvidia_smi_stuck" // NVIDIA-SMI卡死
	DockerStuck    MachineMonitorFaultType = "docker_stuck"     // Docker卡死
	ExistZombie    MachineMonitorFaultType = "zombie"           // 僵尸进程
	RaidAbnormal   MachineMonitorFaultType = "raid_abnormal"    // RAID异常
	NvXid79        MachineMonitorFaultType = "nv_xid_79"        // GPU掉卡
)

type MachineMonitorProcessResult string

const (
	ChangeWorkOrder MachineMonitorProcessResult = "change_work_order" // 转为工单
	MistakeReport   MachineMonitorProcessResult = "mistake_report"    // 误报
	MistakeIgnore   MachineMonitorProcessResult = "mistake_ignore"    // 忽略
)

type WorkOrderOperateRecordType string

const (
	Assign    WorkOrderOperateRecordType = "assign"     // 对象指派
	AddRecord WorkOrderOperateRecordType = "add_record" // 添加记录
	SetStatus WorkOrderOperateRecordType = "set_status" // 设置状态
)

type WorkOrderTenantRole string

const (
	Provider WorkOrderTenantRole = "provider" // 供应商
	Custom   WorkOrderTenantRole = "custom"   // 客户
)

type WorkOrderUserRole string

const (
	Administrator   WorkOrderUserRole = "administrator"    // 管理员
	ProductionStaff WorkOrderUserRole = "production_staff" // 生产员
	AfterSales      WorkOrderUserRole = "after_sales"      // 售后
	Operation       WorkOrderUserRole = "operation"        // 运维
	CustomerService WorkOrderUserRole = "customer_service" // 客服
)

func (b WorkOrderUserRole) IsAdmin() bool {
	return strings.Contains(string(b), string(Administrator))
}
func (b WorkOrderUserRole) IsProductions() bool {
	return strings.Contains(string(b), string(ProductionStaff))
}
func (b WorkOrderUserRole) IsAfterSales() bool {
	return strings.Contains(string(b), string(AfterSales))
}
func (b WorkOrderUserRole) IsOperation() bool {
	return strings.Contains(string(b), string(Operation))
}
func (b WorkOrderUserRole) IsCustomerService() bool {
	return strings.Contains(string(b), string(CustomerService))
}

type WorkOrderUserStatus string

const (
	Normal WorkOrderUserStatus = "normal" // 正常
	Lock   WorkOrderUserStatus = "lock"   // 锁定
)

type WorkOrderMaterialSort string

const (
	Chassis                 WorkOrderMaterialSort = "chassis"                    // 机箱
	CasFan                  WorkOrderMaterialSort = "case_fan"                   // 机箱风扇
	MainBoard               WorkOrderMaterialSort = "main_board"                 // 主板
	CPU                     WorkOrderMaterialSort = "cpu"                        // cpu
	Memory                  WorkOrderMaterialSort = "memory"                     // 内存
	GPU                     WorkOrderMaterialSort = "gpu"                        // gpu
	GPUExtensionComponents  WorkOrderMaterialSort = "gpu_extension_components"   // gpu扩展组件
	GPUPowerCable           WorkOrderMaterialSort = "gpu_power_cable"            // gpu电源线
	HardDisk                WorkOrderMaterialSort = "hard_disk"                  // 硬盘
	HardDiskBackplaneModule WorkOrderMaterialSort = "hard_disk_backplane_module" // 硬盘背板模组
	HardDiskMainCardCable   WorkOrderMaterialSort = "hard_disk_main_card_cable"  // 硬盘总卡线
	HardDiskCable           WorkOrderMaterialSort = "hard_disk_cable"            // 硬盘线
	PowerSource             WorkOrderMaterialSort = "power_source"               // 电源
	PowerStrip              WorkOrderMaterialSort = "power_strip"                // 电源板
	Radiator                WorkOrderMaterialSort = "radiator"                   // 散热器
	OtherWire               WorkOrderMaterialSort = "other_wire"                 // 其他线材
	OtherMaterial           WorkOrderMaterialSort = "other_material"             // 其他
)

type WorkOrderMachineStatus string

const (
	WaitProduction WorkOrderMachineStatus = "wait_production" // 待生产
	WaitDelivery   WorkOrderMachineStatus = "wait_delivery"   // 待交付
	Delivered      WorkOrderMachineStatus = "delivered"       // 已交付
)

type WorkOrderSparePartStatus string

const (
	WaitDeal                   WorkOrderSparePartStatus = "wait_deal" // 待处理
	WorkOrderSparePartClose    WorkOrderSparePartStatus = "close"     // 关闭
	WorkOrderSparePartStocking WorkOrderSparePartStatus = "stocking"  // 备货中
	WorkOrderSparePartShipped  WorkOrderSparePartStatus = "shipped"   // 已发货
)

type DispatchOrderSparePartStatus string

const (
	DispatchOrderSparePartStocking DispatchOrderSparePartStatus = "stocking" // 备货中
	DispatchOrderSparePartShipped  DispatchOrderSparePartStatus = "shipped"  // 已发货
	DispatchOrderSparePartClose    DispatchOrderSparePartStatus = "close"    // 关闭
)

type WorkOrderRepairStatus string

const (
	WorkOrderRepairWaitShipped WorkOrderRepairStatus = "wait_shipped" // 待发货
	WorkOrderRepairShipped     WorkOrderRepairStatus = "shipped"      // 已发货
	WorkOrderRepairClose       WorkOrderRepairStatus = "close"        // 关闭
)

type DispatchOrderRepairStatus string

const (
	DispatchOrderRepairWaitShipped DispatchOrderRepairStatus = "wait_shipped" // 待发货
	DispatchOrderRepairShipped     DispatchOrderRepairStatus = "shipped"      // 已发货
	DispatchOrderRepairClose       DispatchOrderRepairStatus = "close"        // 关闭
)

type CustomerWorkOrderStatus string

const (
	CustomerWorkOrderStatusWaitProcess    CustomerWorkOrderStatus = "wait_process"    // 待处理
	CustomerWorkOrderStatusProcessing     CustomerWorkOrderStatus = "processing"      // 进行中
	CustomerWorkOrderStatusWaitAcceptance CustomerWorkOrderStatus = "wait_acceptance" // 待验收
	CustomerWorkOrderStatusFinish         CustomerWorkOrderStatus = "finish"          // 已完成
)

func CustomerWorkOrderStatusToChinese(status CustomerWorkOrderStatus) string {
	switch status {
	case CustomerWorkOrderStatusWaitProcess:
		return "待处理"
	case CustomerWorkOrderStatusProcessing:
		return "进行中"
	case CustomerWorkOrderStatusWaitAcceptance:
		return "待验收"
	case CustomerWorkOrderStatusFinish:
		return "已完成"
	default:
		return ""
	}
}

type CustomerWorkOrderTag string

const (
	HighPriority              CustomerWorkOrderTag = "high_priority"    // 高级优先
	MediumPriority            CustomerWorkOrderTag = "medium_priority"  // 中级优先
	LowPriority               CustomerWorkOrderTag = "low_priority"     // 低级优先
	StorageTooHigh            CustomerWorkOrderTag = "storage_too_high" // 存储过高
	GpuCannotCall             CustomerWorkOrderTag = "gpu_cannot_call"  // 无法调用GPU
	CloneException            CustomerWorkOrderTag = "clone_exception"  // 克隆异常
	StartAbnormal             CustomerWorkOrderTag = "start_abnormal"   // 开机异常
	StopAbnormal              CustomerWorkOrderTag = "stop_abnormal"    // 关机异常
	CreateAbnormal            CustomerWorkOrderTag = "create_abnormal"  // 创建异常
	InstanceError             CustomerWorkOrderTag = "instance_error"   // 实例宕机
	SSHError                  CustomerWorkOrderTag = "ssh_error"        // 无法SSH
	JupyterError              CustomerWorkOrderTag = "jupyter_error"    // 无法打开Jupyter
	CustomerWorkOrderTagOther CustomerWorkOrderTag = "other"            // 其他
)

type CustomerWorkOrderOperateRecordType string

const (
	CustomerWorkOrderAssign    CustomerWorkOrderOperateRecordType = "assign"     // 对象指派
	CustomerWorkOrderAddRecord CustomerWorkOrderOperateRecordType = "add_record" // 添加记录
	CustomerWorkOrderSetStatus CustomerWorkOrderOperateRecordType = "set_status" // 设置状态
)

type SmsStatusType string

const (
	Sending     SmsStatusType = "sending"      // 发送中
	SendSuccess SmsStatusType = "send_success" // 已发送
	SendFailed  SmsStatusType = "send_failed"  // 发送失败
)

type SendBasisType string

const (
	Instance SendBasisType = "instance" // 实例ID
	Phone    SendBasisType = "phone"    // 手机号
)

type NotifyStatusType string

const (
	WaitNotify         NotifyStatusType = "wait_notify"          // 待通知
	NotifySuccess      NotifyStatusType = "notify_success"       // 通知成功（全部通知成功）
	NotifyFailed       NotifyStatusType = "notify_failed"        // 通知失败
	NotifySendingRetry NotifyStatusType = "notify_sending_retry" // 重试中
)

type InstanceNotifyStatus string

const (
	InEffect InstanceNotifyStatus = "in_effect" // 生效中
	Finished InstanceNotifyStatus = "finished"  // 已结束
)
