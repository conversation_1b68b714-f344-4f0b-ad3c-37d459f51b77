package constant

import (
	"net/http"
	"strings"
)

type OperateRecordSection string
type OperateRecordType string

const (
	OperateUser       OperateRecordSection = "user"       // 用户
	OperateInstance   OperateRecordSection = "instance"   // 容器实例
	OperateDeployment OperateRecordSection = "deployment" // 弹性部署
	OperateImage      OperateRecordSection = "image"

	OperateLogin             OperateRecordType = "login"               // 登录
	OperateLoginWX           OperateRecordType = "login_wx"            // 微信登录
	OperateLoginCode         OperateRecordType = "login_code"          // 验证码登录
	OperateLoginApplet       OperateRecordType = "login_applet"        // 小程序登录
	OperateChangePhone       OperateRecordType = "change_phone"        // 换绑手机号
	OperateSignupCG          OperateRecordType = "signup_cg"           // cg注册
	OperateSignupAdmin       OperateRecordType = "signup_admin"        // 管理员注册
	OperateSignupCode        OperateRecordType = "signup_code"         // 短信注册
	OperateSignupWX          OperateRecordType = "signup_wx"           // 微信注册
	OperateSignupApplet      OperateRecordType = "signup_applet"       // 小程序注册
	OperateUpdateUserSetting OperateRecordType = "update_user_setting" // 余额预警

	OperateInstanceCreate           OperateRecordType = "create"         // 创建实例
	OperateInstanceMigrate          OperateRecordType = "migrate"        // 迁移实例
	OperateInstanceClone            OperateRecordType = "clone"          // 克隆实例
	OperateInstanceCloneByAdmin     OperateRecordType = "clone_by_admin" // 克隆实例
	OperateInstanceCloneRetry       OperateRecordType = "clone_retry"    // 克隆重试
	OperateInstanceCloneStopByAdmin OperateRecordType = "clone_stop"     // 克隆停止

	OperateInstancePowerOn                 OperateRecordType = "power_on"                    // 开机
	OperateInstanceRestart                 OperateRecordType = "restart"                     // 重启
	OperateInstancePowerOff                OperateRecordType = "power_off"                   // 关机
	OperateInstancePowerOffByCharge        OperateRecordType = "power_off_by_charge"         // 按量计费余额不足自动关机
	OperateInstancePowerOffByPrepayExpired OperateRecordType = "power_off_by_prepay_expired" // 包卡到期自动关机
	OperateInstanceSetting                 OperateRecordType = "setting"                     // 设置
	OperateInstanceRelease                 OperateRecordType = "release"                     // 释放
	OperateInstanceInit                    OperateRecordType = "init"                        // 初始化
	OperateInstanceChangeImage             OperateRecordType = "change_image"                // 更换镜像
	OperateInstanceTimedShutDown           OperateRecordType = "timed_shutdown_by_user"      // 定时关机

	OperateDC         OperateRecordType = "dc_operate"   // 操作弹性部署
	OperateDCPowerOff OperateRecordType = "dc_power_off" // 弹性部署容器关机

	OperateImageDelete OperateRecordType = "image_delete"

	OperateUpdateProtocol OperateRecordType = "update_protocol" // 修改协议
)

func (o *OperateRecordType) ToSection() OperateRecordSection {
	switch *o {
	case OperateLogin, OperateLoginWX, OperateLoginCode, OperateSignupCG,
		OperateSignupAdmin, OperateSignupCode, OperateSignupWX, OperateSignupApplet,
		OperateLoginApplet, OperateUpdateUserSetting, OperateChangePhone:
		return OperateUser
	case OperateInstancePowerOn, OperateInstanceRestart, OperateInstancePowerOff, OperateInstanceInit, OperateInstanceChangeImage, OperateInstanceTimedShutDown,
		OperateInstanceCreate, OperateInstanceMigrate, OperateInstanceClone, OperateInstanceCloneRetry, OperateInstanceSetting, OperateInstanceRelease, OperateInstancePowerOffByCharge,
		OperateUpdateProtocol, OperateInstancePowerOffByPrepayExpired:
		return OperateInstance
	case OperateDCPowerOff, OperateDC:
		return OperateDeployment
	case OperateImageDelete:
		return OperateImage
	}
	return ""
}

func (o *OperateRecordType) GetOperateType(r *http.Request) {
	if IsWechatMiniProgram(r) {
		*o = OperateLoginApplet
	}
	return
}

func IsWechatMiniProgram(r *http.Request) bool {
	ua := r.Header.Get("User-Agent")
	return strings.Contains(ua, "MiniProgram")
}
