package constant

import (
	"time"
)

const (
	VoucherPre                 = "38"
	CouponPre                  = "35"
	BillPre                    = "16"
	OrderPre                   = "26"
	ChangeChargeTypeTimesLimit = 50
)

// OrderType 账单类型
type OrderType string

const (
	OrderTypeCreateInstance              OrderType = "create_instance"                // 创建实例
	OrderTypeCreateDeploymentContainer   OrderType = "create_deployment_container"    // 创建弹性部署
	OrderTypeCreateDeploymentDurationPkg OrderType = "create_deployment_duration_pkg" // 购买弹性部署时长包
	OrderTypeRenewalInstance             OrderType = "renewal_instance"               // 实例续费
	OrderTypeUpdateInstance              OrderType = "update_instance"                // 实例变更配置
	OrderTypeMigrateInstance             OrderType = "migrate_instance"               // 实例迁移 已废弃
	OrderTypeCloneInstance               OrderType = "clone_instance"                 // 实例克隆
	OrderTypeImproveConfiguration        OrderType = "improve_configuration"          // 实例升配
	OrderTypeReduceConfiguration         OrderType = "reduce_configuration"           // 实例降配
	OrderTypeChangeDaily                 OrderType = "change_daily"                   // 转包日
	OrderTypeChangeWeekly                OrderType = "change_weekly"                  // 转包周
	OrderTypeChangeMonthly               OrderType = "change_monthly"                 // 转包月
	OrderTypeChangeYearly                OrderType = "change_yearly"                  // 转包年
	OrderTypeChangePayg                  OrderType = "change_payg"                    // 转按量付费
	OrderTypeRefund                      OrderType = "refund"                         // 退款 -- 根据产品要求，添加退款类型订单
	OrderTypeDeploymentDurationPkgRefund OrderType = "deployment_duration_pkg_refund" // 弹性部署时长包退款
	OrderTypeNetDiskExpand               OrderType = "net_disk_expand"                // 网盘扩容
	OrderTypeNetDiskRenewal              OrderType = "net_disk_renewal"               // 网盘续费
	OrderTypeDataDiskExpand              OrderType = "data_disk_expand"               // 数据盘扩容
	OrderTypeDataDiskExpandOfPrepay      OrderType = "data_disk_expand_of_prepay"     // 数据盘扩容
	OrderTypeDataDiskReduce              OrderType = "data_disk_reduce"               // 数据盘缩容
	OrderTypeFileStorage                 OrderType = "file_storage"                   // 文件存储初始化
	OrderTypeAutoFsInit                  OrderType = "autofs_init"                    // 文件存储初始化
	OrderTypePrivateImage                OrderType = "private_image"                  // 私有镜像
)

func (o OrderType) IsChangeToPayg() bool {
	if o == OrderTypeChangePayg {
		return true
	}
	return false
}

func (o OrderType) IsChangeToPrepay() bool {
	switch o {
	case OrderTypeChangeDaily,
		OrderTypeChangeWeekly,
		OrderTypeChangeMonthly,
		OrderTypeChangeYearly:
		return true
	}
	return false
}
func (o OrderType) ToBillType() (b BillType) {
	switch o {
	case OrderTypeChangePayg:
		return BillTypeRefund
	default:
		return BillTypeCharge
	}
}
func (o OrderType) ToBillSubtype() (b BillSubType) {
	switch o {
	case OrderTypeCreateInstance, OrderTypeCloneInstance, OrderTypeCreateDeploymentContainer:
		return BillSubTypeCreateContainer
	case OrderTypeRenewalInstance:
		return BillSubTypeRenewalContainer
	case OrderTypeImproveConfiguration, OrderTypeReduceConfiguration:
		return BillSubTypeUpdateInstance
	case OrderTypeChangeDaily,
		OrderTypeChangeWeekly,
		OrderTypeChangeMonthly,
		OrderTypeChangeYearly:
		return BillSubTypeChangeChargeType
	case OrderTypeChangePayg:
		return BillSubTypeRefundToPayg
	case OrderTypeDataDiskExpandOfPrepay:
		return BillSubTypeDataDiskExpandForPrepay
	case OrderTypeDataDiskExpand, OrderTypeDataDiskReduce:
		return BillSubTypeDataDiskChange
	}

	return
}

func (o OrderType) ToUpdateWalletOperateType() (u UpdateWalletOperateType) {
	switch o {
	case OrderTypeCreateInstance,
		OrderTypeRenewalInstance,
		OrderTypeCloneInstance,
		OrderTypeDataDiskExpandOfPrepay,
		OrderTypeDataDiskExpand,
		OrderTypeDataDiskReduce:
		u = UpdateWalletOperateByOrderType

	case OrderTypeCreateDeploymentContainer,
		OrderTypeChangeDaily,
		OrderTypeChangeWeekly,
		OrderTypeChangeMonthly,
		OrderTypeChangeYearly,
		OrderTypeChangePayg:
		u = UpdateWalletCharge
	default:
	}
	return
}

func (o OrderType) ToProductType() ProductType {
	switch o {
	case OrderTypeNetDiskExpand,
		OrderTypeNetDiskRenewal:
		return ProductTypeNetDisk

	case OrderTypeCreateDeploymentContainer:
		return ProductTypeDeployment

	case OrderTypeCreateDeploymentDurationPkg:
		return ProductTypeDeploymentDurationPkg

	case OrderTypeDataDiskExpand, OrderTypeDataDiskReduce:
		return ProductTypeDataDisk
	}
	return ProductTypeInstance
}

// OrderStatus 账单状态
type OrderStatus string

const (
	OrderStatusSuccess OrderStatus = "success"                // 交易成功
	OrderStatusUnused  OrderStatus = "success_renewal_unused" // 交易成功。续费订单未生效
	OrderStatusClosed  OrderStatus = "closed"                 // 关闭/取消
	OrderStatusUnpaid  OrderStatus = "unpaid"                 // 待付款
	OrderStatusTimeout OrderStatus = "timeout"                // 超时
	OrderStatusRefund  OrderStatus = "refund"                 // 已退款
)

// ChargeType 计费类型
type ChargeType string

// NOTE: 如果此处包年包月大类增加内容, 应该去 AutoChargeType() 添加对应的筛选.
const (
	ChargeTypeDaily   ChargeType = "daily"
	ChargeTypeWeekly  ChargeType = "weekly"
	ChargeTypeMonthly ChargeType = "monthly"
	ChargeTypeYearly  ChargeType = "yearly"
	ChargeTypePayg    ChargeType = "payg"
	ChargeTypePrepay  ChargeType = "prepay"
	ChargeTypeNoGpu   ChargeType = "payg_no_gpu" // 按量付费的无卡模式
)

func (c ChargeType) ToSecond() int64 {
	switch c {
	case ChargeTypeDaily:
		return DaySec
	case ChargeTypeWeekly:
		return WeekSec
	case ChargeTypeMonthly:
		return MonthSec
	case ChargeTypeYearly:
		return YearSec
	case ChargeTypePayg:
		return HourSec
	}
	return 0
}

func (c ChargeType) ToHour() int64 {
	switch c {
	case ChargeTypeDaily:
		return 24
	case ChargeTypeWeekly:
		return 24 * 7
	case ChargeTypeMonthly:
		return 24 * 30
	case ChargeTypeYearly:
		return 24 * 365
	case ChargeTypePayg:
		return 1
	}
	return 0
}

// ToOrderType 变更计费方式用
func (c ChargeType) ToOrderType() (t OrderType) {
	switch c {
	case ChargeTypeDaily:
		t = OrderTypeChangeDaily
	case ChargeTypeWeekly:
		t = OrderTypeChangeWeekly
	case ChargeTypeMonthly:
		t = OrderTypeChangeMonthly
	case ChargeTypeYearly:
		t = OrderTypeChangeYearly
	case ChargeTypePayg:
		t = OrderTypeChangePayg
	}

	return
}

func (c ChargeType) VoucherUseSku(productType ProductType) (u VoucherUseSku) {
	switch productType {
	case ProductTypeFileStorage, ProductTypeDeployment, ProductTypeDeploymentDurationPkg:
		u = VoucherUseSkuAll
	case ProductTypeInstance:
		if c == ChargeTypeDaily || c == ChargeTypeWeekly || c == ChargeTypeMonthly || c == ChargeTypeYearly {
			u = VoucherUseSkuInstance
		} else if c == ChargeTypePayg {
			u = ""
		}
	default:
		u = ""
	}
	return
}

func (c ChargeType) IsNoGpu() bool {
	if c == ChargeTypeNoGpu {
		return true
	}
	return false
}

func (c ChargeType) IsPayg() bool {
	if c == ChargeTypePayg {
		return true
	}
	return false
}

func (c ChargeType) IsRentType() bool {
	if c == ChargeTypeDaily || c == ChargeTypeWeekly || c == ChargeTypeMonthly || c == ChargeTypeYearly {
		return true
	}
	return false
}

func (c ChargeType) To001Limit() bool {
	switch c {
	case ChargeTypeDaily,
		ChargeTypeWeekly,
		ChargeTypeMonthly,
		ChargeTypeYearly,
		ChargeTypePrepay:
		return false
	}
	return true
}

func (c ChargeType) Valid() bool {
	switch c {
	case ChargeTypeDaily,
		ChargeTypeWeekly,
		ChargeTypeMonthly,
		ChargeTypeYearly,
		ChargeTypePayg,
		ChargeTypePrepay,
		ChargeTypeNoGpu:
		return true
	}
	return false
}

type ChargeTypeFilter string

const (
	ChargeTypeFilterPayg   ChargeTypeFilter = "payg"
	ChargeTypeFilterPrepay ChargeTypeFilter = "prepay"
)

// DiscountType 折扣类型
type DiscountType string

const MachineDiscount DiscountType = "machine_discount"
const MemberDiscount DiscountType = "member_discount"
const CouponDiscount DiscountType = "coupon_discount"
const DurationDiscount DiscountType = "duration_discount"

const (
	// AssetsRate 全平台金额转换系数，金额保留两位小数，系数为1000倍，避免float运算
	AssetsRate = 1000

	// LockTimeout redis锁的时延
	LockTimeout = time.Second * 5
)

type BillType string
type BillSubType string

const (
	BillTypeRecharge BillType = "recharge" // 充值
	BillTypeCharge   BillType = "charge"   // 扣费
	BillTypeRefund   BillType = "refund"   // 退款
	BillTypeWithdraw BillType = "withdraw" // 提现

	BillSubTypeAdminRecharge             BillSubType = "recharge_admin"              // 充值 - 管理员充值
	BillSubTypeRecharge                  BillSubType = "recharge_user"               // 充值 - 用户充值
	BillSubTypeRechargeAlipay            BillSubType = "recharge_user_alipay"        // 充值 - 用户充值 - 支付宝
	BillSubTypeRechargeWeChatPay         BillSubType = "recharge_user_wechat_pay"    // 充值 - 用户充值 - 微信
	BillSubTypeRechargeCreditWallet      BillSubType = "recharge_credit_wallet"      // 充值 - 用户充值 - 信用钱包充值
	BillSubTypeRechargeCommunityExchange BillSubType = "recharge_community_exchange" // 充值 - 用户充值 - 社区兑换硬币
	BillSubTypeRechargeBankTransfer      BillSubType = "recharge_bank_transfer"      // 充值 - 用户充值 - 对公汇款
	BillNoteAdminRecharge                            = "[Admin Recharge]"

	BillSubTypeCreateContainer                   BillSubType = "charge_create"                         // 扣费 - 创建实例
	BillSubTypeCharge                            BillSubType = "charge_settle"                         // 扣费 - 定时扣费
	BillSubTypeChargeShutdown                    BillSubType = "charge_settle_shutdown"                // 扣费 - 定时扣费 - 余额不足 - 关机
	BillSubTypeShutdown                          BillSubType = "charge_shutdown"                       // 扣费 - 关机
	BillSubTypeChargeDC                          BillSubType = "charge_settle_dc"                      // 扣费 - 定时扣费
	BillSubTypeChargeShutdownDC                  BillSubType = "charge_settle_shutdown_dc"             // 扣费 - 定时扣费 - 余额不足 - 关机
	BillSubTypeShutdownDC                        BillSubType = "charge_shutdown_dc"                    // 扣费 - 关机
	BillSubTypeRenewalContainer                  BillSubType = "charge_renewal"                        // 扣费 - 实例续费
	BillSubTypeChangeChargeType                  BillSubType = "charge_change_charge_type"             // 扣费 - 计费方式变更
	BillSubTypeUpdateInstance                    BillSubType = "update_instance_conf"                  // 扣费 - 实例变更
	BillSubTypeDataDiskSettle                    BillSubType = "data_disk_settle"                      // 扣费 - 数据盘定时结算
	BillSubTypeDataDiskChange                    BillSubType = "data_disk_change"                      // 扣费 -
	BillSubTypeDataDiskExpandForPrepay           BillSubType = "data_disk_expand_for_prepay"           // 扣费 - 数据盘定时结算
	BillSubTypeNetDiskExpand                     BillSubType = "net_disk_expand"                       // 扣费 - 网盘扩容
	BillSubTypeNetDiskRenewal                    BillSubType = "net_disk_renewal"                      // 扣费 - 网盘续费
	BillSubTypeFileStorage                       BillSubType = "file_storage"                          // 扣费 - 文件存储
	BillSubTypeAutoFs                            BillSubType = "charge_autofs"                         // 扣费 - autoFs
	BillSubTypePrivateImage                      BillSubType = "private_image"                         // 扣费 - 镜像存储
	BillSubTypeChargeCreateDeploymentDurationPkg BillSubType = "charge_create_deployment_duration_pkg" // 扣费 - 弹性部署时长包

	BillSubTypeRefundToPayg                           BillSubType = "refund_change_to_payg"          // 退款 - 转按量计费
	BillSubTypeRefundDeploymentDurationPkgBillSubType BillSubType = "refund_deployment_duration_pkg" // 退款 - 弹性部署时长包
	BillSubTypeTransInstancePrepay                    BillSubType = "refund_trans_instance_prepay"   // 退款-转移实例包卡订单

	BillSubTypeWithdrawAdmin            BillSubType = "withdraw_admin"             // 提现 - 管理员
	BillSubTypeWithdrawUser             BillSubType = "withdraw_user"              // 提现 - 用户
	BillSubTypeWithdrawRefund           BillSubType = "withdraw_refund"            // 提现 - 退款 - 成功
	BillSubTypeWithdrawRefundDelay      BillSubType = "withdraw_refund_delay"      // 延迟提现
	BillSubTypeWithdrawRefundFailed     BillSubType = "withdraw_refund_failed"     // 提现 - 失败
	BillSubTypeWithdrawRefundProcessing BillSubType = "withdraw_refund_processing" // 提现 - 用于触发冻结余额操作
)

func (b BillSubType) ToRechargePathway() RechargePathway {
	switch b {
	case BillSubTypeRechargeCommunityExchange:
		return CommunityExchange
	case BillSubTypeAdminRecharge:
		return AdminPay
	}
	return 0
}

func (b BillSubType) ToProductType() ProductType {
	switch b {
	case BillSubTypeFileStorage, BillSubTypeAutoFs:
		return ProductTypeFileStorage
	case BillSubTypeChargeDC, BillSubTypeShutdownDC, BillSubTypeChargeShutdownDC:
		return ProductTypeDeployment
	case BillSubTypeChargeCreateDeploymentDurationPkg:
		return ProductTypeDeploymentDurationPkg
	case BillSubTypePrivateImage:
		return ProductTypePrivateImage
	}
	return ProductTypeInstance
}

// RechargePathway 正式充值途径
type RechargePathway int
type RefundStatus string

const (
	Alipay            RechargePathway = 1 // 用户充值 - ali
	WeChatPay         RechargePathway = 2 // 用户充值 - wx
	AdminPay          RechargePathway = 3 // 管理员充值
	CreditWalletPay   RechargePathway = 4 // 信用钱包转入
	WechatAppletPay   RechargePathway = 5 // 微信小程序支付
	CommunityExchange RechargePathway = 6 // 社区兑换
	BankTransfer      RechargePathway = 7 // 对公汇款

	RefundSuccess    RefundStatus = "success"
	RefundFailed     RefundStatus = "failed"
	RefundProcessing RefundStatus = "processing"
	RefundWaiting    RefundStatus = "waiting"
)

func (rr RechargePathway) ToBillSubType() BillSubType {
	switch rr {
	case Alipay:
		return BillSubTypeRechargeAlipay
	case WeChatPay:
		return BillSubTypeRechargeWeChatPay
	case AdminPay:
		return BillSubTypeAdminRecharge
	case CreditWalletPay:
		return BillSubTypeRechargeCreditWallet
	case WechatAppletPay:
		return BillSubTypeRechargeWeChatPay
	case CommunityExchange:
		return BillSubTypeRechargeCommunityExchange
	default:
		return BillSubTypeRecharge
	}
}

type UpdateWalletOperateType string

const (
	UpdateWalletSkip                           UpdateWalletOperateType = "skip"                               // 特殊类型，不需要处理，占位用
	UpdateWalletOperateByOrderType             UpdateWalletOperateType = "operate_by_order"                   // 根据order的类型来处理
	UpdateWalletCharge                         UpdateWalletOperateType = "charge"                             // 扣费
	UpdateWalletPaygSettle                     UpdateWalletOperateType = "payg_settle"                        // 按量付费 - 定时结算
	UpdateWalletPaygShutdown                   UpdateWalletOperateType = "payg_shutdown"                      // 按量付费 - 关机结算
	UpdateWalletInstanceCallShutdown           UpdateWalletOperateType = "instance_call_shutdown"             // 按量付费 - 实例通知关机结算
	UpdateWalletChargeTypeToPrepayStopCharging UpdateWalletOperateType = "charge_type_to_other_stop_charging" // 变更实例计费方式到包卡，停止计费
	UpdateWalletDataDiskPaygSettle             UpdateWalletOperateType = "data_disk_payg_settle"              // 数据盘结算
	UpdateWalletSpecialRechargeInsertRecord    UpdateWalletOperateType = "recharge_insert_record"             // 管理员充值，硬币兑换
)

// 变更实例计费方式到包卡，更新实例

func (u *UpdateWalletOperateType) ToBillSubtype(runtimeType ContainerRuntimeType) (b BillSubType) {
	switch *u {
	case UpdateWalletPaygSettle:
		if runtimeType == ContainerRuntimeOfDeployment {
			return BillSubTypeChargeDC
		}
		return BillSubTypeCharge
	case UpdateWalletPaygShutdown:
		if runtimeType == ContainerRuntimeOfDeployment {
			return BillSubTypeChargeShutdownDC
		}
		return BillSubTypeChargeShutdown
	}
	return
}

type CreateContainerTaskRequest struct {
	// 用户指定的实例名称描述
	Name        string `json:"name"`        // 用户指定的名称. 目前不生效. 名称与UUID 作同一概念.
	Description string `json:"description"` //

	// instance/idle job/deployment
	RuntimeType ContainerRuntimeType `json:"runtime_type"`

	// 所需镜像等信息
	Image            string `json:"image"` // 目前仅指公有镜像
	PrivateImageUUID string `json:"private_image_uuid"`
	ReproductionUUID string `json:"reproduction_uuid"` // 社区项目复现uuid
	ReproductionID   int    `json:"reproduction_id"`
	// 指定机器 id
	MachineID string `json:"machine_id"`

	// 指定地区
	RegionSign RegionSignType `json:"region_sign"` // 不是date_center

	// order uuid 双向绑定
	UID         int                  `json:"uid"`
	OrderUUID   string               `json:"order_uuid"`
	ProductUUID string               `json:"product_uuid"`
	RuntimeUUID ContainerRuntimeUUID `json:"runtime_uuid"`

	// GPU
	ReqGPUAmount   int   `json:"req_gpu_amount"`
	ExpandDataDisk int64 `json:"expand_data_disk"`

	// instance ------------------------------------------------------------------------

	// 付费方式
	ChargeType   ChargeType `json:"charge_type"`    // 付费类型
	ExpiredAt    *time.Time `json:"expired_at"`     // 包年包月等非按量付费的的到期时间, 不传指针, 生效与否取决于付费类型
	TakeEffectAt *time.Time `json:"take_effect_at"` // 上次包年包月等非按量付费的的到期时间，现在也是本次续费订单的开始生效时间。在判断是否该生效时，使用这个字段
	PaygPrice    int64      `json:"payg_price"`     // 按量付费的价格
	DeliverTime  time.Time  `json:"deliver_time"`   //如果是裸金属的话，这里传入交付时间

	// 克隆实例，是否拷贝数据盘
	CopyDataDiskAfterClone             bool `json:"copy_data_disk_after_clone,omitempty"`
	CopyDataDiskAfterCloneAdminOperate bool `json:"copy_data_disk_after_clone_admin_operate,omitempty"`

	// 容器优先级
	ContainerPriority PriorityType `json:"container_priority"`

	// 克隆实例，是否使用源实例的自定义服务的地址
	KeepSrcUserServiceAddressAfterClone bool   `json:"keep_src_user_service_address_after_clone,omitempty"`
	DeploymentUUID                      string `json:"deployment_uuid,omitempty"`

	// 等等...
	ServicePortProtocol string `json:"service_port_protocol"` // 协议，http、tcp
}

func (req *CreateContainerTaskRequest) Validate() bool {
	if req.MachineID == "" || req.Image == "" {
		return false
	}

	if req.ReqGPUAmount < 0 {
		return false
	}

	// and so on...

	return true
}

func (req *CreateContainerTaskRequest) GetDeploymentContainerPriority() PriorityType {
	if req.ContainerPriority != 0 {
		return req.ContainerPriority
	}
	return DevelopmentLevel

}

// ---------------- voucher ----------------------------

type VoucherType string
type VoucherStatus string
type ValidType string
type VoucherIssuingMethod string
type VoucherUseSku string
type UVUsedStatus string
type VoucherScopeOfApplicationType string

const (
	VoucherTypeRegister              VoucherType = "register"                // 注册券
	VoucherTypeInvitation            VoucherType = "invitation"              // 带新券
	VoucherTypeAppointment           VoucherType = "appointment"             // 定向券
	VoucherTypeExchange              VoucherType = "exchange"                // 兑换券
	VoucherTypeCommunityCoinExchange VoucherType = "community_coin_exchange" // 社区硬币兑换券
	VoucherTypeRechargeCashback      VoucherType = "recharge_cashback"       // 充值反现

	VoucherStatusReady VoucherStatus = "ready" // 未开始
	VoucherStatusDoing VoucherStatus = "doing" // 派券中
	VoucherStatusClose VoucherStatus = "done"  // 已截止

	ValidTypeRelative ValidType = "relative" // 相对时效 领取后几天
	ValidTypeAbsolute ValidType = "absolute" // 绝对时效 几号到几号

	VoucherIssuingBySystem VoucherIssuingMethod = "by_system" // 系统发放
	VoucherIssuingByAdmin  VoucherIssuingMethod = "by_admin"  // 主动发放
	VoucherIssuingByUser   VoucherIssuingMethod = "by_user"   // 主动领取

	VoucherUseSkuAll      VoucherUseSku = "all"      // 所有产品
	VoucherUseSkuPayg     VoucherUseSku = "payg"     // 容器实例（仅按量付费）
	VoucherUseSkuInstance VoucherUseSku = "instance" // 容器实例

	UVUsedStatusUnused  UVUsedStatus = "unused"  // 未使用
	UVUsedStatusUsing   UVUsedStatus = "using"   // 已使用
	UVUsedStatusUsed    UVUsedStatus = "used"    // 已用完
	UVUsedStatusRevoked UVUsedStatus = "revoked" // 已作废

	InviterGetVoucherLimit        = 100000 // 100 yuan
	InviterGetVoucherLimitForTest = 20     // 2 fen
	InvitationVoucherAsset        = 30000  // 30 yuan

	VoucherScopeOfApplicationTypeUniversal    VoucherScopeOfApplicationType = "universal"      // 全场通用
	VoucherScopeOfApplicationTypeRegionLimit  VoucherScopeOfApplicationType = "region_limit"   // 指定地区
	VoucherScopeOfApplicationTypeGpuTypeLimit VoucherScopeOfApplicationType = "gpu_type_limit" // 指定GPU型号

)

// --------------------- price ----------------------
const (
	NetDiskPricePerGB int64 = 300 // 0.3 yuan
)

type InvoiceOrderType int

const (
	DailyBillType   InvoiceOrderType = 1
	OrderChargeType InvoiceOrderType = 2
	ArrearOrderType InvoiceOrderType = 3
)

type CouponReceiveModeType string
type CouponType string
type ReceiveConditionType string
type UseConditionType string
type ScopeOfApplicationType string
type CouponUseChargeType string
type UCUsedStatus string
type CouponReceiveStatus string
type UCAvailableStatus string

const (
	CouponReceiveModeTypeOpen         CouponReceiveModeType = "open"           // 开放领取，每张券限领一张
	CouponReceiveModeTypeIssueByAdmin CouponReceiveModeType = "issue_by_admin" // 管理员发放，同一张券可以发放多次
	CouponReceiveModeTypeExchangeCode CouponReceiveModeType = "exchange_code"  // 兑换码生成

	CouponTypeDiscount           CouponType = "discount"             // 打折
	CouponTypeCashBack           CouponType = "cash_back"            // 满减
	CouponTypeCashBackRepeatedly CouponType = "cash_back_repeatedly" // 累次满减

	ReceiveConditionTypeNone         ReceiveConditionType = "none"                // 无门槛
	ReceiveConditionTypeDaysRecharge ReceiveConditionType = "days_recharge_limit" // 近多少天充值多少钱可以领取

	UseConditionTypeBillAsset UseConditionType = "bill_asset_limit" // 账单金额达到limit可以使用

	ScopeOfApplicationTypeUniversal    ScopeOfApplicationType = "universal"      // 全场通用
	ScopeOfApplicationTypeRegionLimit  ScopeOfApplicationType = "region_limit"   // 指定地区
	ScopeOfApplicationTypeMachineLimit ScopeOfApplicationType = "machine_limit"  // 指定主机
	ScopeOfApplicationTypeGpuTypeLimit ScopeOfApplicationType = "gpu_type_limit" // 指定GPU型号

	CouponUseChargeTypeAll    CouponUseChargeType = "all"    // 所有计费方式通用
	CouponUseChargeTypePayg   CouponUseChargeType = "payg"   // 按量付费
	CouponUseChargeTypePrepay CouponUseChargeType = "prepay" // 预付费

	UCUsedStatusUnused UCUsedStatus = "unused"
	UCUsedStatusUsed   UCUsedStatus = "used"

	CouponReceiveStatusOpen     CouponReceiveStatus = "open"
	CouponReceiveStatusWait     CouponReceiveStatus = "wait"
	CouponReceiveStatusReceived CouponReceiveStatus = "received"
	CouponReceiveStatusEmpty    CouponReceiveStatus = "empty"

	UCAvailable   UCAvailableStatus = "available"
	UCUnavailable UCAvailableStatus = "unavailable"
)

// ---------------- credit wallet -------------------------

type CreditWalletOperateType string

const (
	CreditWalletOperateLoan          CreditWalletOperateType = "loan"           // 借款
	CreditWalletOperateRepay         CreditWalletOperateType = "repay"          // 还款
	CreditWalletOperateCreate        CreditWalletOperateType = "create"         // 开通
	CreditWalletOperateIncreaseLimit CreditWalletOperateType = "increase_limit" // 提额
	CreditWalletOperateReduceLimit   CreditWalletOperateType = "reduce_limit"   // 降额
)

type BalanceStatementType string

const (
	BalanceStatementIncome      BalanceStatementType = "income"
	BalanceStatementExpenditure BalanceStatementType = "expenditure"
)

func BillTypeToBalanceStatementType(billType BillType) BalanceStatementType {
	switch billType {
	case BillTypeRecharge, BillTypeRefund:
		return BalanceStatementIncome
	case BillTypeCharge, BillTypeWithdraw:
		return BalanceStatementExpenditure
	default:
		return ""
	}
}

func BalanceStatementTypeToBillType(t BalanceStatementType) []BillType {
	switch t {
	case BalanceStatementIncome:
		return []BillType{BillTypeRecharge, BillTypeRefund}
	case BalanceStatementExpenditure:
		return []BillType{BillTypeCharge, BillTypeWithdraw}
	default:
		return []BillType{}
	}
}

const FakeRegionSignForPrivateImageCharge RegionSignType = "private-image"

func ChangeChargeTypeToCouponChargeType(chargeType ChargeType) CouponUseChargeType {
	var couponUseChargeType CouponUseChargeType
	switch chargeType {
	case ChargeTypePayg:
		couponUseChargeType = CouponUseChargeTypePayg
	case ChargeTypeDaily,
		ChargeTypeWeekly,
		ChargeTypeMonthly,
		ChargeTypeYearly,
		ChargeTypePrepay:
		couponUseChargeType = CouponUseChargeTypePrepay
	}
	return couponUseChargeType
}

func ChangeChargeTypeToVoucherChargeType(chargeType ChargeType) []CouponUseChargeType {
	var couponUseChargeType []CouponUseChargeType
	switch chargeType {
	case ChargeTypePayg:
		couponUseChargeType = []CouponUseChargeType{CouponUseChargeTypePayg, CouponUseChargeTypeAll}
	case ChargeTypeDaily,
		ChargeTypeWeekly,
		ChargeTypeMonthly,
		ChargeTypeYearly,
		ChargeTypePrepay:
		couponUseChargeType = []CouponUseChargeType{CouponUseChargeTypeAll}
	}
	return couponUseChargeType
}
