package constant

type InvoiceStatus string

const (
	InvoiceFinished  InvoiceStatus = "finished"
	InvoiceReviewing InvoiceStatus = "reviewing"
	InvoiceReject    InvoiceStatus = "reject"
	InvoiceRevoking  InvoiceStatus = "revoking"
	InvoiceRevoked   InvoiceStatus = "revoked"
)

var InvoiceStatusIndexMap = map[InvoiceStatus]int{InvoiceFinished: 0, InvoiceReject: 1, InvoiceReviewing: 2}

const InvoiceAmountMaxValue = 1000 * 1000
const InvoiceAmountMinValue = 1 * 1000
const InvoiceContent1 = "*信息技术服务*云服务器租赁服务费"
const InvoiceContent2 = "*信息技术服务*云服务器服务费"
const InvoiceContent3 = "*信息技术服务*云服务费"

type InvoiceContentType int

type InvoiceProperty string

const (
	ElectronicInvoice InvoiceProperty = "electronic_invoice"
	PaperInvoice      InvoiceProperty = "paper_invoice"

	InvoiceContentType1 InvoiceContentType = 1
	InvoiceContentType2 InvoiceContentType = 2
	InvoiceContentType3 InvoiceContentType = 3
)

func (i InvoiceContentType) Content() string {
	switch i {
	case InvoiceContentType1:
		return InvoiceContent1
	case InvoiceContentType2:
		return InvoiceContent2
	case InvoiceContentType3:
		return InvoiceContent3
	}
	return InvoiceContent1
}

type InvoiceType string

const (
	SpecialVATInvoice InvoiceType = "special_vat_invoice" // 增值税专用发票
	PlainVATInvoices  InvoiceType = "plain_vat_invoice"   // 增值税普通发票
)

func (i InvoiceType) String() string {
	return string(i)
}

type InvoiceMailType int

const (
	MailAddress  InvoiceMailType = 1
	EmailAddress InvoiceMailType = 2
)

type UserInvoiceType string

const (
	RechargingInvoice UserInvoiceType = "recharging_invoice" // 根据充值开票
	BillInvoice       UserInvoiceType = "bill_invoice"       // 根据账单开票
)
