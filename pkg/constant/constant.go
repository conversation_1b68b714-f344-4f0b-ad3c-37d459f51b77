package constant

import "strings"

const (
	FormatTimeString     = "2006-01-02 15:04:05"
	FormatDateString     = "2006-01-02"
	FormatDateTimeString = "2006-01-02 00:00:00"

	GInKB int64 = 1024 * 1024
)

type ProductType string

const (
	ProductTypeAllFake               ProductType = "all"
	ProductTypeInstance              ProductType = "instance"
	ProductTypeNetDisk               ProductType = "net_disk"
	ProductTypeDataDisk              ProductType = "data_disk"
	ProductTypeFileStorage           ProductType = "file_storage"
	ProductTypeDeployment            ProductType = "deployment"
	ProductTypeDeploymentDurationPkg ProductType = "deployment_duration_pkg"
	ProductTypePrivateImage          ProductType = "private_image"
)

type Platform string

const (
	PlatformPC Platform = "pc"
)

const (
	ProtocolHTTP = "http"
	ProtocolTcp  = "tcp"
)

func BuildHttpUrl(domain, router string) string {
	if domain == "" {
		return ""
	}

	if !strings.HasPrefix(domain, "http") {
		domain = "https://" + domain
	}

	strings.TrimSuffix(domain, "/")

	if !strings.HasPrefix(router, "/") {
		router = "/" + router
	}

	return domain + router
}
