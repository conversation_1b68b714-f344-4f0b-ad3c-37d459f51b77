package constant

import (
	"encoding/json"
	"strings"
)

type MQCoreToBusinessOptType string

const (
	// MQCoreToBusinessUpdateContainerStatusOpt payload 解析为 pkg-core/container_runtime/model/container_hook.go:DoHookParams
	MQCoreToBusinessUpdateContainerStatusOpt MQCoreToBusinessOptType = "mq_core_to_business_update_container_status_opt"

	// MQCoreToBusinessDeleteInstanceRecordOpt payload 解析为 pkg-core/container_runtime/model/container_hook.go:DoHookParams
	MQCoreToBusinessDeleteInstanceRecordOpt MQCoreToBusinessOptType = "mq_core_to_business_delete_instance_record_opt"

	// MQCoreToBusinessModifyMachineOpt payload 解析为 MQCoreBusinessModifyDataPayload
	MQCoreToBusinessModifyMachineOpt MQCoreToBusinessOptType = "mq_core_to_business_modify_machine_opt"

	// MQCoreToBusinessModifyDataDiskOpt payload 解析为 MQCoreBusinessModifyDataPayload
	MQCoreToBusinessModifyDataDiskOpt MQCoreToBusinessOptType = "mq_core_to_business_modify_data_disk_opt"

	// MQCoreToBusinessModifyGpuTypeOpt payload 解析为 MQCoreBusinessModifyDataPayload
	MQCoreToBusinessModifyGpuTypeOpt MQCoreToBusinessOptType = "mq_core_to_business_modify_gpu_type_opt"

	// MQCoreToBusinessModifyRegionOpt payload 解析为 MQCoreBusinessModifyDataPayload
	MQCoreToBusinessModifyRegionOpt MQCoreToBusinessOptType = "mq_core_to_business_modify_region_opt"

	// MQCoreToBusinessInitFileStorageOpt payload 解析为 MQCoreBusinessInitFileStoragePayload
	MQCoreToBusinessInitFileStorageOpt MQCoreToBusinessOptType = "mq_core_to_business_init_file_storage_opt"

	// MQCoreToBusinessUploadImageProcessOpt payload 解析为 constant.UploadOssInfo
	MQCoreToBusinessUploadImageProcessOpt MQCoreToBusinessOptType = "mq_core_to_business_upload_image_progress_opt"

	// MQCoreToBusinessUploadImageCancelOpt payload 解析为 constant.CancelUploadImageInfo
	MQCoreToBusinessUploadImageCancelOpt MQCoreToBusinessOptType = "mq_core_to_business_upload_image_cancel_opt"
)

type MQCoreToBusinessUpdateContainerStatusReq struct {
	Tenant    string                  `json:"tenant"`
	MachineID string                  `json:"machine_id"`
	OptType   MQCoreToBusinessOptType `json:"opt_type"`
	Payload   string                  `json:"payload"` // 根据 opt 选择如何反序列化.
	Caller    OptCaller               `json:"caller"`
}

type MQCoreToBusinessModifyDataReq struct {
	Tenant  string                  `json:"tenant"`
	OptType MQCoreToBusinessOptType `json:"opt_type"`
	Payload string                  `json:"payload"` // 根据 opt 选择如何反序列化.
	Caller  OptCaller               `json:"caller"`
}

// payload

const (
	DBInsert = "insert"
	DBUpdate = "update"
	DBDelete = "delete"
)

type ModelKey string

func (m ModelKey) String() string {
	return string(m)
}

const (
	ModelKeyMachine  ModelKey = "machine_id"
	ModelKeyRegion   ModelKey = "sign"
	ModelKeyDataDisk ModelKey = "machine_id"
	ModelKeyGpuType  ModelKey = "id"
)

type MQCoreBusinessModifyDataPayload struct {
	DBAction string                 `json:"db_action"`
	Key      ModelKey               `json:"key"`
	Data     map[string]interface{} `json:"data"`
}

func (m *MQCoreBusinessModifyDataPayload) ParseFromContent(payload string) error {
	return json.Unmarshal([]byte(payload), m)
}

type MQCoreBusinessInitFileStoragePayload struct {
	RS        RegionSignType `json:"rs"`
	UID       int            `json:"uid"`
	IsSuccess bool           `json:"is_success"`
	ErrInfo   string         `json:"err_info"`
	OrderUUID string         `json:"order_uuid"`
	FsType    FsType         `json:"fs_type"`
}

func (m *MQCoreBusinessInitFileStoragePayload) ParseFromContent(payload string) error {
	return json.Unmarshal([]byte(payload), m)
}

func (m *MQCoreBusinessInitFileStoragePayload) String() string {
	b, _ := json.Marshal(m)
	return string(b)
}

// tenant 租户

const (
	TenantAutoDL  string = "autodl"
	TenantGPUHub  string = "gpuhub"
	TenantGPUFree string = "gpufree"
)

func GetTenants() []string {
	return []string{TenantAutoDL}
}

func GetTenantsFromString(s string) []string {
	if strings.Contains(s, TenantAutoDL) {
		return []string{TenantAutoDL}
	} else if strings.Contains(s, TenantGPUHub) {
		return []string{TenantGPUHub}
	} else if strings.Contains(s, TenantGPUFree) {
		return []string{TenantGPUFree}
	} else {
		return GetTenants()
	}

}

// 事务

type TxStatus string

const (
	TxStatusPrepared  TxStatus = "prepared"
	TxStatusRollback  TxStatus = "rollback"
	TxStatusCommitted TxStatus = "committed"
)

type DataDiskOperateType string

const (
	DataDiskReserve DataDiskOperateType = "reserve"
	DataDiskRelease DataDiskOperateType = "release"
)
