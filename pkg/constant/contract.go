package constant

type ContractType string

const (
	FrameworkContract   ContractType = "framework_contract"    // 框架合同
	FixedAmountContract ContractType = "fixed_amount_contract" // 固定金额合同
)

type BusinessType string

const (
	BareMetal     BusinessType = "bare_metal"     // 裸金属
	ServerHosting BusinessType = "server_hosting" // 服务器托管
	NFSStorage    BusinessType = "nfs_storage"    // nfs专用存储
)

type ContractStatus string

const (
	DraftContract      ContractStatus = "draft"      // 草稿合同
	InEffectContract   ContractStatus = "in_effect"  // 生效中合同
	ExpiredContract    ContractStatus = "expired"    // 已过期合同
	TerminatedContract ContractStatus = "terminated" // 已终止合同
)

type ContractBillStatus string

const (
	WaitUploadContractBill          ContractBillStatus = "wait_upload"           // 待上传账单
	WaitCustomerConfirmContractBill ContractBillStatus = "wait_customer_confirm" // 待客户确认账单
	WaitInvoiceContractBill         ContractBillStatus = "wait_invoice"          // 待开票
	WaitPayContractBill             ContractBillStatus = "wait_pay"              // 待付款
	PaidContractBill                ContractBillStatus = "paid"                  // 已支付
)

type ProgressBillStatus string

const (
	WaitPartyBHandle ProgressBillStatus = "wait_party_b_handle" // 待我们处理（乙方）
	WaitPartyAHandle ProgressBillStatus = "wait_party_a_handle" // 待客户处理（甲方）
	Completed        ProgressBillStatus = "completed"           // 已完成（无待处理）
)
