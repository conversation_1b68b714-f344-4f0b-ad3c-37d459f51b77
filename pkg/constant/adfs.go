package constant

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"
)

const (
	ADFSDefaultLimited   = 1
	ADFSDefaultQuotaSize = "200GiB"
	ADFSDefaultInodeSize = 500000

	ADFSAdminLimited   = 6
	ADFSAdminQuotaSize = "10TiB"
	ADFSAdminInodeSize = 3000000

	// 修改时需要与上面同步
	ADFSFreeSize     = 20 * 1024 * 1024 * 1024        // 免费大小
	ADFSDefaultSize  = 200 * 1024 * 1024 * 1024       // 默认容量
	ADFSMaxSize      = 50 * 1024 * 1024 * 1024 * 1024 // 最大容量
	ADFSDefaultInode = 200000                         // 默认inode
	ADFSMaxInode     = 100000000                      // 最大inode

	// ADFSKVAllUserDayMaxUsageKey 所有用户的当天的峰值数据key
	ADFSKVAllUserDayMaxUsageKey = "adfs-%s-%d-%d-%d"

	// ADFSKVAllUserUsageInfoKey 所有用户的数据使用信息key
	ADFSKVAllUserUsageInfoKey = "adfs-usage-info-%s"

	ContainerEnvRegionSignForAdfs           = "AutoDlRegionSign"
	ContainerEnvUrlForAdfs                  = "AutoDlUrl"
	ContainerEnvUidForAdfs                  = "AutoDlUid"
	ContainerEnvContainerRuntimeTypeForAdfs = "AutoDlContainerRuntimeType"
)

// GetADFSCommand Command: "bash /root/mount.sh quota-1 192.168.65.41:8888 2 10GiB 1000",
func GetADFSCommand(uid int, filerAddr string, quotaSizeOverwrite string, quotaInodeOverwrite, concurrentLimit int) string {
	mountPath := fmt.Sprintf("quota-%d", uid)
	limit := ADFSDefaultLimited

	if concurrentLimit != 0 {
		limit = concurrentLimit
	}

	quotaSize := quotaSizeOverwrite
	inodeSize := quotaInodeOverwrite

	if len(quotaSize) == 0 || inodeSize == 0 {
		quotaSize = ADFSDefaultQuotaSize
		inodeSize = ADFSDefaultInodeSize
		if uid == 1 {
			limit = ADFSAdminLimited
			quotaSize = ADFSAdminQuotaSize
			inodeSize = ADFSAdminInodeSize
		}
	}

	var readOnly = false

	hash := AdfsOptionCheckSum(mountPath, quotaSize, uint64(inodeSize), readOnly)

	filers := strings.Split(filerAddr, ",")
	if len(filers) > 0 {
		rand.Seed(time.Now().UnixNano())
		filerAddr = filers[rand.Intn(len(filers))]
	}

	formatStr := "bash /root/mount.sh %s %s %d %s %d -readOnly=%v %s"
	return fmt.Sprintf(formatStr, mountPath, filerAddr, limit, quotaSize, inodeSize, readOnly, hash)
}

func AdfsOptionCheckSum(mountPath string, quota string, inode uint64, readOnly bool) string {
	mountPath = "/" + strings.TrimLeft(mountPath, "/")
	payload := fmt.Sprintf("autodl-check-sum-%s-%s-%d-%v", mountPath, quota, inode, readOnly)
	return Md5String([]byte(payload))
}

func Md5(data []byte) []byte {
	hash := md5.New()
	hash.Write(data)
	return hash.Sum(nil)
}

func Md5String(data []byte) string {
	return fmt.Sprintf("%x", Md5(data))
}

// adfs kv redis struct

type ADFSKV struct {
	UID   int   `json:"uid"`
	Usage int64 `json:"usage"`
}

// 用户文件存储状态

type FileStorageStatusType string

// TODO: 状态的设置与挂载方式有关
const (
	FileStorageUninitialized FileStorageStatusType = "uninitialized"
	FileStorageCreating      FileStorageStatusType = "creating"
	FileStorageCreateFailed  FileStorageStatusType = "create_failed"
	FileStorageCreated       FileStorageStatusType = "created"
)

// ADFS size

// 计费根据使用量计算应该花费的钱（返回值格式类似于 10.95元*1000=10950）
func ADFSCost(usage int64) int64 {
	if usage <= 0 {
		return 0
	}

	var notFree int64
	if usage > ADFSFreeSize { // 超出免费大小
		notFree = usage - ADFSFreeSize
	} else {
		return 0
	}

	gb := float64(1024 * 1024 * 1024)

	// 字节换算成 GB，保留小数点后两位数
	notFreeGB, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", float64(notFree)/gb), 64)
	cost, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", notFreeGB*0.01), 64)

	return int64(cost * 1000)
}

// messenger payload

type FSInit struct {
	UID        int            `json:"uid"`
	RegionSign RegionSignType `json:"region_sign"`
}

func (a FSInit) String() string {
	b, _ := json.Marshal(a)
	return string(b)
}

type FSInitResult struct {
	UID        int            `json:"uid"`
	IsSucceed  bool           `json:"is_succeed"`
	RegionSign RegionSignType `json:"region_sign"`
	Msg        string         `json:"msg"`
}

type ADFSSyncUsage struct {
	RegionSign RegionSignType            `json:"region_sign"`
	UsageInfo  map[int]ADFSSyncUsageData `json:"usage_info"`
}

type ADFSSyncUsageData struct {
	UID          int   `json:"uid"`
	MaxUsage     int64 `json:"max_usage"`
	CurrentUsage int64 `json:"current_usage"`
	QuotaSize    int64 `json:"quota_size"`
	QuotaInode   int64 `json:"quota_inode"`
	Size         int64 `json:"size"` // size和上面的current_usage一样
	Inode        int64 `json:"inode"`
}

func (r ADFSSyncUsage) Marshal() string {
	b, _ := json.Marshal(r)
	return string(b)
}

type ADFSSetQuota struct {
	UID        int            `json:"uid"`
	RegionSign RegionSignType `json:"region_sign"`
	QuotaSize  int64          `json:"quota_size"`
	QuotaInode int64          `json:"quota_inode"`
}

func (r ADFSSetQuota) Marshal() string {
	b, _ := json.Marshal(r)
	return string(b)
}

type ADFSMkdir struct {
	UID        int            `json:"uid"`
	RegionSign RegionSignType `json:"region_sign"`
	DirPath    string         `json:"dir_path"`
}

func (r ADFSMkdir) String() string {
	b, _ := json.Marshal(r)
	return string(b)
}

type ADFSUserUsage struct {
	MaxUsage     int64 `json:"max_usage"`
	CurrentUsage int64 `json:"current_usage"`
	QuotaSize    int64 `json:"quota_size"`
	QuotaInode   int64 `json:"quota_inode"`
	Size         int64 `json:"size"` // size和上面的current_usage一样
	Inode        int64 `json:"inode"`
}

type ADFSUserDetail struct {
	QuotaSize  int64 `json:"quota_size"`
	QuotaInode int64 `json:"quota_inode"`
	Size       int64 `json:"size"`
	Inode      int64 `json:"inode"`
}
