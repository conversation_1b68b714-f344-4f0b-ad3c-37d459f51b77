package constant

import "regexp"

type InstanceFileTransferOptRequest struct {
	SrcInstanceUUID InstanceUUIDType `form:"src_instance_uuid" json:"src_instance_uuid" binding:"required"` // 数据来源的实例
	SrcPath         string           `form:"src_path" json:"src_path"`                                      // copy文件的路径，默认为*
	DstInstanceUUID InstanceUUIDType `form:"dst_instance_uuid" json:"dst_instance_uuid" binding:"required"` // 数据目的地的实例
	DstPath         string           `form:"dst_path" json:"dst_path"`                                      // 目标路径，默认为/
	IsTransferDiff  bool             `form:"is_transfer_diff" json:"is_transfer_diff"`                      // 是否克隆系统盘
}

func (i InstanceFileTransferOptRequest) ValidatePath() bool {
	// 匹配 ../ 或绝对路径
	pattern := `^[0-9a-zA-Z_/\-*]*$`
	matched, _ := regexp.MatchString(pattern, i.SrcPath)
	return matched
}
