package constant

import (
	"encoding/json"
	"fmt"

	"math"
	"server/pkg-agent/agent_constant"
	intl "server/pkg/international"
	"server/pkg/logger"
	"strings"
	"time"
)

const RuntimeUUIDPrefix = "container"

type ContainerRuntimeUUID string

func NewContainerRuntimeUUID(sth string) ContainerRuntimeUUID {
	return ContainerRuntimeUUID(sth)
}

func (u ContainerRuntimeUUID) String() string {
	return string(u)
}

type ContainerStatusType string

type ContainerSubStatusType string

func (t ContainerStatusType) String() string {
	return string(t)
}

func (t ContainerSubStatusType) String() string {
	return string(t)
}

/**
 * START MODE
 */

// ContainerStartMode 兼容旧有接口, 正常启动可以传空字符串, 无卡等一定要传具体模式.
type ContainerStartMode string

const (
	ContainerStartWithGPU    ContainerStartMode = "gpu" // default
	ContainerStartWithoutGPU ContainerStartMode = "non_gpu"
)

func NewContainerStartMode(s string) ContainerStartMode {
	mode := ContainerStartMode(s)
	if len(mode) == 0 {
		mode = ContainerStartWithGPU // default
	}

	return mode
}

func (t ContainerStartMode) String() string {
	return string(t)
}

func (t ContainerStartMode) IsGPU() bool {
	return t == ContainerStartWithGPU
}

func (t ContainerStartMode) IsNonGPU() bool {
	return t == ContainerStartWithoutGPU
}

// Available 增加新 mode 时也要加在此处.
func (t ContainerStartMode) Available() bool {
	switch t {
	case ContainerStartWithGPU,
		ContainerStartWithoutGPU:
		return true
	}
	return false
}

/**
 * 基本从 docker 状态派生出来. 然后 instance 和闲时任务模块再基于本状态派生.
 */

const (
	ContainerRecordInserted ContainerStatusType = "record_inserted" // 字段创建

	// 创建时

	ContainerCreating ContainerStatusType = "creating" // 创建中

	ContainerPullingInCreating ContainerSubStatusType = "pulling_in_creating" // 创建中子状态: 1. 同步镜像中
	ContainerPulledInCreating  ContainerSubStatusType = "pulled_in_creating"  // 创建中子状态: 2. 同步镜像完毕, 开始创建容器

	ContainerPullFailedInCreateFailed   ContainerSubStatusType = "pull_failed"   // 创建失败子状态: 1. 镜像拉取失败
	ContainerCreateFailedInCreateFailed ContainerSubStatusType = "create_failed" // 创建失败子状态: 2. 实例创建失败

	ContainerCreateFailed ContainerStatusType = "create_failed" // 创建失败
	ContainerCreated      ContainerStatusType = "created"       // 创建完成 -> docker created

	// 克隆中

	ContainerCloneLocked  ContainerStatusType = "stopped_cloning"       // 克隆 - 原实例锁定状态
	ContainerCloning      ContainerStatusType = "created_cloning"       // 克隆 - 目标实例 created -> 克隆中
	ContainerCloneFailed  ContainerStatusType = "created_clone_failed"  // 克隆 - 目标实例
	ContainerCloneSuccess ContainerStatusType = "created_clone_success" // 克隆 - 目标实例

	//   目标实例通知源实例上传 diff
	//ContainerMigratingOfDiffUploading ContainerStatusType = "diff_uploading"
	//ContainerMigratingOfDiffUploaded ContainerStatusType = "diff_uploaded"

	//   目标实例下载 diff, 最后自动开机, 相当于还原被打断的 created -> starting 的路径
	//ContainerMigratingOfDiffMerging ContainerStatusType = "diff_merging"
	//ContainerMigratingOfDiffMerged ContainerStatusType = "diff_merged"

	// 运行中

	ContainerStarting   ContainerStatusType = "starting"   // 开机中
	ContainerRestarting ContainerStatusType = "restarting" // 重启中 -> docker restarting
	ContainerRunning    ContainerStatusType = "running"    // 运行中 -> docker running
	ContainerStopping   ContainerStatusType = "stopping"   // 关机中

	// 关机后

	ContainerStopped                ContainerStatusType = "stopped"                   // 已关机 -> docker paused, dead, exited
	ContainerStoppedByStartingError ContainerStatusType = "stopped_by_starting_error" // 已关机-开机异常
	ContainerStoppedByRunningError  ContainerStatusType = "stopped_by_running_error"  // 已关机-意外宕机

	// 移除

	ContainerRemoving ContainerStatusType = "removing" // -> docker removing
	ContainerRemoved  ContainerStatusType = "removed"  // 在数据库中已软删除, 不会显示到前端

	// 初始化

	ContainerReInitializing ContainerStatusType = "re_initializing" // 初始化中
	ContainerReInitialized  ContainerStatusType = "re_initialized"  // 初始化完成
	ContainerReInitFailed   ContainerStatusType = "re_init_failed"  // 初始化失败, 如果初始化成功则状态变为关机.

	ContainerOssOfDiffMerging ContainerStatusType = "oss_diff_merging"
	ContainerOssOfDiffMerged  ContainerStatusType = "oss_diff_merged"

	// 重试可以通过对源容器的状态判断来得知进行到哪一阶段.

	ContainerOssMergeFailed ContainerStatusType = "oss_merge_failed"

	// not found

	ContainerUnknown ContainerStatusType = "unknown"
	ContainerLost    ContainerStatusType = "lost"
)

func (t ContainerStatusType) SimplifiedToString(containerTaskType ContainerRuntimeType) string {
	switch containerTaskType {
	case ContainerRuntimeOfInstance:
		return t.SimplifiedForInstance().String()
	case ContainerRuntimeOfDeployment:
		return t.SimplifiedForDC().String()
	default:
		return t.SimplifiedForInstance().String()
	}
}

func (t ContainerSubStatusType) SimplifiedToString(containerTaskType ContainerRuntimeType) string {
	switch containerTaskType {
	case ContainerRuntimeOfInstance:
		return t.SimplifiedForInstance().String()
	default:
		return t.SimplifiedForInstance().String()
	}
}

// SimplifiedForInstance 为 instance 模块简化状态, 也和 instance hook 相关联.
func (t ContainerStatusType) SimplifiedForInstance() (smp InstanceStatusType) {
	switch t {
	case ContainerRecordInserted:
		smp = InstanceRecordInserted

	case ContainerCreating:
		smp = InstanceCreating

	case ContainerCreateFailed:
		smp = InstanceCreateFailed

	case ContainerCreated:
		smp = InstanceCreated

	case ContainerStarting, ContainerOssOfDiffMerging:
		smp = InstanceStarting

	case ContainerRestarting:
		smp = InstanceRestarting

	case ContainerRunning:
		smp = InstanceRunning

	case ContainerStopping:
		smp = InstanceShuttingDown

	case ContainerStopped, ContainerReInitialized:
		smp = InstanceShutdown

	case ContainerStoppedByStartingError:
		smp = InstanceShutdownByStartingError

	case ContainerStoppedByRunningError:
		smp = InstanceShutdownByRunningError

	case ContainerReInitializing:
		smp = InstanceReInitializing

	case ContainerReInitFailed:
		smp = InstanceReInitFailed

	case ContainerRemoving:
		smp = InstanceRemoving

	case ContainerRemoved:
		smp = InstanceRemoved

	case ContainerLost:
		smp = InstanceLost

	case ContainerOssOfDiffMerged:
		smp = InstanceOssMerged

	case ContainerOssMergeFailed:
		smp = InstanceShutdownByStartingError

	case ContainerCloning:
		smp = InstanceCreatedCloning

	case ContainerCloneFailed:
		smp = InstanceCreatedCloneFailed

	case ContainerCloneSuccess:
		smp = InstanceCreatedCloneSuccess

	case ContainerCloneLocked:
		smp = InstanceShutdownCloning

	default:
		smp = InstanceStatusType(t)
	}
	return
}

func (t ContainerSubStatusType) SimplifiedForInstance() (smp InstanceStatusType) {
	switch t {
	case ContainerPullingInCreating:
		smp = InstancePullingInCreating
	case ContainerPulledInCreating:
		smp = InstancePulledInCreating
	default:
		smp = InstanceStatusType(t)
	}

	return
}

// SimplifiedForDC 为弹性部署模块简化状态
func (t ContainerStatusType) SimplifiedForDC() (smp DCStatus) {
	switch t {
	case ContainerRecordInserted, ContainerCreating:
		smp = DCCreating

	case ContainerCreateFailed:
		smp = DCCreateFailed

	case ContainerCreated:
		smp = DCCreated

	case ContainerStarting, ContainerOssOfDiffMerging:
		smp = DCStarting

	case ContainerRestarting:
		smp = DCRestarting

	case ContainerRunning:
		smp = DCRunning

	case ContainerStopping:
		smp = DCShuttingDown

	case ContainerStopped:
		smp = DCShutdown

	case ContainerReInitialized:
		smp = DCReInitialized

	case ContainerStoppedByStartingError:
		smp = DCShutdownByStartingError

	case ContainerStoppedByRunningError:
		smp = DCShutdownByRunningError

	case ContainerReInitializing:
		smp = DCReInitializing

	case ContainerReInitFailed:
		smp = DCReInitFailed

	case ContainerRemoving:
		smp = DCRemoving

	case ContainerRemoved:
		smp = DCRemoved

	case ContainerLost:
		smp = DCLost

	case ContainerOssOfDiffMerged:
		smp = DCOssMerged

	case ContainerCloneFailed:
		smp = DCMigrateFailed

	case ContainerOssMergeFailed:
		smp = DCShutdownByStartingError

	case ContainerCloneLocked:
		smp = DCAbort
	default:
		smp = DCStatus(t)
	}
	return
}

// IsRun 判断容器是否在运行中
func (t ContainerStatusType) IsRun() bool {
	switch t {
	case ContainerCreating, ContainerCreated, ContainerStarting, ContainerRestarting, ContainerRunning:
		return true
	}
	return false
}

// IsRunningOrStarting 用于判断有启动数量限制的, 如无 GPU 启动功能
func (t ContainerStatusType) IsRunningOrStarting() bool {
	switch t {
	case ContainerStarting, ContainerRestarting, ContainerRunning:
		return true
	}
	return false
}

// IsStop 判断容器是否停止
func (t ContainerStatusType) IsStop() bool {
	switch t {
	case ContainerCreateFailed, ContainerStopping, ContainerStopped, ContainerRemoving, ContainerRemoved,
		ContainerStoppedByStartingError, ContainerStoppedByRunningError:
		return true
	}
	return false
}

// IsStopFinished 判断容器是否已经完成停止
func (t ContainerStatusType) IsStopFinished() bool {
	switch t {
	case ContainerCreateFailed, ContainerStopped, ContainerRemoved,
		ContainerStoppedByStartingError, ContainerStoppedByRunningError:
		return true
	}
	return false
}

func (t ContainerStatusType) IsClone() bool {
	switch t {
	case ContainerCloneLocked,
		ContainerCloneSuccess,
		ContainerCloning,
		ContainerCloneFailed:
		return true
	}
	return false
}

func (t ContainerStatusType) OptingTypeDefaultResultIsSucceed() bool {
	switch t {
	case ContainerStopping:
		return true
	case ContainerRemoving: // special
		return true
	case ContainerStarting:
		return false
	}
	return false
}

// IsCronFrozenType 无法被轮询更新, 包括 ing 状态
func (t ContainerStatusType) IsCronFrozenType() bool {
	switch t {
	case ContainerCreateFailed,
		ContainerReInitFailed,
		ContainerReInitialized,
		ContainerStopped,
		ContainerStoppedByStartingError,
		ContainerStoppedByRunningError,
		ContainerRemoving,
		ContainerRemoved:
		return true
	}

	if t.IsClone() {
		return true
	}

	return false
}

func (t ContainerStatusType) noNeedToDoAnyOpt() bool {
	return t == ContainerRemoved
}

func (t ContainerStatusType) noNeedToDoStopOpt() bool {
	switch t {
	case ContainerStopping,
		ContainerStopped,
		ContainerStoppedByStartingError,
		ContainerStoppedByRunningError:
		return true
	}

	return false
}

func (t ContainerStatusType) noNeedToDoStartOpt() bool {
	switch t {
	case ContainerStarting,
		ContainerRunning:
		return true
	}

	return false
}

func (t ContainerStatusType) noNeedToDoReInitOpt() bool {
	switch t {
	case ContainerReInitializing, ContainerCreating:
		return true
	}

	return false
}

func (t ContainerStatusType) noNeedToDoCreateOpt() bool {
	switch t {
	case ContainerCreating, ContainerCreated, ContainerCreateFailed:
		return true
	}

	return false
}

func (t ContainerStatusType) noNeedToDoMigrateOpt() bool {
	switch t {
	case ContainerCloneLocked, ContainerStopped, ContainerReInitialized, ContainerCreated:
		return false
	}
	return true
}

func (t ContainerStatusType) MayNotFoundType() bool {
	switch t {
	case ContainerCreating, ContainerReInitializing, ContainerReInitFailed, ContainerCreateFailed, "":
		return true
	}

	return false
}

// IsUsefulCommandOpt 防止重复操作
func (t ContainerStatusType) IsUsefulCommandOpt(opt OptType) bool {
	if t.noNeedToDoAnyOpt() {
		return false
	}

	switch opt {
	case ContainerCreateOpt:
		if t.noNeedToDoCreateOpt() {
			return false
		}
	case ContainerStartOpt:
		if t.noNeedToDoStartOpt() {
			return false
		}
	case ContainerReInitOpt:
		if t.noNeedToDoReInitOpt() {
			return false
		}
	case ContainerStopOpt:
		if t.noNeedToDoStopOpt() {
			return false
		}
	case ContainerRemoveOpt:
		if t.noNeedToDoAnyOpt() {
			return false
		}
	case ContainerCloneOpt:
		if t.noNeedToDoMigrateOpt() {
			return false
		}
	case ContainerRestartOpt:
		// TODO: next version
	}
	return true
}

func (t ContainerStatusType) ShouldStopCharging() bool {
	switch t {
	case ContainerStopping, ContainerStopped, ContainerStoppedByStartingError, ContainerStoppedByRunningError, ContainerRemoving:
		return true
	}
	return false

}

func NewContainerStatusByOptionResult(opt OptType, oldStatus ContainerStatusType, isSucceed bool) (newStatus ContainerStatusType) {
	newStatus = oldStatus
	l := logger.NewLogger("Migrate Opt")
	// TODO: 继续测试其他 SUCCEED 可以稳定依靠底层返回的状态, 减少依据操作结果直接判断的情况.
	switch opt {
	case ContainerCreateOpt:
		if oldStatus == ContainerCreating {
			newStatus = ContainerCreated
			if !isSucceed {
				newStatus = ContainerCreateFailed
			}
		}

	case ContainerStartOpt:
		if oldStatus == ContainerStarting {
			// succeed 不修改, 等待底层返回 running
			// 启动容器操作成功距离容器运行起来还有一段时间, 等待底层返回running状态更为稳妥
			if !isSucceed {
				newStatus = ContainerStoppedByStartingError
			}
		}

	case ContainerStopOpt:
		if oldStatus == ContainerStopping {
			// 立刻认为关机成功, 尽早释放资源
			newStatus = ContainerStopped
			// 关机失败不再认为是意外宕机
			//if !isSucceed {
			//	newStatus = ContainerStoppedByRunningError
			//}
		}

	case ContainerReInitOpt:
		if oldStatus == ContainerCreating {
			// 立刻认为重新创建成功
			newStatus = ContainerCreated
			if !isSucceed {
				newStatus = ContainerCreateFailed
			}
		} else if oldStatus == ContainerReInitializing {
			// 正常状态下的初始化, 立刻认为成功
			newStatus = ContainerReInitialized
			if !isSucceed {
				newStatus = ContainerReInitFailed
			}
		}

	case ContainerRemoveOpt:
		if oldStatus == ContainerRemoving {
			// 由于产品未考虑移除失败, 所以不论成功失败都认为移除了且释放资源.
			newStatus = ContainerRemoved
		}

	case ContainerRestartOpt:
		if oldStatus == ContainerRestarting {
			// wait for true running
			if !isSucceed {
				newStatus = ContainerStoppedByStartingError
			}
		}
	//case ContainerUploadDiffOpt:
	//	if oldStatus == ContainerMigratingOfDiffUploading {
	//		if isSucceed {
	//			l.Trace("ContainerMigratingOfDiffUploading -> (ContainerUploadDiffOpt succeed) -> ContainerMigratingOfDiffUploaded.")
	//			newStatus = ContainerMigratingOfDiffUploaded
	//		} else {
	//			newStatus = ContainerCloneFailed
	//		}
	//	}
	case ContainerMergeDiffOpt:
		//if oldStatus == ContainerMigratingOfDiffMerging {
		//	if isSucceed {
		//		l.Trace("ContainerMigratingOfDiffMerging -> (ContainerMergeDiffOpt succeed) -> ContainerMigratingOfDiffMerged.")
		//		newStatus = ContainerMigratingOfDiffMerged
		//	} else {
		//		l.Trace("ContainerMigratingOfDiffMerging -> (ContainerMergeDiffOpt failed) -> ContainerCloneFailed.")
		//		newStatus = ContainerCloneFailed
		//	}
		//} else
		if oldStatus == ContainerOssOfDiffMerging {
			if isSucceed {
				l.Trace("ContainerOssOfDiffMerging -> (ContainerMergeOssDiffOpt succeed) -> ContainerOssOfDiffMerged.")
				newStatus = ContainerOssOfDiffMerged
			} else {
				l.Trace("ContainerOssOfDiffMerging -> (ContainerMergeOssDiffOpt failed) -> ContainerOssMergeFailed.")
				newStatus = ContainerOssMergeFailed
			}
		}
	}

	return
}

/*OptType
 * 如果后续新增操作会导致状态变化, 必须加入到 CanCauseStatusChanged() 中.
 * 新操作必须分类, 以分流交给不同的 switch 处理.
 * 新操作需要存储操作历史的, 必须加入 NeedHistory() 函数.
 */
type OptType string

// 普通操作
const (
	/*
	 * CRUD 类
	 */

	ContainerInsertRecordOpt OptType = "insert_container_record" // sync 创建实例数据库字段

	/*
	 * 命令操作类
	 */

	ContainerCreateOpt     OptType = "create_container"     // 创建实例
	ContainerStartOpt      OptType = "start_container"      // 开机
	ContainerStopOpt       OptType = "stop_container"       // 关机
	ContainerReInitOpt     OptType = "re_init_container"    // 初始化/重新创建, 实现方式是先删除, 保留卷, 再创建
	ContainerRemoveOpt     OptType = "remove_container"     // 释放, 此时会自动释放所有资源.
	ContainerRestartOpt    OptType = "restart_container"    // 重启. 原型图中还没有针对重启的操作... next version...
	DataTransferType       OptType = "data_transfer"        // 转移数据
	DataTransferCancelType OptType = "data_transfer_cancel" // 转移数据

	/*
	 * 命令操作类 - 克隆
	 */

	ContainerCloneOpt      OptType = "clone_container"
	ContainerUploadDiffOpt OptType = "upload_diff"

	ContainerMergeDiffOpt OptType = "merge_diff"

	ContainerCleanDiffOpt OptType = "clean_diff" // 非操作，不涉及状态变化

	/*
	 * 更新类
	 */

	ContainerDCUpdateContainerParamOpt OptType = "dc_update_container_param" // 弹性部署复用，更新容器运行参数. payload: constant.ContainerStatusUpdateReq
	ContainerUpdateStatusOpt           OptType = "update_status"             // 普通地更新实例状态. payload: constant.ContainerStatusUpdateReq
	ContainerUpdateStatusByResultOpt   OptType = "update_status_by_result"   // 依据创建, 停止等操作的结果来更新实例状态. payload: constant.ContainerUpdateStatusByResultOptReq

	ContainerReleaseGpuOpt        OptType = "release_gpu_opt" // 特殊: 上层控制释放 GPU. no payload
	ContainerReserveGpuByOrderOpt OptType = "reserve_gpu_opt" // 特殊: 上层控制重新申请 GPUs. no payload

	ContainerChangeImageOpt       OptType = "change_image_opt"              // 特殊: 上层控制更新 image (在初始化时). payload: image string
	ContainerUpdateByRenew        OptType = "change_order_uuid_and_cfg_opt" // 特殊: 上层控制续费 更新到期时间. payload: order_uuid sting
	ContainerUpdateSettingsByUser OptType = "update_settings_by_user"       // 用户调整实例配置, 更新 gpu cpu memory

	ContainerDeleteRecordOpt OptType = "delete_record_opt" // 特殊: 上层控制是否彻底删除字段. no payload

	ContainerSaveImageOpt          OptType = "save_image_opt"           // commit and push 到仓库中
	ContainerCancelSaveImageOpt    OptType = "cancel_save_image_opt"    // cancel save image
	ContainerUpdateSettingsByAdmin OptType = "update_settings_by_admin" // 特殊: 管理员调整实例配置 , 更新 cpu memory payload: constant.ContainerUpdateSettingsByAdminReq

	ContainerRenewalOrderTakeEffect OptType = "renewal_order_take_effect" // 续费订单到生效时间，变更orderUUID
	ContainerChangeChargeType       OptType = "update_order_uuid"         // 变更计费方式：其实作用到container时只需要更换orderUUID，但是要记录实例操作历史
	ContainerUpdateDataDiskSize     OptType = "update_data_disk_size"     // 更新容器的数据盘大小
	ContainerUpdateStoppedAt        OptType = "update_stopped_at"         // 更新容器的关机时间（延长释放时间）
	ContainerUpdateSSHPwd           OptType = "update_ssh_pwd"            // 更新容器root/ssh密码
	ContainerUpdateProtocol         OptType = "update_protocol"           // 修改协议
	ContainerTransPrepay            OptType = "trans_prepay"              // 转移包卡订单

	/*
	 * GRAPH 仅用于内部表示。不参与业务
	 */

	SucceedOpt      string = "SUCCEED" // NOTE: 以后成功状态逐步由真实底层状态同步而来 SyncByAgentOpt
	FailedOpt       string = "FAILED"
	SyncByAgentOpt  string = "AGENT_SYNC"    // 真实回调来的状态
	TimeoutCheckOpt string = "TIMEOUT_CHECK" // 由于停留在某个状态超时而强制转移到下一个状态。 如 starting 超时，转到开机失败。
)

func (o OptType) IsCtrlOpt() bool {
	switch o {
	case ContainerCreateOpt,
		ContainerStartOpt,
		ContainerStopOpt,
		ContainerReInitOpt,
		ContainerRemoveOpt,
		ContainerRestartOpt,
		ContainerCloneOpt,
		ContainerMergeDiffOpt:
		return true
	}
	return false
}

// NeedMachineRealHealth 关机和释放时不检查状态.
func (o OptType) NeedMachineRealHealth() bool {
	switch o {
	case ContainerCreateOpt,
		ContainerStartOpt,
		ContainerReInitOpt,
		ContainerRestartOpt,
		ContainerCloneOpt:
		return true
	}
	return false
}

func (o OptType) CanCauseStatusChanged() bool {
	switch o {
	case ContainerInsertRecordOpt,
		ContainerCreateOpt,
		ContainerStartOpt,
		ContainerStopOpt,
		ContainerReInitOpt,
		ContainerRemoveOpt,
		ContainerRestartOpt,
		ContainerCloneOpt,
		ContainerUpdateStatusOpt,
		ContainerUpdateStatusByResultOpt:

		return true
	}
	return false
}

func (o OptType) NeedHistory() bool {
	switch o {
	case ContainerInsertRecordOpt, ContainerUpdateStatusOpt, ContainerRenewalOrderTakeEffect:
		return false
	}
	return true
}

func (o OptType) String() string {
	return string(o)
}

type OptContainerReq struct {
	RuntimeUUID ContainerRuntimeUUID `json:"runtime_uuid"` // 必填
	Opt         OptType              `json:"opt"`          // 必填, 操作类型
	Caller      OptCaller            `json:"caller"`       // 调用操作的是内部服务还是用户
	Payload     string               `json:"payload"`      // 根据 opt 选择如何反序列化.
	OptAt       time.Time            `json:"opt_at"`       // 如果不指定就是 now
	StdMessage  *intl.MessageCode    `json:"std_message"`
	DebugMsg    string               `json:"debug_msg,omitempty"`
	Timestamp   int64                `json:"timestamp"`
}

type OptContainerAuxParams struct {
	OrderUUID         string
	SourceRuntimeUUID ContainerRuntimeUUID
	SourceMachineID   string
	SourceRegionSign  RegionSignType
	ImageUUID         string
	MigrateHistoryID  int
	TargetRuntimeUUID ContainerRuntimeUUID
	MachineID         string
	UID               int
	StartMode         ContainerStartMode
	RegionSign        RegionSignType
	RuntimeType       ContainerRuntimeType
	GpuPriority       PriorityType
	SubName           string
	DeploymentUUID    string
}

func (c OptContainerAuxParams) IsFromCloneOpt() bool {
	return len(c.SourceRuntimeUUID) != 0
}

type OptCaller string // 调用操作的是内部服务 (string) 还是用户 (uid int)

const (
	OptCallerInternal          OptCaller = "internal_call_opt"
	OptCallerAdmin             OptCaller = "admin_call_opt"
	OptCallerDeployment        OptCaller = "deployment_call_opt"
	OptCallerDeploymentRemover OptCaller = "deployment_remover_call_opt"

	OptCallerInstance            OptCaller = "instance_call_opt"
	OptCallerInstanceCron        OptCaller = "instance_cron_call_opt"
	OptCallerInstanceTimed       OptCaller = "instance_timed_call_opt"
	OptCallerInstanceDisableUser OptCaller = "instance_disable_user_call_opt"
	OptCallerInstanceHook        OptCaller = "instance_hook_call_opt"
	OptCallerDCHook              OptCaller = "dc_hook_call_opt"
	OptCallerContainerHook       OptCaller = "container_hook_call_opt"
	OptCallerBC                  OptCaller = "bill_center_call_opt"
	OptCallerGuard               OptCaller = "guard_call_opt"
	OptCallerCloneKVHook         OptCaller = "clone_kv_hook_call_opt"

	// core

	OptCallerUpdateContainerStatus      OptCaller = "update_container_status_opt"
	OptCallerCronRemoveInstanceCacheOpt OptCaller = "cron_remove_instance_cache_opt"

	OptCallerModifyMachine   OptCaller = "modify_machine_opt"
	OptCallerModifyDataDisk  OptCaller = "modify_data_disk_opt"
	OptCallerModifyGpuType   OptCaller = "modify_gpu_type_opt"
	OptCallerModifyRegion    OptCaller = "modify_region_opt"
	OptCallerInitFileStorage OptCaller = "init_file_storage_opt"
	OptCallerAutoFsInit      OptCaller = "autofs_init_opt"

	// and so on...
)

func NewOptCallerUser(uid int) OptCaller {
	return OptCaller(fmt.Sprintf("uid:%d", uid))
}

func (oc OptCaller) IsUser() bool {
	return strings.HasPrefix(oc.String(), "uid:")
}

func (oc OptCaller) IsAdmin() bool {
	return oc == OptCallerAdmin
}

func (oc OptCaller) String() string {
	return string(oc)
}

type ContainerStatusUpdateReq struct {
	ValidAt time.Time `json:"valid_at"` // 检验消息重复发送
	// TimeStamp int64     `json:"time_stamp"`

	// 手动指定的业务状态, 如果为空. 说明需要根据底层状态重新计算业务状态
	ContainerStatus    ContainerStatusType    `json:"container_status"`
	ContainerSubStatus ContainerSubStatusType `json:"container_sub_status"`
	Message            string                 `json:"message"`

	IsForce bool `json:"is_force"`

	// 操作, 如果不为空, 说明需要根据操作类型和底层状态重新计算业务状态.
	Operate     string `json:"operate"`       // 仅用于状态跳转, 是 string 类型.
	IsOptResult bool   `json:"is_opt_result"` // 为 true 时 Operate 必定不为空

	// 回调带来的底层状态
	StatePhase   agent_constant.ContainerStatePhase `json:"state_phase"`
	OOMKilled    bool                               `json:"oom_killed"`
	Pid          int                                `json:"pid"`
	ExitCode     int                                `json:"exit_code"`
	Error        string                             `json:"error"`
	RestartCount int                                `json:"restart_count"`
	CreatedAt    time.Time                          `json:"created_at"`
	StartedAt    time.Time                          `json:"started_at"`
	FinishedAt   time.Time                          `json:"finished_at"`
}

// IsErrorState 根据 err_msg 或者退出码, 判断状态为出错状态. 目前仅凭 error 无法识别出所有退出, 可能需要一个一个核实所有退出码
func (c *ContainerStatusUpdateReq) IsErrorState() bool {
	return c.Error != "" || ExplainTheExitCode(c.ExitCode).IsErr
}

func (c *ContainerStatusUpdateReq) ErrorMessageByExitCode() string {
	msg := ExplainTheExitCode(c.ExitCode).Msg
	if msg != "" {
		c.Message = msg
	}
	return c.Message
}

func (c *ContainerStatusUpdateReq) String() string {
	out, _ := json.Marshal(c)
	return string(out)
}

type ContainerUpdateStatusByResultOptReq struct {
	ValidAt   time.Time `json:"valid_at"`
	TimeStamp int64     `json:"time_stamp"`

	OptType OptType `json:"opt"`

	// 其他状态子属性
	ExitCode int    `json:"exit_code"`
	Error    string `json:"error"`
}

func (c *ContainerUpdateStatusByResultOptReq) String() (string, error) {
	out, err := json.Marshal(c)
	return string(out), err
}

type ContainerUpdateSettingsByAdminReq struct {
	ContainerParamCPUAndMemRequest
	AdditionalDiskList     []AdditionalDiskInfo                   `json:"additional_disk_list"`
	AdditionalPortList     []agent_constant.AdditionalPortReflect `json:"additional_port_list"`
	MaxLocalDiskSizeInByte int64                                  `json:"max_local_disk_size_in_byte"`
	ShmSize                int64                                  `json:"shm_size"`
}

func (c *ContainerUpdateSettingsByAdminReq) String() (string, error) {
	out, err := json.Marshal(c)
	return string(out), err
}

type ContainerUpdateSettingsByAdminDetail struct {
	ContainerUpdateSettingsByAdminReq
	ReqGpuAmount           int   `json:"req_gpu_amount"`
	MaxWritableSizeInByte  int64 `json:"max_writable_size_in_byte"`
	MaxLocalDiskSizeInByte int64 `json:"max_local_disk_size_in_byte"`
}

const (
	ContainerMaxWritableSizeInByte int64 = 25 * 1024 * 1024 * 1024 // 20G 容器分配的可写空间
	ContainerWorkingDir                  = "/root"                 // 容器默认挂载目录
	ContainerRootPasswordLength    int   = 12                      // 容器默认密码长度

	ContainerRegionEnvKey               = "AutoDLRegion"
	ContainerUUID                       = "AutoDLContainerUUID"
	AutoDLDeploymentUUID                = "AutoDLDeploymentUUID"
	ContainerEnvAutodlAutoPanelToken    = "AutodlAutoPanelToken"
	ContainerEnvAutodlDataCenter        = "AutoDLDataCenter"
	ContainerEnvAgentHost               = "AgentHost"
	ContainerEnv_OMP_NUM_THREADS        = "OMP_NUM_THREADS"
	ContainerEnv_MKL_NUM_THREADS        = "MKL_NUM_THREADS"
	ContainerEnvDefineSvcAddr           = "AutoDLServiceURL"
	ContainerEnvContainerMonitorSetting = "AutoDLContainerMonitorSetting" // 用于容器内获取监控信息

	PushGatewayJobName = "AutoDLContainerMonitor"
)

// 容器标签 pkg-core/container_runtime/model/type_define.go ContainerParam Tag
// 用于处理一些特殊逻辑
const (
	// ContainerTagInstanceAfter20230720  instance创建是否在2023-07-20之后
	ContainerTagInstanceAfter20230720 = "instance-after-2023-07-20"
)

func GetContainerMaxWritableSize(tags []string) int64 {
	containerMaxWritableSizeInByte := ContainerMaxWritableSizeInByte // 默认容器最大可写入大小
	for _, tag := range tags {
		if tag == ContainerTagInstanceAfter20230720 {
			containerMaxWritableSizeInByte = int64(30 * 1024 * 1024 * 1024)
		}
	}
	return containerMaxWritableSizeInByte
}

type ContainerRuntimeType string

const (
	ContainerRuntimeOfInstance   ContainerRuntimeType = "instance" // 默认
	ContainerRuntimeOfDeployment ContainerRuntimeType = "deployment"
)

// ToPriorityType NOTE: 如果 PriorityType/ContainerRuntimeType 有更新, 需要及时在此处补充.
func (t ContainerRuntimeType) ToPriorityType() PriorityType {
	switch t {
	case ContainerRuntimeOfInstance:
		return InstanceLevel
	case ContainerRuntimeOfDeployment:
		return DevelopmentLevel
	}

	return 0
}

/**
 * USAGE
 */

type UsageInfo struct {
	agent_constant.ContainerUsage

	// Network health, and so on...
}

type ContainerUsageInfo struct {
	agent_constant.ContainerUsage
	DiskHealthStatus DiskHealthStatusType `json:"disk_health_status"`
	RootFSUsedRate   float64              `json:"root_fs_used_rate"`
}

// Update 控制更新. 防止进度显示异常.
func (u *UsageInfo) Update(newUsage UsageInfo) {
	if u.DownloadImageProgress > newUsage.DownloadImageProgress { // //更新的进度值小于原来值时不更新进度值
		newUsage.DownloadImageProgress = u.DownloadImageProgress
	}

	*u = newUsage
}

func (u *UsageInfo) Count(tags []string) (rootFSUsedRate float64, diskHealth DiskHealthStatusType) {
	// 0~1, 百分号后两位小数
	rootFSUsedRate = float64(int64(math.Round((float64(u.RootFSUsedSize)/float64(GetContainerMaxWritableSize(tags)))*10000))) / 10000

	if rootFSUsedRate > 1 {
		rootFSUsedRate = 1
	}
	if rootFSUsedRate < 0 {
		rootFSUsedRate = 0
	}

	if rootFSUsedRate >= 0 && rootFSUsedRate <= 0.95 {
		diskHealth = DiskHealthNormal // 正常
	} else if rootFSUsedRate > 0.95 && rootFSUsedRate <= 1 {
		diskHealth = DiskHealthAlert // 预警
	} else {
		diskHealth = DiskHealthAbnormal // 异常
	}

	if diskHealth == "" {
		diskHealth = DiskHealthUnknown
	}

	return
}

func (u *UsageInfo) String() (string, error) {
	out, err := json.Marshal(u)
	return string(out), err
}

type DiskHealthStatusType string // 磁盘健康状态

const (
	DiskHealthNormal   DiskHealthStatusType = "normal"   // 磁盘正常, 初始直接认为正常
	DiskHealthAlert    DiskHealthStatusType = "alert"    // 磁盘预警
	DiskHealthAbnormal DiskHealthStatusType = "abnormal" // 磁盘异常
	DiskHealthUnknown  DiskHealthStatusType = "unknown"  // 默认未知, 等待回调更新. 原型图中没有...
)

/*
 * Settings
 */

type AdditionalDiskInfo struct {
	Source string `json:"source"`
	Target string `json:"target"`
	Mode   string `json:"mode"`
}

// Path 参考 "github.com/docker/docker/volume/mounts" ParseMountRaw()
func (a AdditionalDiskInfo) Path() string {
	// Just a destination path in the container
	if len(a.Source) == 0 {
		return a.Target
	}

	// HostSourcePath+DestinationPath+Mode
	// "/path/in/host:/path/in/container:rw,nocopy"
	if len(a.Mode) != 0 {
		return fmt.Sprintf("%s:%s:%s", a.Source, a.Target, a.Mode)
	}

	// Host Source Path or Name + Destination
	// "/path/in/host:/path/in/container"
	return fmt.Sprintf("%s:%s", a.Source, a.Target)

}

type ContainerParamCPUAndMemRequest struct {
	CpuLimit       float64 `json:"cpu_limit"`
	MemLimitInByte int64   `json:"mem_limit_in_byte"`
}

type ProductUpdateStatusByContainerParams struct {
	ProductUUID        string                 `json:"product_uuid"` // 主要给弹性部署复用使用
	RuntimeUUID        ContainerRuntimeUUID   `json:"runtime_uuid"`
	OldStatus          DCStatus               `json:"old_status"`
	NewStatus          DCStatus               `json:"new_status"`
	ContainerSubStatus ContainerSubStatusType `json:"container_sub_status"`
	Error              string                 `json:"error"`
	OomKilled          bool                   `json:"oom_killed"`
	StatusAt           time.Time              `json:"status_at"`
	StoppedAt          *time.Time             `json:"stopped_at"`
	Caller             string                 `json:"caller"`
}

type LatestUserMachineRegionInfo struct {
	// nas
	ExistUserNetDisk    bool
	UserNetDiskQuotaOK  bool
	ExistMachineNetDisk bool

	// adfs/autofs
	ExistMachineADFSDisk    bool
	UserADFSAuth            bool
	UserADFSQuotaSize       string
	UserADFSQuotaInode      int
	UserADFSConcurrentLimit int
	UserFSMaxUploads        int
	UserAutoFSBufferSize    int
	UserAutoFsFSCacheSize   int
	UserAutoFsNoBGJob       bool
	FsSubPath               string // 挂载某个文件存储的某个子目录
	FsType                  FsType
	FsConfigVersion         string // core_region_file_storage 表中用户所使用的文件存储配置版本。

	// exclusive nfs
	ExistExclusiveNfs bool
	ExclusiveNfsAddr  string

	// proxy
	ProxyHosts      []string
	ProxyHost       string
	ProxyHostPublic string
	ProxyPort       int
	ProxyToken      string
}

type DCUpdateContainerParamPayload struct {
	OrderUUID    string   `json:"order_uuid"`
	ProductUUID  string   `json:"product_uuid"`
	RootPassword string   `json:"root_password"`
	JupyterToken string   `json:"jupyter_token"`
	PreCmd       []string `json:"pre_cmd"`
}

func (d *DCUpdateContainerParamPayload) String() string {
	s, _ := json.Marshal(d)
	return string(s)
}

func (d *DCUpdateContainerParamPayload) ParseFromString(s string) error {
	return json.Unmarshal([]byte(s), &d)
}

type ContainerMonitorSetting struct {
	PushGatewayUrl      string `json:"push_gateway_url"`      // 推送网关地址
	PushGatewayUsername string `json:"push_gateway_username"` // 推送网关用户名
	PushGatewayPassword string `json:"push_gateway_password"` // 推送网关密码
}

func (c *ContainerMonitorSetting) Marshal() []byte {
	b, _ := json.Marshal(c)
	return b
}

func (c *ContainerMonitorSetting) Parse(b []byte) error {
	return json.Unmarshal(b, &c)
}
