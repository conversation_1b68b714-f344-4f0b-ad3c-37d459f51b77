package constant

import (
	"fmt"
	"strings"
)

const (
	Register                = "register"
	ResetPassword           = "reset_password"
	ChangePhone             = "change_phone"
	Login                   = "login"
	Abroad                  = "abroad"
	SubUserBind             = "sub_user_bind"
	SubUserUpdatePassword   = "sub_user_update_password"
	InstanceReleaseWarning  = "instance_release_warning"
	InstanceExpireWarning   = "instance_expire_warning"
	BalanceWarning          = "balance_warning"
	BankRemitAccept         = "bank_remit_accept"
	InstanceCreateRunning   = "instance_create_running"
	MachineStoppedErr       = "machine_stopped_err"       // 主机异常宕机
	RegionRelease           = "region_release"            // 地区释放
	InstanceScheduleSuccess = "instance_schedule_success" // 实例调度成功
	BalanceNegative         = "balance_negative"          // 余额由正变为负数
)

// 用户名:炼丹师<手机号后4位>
func GetUserName(phone string) string {
	r := fmt.Sprintf("%s%s", "炼丹师", phone[len(phone)-4:])
	return r
}

const (
	EncodingAESKey             = "1O7FdyLrtEWG5zOzpmff6SC7LlmUSvKQrHEopkSdUPJ" // 消息加解密密钥
	Token                      = "asdfqwerjalksh21o39812hkas"                  // 令牌
	TestToken                  = "iuerwbrveiu"
	AppID                      = "wx885b877c88015765"               // 实际的公众号开发者ID
	AppSecret                  = "2faecd2119028365a81b4b57e28f2c5d" // 实际的公众号开发者密码
	TestAppID                  = "wx885b877c88015765"
	TestAppSecret              = "2faecd2119028365a81b4b57e28f2c5d"
	AccessTokenUrl             = "https://api.weixin.qq.com/cgi-bin/token"                       // 获取access_token url
	AccessTicketUrl            = "https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=" // 用access_token 换取 ticket
	UserInfoUrl                = "https://api.weixin.qq.com/cgi-bin/user/info"                   // 根据用户openID获取 微信用户信息
	AccountGetQRCodeImgAddrURL = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=%s"         // 用ticket凭借生成二维码的url
	CustomServicePostUrl       = "https://api.weixin.qq.com/cgi-bin/message/custom/send"
	TemplateMessageSendUrl     = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token="
	TemplateID                 = "z1BVKBgsnZKGiLceBKuGVpX93INbgHNC_rq3AXhxttw"
	TestTemplateID             = "z1BVKBgsnZKGiLceBKuGVpX93INbgHNC_rq3AXhxttw"
	MaxPersonalTokenNumbers    = 10
	WechatAppletAppID          = "wxa56694a84786737a"
	WechatAppletAppSecret      = "0f0ee69cfa6b850735edb26a617f8167"
	WechatAppletPhoneUrl       = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token="
	WechatAppletOpenIdUrl      = "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code"

	// TemplateIDBalanceWarning 通知余额预警模板
	TemplateIDBalanceWarning = "us4ttTsTXI0TFh77oKjWbvV8e_HAYlEvwrg4H5RQhas"

	// 敏感词过滤
	DetectSensitiveUrl = "https://jmwbsh.market.alicloudapi.com/wbsh/text/review"
)

const (
	SubscribeContent = "终于等到你，尊敬的炼丹师! 请在PC打开autodl.com使用GPU开始炼丹吧。\n点击「联系客服」加客服微信进用户交流群~\n再次感谢你的关注与支持~" // 用户关注时的自动回复
)

const MaxUserCapacity = 9999

// 会员等级名称

type MemberLevelName string

const (
	NormalUser MemberLevelName = "normal_user" // 普通用户
	MemberUser MemberLevelName = "member_user" // 会员
)

type RoleType string
type MemberIdentify string
type MemberIdentifyStatus int

func (m MemberIdentifyStatus) Valid() bool {
	return m == MemberIdentifyStatusPass || m == MemberIdentifyStatusAdminSet
}

const (
	NormalVip     MemberIdentify = "normal"     // 普通用户
	PersonalVip   MemberIdentify = "personal"   // 个人用户认证
	StudentVip    MemberIdentify = "student"    // 学生
	EnterpriseVip MemberIdentify = "enterprise" // 企业用户

	MemberIdentifyStatusNone     MemberIdentifyStatus = 0  // 没有认证
	MemberIdentifyStatusPass     MemberIdentifyStatus = 1  // 用户自己认证
	MemberIdentifyStatusAdminBan MemberIdentifyStatus = -2 // 管理员禁用
	MemberIdentifyStatusAdminSet MemberIdentifyStatus = 2  // 管理员设置身份

	RoleInstance      RoleType = "instance"
	RoleDeployment    RoleType = "deployment"
	RoleImage         RoleType = "image"
	RoleFileStorage   RoleType = "file_storage"
	RoleBillingCenter RoleType = "billing_center"
)

const LevelNum = 2

var LevelRank = map[MemberLevelName]int64{
	NormalUser: 0,
	MemberUser: 4,
}

// 用户一些常规的加成长值的操作
const (
	VipInviteUserRegister = 5
	VipUserBindWechat     = 10
	// ...
)

// 用户会员成长值获取途径

type GrowthValueOptTypes string

const (
	VipRecharge            GrowthValueOptTypes = "recharge"             // 用户充值
	VipWechatBind          GrowthValueOptTypes = "wechat_bind"          // 微信绑定
	VipWechatRegister      GrowthValueOptTypes = "wechat_register"      // 微信注册
	VipInviteRegister      GrowthValueOptTypes = "invite_register"      // 邀请用户注册(邀请人获得的奖励)
	VipFinishDailyJobs     GrowthValueOptTypes = "finish_daily_jobs"    // 完成日常任务
	VipStudentAuthenticate GrowthValueOptTypes = "student_authenticate" // 学生认证
	VipRefund              GrowthValueOptTypes = "refund"               // 用户退款此为减成长值
)

type MemberGainWays string

// 同时有的时候，三者显示的优先级，管理员指定 - student - normal
const (
	VIPAdminAppointed MemberGainWays = "vip_admin_appointed"
	VIPStudent        MemberGainWays = "vip_student"
	VIPUserAcquire    MemberGainWays = "vip_user_acquire"
	VIPWechatBind     MemberGainWays = "vip_wechat_bind"
	// ...
)

type UserPrizeType int

const (
	NonePrize     UserPrizeType = 0
	GpuPrize      UserPrizeType = 1
	HardDiskPrize UserPrizeType = 2
	Gpu4080Prize  UserPrizeType = 3
)

type HeaderContent string

const (
	TokenHeaderContent  HeaderContent = "token_header_content"
	NormalHeaderContent HeaderContent = "normal_header_content"
)

const ContentType = "Content-Type"

type ContentTypeValue string

const (
	TokenContentType ContentTypeValue = "application/x-www-form-urlencoded"
	JsonContentType  ContentTypeValue = "application/json;encoding=utf-8"
)

type SUPaymentMethod string

const (
	SUPaymentMethodQuota SUPaymentMethod = "quota"
	SUPaymentMethodShare SUPaymentMethod = "share"
)

type SubUserAuthorityType string
type SubUserAuthorityName string

const (
	SubUserMyselfData   SubUserAuthorityType = "permit_own_data"
	SubUserAllData      SubUserAuthorityType = "permit_all_data"
	SubUserNoPermission SubUserAuthorityType = "no_permission"

	SubUserInstance   SubUserAuthorityName = "instance"
	SubUserDeployment SubUserAuthorityName = "deployment"
	SubUserImage      SubUserAuthorityName = "image"
	SubUserFileStore  SubUserAuthorityName = "file_storage"
	SubUserBill       SubUserAuthorityName = "billing_center"
)

type UserCertificationType string
type UserCertificationStatus string
type UserIdentify string
type BackstageRole string

const (
	UserIdentifyNormal     UserIdentify = "normal"
	UserIdentifyRealName   UserIdentify = "real_name"
	UserIdentifyStudent    UserIdentify = "student"
	UserIdentifyEnterprise UserIdentify = "enterprise"

	UserCertificationPersonal              UserCertificationType = "personal"                // 个人认证
	UserCertificationPersonalStudent       UserCertificationType = "personal_student"        // 学生认证
	UserCertificationEnterpriseBankAccount UserCertificationType = "enterprise_bank_account" // 企业认证 - 银行对公账户
	UserCertificationEnterpriseCertificate UserCertificationType = "enterprise_certificate"  // 企业认证 - 企业证件认证

	UserCertificationSubmitted  UserCertificationStatus = "submitted"   // 提交申请
	UserCertificationReject     UserCertificationStatus = "reject"      // 拒绝
	UserCertificationRevoked    UserCertificationStatus = "revoked"     // 用户主动撤销
	UserCertificationPass       UserCertificationStatus = "pass"        // 审核通过
	UserCertificationExpired    UserCertificationStatus = "expired"     // 已过期
	UserCertificationRepeal     UserCertificationStatus = "repeal"      // 用户主动撤销（剥夺身份）
	UserCertificationAdminPoint UserCertificationStatus = "admin_point" // 管理员指定
	UserCertificationAdminAbort UserCertificationStatus = "admin_abort" // 管理员终止

	Admin                    BackstageRole = "admin"                      // 管理员
	SuperAdmin               BackstageRole = "super_admin"                // 超级管理员
	Finance                  BackstageRole = "finance"                    // 财务
	Operations               BackstageRole = "operations"                 // 运维
	TechnicalCustomerService BackstageRole = "technical_customer_service" // 技术客服
	BusinessCustomerService  BackstageRole = "business_customer_service"  // 商务客服
	AdminOperate             BackstageRole = "admin_operate"              // 后台操作
)

func (b BackstageRole) IsAdmin() bool {
	return strings.Contains(string(b), string(Admin))
}
func (b BackstageRole) IsSuperAdmin() bool {
	return strings.Contains(string(b), string(SuperAdmin))
}
func (b BackstageRole) IsFinance() bool {
	return strings.Contains(string(b), string(Finance))
}
func (b BackstageRole) IsOperations() bool {
	return strings.Contains(string(b), string(Operations))
}
func (b BackstageRole) IsTechnicalCustomerService() bool {
	return strings.Contains(string(b), string(TechnicalCustomerService))
}
func (b BackstageRole) IsBusinessCustomerService() bool {
	return strings.Contains(string(b), string(BusinessCustomerService))
}

// user -> roles
// role -> router
// 比如想为finance添加instance功能，只需要在router页面，为instance的路由的中间件添加对应的角色鉴权即可

const (
	FaceCertifyRouter = "/api/v1/certification/result?outer_order_no="
)
