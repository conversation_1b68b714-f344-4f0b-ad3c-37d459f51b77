package constant

import (
	"encoding/json"
	"fmt"
	"time"
)

// RegionSignType 不相同不再代表两个地区物理分割，由于北京A区 和 北京B区合并了
// 判断多个地区标识是否处于同一个内网环境，使用该方法：regionModel.CheckRegionIsInSameDataCenter
type RegionSignType string // 地区的唯一标识
type ExclusiveNfsStatus string

func (rs RegionSignType) String() string {
	return string(rs)
}

func NewRegionSignType(region string) RegionSignType {
	return RegionSignType(region)
}

const (
	RegionDefault     RegionSignType = "nanjing-A" // default
	WaveRegionDefault RegionSignType = "wave-A"    // default

	ExclusiveNfsEnable  ExclusiveNfsStatus = "enable"
	ExclusiveNfsDisable ExclusiveNfsStatus = "disable"
)

const FrpcDefaultProxyToken = "seetatech666"
const FrpcDefaultProxyPort = 7000

type NetDiskInit struct {
	UID             int            `json:"uid"`
	RegionSign      RegionSignType `json:"region_sign"`
	UserQuotaInByte int64          `json:"user_quota_in_byte"`
}

func (n *NetDiskInit) Marshal() (string, error) {
	j, err := json.Marshal(n)
	if err != nil {
		return "", err
	}
	return string(j), nil
}

func (n *NetDiskInit) Unmarshal(str string) error {
	return json.Unmarshal([]byte(str), &n)
}

type NetDiskInitResult struct {
}

// agent 向 server 同步usage
type StorageSyncUsageModel struct {
	Sign    RegionSignType              `json:"region_sign"`
	Content []StorageSyncUsageUserModel `json:"content"`
}

type StorageSyncUsageUserModel struct {
	UID   int   `json:"uid"`
	Used  int64 `json:"used"`
	Quota int64 `json:"quota"`
}

// agent 向 server 同步/data的使用量，用于磁盘预警
type StorageSyncDiskAlertsModel struct {
	Sign      RegionSignType `json:"sign"`
	TotalSize int64          `json:"total_size"`
	Used      int64          `json:"used"`
	Available int64          `json:"available"`
	UsedRate  int            `json:"used_rate"`
}

func (s *StorageSyncDiskAlertsModel) WarningString() string {
	g := int64(1024 * 1024)
	return fmt.Sprintf("TotalSize:%d, Used:%d, Available:%d, UsedRate%d.", s.TotalSize/g, s.Used/g, s.Available/g, s.UsedRate/int(g))
}

type RegionAuthRequest struct {
	RegionSign RegionSignType `form:"region_sign" json:"region_sign"`
	AgentToken string         `form:"agent_token" json:"agent_token"`

	// TODO: 传递其他用于安全验证的信息
}

type NFSMachineStatus string

const (
	NFSMachineHealth     NFSMachineStatus = "health"
	NFSMachineTimeout    NFSMachineStatus = "timeout"
	NFSMachineDiskAlerts NFSMachineStatus = "diskAlerts"
	NFSMachineUnknown    NFSMachineStatus = "unknown"
)

type NFSMachineStatusInfo struct {
	Status         NFSMachineStatus `json:"status"`
	UpdateAt       time.Time        `json:"update_at"`
	DiskAlertsInfo string           `json:"disk_alerts_info"`
}

func (n *NFSMachineStatusInfo) Marshal() (str string, err error) {
	var m []byte
	m, err = json.Marshal(n)
	if err != nil {
		return
	}

	str = string(m)
	return
}

func (n *NFSMachineStatusInfo) UnMarshal(str string) (err error) {
	return json.Unmarshal([]byte(str), &n)
}

var StorageDiskAltersWarningLine = 90

type NetDiskInitStatusType string

// TODO: 状态的设置与挂载方式有关
const (
	NetDiskUninitialized NetDiskInitStatusType = "uninitialized"
	NetDiskCreating      NetDiskInitStatusType = "creating"
	NetDiskCreateFailed  NetDiskInitStatusType = "create_failed"

	NetDiskNFSCreated NetDiskInitStatusType = "nfs_created"

	// mount

	NetDiskInitDone NetDiskInitStatusType = "init_done"
)

type StorageAgentFrpcRequest struct {
	RegionSign RegionSignType `json:"region_sign"`
	FsType     FsType         `json:"fs_type"`
}

func (s *StorageAgentFrpcRequest) String() string {
	b, _ := json.Marshal(s)
	return string(b)
}

type StorageAgentFrpcInfo struct {
	ProxyHost          string `json:"proxy_host"`
	ProxyPort          int    `json:"proxy_port"`
	ProxyToken         string `json:"proxy_token"`
	ProxyApiServerPort int    `json:"proxy_api_server_port"`
}

func (c *StorageAgentFrpcInfo) String() (string, error) {
	out, err := json.Marshal(c)
	return string(out), err
}

const (
	MaxExpandCapacityInGB int = 40960
	MaxExpandCapacityInKB     = 40960 * GInKB
)

type RegionVisible string
type RegionUsedFor string

const (
	RegionVisibleNone       RegionVisible = ""
	RegionVisibleAll        RegionVisible = "all"
	RegionVisibleEnterprise RegionVisible = "enterprise"

	RegionUsedForAll        RegionUsedFor = "all"
	RegionUsedForInstance   RegionUsedFor = "instance"
	RegionUsedForDeployment RegionUsedFor = "deployment"
)

func (r RegionUsedFor) CanDeployment() bool {
	switch r {
	case RegionUsedForDeployment, RegionUsedForAll:
		return true
	}
	return false
}

func (r RegionUsedFor) CanInstance() bool {
	switch r {
	case RegionUsedForInstance, RegionUsedForAll:
		return true
	}
	return false
}
