package module_definition

import (
	coreRegionModel "server/pkg-core/region/model"
	bcm "server/pkg/billing_center/model"
	"server/pkg/constant"
	"server/plugin/queue_interface"

	"gorm.io/gorm"
)

type BCServerInterface interface {
	CreateInstance(params *bcm.CreateInstanceRequest) (newOrder *bcm.Order, err error)
	CreateDeploymentContainer(tx *gorm.DB, params *bcm.CreateInstanceRequest) (newOrder *bcm.Order, gpuDeferFunc func(error), err error)
	RenewalInstance(params *bcm.RenewalInstanceRequest) (renewalOrder *bcm.Order, err error)
	ChangeInstance(params *bcm.ChangeInstanceParams) (err error)
	CloneInstanceCheck(instanceUUID, dstMachineId string, isAdmin bool) (err error)
	PaygToPrepay(params *bcm.PaygToPrepayCreateOrderParams) (newOrder *bcm.Order, err error)
	PrepayToPayg(params *bcm.PrepayToPaygParams) (preview *bcm.CreateOrderForPrepayToPaygResponse, err error)
	OrderPay(params *bcm.OrderPayCancelParams) (order *bcm.Order, productUUID string, err error)
	CancelOrder(params *bcm.OrderPayCancelParams) (err error)
	NetDiskExpand(uid, expandCapacityInGB, durationInMonth int, sign constant.RegionSignType) (err error)
	NetDiskRenewal(uid, recordID, durationInMonth int) (err error)
	GetPricePreview(params *bcm.GetPricePreviewParams) (err error)
	DataDiskChangeSize(params *bcm.ChangeDataDiskSizeParams) (newOrder *bcm.Order, err error)
	ChangeDataDiskSizePreview(params *bcm.ChangeDataDiskSizeParams) (res *bcm.ChangeDataSizePreviewRes, err error)
	ChangeChargeTypeCheck(uid int, instanceUUID constant.InstanceUUIDType) (err error)
	MostWithdraw(uid int) (most int64, err error)
	InitFileStorageWithRegionForUser(uid int, rs constant.RegionSignType) (ru *coreRegionModel.FileStorage, err error)
	GetAfterHook(order *bcm.Order) (afterHook queue_interface.ElementPayloadContent, afterFunc func(tx *gorm.DB) error)
	UserNetDiskRenewalByAdmin(phone string, uid, durationInMonth int, sign constant.RegionSignType) (err error)
	CreateOrderForDeploymentDuration(params *bcm.CreateOrderForDeploymentDurationParams) (res *bcm.CreateOrderForDeploymentDurationResp, err error)
	CreateOrderForCancelDeploymentDurationRefund(params *bcm.CreateOrderForCancelDeploymentDurationParams) (resp *bcm.CreateOrderForCancelDeploymentDurationResp, err error)
	CreateOrderForCancelDDRefundSingle(params *bcm.CreateOrderForCancelDDSingleParams) (resp *bcm.CreateOrderForCancelDDSingleResp, err error)
	ChangeProtocol(params *bcm.ChangeProtocolReq) (err error)
	GetPrice(params *bcm.GetPriceParams) (err error)
}
