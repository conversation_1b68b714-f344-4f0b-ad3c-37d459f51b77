package module_definition

import (
	"context"
	"gorm.io/gorm"
	"server/pkg-agent/agent_constant"
	"server/pkg-agent/messenger"
	"server/pkg-core/api/coreapi"
	coreRegionModel "server/pkg-core/region/model"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/region/model"
	userModel "server/pkg/user/model"
	"server/plugin/queue"
	"server/plugin/queue_interface"
	"time"
)

type RegionInterface interface {
	/*
	 * crud
	 */

	CreateRegion(params *model.CreateRegionParams) (res *coreRegionModel.Region, err error)
	GetRegionDetail(rs constant.RegionSignType) (region *model.Region, err error) // 供其他内部模块使用
	GetRegionDetailFromRO(rs constant.RegionSignType) (region *model.Region, err error)
	GetRegionList() (regions []model.Region, err error) // 供其他内部模块使用
	GetRegionListByDatacenter(datacenterList []string, datacenter string) (regions []model.Region, err error)
	GetRegionDetailWithStorageInfo(rs constant.RegionSignType) (region *coreRegionModel.Region, storage coreRegionModel.RegionStorageOSSDetailList, err error)
	GetStorageDetail(rs agent_constant.StorageOSSSignType) (oss *coreRegionModel.StorageOSS, err error)

	//CheckRegionSignExist(rs constant.RegionSignType) (exist bool, err error) // 地区标识不可重复, 也可以用于 agent-server 检查连接来的机器是否合法.
	//CheckRegionNameExist(name string) (exist bool, err error)                // 地区名称不可重复

	/*
	* agent
	 */

	AuthAgent(token string) (err error)                           // 验证 agent 携带的 token 是否正确.
	RegisterAgent(rs constant.RegionSignType, token string) error // 为 agent 注册, 需要支持重复进行. 具体参数和返回值还要考虑使用什么注册方法.
	CheckAgentRealHealth(rs constant.RegionSignType) bool         // 仅需要检查真实状态, NFS 机器不可以下架.

	/*
	* net disk
	 */

	InitNetDiskWithRegionForUser(uid int, rs constant.RegionSignType, tenant string) (ru *coreRegionModel.NetDisk, err error) // 初始化网盘
	GetNetDiskListForUser(uid int) (res []model.RegionUsageInfo, err error)                                                   // 前端显示网盘列表
	GetRegionListForIndex() (res []model.GetRegionListForIndexRes, err error)                                                 // 前端gpu市场筛选
	GetUserNetDiskMountForInstance(uid int, rs constant.RegionSignType) (path string, exist, quotaOK bool, err error)         // 供其他内部模块使用, get /data/#{uid}
	NetDiskSetQuota(Sign constant.RegionSignType, uid int, quota int64) error
	GetSetQuotaMessage(sign constant.RegionSignType, uid int, quota int64, operateType messenger.MessageType) (msg *queue_interface.NewQueueForStorageAgentOnMachine, err error)
	GetUserNetDiskQuotaList(req model.UserNetDiskQuotaParams) (paged *db_helper.PagedData, list []coreRegionModel.UserNetDiskQuotaForAdminInfo, err error)

	// file storage
	InitFileStorageWithRegionForUser(user *userModel.User, rs constant.RegionSignType) (ru *coreRegionModel.FileStorage, err error) // 初始化文件存储
	InitFileStorageFinal(payload string) (err error)
	GetFileStorageListForUser(uid int) (res []coreRegionModel.RegionFileStorageInfo, err error) // 前端显示网盘列表
	GetFileStorageByUids(uids []int, rs constant.RegionSignType) (fs []coreRegionModel.FileStorage, err error)
	GetFileStorageForCharge(rs constant.RegionSignType, fsType constant.FsType, settleTime time.Time) (data *coreapi.GetFileStorageForChargeData, err error)
	AdminFileStorageList(req model.AdminFileStorageListReq) (*db_helper.PagedData, error)
	AdminUpdateFileStorageSetting(req *coreapi.UpdateFileStorageSettingReq) error
	FileStorageMountAccessList(uid int) (list []model.FsUnmountForUser, err error)
	FileStorageMountCtrl(params *model.FsMountCtrlParams) (err error)
	FileStorageMount(uid int, sign constant.RegionSignType) (mount bool, err error)
	AutoFsGetUsage(uid int, rs constant.RegionSignType) (resp *model.AutoFsGetUsageResp, err error)

	/*
	* net disk expansion
	 */

	CreateExpansionRecord(tx *gorm.DB, record *model.NetDiskExpansion) (err error)
	GetExpansionRecordActive(uid int, sign constant.RegionSignType) (record *model.NetDiskExpansion, err error)
	GetExpansionRecordByID(id int) (record *model.NetDiskExpansion, err error)
	DeleteExpansionRecordByID(tx *gorm.DB, recordID int) (err error)
	GetExpansionRecordByOrderUUID(orderUUID string) (res *model.NetDiskExpansion, err error)

	/*
	* cron
	 */

	CronJobRegister(ctx context.Context)
	MsgRegister() []queue.RegisterInfo
	GuardCronJobRegister(ctx context.Context, regionSign constant.RegionSignType, writer chan<- messenger.Message)

	/*
	 * other
	 */

	DistributeFrpcProxy(rs constant.RegionSignType) (proxyHosts []string, proxyHost, proxyHostPublic string, proxyPort, proxyApiPort int, proxyToken string, region *model.Region, err error)

	// core
	CoreModifyRegion(payload *constant.MQCoreBusinessModifyDataPayload) (err error)

	ExclusiveNfsList(params *model.ExclusiveNfsListParams) (paged *db_helper.PagedData, list []model.ExclusiveNfs, err error)
	ExclusiveNfsGetForProduct(uid int, sign constant.RegionSignType, runtimeType constant.ContainerRuntimeType) (nfs *model.ExclusiveNfs, err error)

	PublicApiGetFileStorageList(req model.PublicApiGetFileStorageListRequest) ([]coreRegionModel.FileStorage, error)
	PublicApiReformatFileStorage(req model.PublicApiReformatFileStorageRequest) error
}

type AdminRegionInterface interface {
	/*
	 * crud
	 */

	//AddRegion 地区名称不可重复, 如果有修改的需求，需要刷DB修改记录, frps端口和NFS的管理端口由系统做约定
	AddRegion(params model.AddRegionParams) error
	ListRegionForFrontend(filter model.RegionFilter, pagedReq db_helper.GetPagedRangeRequest) (*db_helper.PagedData, []coreRegionModel.Region, error)

	GetRegionDetail(rs constant.RegionSignType) (coreRegionModel.Region, error) // 供其他内部模块使用
	GetAllRegionDetail() ([]coreRegionModel.Region, error)                      // 供其他内部模块使用

	CheckRegionSignExist(rs constant.RegionSignType) (exist bool, err error) // 地区标识不可重复, 也可以用于 agent-server 检查连接来的机器是否合法.
	CheckRegionNameExist(name string) (exist bool, err error)                // 地区名称不可重复
}
