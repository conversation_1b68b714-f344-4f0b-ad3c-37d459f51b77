package notify

type ChannelType uint8

const (
	ChannelDefault ChannelType = 0
	ChannelWeixin  ChannelType = 1
	ChannelSMS     ChannelType = 2
)

type NotifyStatus string

const (
	Success NotifyStatus = "success" // 成功
	Failed  NotifyStatus = "failed"  // 失败
)

func (t ChannelType) String() string {
	var s string

	switch t {
	case ChannelDefault:
		s = "默认"
	case ChannelWeixin:
		s = "微信"
	case ChannelSMS:
		s = "短信"
	}

	return s
}

// Channel 渠道
type Channel interface {
	// Notify 发送通知
	Notify(input interface{}) (string, error)
}
