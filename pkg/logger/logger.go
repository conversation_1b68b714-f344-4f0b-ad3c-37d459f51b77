package logger

import (
	"fmt"
	"github.com/sirupsen/logrus"
	"os"
	"server/conf"
	"sync"
)

var hooksMutex sync.RWMutex
var hooks = make(logrus.LevelHooks)

// init 自动初始化日志系统的Hook
func init() {
	InitRequestIDHook()
}

type Logger struct {
	sectionName string
	fields      map[string]interface{}
	realLogger  *logrus.Logger
	lock        sync.RWMutex
}

func NewLogger(sectionName string) *Logger {
	realLogger := logrus.New()
	// 初始化日志组件
	realLogger.SetFormatter(&logrus.TextFormatter{
		ForceColors:   true,
		FullTimestamp: true,
	})
	h, _ := os.Hostname()
	hooksMutex.RLock()
	defer hooksMutex.RUnlock()
	realLogger.ReplaceHooks(hooks)
	return &Logger{
		sectionName: sectionName,
		fields: map[string]interface{}{
			"hostname": h,
		},
		realLogger: realLogger,
	}
}

func (logger *Logger) WithField(key string, value interface{}) *Logger {
	logger.lock.RLock()
	defer logger.lock.RUnlock()
	fields := make(map[string]interface{})
	for k, v := range logger.fields {
		fields[k] = v
	}
	fields[key] = value
	return &Logger{
		sectionName: logger.sectionName,
		fields:      fields,
		realLogger:  logger.realLogger,
	}
}

func (logger *Logger) WithError(err error) *Logger {
	return logger.WithField("err", err)
}

type Fields map[string]interface{}

func (logger *Logger) WithFields(appendFields Fields) *Logger {
	logger.lock.RLock()
	defer logger.lock.RUnlock()
	// 保留旧的
	fields := make(map[string]interface{})
	for k, v := range logger.fields {
		fields[k] = v
	}

	// 接入新的
	for k, v := range appendFields {
		fields[k] = v
	}

	return &Logger{
		sectionName: logger.sectionName,
		fields:      fields,
		realLogger:  logger.realLogger,
	}
}

func (logger *Logger) beforeDo() *logrus.Entry {
	logger.lock.Lock()
	defer logger.lock.Unlock()
	if logger.realLogger == nil {
		logger.realLogger = NewLogger("Unknown").realLogger
	}

	hooksMutex.RLock()
	if len(logger.realLogger.Hooks) != len(hooks) {
		logger.realLogger.ReplaceHooks(hooks)
	}
	hooksMutex.RUnlock()

	if conf.GetGlobalGsConfig().App.DebugLog {
		logger.realLogger.SetLevel(logrus.DebugLevel)
	} else {
		logger.realLogger.SetLevel(logrus.InfoLevel)
	}

	if logger.fields == nil {
		logger.fields = make(map[string]interface{})
	}

	logger.fields["sec"] = logger.sectionName
	return logger.realLogger.WithFields(logger.fields)
}

func (logger *Logger) Info(format string, args ...interface{}) {
	logger.beforeDo().Infof(format, args...)
}

func (logger *Logger) Debug(format string, args ...interface{}) {
	logger.beforeDo().Debugf(format, args...)
}

func (logger *Logger) Warn(format string, args ...interface{}) {
	caller := getCaller()
	logger.WithFields(map[string]interface{}{
		"line": fmt.Sprintf("%s(): %d", caller.Function, caller.Line),
	}).beforeDo().Warnf(format, args...)
}

func (logger *Logger) WarnE(err error, format string, args ...interface{}) {
	caller := getCaller()
	logger.WithFields(map[string]interface{}{
		"err":  err,
		"line": fmt.Sprintf("%s(): %d", caller.Function, caller.Line),
	}).beforeDo().Warnf(format, args...)
}

func (logger *Logger) Error(format string, args ...interface{}) {
	caller := getCaller()
	logger.WithFields(map[string]interface{}{
		"line": fmt.Sprintf("%s(): %d", caller.Function, caller.Line),
	}).beforeDo().Errorf(format, args...)
}

func (logger *Logger) ErrorE(err error, format string, args ...interface{}) {
	caller := getCaller()
	logger.WithFields(map[string]interface{}{
		"err":  err,
		"line": fmt.Sprintf("%s(): %d", caller.Function, caller.Line),
	}).beforeDo().Errorf(format, args...)
}

func (logger *Logger) E(err error) {
	caller := getCaller()
	logger.WithFields(map[string]interface{}{
		"err":  err,
		"line": fmt.Sprintf("%s(): %d", caller.Function, caller.Line),
	}).beforeDo().Error("type of error")
}

func (logger *Logger) Trace(format string, args ...interface{}) {
	caller := getCaller()
	logger.WithFields(map[string]interface{}{
		"line": fmt.Sprintf("%s(): %d", caller.Function, caller.Line),
	}).beforeDo().Warnf(tracePrefix+format, args...)
}

// Probe 探针方法， 用于暴露各种细节参数， 以便分析调优
func (logger *Logger) Probe(tp ProbeType, format string, args ...interface{}) {
	logger.beforeDo().Warnf(fmt.Sprintf("%s%s", getProbeHolePrefix(tp), format), args...)
}
