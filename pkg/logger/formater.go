package logger

import (
	"strings"

	"github.com/sirupsen/logrus"
)

func logrusJsonFormatter(e *logrus.Entry) string {
	formatter := &logrus.JSONFormatter{}
	content, err := formatter.Format(e)
	if err != nil {
		return e.Message
	}
	return string(content)
}

const tracePrefix = "Trace: "
const probePrefix = "Probe_"

type ProbeType string

const (
	ProbeTypeOfGPU     ProbeType = "gpu"
	ProbeTypeOfPort    ProbeType = "port"
	ProbeTypeOfMessage ProbeType = "message"
	ProbeTypeOfMailBox ProbeType = "mailBox"
)

const probeSeparator = "///probeSeparator///"

func getProbeHolePrefix(tp ProbeType) string {
	return probePrefix + string(tp) + probeSeparator
}
func getProbeTypeFromMessage(msg string) string {
	msgs := strings.Split(msg, probeSeparator)
	if len(msgs) > 1 {
		return msgs[0]
	}

	return "none"
}

func isTrace(content string) bool {
	return strings.HasPrefix(content, tracePrefix)
}

func isProbe(content string) bool {
	return strings.HasPrefix(content, probePrefix)
}
