package logger

import (
	"server/pkg/threadlocal"

	"github.com/sirupsen/logrus"
)

type TreadLocalLogHook struct{}

func (h *TreadLocalLogHook) Levels() []logrus.Level {
	return logrus.AllLevels
}

func (h *TreadLocalLogHook) Fire(entry *logrus.Entry) error {
	// 从ThreadLocal中注入RequestID
	if id := threadlocal.GetRequestID(); id != "" {
		entry.Data[threadlocal.RequestID] = id
	}
	return nil
}

// InitRequestIDHook 初始化RequestID Hook到日志系统
func InitRequestIDHook() {
	hooksMutex.Lock()
	defer hooksMutex.Unlock()
	hooks.Add(&TreadLocalLogHook{})
}
