package service

import (
	"context"
	"database/sql"
	"encoding/json"
	"server/pkg-work-order/work_order/model"
	"server/pkg/constant"
	"server/pkg/db_helper"
	model2 "server/pkg/machine/model"
	"strconv"
	"strings"
	"time"
)

func (svc *WorkOrderService) CronWorkOrder(ctx context.Context) {
	go svc.cronSendAgencyNotify(ctx)               // 发送代办通知
	go svc.cronAgencyNotifyRetry(ctx)              // 代办通知重试机制
	go svc.cronSubscribeWorkOrderFinishNotify(ctx) // 运维工单订阅主机空闲事件发送飞书通知
}

func (svc *WorkOrderService) cronSendAgencyNotify(ctx context.Context) {
	svc.log.Info("cornSendAgencyNotify start...")
	svc.doSendAgencyNotify()
	ticker := time.NewTicker(time.Second * 20)
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			svc.doSendAgencyNotify()
		}
	}
}

func (svc *WorkOrderService) doSendAgencyNotify() {
	agencyNotify := &model.AgencyNotify{}
	notifyList, err := agencyNotify.AgencyNotifyGetAll(db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"notify_status": constant.WaitNotify,
		},
		CompareFilters: []db_helper.Compare{{Key: "notify_time", Sign: db_helper.SmallerThan, CompareValue: time.Now()}},
		NullField:      []string{"deleted_at"},
	})
	if err != nil {
		svc.log.WithError(err).Error("get all agency notify failed")
		return
	}

	for _, notify := range notifyList {
		usersIds := strings.Split(notify.NotifyUser, ",")
		createUser, err := svc.GetWorkOrderTenantUserByUserId(notify.UserId)
		if err != nil {
			svc.log.WithField("err", err).Error("get notify create user failed")
			continue
		}
		subNotifyStatus := make([]model.SubNotifyStatus, 0)
		successCount := 0
		for _, userId := range usersIds {
			userIdInt, _ := strconv.Atoi(userId)
			notifyUser, err := svc.GetWorkOrderTenantUserByUserId(userIdInt)
			if err != nil {
				svc.log.WithField("err", err).Error("get notify user failed")
				continue
			}

			if notifyUser.FeishuBotUrl != "" {
				resp, err := svc.SendCustomerWorkOrderMsg(notifyUser.FeishuBotUrl, constant.FeishuMsgTypeInteractive, map[string]interface{}{
					"type": "template",
					"data": map[string]interface{}{
						"template_id":           constant.FeiShuTemplateIDSendAgencyNotify,
						"template_version_name": constant.FeiShuTemplateVersionSendAgencyNotify,
						"template_variable": map[string]interface{}{
							"content":     notify.NotifyContent,
							"machine_id":  notify.MachineId,
							"create_user": createUser.Username,
						},
					},
				})

				if err != nil {
					nextRetryTimeTime := time.Now().Add(time.Duration(30) * time.Second)
					var result map[string]interface{}
					if err := json.Unmarshal([]byte(resp), &result); err != nil {
						svc.log.WithError(err).Error("Failed to parse response JSON")
						return
					}

					subNotifyStatus = append(subNotifyStatus, model.SubNotifyStatus{
						NotifyUserId:   userIdInt,
						NotifyUserName: notifyUser.Username,
						NotifyStatus:   constant.NotifySendingRetry,
						FailedReason:   result["msg"].(string),
						NextNotifyTime: sql.NullTime{
							Time:  nextRetryTimeTime,
							Valid: true,
						},
					})
					continue
				}
				subNotifyStatus = append(subNotifyStatus, model.SubNotifyStatus{
					NotifyUserId:   userIdInt,
					NotifyUserName: notifyUser.Username,
					NotifyStatus:   constant.NotifySuccess,
					NextNotifyTime: sql.NullTime{
						Time: time.Time{}, Valid: false,
					},
				})
				successCount += 1
			}
		}
		subNotifyStatusMarshal, _ := json.Marshal(subNotifyStatus)
		updateNotifyStatus := map[string]interface{}{
			"sub_notify_status": subNotifyStatusMarshal,
		}
		if successCount == len(usersIds) {
			updateNotifyStatus["notify_status"] = constant.NotifySuccess
		} else {
			updateNotifyStatus["notify_status"] = constant.NotifySendingRetry
		}
		err = notify.AgencyNotifyUpdate(nil, &db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{"id": notify.ID},
		}, updateNotifyStatus)
		if err != nil {
			svc.log.WithField("id", notify.ID).Error("update notify status failed")
			continue
		}

	}
}

func (svc *WorkOrderService) cronAgencyNotifyRetry(ctx context.Context) {
	svc.log.Info("cornAgencyNotifyRetry start...")
	svc.doSendAgencyNotifyRetry()
	ticker := time.NewTicker(time.Second * 10)
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			svc.doSendAgencyNotifyRetry()
		}
	}
}

func (svc *WorkOrderService) doSendAgencyNotifyRetry() {
	agentNotify := &model.AgencyNotify{}
	notifyRetryList, err := agentNotify.AgencyNotifyGetAll(db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"notify_status": constant.NotifySendingRetry,
		},
		NullField: []string{"deleted_at"},
	})
	if err != nil {
		svc.log.WithField("notify_status", constant.NotifySendingRetry).ErrorE(err, "Filters notify that are in retry stastus failed")
		return
	}

	for _, notifyRetry := range notifyRetryList {
		subStatusNotifyInfoList := make([]*model.SubNotifyStatus, 0)
		err = json.Unmarshal(notifyRetry.SubNotifyStatus, &subStatusNotifyInfoList)
		if err != nil {
			svc.log.WithField("subNotifyStatus", notifyRetry.SubNotifyStatus).ErrorE(err, "unmarshal notifyRetry.SubNotifyStatus failed")
			continue
		}
		agencyNotify := &model.AgencyNotify{}
		err = agencyNotify.AgencyNotifyGetFirst(&db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{"id": notifyRetry.ID},
		})
		if err != nil {
			svc.log.WithField("agency_notify_id", notifyRetry.ID).ErrorE(err, "get agency notify by id failed")
			continue
		}

		for _, subStatusNotifyInfo := range subStatusNotifyInfoList {
			if subStatusNotifyInfo.NextNotifyTime.Time.Before(time.Now()) {
				continue
			}

			if subStatusNotifyInfo.NotifyStatus == constant.NotifySuccess {
				continue
			}

			// 再次发送消息
			notifyUser, err := svc.GetWorkOrderTenantUserByUserId(subStatusNotifyInfo.NotifyUserId)
			if err != nil {
				svc.log.WithField("err", err).Error("get notify user failed")
				continue
			}
			resp, err := svc.SendCustomerWorkOrderMsg(notifyUser.FeishuBotUrl, constant.FeishuMsgTypeInteractive, map[string]interface{}{
				"type": "template",
				"data": map[string]interface{}{
					"template_id":           constant.FeiShuTemplateIDSendAgencyNotify,
					"template_version_name": constant.FeiShuTemplateVersionSendAgencyNotify,
					"template_variable": map[string]interface{}{
						"content":     agencyNotify.NotifyContent,
						"machine_id":  agencyNotify.MachineId,
						"create_user": notifyUser.Username,
					},
				},
			})

			if err != nil {
				// 更新下次重试时间
				var result map[string]interface{}
				if err := json.Unmarshal([]byte(resp), &result); err != nil {
					svc.log.Error("Failed to parse response JSON")
					return
				}
				subStatusNotifyInfo.NextNotifyTime.Time = subStatusNotifyInfo.NextNotifyTime.Time.Add(30 * time.Second)
				subStatusNotifyInfo.FailedReason = result["msg"].(string)
				continue
			}

			// 成功更新状态
			subStatusNotifyInfo.NotifyStatus = constant.NotifySuccess

		}

		// 更新重试次数和总状态
		successCount := 0
		for _, subStatusNotifyInfo := range subStatusNotifyInfoList {
			if subStatusNotifyInfo.NotifyStatus == constant.NotifySuccess {
				successCount += 1
			}
		}

		updateNotify := make(map[string]interface{})
		if successCount == len(subStatusNotifyInfoList) {
			updateNotify["notify_status"] = constant.NotifySuccess
		}
		retryCount := agencyNotify.SendRetryCount + 1
		if retryCount == 10 { // 最后一次机会
			// 总状态更新为失败
			if successCount != len(subStatusNotifyInfoList) {
				updateNotify["notify_status"] = constant.NotifyFailed
			}
			// 子等待状态更新为失败
			for _, subStatusNotifyInfo := range subStatusNotifyInfoList {
				if subStatusNotifyInfo.NotifyStatus == constant.NotifySendingRetry {
					subStatusNotifyInfo.NotifyStatus = constant.NotifyFailed
				}
			}
		}
		updateSubStatusNotifyInfo, _ := json.Marshal(subStatusNotifyInfoList)
		updateNotify["sub_notify_status"] = updateSubStatusNotifyInfo
		updateNotify["send_retry_count"] = retryCount
		err = agencyNotify.AgencyNotifyUpdate(nil, &db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{"id": notifyRetry.ID},
		}, updateNotify)
		if err != nil {
			svc.log.WithFields(map[string]interface{}{
				"agency_notify_id": notifyRetry.ID,
				"updateNotify":     updateNotify,
			}).ErrorE(err, "get agency notify by id failed")
			continue
		}

	}
}

func (svc *WorkOrderService) cronSubscribeWorkOrderFinishNotify(ctx context.Context) {
	svc.log.Info("cronSubscribeWorkOrderFinishNotify start...")
	svc.doSubscribeWorkOrderFinishNotify()
	ticker := time.NewTicker(time.Second * 30)
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			svc.doSubscribeWorkOrderFinishNotify()
		}
	}
}

func (svc *WorkOrderService) doSubscribeWorkOrderFinishNotify() {
	workOrder := &model.WorkOrder{}

	workOrderList, err := workOrder.WorkOrderGetAll(db_helper.QueryFilters{
		InFilters: []db_helper.In{{
			Key: "status",
			InSet: []constant.WorkOrderStatus{
				constant.PressureMeasurement,
				constant.ShutdownMaintenance,
				constant.Processing,
				constant.WaitProcess},
		}},
		NullField: []string{"deleted_at"},
	})
	if err != nil {
		svc.log.ErrorE(err, "get work order in doSubscribeWorkOrderFinishNotify")
		return
	}

	for _, workOrder := range workOrderList {
		count, err := svc.gpuStock.CountMachineGpuStockUsed(workOrder.MachineId, constant.PriorityType(10))
		if err != nil {
			svc.log.ErrorE(err, "CountMachineGpuStockUsed failed")
			continue
		}

		if count != 0 {
			continue
		}

		// 发送消息
		subNotifyStatus := make([]*model.SubNotify, 0)
		err = json.Unmarshal(workOrder.IsSendMachineIdleNotify, &subNotifyStatus)
		if err != nil {
			continue
		}

		isUpdate := false
		for _, subNotifyInfo := range subNotifyStatus {
			if subNotifyInfo.IsSendMsg {
				continue
			}

			user, err := svc.GetWorkOrderTenantUserByUserId(subNotifyInfo.UserId)
			if err != nil {
				svc.log.ErrorE(err, "get sms assign send user failed")
				continue
			}

			machine := &model2.Machine{}
			err = machine.MachineGet(&db_helper.QueryFilters{EqualFilters: map[string]interface{}{"machine_id": workOrder.MachineId}})
			if err != nil {
				svc.log.WithField("machine_id", workOrder.MachineId).WarnE(err, "get machine by id failed")
				continue
			}
			if user.FeishuBotUrl != "" {
				_, err := svc.SendCustomerWorkOrderMsg(user.FeishuBotUrl, constant.FeishuMsgTypeInteractive, map[string]interface{}{
					"type": "template",
					"data": map[string]interface{}{
						"template_id":           constant.FeishuTemplateIDWOMachineIdleSubscribe,
						"template_version_name": constant.FeishuTemplateVersionWOMachineIdleSubscribe,
						"template_variable": map[string]interface{}{
							"machine_id":   workOrder.MachineId,
							"machine_name": machine.MachineName,
						},
					},
				})
				if err != nil {
					svc.log.WithFields(map[string]interface{}{
						"template_id":           constant.FeishuTemplateIDWOMachineIdleSubscribe,
						"template_version_name": constant.FeishuTemplateVersionWOMachineIdleSubscribe,
						"template_variable": map[string]interface{}{
							"machine_id":   workOrder.MachineId,
							"machine_name": machine.MachineName,
						},
					}).ErrorE(err, "send feishu message about subscribe machine idle failed")
				} else {
					subNotifyInfo.IsSendMsg = true
					isUpdate = true
				}
			}
		}

		if !isUpdate {
			continue
		}

		subNotifyStatusInfo, _ := json.Marshal(subNotifyStatus)
		err = workOrder.WorkOrderUpdate(nil, &db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{"id": workOrder.ID},
		}, map[string]interface{}{
			"is_send_machine_idle_notify": subNotifyStatusInfo,
		})
		if err != nil {
			svc.log.WithField("work_order_id", workOrder.ID).Error("update work order in doSubscribeWorkOrderFinishNotify failed .")
			continue
		}

	}
}
