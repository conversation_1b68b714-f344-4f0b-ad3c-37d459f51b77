package service

import (
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"server/pkg-work-order/work_order/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"
)

func (svc *WorkOrderService) CreateWorkOrderRepair(params *model.CreateWorkOrderRepairReq, uuid string) (err error) {
	// 判断机器是否存在
	machine, err := svc.GetWorkOrderMachineByMachineID(params.MachineID)
	if err != nil {
		log.WithError(err).WithField("machine_id", params.MachineID).Error("get machine failed.")
		return
	}
	if machine.ID == 0 {
		err = businesserror.ErrWorkOrderMachineNotExist
		return err
	}
	if machine.CustomID != params.CustomerID {
		err = businesserror.ErrMachineNotExistInTenant
		return err
	}

	// 获取创建人
	createUser, err := svc.GetWorkOrderCreateUser(uuid, params.CustomerID)
	if err != nil {
		svc.log.WithError(err).WithField("autodl_uuid", uuid).WithField("custom_id", params.CustomerID).Error("get create user failed.")
		return
	}

	repair := &model.WorkOrderRepair{
		MachineID:    params.MachineID,
		MachineRoom:  params.MachineRoom,
		ProviderID:   params.ProviderID,
		CustomID:     params.CustomerID,
		CreateUserId: createUser.ID,
		Remark:       params.Remark,
	}
	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
		// 创建返修工单
		err = tx.Model(&model.WorkOrderRepair{}).Create(&repair).Error
		if err != nil {
			return
		}
		// 添加返修工单-物料记录
		for _, material := range params.MaterialInfo {
			repairMaterial := model.WorkOrderRepairMaterial{
				MaterialID:    material.MaterialID,
				MaterialCount: int64(material.Count),
				RepairID:      repair.ID,
			}
			err = tx.Model(&model.WorkOrderRepairMaterial{}).Create(&repairMaterial).Error
			if err != nil {
				return
			}
		}
		return nil
	})
	if err != nil {
		svc.log.WithError(err).Error("db failed")
		return businesserror.ErrServerBusy
	}

	return nil
}

func (svc *WorkOrderService) GetWorkOrderCustomRepairList(params *model.GetWorkOrderCustomRepairReq) (paged *db_helper.PagedData, list []*model.WorkOrderRepair, err error) {
	list = make([]*model.WorkOrderRepair, 0)

	db := db_helper.GlobalDBConn().Table(model.TableNameWorkOrderRepair).
		Where("deleted_at is null").Where("custom_id = ?", params.CustomID)

	if params.Status != "" {
		db = db.Where("status = ?", params.Status)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Error("Count failed.")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged = db_helper.BuildPagedDataUtil(params.PageIndex, params.PageSize, int(count), 10)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("created_at desc").
		Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find work order user failed")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged.List = &list
	return
}

func (svc *WorkOrderService) GetWorkOrderRepairByDispatchOrderId(dispatchOrderId int) ([]model.WorkOrderRepair, error) {
	var workOrderRepairs []model.WorkOrderRepair

	err := db_helper.GlobalDBConn().Model(&model.WorkOrderRepair{}).Where("deleted_at is null").
		Where("dispatch_order_id = ?", dispatchOrderId).Find(&workOrderRepairs).Error

	if err != nil {
		svc.log.WithError(err).Error("db find failed")
		return nil, businesserror.ErrServerBusy
	}
	return workOrderRepairs, nil
}

func (svc *WorkOrderService) GetWorkOrderProviderRepairList(params *model.GetWorkOrderProviderRepairReq) (paged *db_helper.PagedData, list []*model.WorkOrderRepair, err error) {
	list = make([]*model.WorkOrderRepair, 0)

	db := db_helper.GlobalDBConn().Table(model.TableNameWorkOrderRepair).
		Where("deleted_at is null").Where("provider_id = ?", params.ProviderID)

	if params.Status != "" {
		db = db.Where("status = ?", params.Status)
	}
	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Error("Count failed.")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged = db_helper.BuildPagedDataUtil(params.PageIndex, params.PageSize, int(count), 10)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("created_at desc").
		Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find work order user failed")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged.List = &list
	return
}

func (svc *WorkOrderService) GetWorkOrderMaterialByRepairID(repairId int) (materials []model.MaterialInfo, err error) {
	var workOrderRepairMaterials []model.WorkOrderRepairMaterial
	err = db_helper.GlobalDBConn().Model(&model.WorkOrderRepairMaterial{}).Where("deleted_at is null").
		Where("repair_id = ?", repairId).
		Find(&workOrderRepairMaterials).Error

	if err != nil {
		svc.log.WithError(err).Error("db find failed")
		return nil, businesserror.ErrServerBusy
	}

	for _, workOrderRepairMaterial := range workOrderRepairMaterials {
		// 获取单个material的详细信息
		material, err := svc.GetWorkOrderMaterialByMaterialID(workOrderRepairMaterial.MaterialID)
		if err != nil {
			svc.log.WithError(err).Error("db find failed")
			return nil, err
		}
		materials = append(materials, model.MaterialInfo{
			MaterialId:   workOrderRepairMaterial.MaterialID,
			MaterialSort: string(material.Sort),
			MaterialName: material.Name,
			Count:        int(workOrderRepairMaterial.MaterialCount),
		})
	}
	return materials, nil
}

func (svc *WorkOrderService) CreateDispatchOrderRepair(params *model.CreateDispatchOrderRepairReq, uuid string) (dispatchOrderRepairID int, err error) {
	if len(params.WorkOrderRepairIDs) == 0 {
		err = businesserror.ErrWorkOrderRepairNotExistInDispatchOrder
		return 0, err
	}
	// 获取创建人
	createUser, err := svc.GetWorkOrderCreateUser(uuid, params.CustomID)
	if err != nil {
		log.WithError(err).WithField("autodl_uuid", uuid).WithField("custom_id", params.CustomID).Error("get create user failed.")
		return 0, nil
	}

	dispatchOrderRepair := &model.DispatchOrderRepair{
		ProviderID:       params.ProviderID,
		CustomID:         params.CustomID,
		MachineRoom:      params.MachineRoom,
		RecipientName:    params.RecipientName,
		RecipientPhone:   params.RecipientPhone,
		RecipientAddress: params.RecipientAddress,
		ExpressNumber:    params.ExpressNumber,
		CreateUserId:     createUser.ID,
		Status:           constant.DispatchOrderRepairWaitShipped,
	}

	// 创建返修发货单
	err = db_helper.GlobalDBConn().Model(&model.DispatchOrderRepair{}).Create(&dispatchOrderRepair).Error
	if err != nil {
		svc.log.WithError(err).Error("create dispatchOrderRepair failed")
		return 0, err
	}

	return dispatchOrderRepair.ID, nil
}

func (svc *WorkOrderService) BindRepairDispatchOrderID(workOrderIds []int, dispatchOrderId int) (err error) {
	err = db_helper.UpdateAll(db_helper.QueryDefinition{
		ModelDefinition: &model.WorkOrderRepair{},
		Filters:         db_helper.QueryFilters{InFilters: []db_helper.In{{Key: "id", InSet: workOrderIds}}},
		UpdatePayload: map[string]interface{}{
			"dispatch_order_id": dispatchOrderId,
			"updated_at":        time.Now(),
			"status":            constant.DispatchOrderRepairWaitShipped},
	}).GetError()
	if err != nil {
		svc.log.WithField("WorkOrderRepairIdList", workOrderIds).WithField("dispatch_order_id", dispatchOrderId).
			WithError(err).Error("bind work order repair dispatch_order_id failed")
		err = businesserror.ErrDatabaseError
	}
	return
}

func (svc *WorkOrderService) GetDispatchOrderCustomRepairList(params *model.GetDispatchOrderCustomRepairListReq) (paged *db_helper.PagedData, list []*model.DispatchOrderRepair, err error) {
	list = make([]*model.DispatchOrderRepair, 0)

	db := db_helper.GlobalDBConn().Table(model.TableNameDispatchOrderRepair).
		Where("deleted_at is null").Where("custom_id = ?", params.CustomID)

	if params.Status != "" {
		db = db.Where("status = ?", params.Status)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Error("Count failed.")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged = db_helper.BuildPagedDataUtil(params.PageIndex, params.PageSize, int(count), 10)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("created_at desc").
		Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find work order user failed")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged.List = &list
	return
}

func (svc *WorkOrderService) GetDispatchOrderProviderRepairList(params *model.GetDispatchOrderProviderRepairListReq) (paged *db_helper.PagedData, list []*model.DispatchOrderRepair, err error) {
	list = make([]*model.DispatchOrderRepair, 0)

	db := db_helper.GlobalDBConn().Table(model.TableNameDispatchOrderRepair).
		Where("deleted_at is null").Where("provider_id = ?", params.ProviderID)

	if params.Status != "" {
		db = db.Where("status = ?", params.Status)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Error("Count failed.")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged = db_helper.BuildPagedDataUtil(params.PageIndex, params.PageSize, int(count), 10)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("created_at desc").
		Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find work order user failed")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged.List = &list
	return
}

func (svc *WorkOrderService) GetWorkOrderRepairNoBindDispatchList(params *model.GetWorkOrderRepairNoBindDispatchListReq) ([]model.GetWorkOrderRepairNoBindDispatch, error) {
	list := make([]model.GetWorkOrderRepairNoBindDispatch, 0)
	var workOrderRepairs []model.WorkOrderRepair
	err := db_helper.GlobalDBConn().Table(model.TableNameWorkOrderRepair).
		Where("deleted_at is null").
		Where("custom_id = ?", params.CustomId).Where("dispatch_order_id = 0").
		Where("status != ?", constant.DispatchOrderRepairClose).
		Find(&workOrderRepairs).Error
	if err != nil {
		svc.log.WithError(err).Error("find machine type failed")
		return list, err
	}

	for _, workOrderRepair := range workOrderRepairs {
		provider, err := svc.GetWorkOrderTenantByTenantId(workOrderRepair.ProviderID)
		if err != nil {
			svc.log.WithError(err).Error("get provider failed")
			return list, err
		}

		materialInfo, err := svc.GetWorkOrderMaterialByRepairID(workOrderRepair.ID)
		if err != nil {
			svc.log.WithError(err).Error("get provider failed")
			return list, err
		}
		list = append(list, model.GetWorkOrderRepairNoBindDispatch{
			Id:           workOrderRepair.ID,
			MaterialInfo: materialInfo,
			MachineId:    workOrderRepair.MachineID,
			ProviderName: provider.Name,
		})
	}
	return list, nil
}

func (svc *WorkOrderService) RepairBindExpressNumber(params *model.RepairBindExpressNumberReq) (err error) {
	err = db_helper.UpdateOne(db_helper.QueryDefinition{
		ModelDefinition: &model.DispatchOrderRepair{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"id": params.DispatchOrderRepairId,
			},
		},
	}, map[string]interface{}{
		"express_number": params.ExpressNumber,
	}).GetError()
	if err != nil {
		svc.log.WithError(err).Error("update DispatchOrderRepair express_number failed")
		return err
	}
	return nil
}

func (svc *WorkOrderService) UpdateDispatchOrderRepairStatus(params *model.UpdateDispatchOrderRepairStatusReq) (err error) {
	dispatchOrder := map[string]interface{}{
		"updated_at": time.Now(),
		"status":     params.Status,
	}

	if params.Status == string(constant.DispatchOrderSparePartShipped) {
		dispatchOrder["dispatch_time"] = time.Now()
	}

	workOrder := map[string]interface{}{
		"updated_at": time.Now(),
		"status":     params.Status,
	}

	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
		err = db_helper.UpdateAll(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.DispatchOrderRepair{},
			Filters:                 db_helper.QueryFilters{InFilters: []db_helper.In{{Key: "id", InSet: params.DispatchOrderRepairIDs}}},
			UpdatePayload:           dispatchOrder,
		}).GetError()
		if err != nil {
			return err
		}

		// 更新该改 返修发货单绑定的返修工单中发货单状态
		err = db_helper.UpdateAll(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.WorkOrderRepair{},
			Filters:                 db_helper.QueryFilters{InFilters: []db_helper.In{{Key: "dispatch_order_id", InSet: params.DispatchOrderRepairIDs}}},
			UpdatePayload:           workOrder,
		}).GetError()
		if err != nil {
			return err
		}
		return nil
	})

	return nil
}

func (svc *WorkOrderService) GetWorkOrderRepairByID(workOrderRepairID int) (model.WorkOrderRepair, error) {
	var workOrderRepair model.WorkOrderRepair

	err := db_helper.GlobalDBConn().Model(&model.WorkOrderRepair{}).Where("deleted_at is null").
		Where("id = ?", workOrderRepairID).
		Find(&workOrderRepair).Error

	if err != nil {
		svc.log.WithError(err).Error("db find failed")
		return model.WorkOrderRepair{}, businesserror.ErrServerBusy
	}
	return workOrderRepair, nil
}

func (svc *WorkOrderService) UpdateWorkOrderRepairStatus(params *model.UpdateWorkOrderRepairStatusReq) (err error) {
	workOrderRepair, err := svc.GetWorkOrderRepairByID(params.WorkOrderRepairID)
	if err != nil {
		svc.log.Error("get work order repair by id failed")
		return err
	}

	if workOrderRepair.ID == 0 {
		err = businesserror.ErrWorkOrderRepairNotExist
		return err
	}

	if workOrderRepair.DispatchOrderID != 0 {
		err = businesserror.ErrWorkOrderRepairHasBoundDispatchOrder
		return err
	}

	data := map[string]interface{}{
		"status":     params.Status,
		"updated_at": time.Now(),
	}

	if params.Status == string(constant.WorkOrderRepairShipped) {
		data["dispatch_time"] = time.Now()
	}

	err = db_helper.UpdateOne(
		db_helper.QueryDefinition{
			ModelDefinition: &model.WorkOrderRepair{},
			Filters: db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"id": params.WorkOrderRepairID,
				},
			},
		}, data).GetError()
	if err != nil {
		svc.log.Error("update work order repair status failed")
		return err
	}
	return nil
}

func (svc *WorkOrderService) GetDispatchOrderRepairByID(id int) (dispatchOrderRepair model.DispatchOrderRepair, err error) {
	err = db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: &model.DispatchOrderRepair{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"id": id,
			},
		},
	}, &dispatchOrderRepair).GetError()

	if err != nil {
		log.WithError(err).WithField("dispatch_order_repair_id", id).Error("get dispatchOrderRepair failed")
		return dispatchOrderRepair, businesserror.ErrDatabaseError
	}
	return
}
