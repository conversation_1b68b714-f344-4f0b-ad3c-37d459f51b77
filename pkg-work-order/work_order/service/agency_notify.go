package service

import (
	"database/sql"
	"server/pkg-work-order/work_order/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"strconv"
	"strings"
	"time"
)

func (svc *WorkOrderService) CreateAgencyNotify(params *model.CreateAgencyNotifyReq, uid int, tenantID int) (err error) {
	if params == nil {
		svc.log.WithField("params", params).Error("Params is nil")
		err = businesserror.ErrInternalError
		return
	}

	strNotifyUser := make([]string, len(params.NotifyUser))
	for i, user := range params.NotifyUser {
		strNotifyUser[i] = strconv.Itoa(user)
	}

	notifyUser := strings.Join(strNotifyUser, ",")
	smsTemplate := &model.AgencyNotify{
		MachineId:     params.MachineId,
		NotifyContent: params.NotifyContent,
		NotifyStatus:  constant.WaitNotify,
		NotifyTime: sql.NullTime{
			Time:  params.NotifyTime,
			Valid: true,
		},
		UserId:     uid,
		TenantId:   tenantID,
		NotifyUser: notifyUser,
	}

	err = smsTemplate.AgencyNotifyCreate(nil)
	if err != nil {
		svc.log.WithError(err).WithField("content", params.NotifyContent).Error("create agency notify failed")
		return
	}

	return nil
}

func (svc *WorkOrderService) DeleteAgencyNotify(notifyId int) (err error) {
	updateTemplate := map[string]interface{}{
		"updated_at": time.Now(),
		"deleted_at": time.Now(),
	}

	smsTemplate := &model.AgencyNotify{}
	err = smsTemplate.AgencyNotifyUpdate(nil, &db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": notifyId}}, updateTemplate)
	if err != nil {
		svc.log.WithField("agency_notify_id", notifyId).WithError(err).Warn("update work order agency notify by id failed.")
		return
	}
	return nil
}

func (svc *WorkOrderService) GetAgencyNotifyList(pageReq *db_helper.GetPagedRangeRequest, tenantID int) (paged *db_helper.PagedData, list []*model.AgencyNotify, err error) {
	list = make([]*model.AgencyNotify, 0)

	db := db_helper.GlobalDBConn().Table(model.TableNameAgencyNotify).
		Where("deleted_at is null").Where("tenant_id = ?", tenantID)

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Error("Count failed.")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged = db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, int(count), 10)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("id desc").
		Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find work order failed")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged.List = &list

	return
}
