package controller

import (
	"github.com/gin-gonic/gin"
	"server/pkg-work-order/work_order/model"
	"server/pkg/businesserror"
	"server/pkg/db_helper"
	"server/pkg/http"
)

func (ctrl *WorkOrderController) CreateInstanceNotify(c *gin.Context) {
	var req model.CreateInstanceNotifyReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	u := http.GetTenantUserInfo(c)

	// 获取创建人
	createUser, err := ctrl.WorkOrderSvc.GetWorkOrderCreateUser(u.UUID, u.TenantId)
	if err != nil {
		ctrl.log.WithError(err).WithField("autodl_uuid", u.UUID).WithField("custom_id", u.TenantId).Error("get create user failed.")
		http.SendError(c, err)
		return
	}

	err = ctrl.WorkOrderSvc.CreateInstanceNotify(&req, createUser.ID, u.TenantId)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *WorkOrderController) UpdateInstanceNotify(c *gin.Context) {
	var req model.UpdateInstanceNotifyReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	err := ctrl.WorkOrderSvc.UpdateInstanceNotify(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)

}

func (ctrl *WorkOrderController) GetInstanceNotifyList(c *gin.Context) {
	var req model.WorkOrderInstanceNotifyListReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	u := http.GetTenantUserInfo(c)
	paged, instanceNotifies, err := ctrl.WorkOrderSvc.GetInstanceNotifyList(&req.WorkOrderInstanceNotifySearchReq, &req.GetPagedRangeRequest, u.TenantId)
	if err != nil {
		http.SendError(c, err)
		return
	}

	if len(instanceNotifies) == 0 {
		paged = &db_helper.PagedData{List: []int{}}
		http.SendOK(c, paged)
		return
	}

	var list []model.GetInstanceNotifyListData
	for _, instanceNotify := range instanceNotifies {
		createdUser, err := ctrl.WorkOrderSvc.GetWorkOrderTenantUserByUserId(instanceNotify.UserId)
		if err != nil {
			ctrl.log.WithField("err", err).Error("get notify user failed")
			http.SendError(c, err)
			return
		}
		info := model.GetInstanceNotifyListData{
			ID:                   instanceNotify.ID,
			CreatedAt:            instanceNotify.CreatedAt,
			MachineId:            instanceNotify.MachineId,
			InstanceNotifyStatus: instanceNotify.InstanceNotifyStatus,
			ReceiptCount:         instanceNotify.ReceiptCount,
			NotifyInfo:           instanceNotify.NotifyInfo,
			UserId:               instanceNotify.UserId,
			UserName:             createdUser.Username,
			TenantId:             instanceNotify.TenantId,
		}
		list = append(list, info)
	}

	paged.List = list
	http.SendOK(c, paged)
	return
}

func (ctrl *WorkOrderController) FinishInstanceNotify(c *gin.Context) {
	var req model.FinishInstanceNotifyReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.WorkOrderSvc.FinishInstanceNotify(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}
