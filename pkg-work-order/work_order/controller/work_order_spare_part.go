package controller

import (
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"server/pkg-work-order/work_order/model"
	"server/pkg/businesserror"
	"server/pkg/http"
)

func (ctrl *WorkOrderController) CreateWorkOrderSparePart(c *gin.Context) {
	var req model.CreateWorkOrderSparePartReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user := http.GetTenantUserInfo(c)
	req.ProviderID = user.TenantId

	err := ctrl.WorkOrderSvc.CreateWorkOrderSparePart(&req, user.UUID)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *WorkOrderController) UpdateWorkOrderSparePartProvider(c *gin.Context) {
	var req model.UpdateWorkOrderSparePartByProviderReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.WorkOrderSvc.UpdateWorkOrderSparePartByProvider(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}
func (ctrl *WorkOrderController) CreateDispatchOrderSparePart(c *gin.Context) {
	var req model.CreateDispatchOrderSparePartReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user := http.GetTenantUserInfo(c)
	req.ProviderID = user.TenantId

	err := ctrl.WorkOrderSvc.CreateDispatchOrderSparePart(&req, user.UUID)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *WorkOrderController) UpdateDispatchOrderSparePartUpdateByProvider(c *gin.Context) {
	var req model.UpdateDispatchOrderUpdateByProviderReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.WorkOrderSvc.UpdateDispatchOrderSparePartUpdateByProvider(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *WorkOrderController) GetWorkOrderSparePartProviderList(c *gin.Context) {
	var (
		req  model.GetWorkOrderSparePartProviderListReq
		resp model.GetWorkOrderSparePartProviderListResp
	)
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user := http.GetTenantUserInfo(c)
	req.ProviderID = user.TenantId
	paged, workOrderSpareParts, err := ctrl.WorkOrderSvc.GetWorkOrderSparePartList(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	resp = model.GetWorkOrderSparePartProviderListResp{
		PagedData: paged,
		List:      make([]model.GetWorkOrderSparePartList, 0, len(workOrderSpareParts)),
	}

	machineMap := make(map[string]model.WorkOrderMachine)
	machineTypeMap := make(map[int]model.WorkOrderMachineType)
	createUserMap := make(map[int]model.WorkOrderOperateUser)
	customMap := make(map[int]model.WorkOrderTenant)
	materialMap := make(map[int]model.WorkOrderMaterial)
	for _, workOrderSparePart := range workOrderSpareParts {
		if _, ok := machineMap[workOrderSparePart.MachineID]; !ok {
			machine, err := ctrl.WorkOrderSvc.GetWorkOrderMachineByMachineID(workOrderSparePart.MachineID)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get machine failed")
				http.SendError(c, err)
				return
			}

			machineMap[workOrderSparePart.MachineID] = machine
		}

		if _, ok := machineTypeMap[machineMap[workOrderSparePart.MachineID].MachineTypeID]; !ok {
			machineType, err := ctrl.WorkOrderSvc.GetWorkOrderMachineTypeByTypeID(machineMap[workOrderSparePart.MachineID].MachineTypeID)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get machine type failed")
				http.SendError(c, err)
				return
			}

			machineTypeMap[machineMap[workOrderSparePart.MachineID].MachineTypeID] = machineType
		}

		if _, ok := createUserMap[workOrderSparePart.CreateUserId]; !ok {
			createUser, err := ctrl.WorkOrderSvc.GetWorkOrderTenantUserByUserId(workOrderSparePart.CreateUserId)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get create user failed")
				http.SendError(c, err)
				return
			}

			createUserMap[workOrderSparePart.CreateUserId] = createUser
		}

		if _, ok := customMap[workOrderSparePart.CustomID]; !ok {
			custom, err := ctrl.WorkOrderSvc.GetWorkOrderTenantByTenantId(workOrderSparePart.CustomID)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get custom failed")
				http.SendError(c, err)
				return
			}

			customMap[workOrderSparePart.CustomID] = custom
		}

		if _, ok := materialMap[workOrderSparePart.MaterialID]; !ok {
			material, err := ctrl.WorkOrderSvc.GetWorkOrderMaterialByMaterialID(workOrderSparePart.MaterialID)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get material failed")
				http.SendError(c, err)
				return
			}

			materialMap[workOrderSparePart.MaterialID] = material
		}

		deliveryTime := workOrderSparePart.DispatchTime

		var dispatchBillID int
		if workOrderSparePart.DispatchBillID != 0 {
			dispatchOrderSparePart, err := ctrl.WorkOrderSvc.GetDispatchOrderSparePartByID(workOrderSparePart.DispatchBillID)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get dispatchOrderSparePart failed")
				http.SendError(c, err)
				return
			}
			dispatchBillID = dispatchOrderSparePart.ID
			deliveryTime = dispatchOrderSparePart.DispatchTime
		}

		resp.List = append(resp.List, model.GetWorkOrderSparePartList{
			ID:              workOrderSparePart.ID,
			MaterialID:      workOrderSparePart.MaterialID,
			MaterialName:    materialMap[workOrderSparePart.MaterialID].Name,
			MaterialCount:   workOrderSparePart.MaterialCount,
			MachineID:       workOrderSparePart.MachineID,
			MachineTypeName: machineTypeMap[machineMap[workOrderSparePart.MachineID].MachineTypeID].Name,
			CustomID:        workOrderSparePart.CustomID,
			CustomName:      customMap[workOrderSparePart.CustomID].Name,
			MachineRoom:     workOrderSparePart.MachineRoom,
			CreateUserName:  createUserMap[workOrderSparePart.CreateUserId].Username,
			Status:          string(workOrderSparePart.Status),
			DispatchBillID:  dispatchBillID,
			CreatedAt:       workOrderSparePart.CreatedAt,
			Remark:          workOrderSparePart.Remark,
			DeliveryTime:    deliveryTime,
		})

	}
	http.SendOK(c, resp)
	return
}

func (ctrl *WorkOrderController) GetWorkOrderSparePartCustomList(c *gin.Context) {
	var (
		req  model.GetWorkOrderSparePartCustomListReq
		resp model.GetWorkOrderSparePartCustomListResp
	)
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user := http.GetTenantUserInfo(c)
	req.CustomID = user.TenantId
	paged, workOrderSpareParts, err := ctrl.WorkOrderSvc.GetWorkOrderSparePartCustomList(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	resp = model.GetWorkOrderSparePartCustomListResp{
		PagedData: paged,
		List:      make([]model.GetWorkOrderSparePartCustomList, 0, len(workOrderSpareParts)),
	}
	providerMap := make(map[int]model.WorkOrderTenant)
	materialMap := make(map[int]model.WorkOrderMaterial)
	dispatchOrderSparePartMap := make(map[int]model.DispatchOrderSparePart)
	for _, workOrderSparePart := range workOrderSpareParts {
		if _, ok := providerMap[workOrderSparePart.ProviderID]; !ok {
			provider, err := ctrl.WorkOrderSvc.GetWorkOrderTenantByTenantId(workOrderSparePart.ProviderID)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get custom failed")
				http.SendError(c, err)
				return
			}

			providerMap[workOrderSparePart.ProviderID] = provider
		}

		if _, ok := materialMap[workOrderSparePart.MaterialID]; !ok {
			material, err := ctrl.WorkOrderSvc.GetWorkOrderMaterialByMaterialID(workOrderSparePart.MaterialID)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get material failed")
				http.SendError(c, err)
				return
			}
			materialMap[workOrderSparePart.MaterialID] = material
		}

		if _, ok := dispatchOrderSparePartMap[workOrderSparePart.DispatchBillID]; !ok {
			dispatchOrderSparePart, err := ctrl.WorkOrderSvc.GetDispatchOrderSparePartByID(workOrderSparePart.DispatchBillID)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get dispatchOrderSparePart failed")
				http.SendError(c, err)
				return
			}

			dispatchOrderSparePartMap[workOrderSparePart.DispatchBillID] = dispatchOrderSparePart
		}

		resp.List = append(resp.List, model.GetWorkOrderSparePartCustomList{
			ID:             workOrderSparePart.ID,
			MaterialName:   materialMap[workOrderSparePart.MaterialID].Name,
			MaterialCount:  workOrderSparePart.MaterialCount,
			MachineID:      workOrderSparePart.MachineID,
			ProviderName:   providerMap[workOrderSparePart.ProviderID].Name,
			Status:         string(workOrderSparePart.Status),
			DispatchBillID: workOrderSparePart.DispatchBillID,
			CreatedAt:      workOrderSparePart.CreatedAt,
			Remark:         workOrderSparePart.Remark,
			ExpressNumber:  dispatchOrderSparePartMap[workOrderSparePart.DispatchBillID].ExpressNumber,
		})

	}
	http.SendOK(c, resp)
	return
}

func (ctrl *WorkOrderController) GetDispatchOrderSparePartList(c *gin.Context) {
	var (
		req  model.GetDispatchOrderSparePartCustomListReq
		resp model.GetDispatchOrderSparePartCustomListResp
	)

	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user := http.GetTenantUserInfo(c)
	req.ProviderID = user.TenantId
	paged, dispatchOrderSpareParts, err := ctrl.WorkOrderSvc.GetDispatchOrderSparePartList(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	resp = model.GetDispatchOrderSparePartCustomListResp{
		PagedData: paged,
		List:      make([]model.GetDispatchOrderSparePartCustomList, 0, len(dispatchOrderSpareParts)),
	}
	createUserMap := make(map[int]model.WorkOrderOperateUser)
	customMap := make(map[int]model.WorkOrderTenant)
	for _, dispatchOrderSparePart := range dispatchOrderSpareParts {
		if _, ok := createUserMap[dispatchOrderSparePart.CreateUserId]; !ok {
			createUser, err := ctrl.WorkOrderSvc.GetWorkOrderTenantUserByUserId(dispatchOrderSparePart.CreateUserId)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get create user failed")
				http.SendError(c, err)
				return
			}

			createUserMap[dispatchOrderSparePart.CreateUserId] = createUser
		}

		if _, ok := customMap[dispatchOrderSparePart.CustomID]; !ok {
			custom, err := ctrl.WorkOrderSvc.GetWorkOrderTenantByTenantId(dispatchOrderSparePart.CustomID)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get custom failed")
				http.SendError(c, err)
				return
			}

			customMap[dispatchOrderSparePart.CustomID] = custom
		}

		// 获取物料
		dispatchOrderSparePartMaterials, err := ctrl.WorkOrderSvc.GetDispatchOrderSparePartMaterial(dispatchOrderSparePart.ID)
		if err != nil {
			log.WithError(err).Error("get custom failed")
			http.SendError(c, err)
			return
		}
		materialInfo := make([]model.MaterialInfo, 0)
		for _, dispatchOrderSparePartMaterial := range dispatchOrderSparePartMaterials {
			dispatchOrderMaterial, err := ctrl.WorkOrderSvc.GetWorkOrderMaterialByMaterialID(dispatchOrderSparePartMaterial.MaterialID)
			if err != nil {
				ctrl.log.WithError(err).Error("get work order material by materialID failed")
				http.SendError(c, err)
				return
			}
			material := model.MaterialInfo{
				MaterialId:   dispatchOrderSparePartMaterial.MaterialID,
				MaterialSort: string(dispatchOrderMaterial.Sort),
				MaterialName: dispatchOrderMaterial.Name,
				Count:        int(dispatchOrderSparePartMaterial.MaterialCount),
			}

			materialInfo = append(materialInfo, material)
		}
		resp.List = append(resp.List, model.GetDispatchOrderSparePartCustomList{
			Id:               dispatchOrderSparePart.ID,
			MaterialInfo:     materialInfo,
			Status:           string(dispatchOrderSparePart.Status),
			MachineRoom:      dispatchOrderSparePart.MachineRoom,
			RecipientName:    dispatchOrderSparePart.RecipientName,
			RecipientPhone:   dispatchOrderSparePart.RecipientPhone,
			RecipientAddress: dispatchOrderSparePart.RecipientAddress,
			ExpressNumber:    dispatchOrderSparePart.ExpressNumber,
			CustomID:         dispatchOrderSparePart.CustomID,
			CustomName:       customMap[dispatchOrderSparePart.CustomID].Name,
			CreateUserName:   createUserMap[dispatchOrderSparePart.CreateUserId].Username,
			CreatedAt:        dispatchOrderSparePart.CreatedAt,
			DeliveryTime:     dispatchOrderSparePart.DispatchTime,
		})
	}
	http.SendOK(c, resp)
	return
}

func (ctrl *WorkOrderController) BindDispatchOrderID(c *gin.Context) {
	var req model.BindDispatchOrderIDReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user := http.GetTenantUserInfo(c)
	req.ProviderID = user.TenantId
	err := ctrl.WorkOrderSvc.BindDispatchOrderID(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *WorkOrderController) BindExpressNumber(c *gin.Context) {
	var req model.BindExpressNumberReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.WorkOrderSvc.BindExpressNumber(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *WorkOrderController) UpdateDispatchOrderSparePartStatus(c *gin.Context) {
	var req model.UpdateDispatchOrderSparePartStatusReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.WorkOrderSvc.UpdateDispatchOrderSparePartStatus(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *WorkOrderController) UpdateWorkOrderSparePartStatus(c *gin.Context) {
	var req model.UpdateWorkOrderSparePartStatusReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.WorkOrderSvc.UpdateWorkOrderSparePartStatus(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)

}
