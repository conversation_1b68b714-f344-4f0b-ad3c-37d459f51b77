package controller

import (
	"github.com/gin-gonic/gin"
	"server/pkg-work-order/work_order/model"
	"server/pkg/businesserror"
	"server/pkg/http"
)

func (ctrl *WorkOrderController) CreateWorkOrderMachineType(c *gin.Context) {
	var req model.CreateWorkOrderMachineTypeReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user := http.GetTenantUserInfo(c)
	req.ProviderID = user.TenantId

	err := ctrl.WorkOrderSvc.CreateWorkOrderMachineType(&req, user.UUID)

	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *WorkOrderController) UpdateWorkOrderMachineType(c *gin.Context) {
	var req model.UpdateWorkOrderMachineTypeReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.WorkOrderSvc.UpdateWorkOrderMachineType(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)

}

func (ctrl *WorkOrderController) GetWorkOrderMachineTypeList(c *gin.Context) {
	var (
		req  model.GetWorkOrderMachineTypeListReq
		resp model.GetWorkOrderMachineTypeListResp
	)

	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	user := http.GetTenantUserInfo(c)
	req.ProviderID = user.TenantId
	paged, machineTypes, err := ctrl.WorkOrderSvc.GetWorkOrderMachineTypeList(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	resp = model.GetWorkOrderMachineTypeListResp{
		PagedData: paged,
		List:      make([]model.GetWorkOrderMachineTypeListData, 0, len(machineTypes)),
	}

	for _, machineType := range machineTypes {
		resp.List = append(resp.List, model.GetWorkOrderMachineTypeListData{
			ID:             machineType.ID,
			Name:           machineType.Name,
			MaterialInfo:   machineType.MaterialInfo,
			Remark:         machineType.Remark,
			CreateUserName: machineType.CreateUserName,
			CreatedAt:      machineType.CreatedAt,
			MachineNumber:  machineType.MachineNumber,
		})
	}
	http.SendOK(c, resp)
	return
}

func (ctrl *WorkOrderController) GetWorkOrderMachineTypeInfo(c *gin.Context) {
	var (
		req  model.GetWorkOrderMachineTypeInfoReq
		resp model.GetWorkOrderMachineTypeInfoResp
	)

	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	user := http.GetTenantUserInfo(c)
	req.ProviderID = user.TenantId
	machineTypeInfo, err := ctrl.WorkOrderSvc.GetWorkOrderMachineTypeInfo(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	resp.List = make([]model.GetWorkOrderMachineTypeInfo, 0, len(machineTypeInfo))

	if len(machineTypeInfo) == 0 {
		http.SendOK(c, resp)
		return
	}

	for _, machineType := range machineTypeInfo {
		resp.List = append(resp.List, model.GetWorkOrderMachineTypeInfo{
			MachineTypeID:   machineType.ID,
			MachineTypeName: machineType.Name,
		})
	}

	http.SendOK(c, resp)
	return
}
