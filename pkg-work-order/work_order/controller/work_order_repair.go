package controller

import (
	"github.com/gin-gonic/gin"
	"server/pkg-work-order/work_order/model"
	"server/pkg/businesserror"
	"server/pkg/http"
	"strings"
)

func (ctrl *WorkOrderController) CreateWorkOrderRepair(c *gin.Context) {
	var req model.CreateWorkOrderRepairReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	user := http.GetTenantUserInfo(c)
	req.CustomerID = user.TenantId

	err := ctrl.WorkOrderSvc.CreateWorkOrderRepair(&req, user.UUID)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
	return
}

func (ctrl *WorkOrderController) GetWorkOrderCustomRepairList(c *gin.Context) {
	var (
		req  model.GetWorkOrderCustomRepairReq
		resp model.GetWorkOrderCustomRepairResp
	)
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
	}
	user := http.GetTenantUserInfo(c)
	req.CustomID = user.TenantId
	paged, workOrderRepairs, err := ctrl.WorkOrderSvc.GetWorkOrderCustomRepairList(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	resp = model.GetWorkOrderCustomRepairResp{
		PagedData: paged,
		List:      make([]model.GetWorkOrderCustomRepairData, 0, len(workOrderRepairs)),
	}
	providerMap := make(map[int]model.WorkOrderTenant)
	for _, repair := range workOrderRepairs {
		if _, ok := providerMap[repair.ProviderID]; !ok {
			provider, err := ctrl.WorkOrderSvc.GetWorkOrderTenantByTenantId(repair.ProviderID)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get provider failed")
				http.SendError(c, err)
				return
			}

			providerMap[repair.ProviderID] = provider
		}

		materialInfo, err := ctrl.WorkOrderSvc.GetWorkOrderMaterialByRepairID(repair.ID)
		if err != nil {
			ctrl.log.WithField("err", err).Error("get materialInfo failed")
			http.SendError(c, err)
			return
		}

		deliveryTime := repair.DispatchTime

		var dispatchOrderID int
		if repair.DispatchOrderID != 0 {
			dispatchOrderRepair, err := ctrl.WorkOrderSvc.GetDispatchOrderRepairByID(repair.DispatchOrderID)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get dispatchOrderRepair failed")
				http.SendError(c, err)
				return
			}
			dispatchOrderID = dispatchOrderRepair.ID

			deliveryTime = dispatchOrderRepair.DispatchTime
		}

		resp.List = append(resp.List, model.GetWorkOrderCustomRepairData{
			ID:                    repair.ID,
			MaterialInfo:          materialInfo,
			MachineID:             repair.MachineID,
			MachineRoom:           repair.MachineRoom,
			ProviderName:          providerMap[repair.ProviderID].Name,
			Status:                string(repair.Status),
			DeliveryTime:          deliveryTime,
			CreatedAt:             repair.CreatedAt,
			Remark:                repair.Remark,
			DispatchOrderRepairID: dispatchOrderID,
		})
	}
	http.SendOK(c, resp)
	return
}

func (ctrl *WorkOrderController) GetWorkOrderProviderRepairList(c *gin.Context) {
	var (
		req  model.GetWorkOrderProviderRepairReq
		resp model.GetWorkOrderProviderRepairResp
	)
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
	}

	user := http.GetTenantUserInfo(c)
	req.ProviderID = user.TenantId
	paged, workOrderRepairs, err := ctrl.WorkOrderSvc.GetWorkOrderProviderRepairList(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	resp = model.GetWorkOrderProviderRepairResp{
		PagedData: paged,
		List:      make([]model.GetWorkOrderProviderRepairData, 0, len(workOrderRepairs)),
	}

	customMap := make(map[int]model.WorkOrderTenant)
	for _, repair := range workOrderRepairs {
		if _, ok := customMap[repair.CustomID]; !ok {
			provider, err := ctrl.WorkOrderSvc.GetWorkOrderTenantByTenantId(repair.CustomID)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get provider failed")
				http.SendError(c, err)
				return
			}

			customMap[repair.CustomID] = provider
		}

		materialInfo, err := ctrl.WorkOrderSvc.GetWorkOrderMaterialByRepairID(repair.ID)
		if err != nil {
			ctrl.log.WithField("err", err).Error("get materialInfo failed")
			http.SendError(c, err)
			return
		}

		var expressNumber string

		deliveryTime := repair.DispatchTime
		if repair.DispatchOrderID != 0 {
			dispatchOrderRepair, err := ctrl.WorkOrderSvc.GetDispatchOrderRepairByID(repair.DispatchOrderID)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get dispatchOrderRepair failed")
				http.SendError(c, err)
				return
			}

			expressNumber = dispatchOrderRepair.ExpressNumber
			deliveryTime = dispatchOrderRepair.DispatchTime
		}

		resp.List = append(resp.List, model.GetWorkOrderProviderRepairData{
			ID:                    repair.ID,
			MaterialInfo:          materialInfo,
			MachineID:             repair.MachineID,
			MachineRoom:           repair.MachineRoom,
			CustomName:            customMap[repair.CustomID].Name,
			Status:                string(repair.Status),
			CreatedAt:             repair.CreatedAt,
			Remark:                repair.Remark,
			DispatchOrderRepairID: repair.DispatchOrderID,
			DeliveryTime:          deliveryTime,
			ExpressNumber:         expressNumber,
		})
	}
	http.SendOK(c, resp)
	return
}

func (ctrl *WorkOrderController) CreateDispatchOrderRepair(c *gin.Context) {
	var req model.CreateDispatchOrderRepairReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user := http.GetTenantUserInfo(c)
	req.CustomID = user.TenantId

	dispatchOrderID, err := ctrl.WorkOrderSvc.CreateDispatchOrderRepair(&req, user.UUID)
	if err != nil {
		http.SendError(c, err)
		return
	}

	// 更新返修工单表
	err = ctrl.WorkOrderSvc.BindRepairDispatchOrderID(req.WorkOrderRepairIDs, dispatchOrderID)
	if err != nil {
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)
	return
}

func (ctrl *WorkOrderController) GetDispatchOrderCustomRepairList(c *gin.Context) {
	var (
		req  model.GetDispatchOrderCustomRepairListReq
		resp model.GetDispatchOrderCustomRepairListResp
	)
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	user := http.GetTenantUserInfo(c)
	req.CustomID = user.TenantId
	paged, dispatchOrderRepairs, err := ctrl.WorkOrderSvc.GetDispatchOrderCustomRepairList(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	resp = model.GetDispatchOrderCustomRepairListResp{
		PagedData: paged,
		List:      make([]model.GetDispatchOrderCustomRepairList, 0, len(dispatchOrderRepairs)),
	}
	providerMap := make(map[int]model.WorkOrderTenant)
	createUserMap := make(map[int]model.WorkOrderOperateUser)
	for _, dispatchOrderRepair := range dispatchOrderRepairs {
		if _, ok := providerMap[dispatchOrderRepair.ProviderID]; !ok {
			provider, err := ctrl.WorkOrderSvc.GetWorkOrderTenantByTenantId(dispatchOrderRepair.ProviderID)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get provider failed")
				http.SendError(c, err)
				return
			}

			providerMap[dispatchOrderRepair.ProviderID] = provider
		}

		if _, ok := createUserMap[dispatchOrderRepair.CreateUserId]; !ok {
			createUser, err := ctrl.WorkOrderSvc.GetWorkOrderTenantUserByUserId(dispatchOrderRepair.CreateUserId)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get create user failed")
				http.SendError(c, err)
				return
			}

			createUserMap[dispatchOrderRepair.CreateUserId] = createUser
		}

		workOrderRepairs, err := ctrl.WorkOrderSvc.GetWorkOrderRepairByDispatchOrderId(dispatchOrderRepair.ID)
		if err != nil {
			ctrl.log.WithField("err", err).Error("get workOrderRepairs failed")
			http.SendError(c, err)
			return
		}

		// 获取发货内容和备注
		var remarks []string
		var mergedMaterials []model.MaterialInfo
		materialCountMap := make(map[int]int)
		for _, workOrderRepair := range workOrderRepairs {
			remarks = append(remarks, workOrderRepair.Remark)
			// 获取物料信息
			// 单个返修发货单--n个返修工单 单个返修工单--n种材料
			materials, err := ctrl.WorkOrderSvc.GetWorkOrderMaterialByRepairID(workOrderRepair.ID)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get materials failed")
				http.SendError(c, err)
				return
			}
			for _, material := range materials {
				materialCountMap[material.MaterialId] += material.Count
			}
		}

		mergedRemarks := strings.Join(remarks, "#")

		for materialId, count := range materialCountMap {
			material, err := ctrl.WorkOrderSvc.GetWorkOrderMaterialByMaterialID(materialId)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get material by id failed")
				http.SendError(c, err)
				return
			}
			mergedMaterials = append(mergedMaterials, model.MaterialInfo{
				MaterialId:   materialId,
				MaterialName: material.Name,
				Count:        count,
				MaterialSort: string(material.Sort),
			})
		}

		resp.List = append(resp.List, model.GetDispatchOrderCustomRepairList{
			Id:               dispatchOrderRepair.ID,
			MaterialInfo:     mergedMaterials,
			Status:           string(dispatchOrderRepair.Status),
			ProviderName:     providerMap[dispatchOrderRepair.ProviderID].Name,
			MachineRoom:      dispatchOrderRepair.MachineRoom,
			RecipientName:    dispatchOrderRepair.RecipientName,
			RecipientPhone:   dispatchOrderRepair.RecipientPhone,
			RecipientAddress: dispatchOrderRepair.RecipientAddress,
			ExpressNumber:    dispatchOrderRepair.ExpressNumber,
			DispatchTime:     dispatchOrderRepair.DispatchTime,
			CreateUserName:   createUserMap[dispatchOrderRepair.CreateUserId].Username,
			CreateAt:         dispatchOrderRepair.CreatedAt,
			Remark:           mergedRemarks,
		})
	}
	http.SendOK(c, resp)
	return
}

func (ctrl *WorkOrderController) GetDispatchOrderProviderRepairList(c *gin.Context) {
	var (
		req  model.GetDispatchOrderProviderRepairListReq
		resp model.GetDispatchOrderProviderRepairListResp
	)
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	user := http.GetTenantUserInfo(c)
	req.ProviderID = user.TenantId
	paged, dispatchOrderRepairs, err := ctrl.WorkOrderSvc.GetDispatchOrderProviderRepairList(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	resp = model.GetDispatchOrderProviderRepairListResp{
		PagedData: paged,
		List:      make([]model.GetDispatchOrderProviderRepairList, 0, len(dispatchOrderRepairs)),
	}

	customMap := make(map[int]model.WorkOrderTenant)
	createUserMap := make(map[int]model.WorkOrderOperateUser)
	for _, dispatchOrderRepair := range dispatchOrderRepairs {

		if _, ok := customMap[dispatchOrderRepair.CustomID]; !ok {
			custom, err := ctrl.WorkOrderSvc.GetWorkOrderTenantByTenantId(dispatchOrderRepair.CustomID)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get provider failed")
				http.SendError(c, err)
				return
			}

			customMap[dispatchOrderRepair.CustomID] = custom
		}

		if _, ok := createUserMap[dispatchOrderRepair.CreateUserId]; !ok {
			createUser, err := ctrl.WorkOrderSvc.GetWorkOrderTenantUserByUserId(dispatchOrderRepair.CreateUserId)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get create user failed")
				http.SendError(c, err)
				return
			}

			createUserMap[dispatchOrderRepair.CreateUserId] = createUser
		}

		// 获取绑定的返修工单
		workOrderRepairs, err := ctrl.WorkOrderSvc.GetWorkOrderRepairByDispatchOrderId(dispatchOrderRepair.ID)
		if err != nil {
			ctrl.log.WithField("err", err).Error("get workOrderRepairs failed")
			http.SendError(c, err)
			return
		}

		// 获取发货内容和备注
		var remarks []string
		var mergedMaterials []model.MaterialInfo
		materialCountMap := make(map[int]int)
		for _, workOrderRepair := range workOrderRepairs {
			remarks = append(remarks, workOrderRepair.Remark)
			// 获取物料信息
			// 单个返修发货单--n个返修工单 单个返修工单--n种材料
			materials, err := ctrl.WorkOrderSvc.GetWorkOrderMaterialByRepairID(workOrderRepair.ID)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get materials failed")
				http.SendError(c, err)
				return
			}
			for _, material := range materials {
				materialCountMap[material.MaterialId] += material.Count
			}
		}

		mergedRemarks := strings.Join(remarks, "#")

		for materialId, count := range materialCountMap {
			material, err := ctrl.WorkOrderSvc.GetWorkOrderMaterialByMaterialID(materialId)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get material by id failed")
				http.SendError(c, err)
				return
			}
			mergedMaterials = append(mergedMaterials, model.MaterialInfo{
				MaterialId:   materialId,
				MaterialName: material.Name,
				Count:        count,
				MaterialSort: string(material.Sort),
			})
		}

		resp.List = append(resp.List, model.GetDispatchOrderProviderRepairList{
			Id:               dispatchOrderRepair.ID,
			MaterialInfo:     mergedMaterials,
			Status:           string(dispatchOrderRepair.Status),
			CustomName:       customMap[dispatchOrderRepair.CustomID].Name,
			MachineRoom:      dispatchOrderRepair.MachineRoom,
			RecipientName:    dispatchOrderRepair.RecipientName,
			RecipientPhone:   dispatchOrderRepair.RecipientPhone,
			RecipientAddress: dispatchOrderRepair.RecipientAddress,
			ExpressNumber:    dispatchOrderRepair.ExpressNumber,
			DispatchTime:     dispatchOrderRepair.DispatchTime,
			CreateUserName:   createUserMap[dispatchOrderRepair.CreateUserId].Username,
			CreateAt:         dispatchOrderRepair.CreatedAt,
			Remark:           mergedRemarks,
		})
	}
	http.SendOK(c, resp)
	return
}

func (ctrl *WorkOrderController) GetWorkOrderRepairNoBindDispatchList(c *gin.Context) {
	var (
		req  model.GetWorkOrderRepairNoBindDispatchListReq
		resp model.GetWorkOrderRepairNoBindDispatchListResp
	)
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	user := http.GetTenantUserInfo(c)
	req.CustomId = user.TenantId
	list, err := ctrl.WorkOrderSvc.GetWorkOrderRepairNoBindDispatchList(&req)
	if err != nil {
		ctrl.log.WithField("err", err).Error("get GetWorkOrderRepairNoBindDispatch failed")
		http.SendError(c, err)
		return
	}

	resp.List = list
	http.SendOK(c, resp)
	return
}

func (ctrl *WorkOrderController) UpdateDispatchOrderRepairStatus(c *gin.Context) {
	var req model.UpdateDispatchOrderRepairStatusReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.WorkOrderSvc.UpdateDispatchOrderRepairStatus(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *WorkOrderController) RepairBindExpressNumber(c *gin.Context) {
	var req model.RepairBindExpressNumberReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.WorkOrderSvc.RepairBindExpressNumber(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *WorkOrderController) UpdateWorkOrderRepairStatus(c *gin.Context) {
	var req model.UpdateWorkOrderRepairStatusReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.WorkOrderSvc.UpdateWorkOrderRepairStatus(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)

}
