package model

import (
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"server/pkg/db_helper"
	"time"
)

const TableNameWorkOrderSmsTemplate = "work_order_sms_template"

type WorkOrderSmsTemplate struct {
	ID        int            `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`

	Name               string         `gorm:"type:varchar(255);column:name" json:"name"`                          // 模板名称
	AliyunTemplateId   string         `gorm:"int;column:aliyun_template_id" json:"aliyun_template_id"`            // 阿里云模板id
	TemplateContent    string         `gorm:"text;column:template_content" json:"template_content"`               // 短信内容
	TemplateParamsInfo datatypes.JSON `gorm:"type:json;column:template_params_info;" json:"template_params_info"` // 模板参数：是否关联
	UserId             int            `gorm:"type:int;column:user_id" json:"user_id"`                             // 创建用户id
	TenantId           int            `gorm:"type:int;column:tenant_id" json:"tenant_id"`
	//IsInstanceIdAutoAssociation bool           `gorm:"type:tinyint(1);column:is_instance_id_auto_association" json:"is_instance_id_auto_association"` // 实例id是否自动关联
}

func (w *WorkOrderSmsTemplate) TableName() string {
	return TableNameWorkOrderSmsTemplate
}

func (w *WorkOrderSmsTemplate) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&WorkOrderSmsTemplate{})
}

func (w *WorkOrderSmsTemplate) WorkOrderSmsTemplateCreate(tx *gorm.DB) error {
	return db_helper.InsertOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         w,
		InsertPayload:           w,
	}).GetError()
}

func (w *WorkOrderSmsTemplate) WorkOrderSmsTemplateUpdate(tx *gorm.DB, filter *db_helper.QueryFilters, um interface{}) error {
	return db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         w,
		Filters:                 *filter,
	}, um).GetError()
}

func (w *WorkOrderSmsTemplate) WorkOrderSmsTemplateCount(filter *db_helper.QueryFilters) (count int64, err error) {
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: w,
		Filters:         *filter,
	}, &count).GetError()
	return
}

func (w *WorkOrderSmsTemplate) WorkOrderSmsTemplateGet(filter db_helper.QueryFilters) (err error) {
	return db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: w,
		Filters:         filter,
	}, &w).GetError()
}

func (w *WorkOrderSmsTemplate) WorkOrderSmsTemplateGetAll(filter db_helper.QueryFilters) (list []WorkOrderSmsTemplate, err error) {
	list = []WorkOrderSmsTemplate{}
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: w,
		Filters:         filter,
	}, &list).GetError()
	return
}
