package model

import (
	"encoding/json"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"
)

const TableNameCustomerServiceWorkOrder = "customer_service_work_order" // 客服工单

type CustomerServiceWorkOrder struct {
	ID               int                              `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	CreatedAt        time.Time                        `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt        time.Time                        `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt        gorm.DeletedAt                   `gorm:"type:datetime;column:deleted_at;index" json:"-"`
	WorkOrderUUID    string                           `gorm:"type:varchar(255);column:work_order_uuid;" json:"work_order_uuid"`
	MachineId        string                           `gorm:"type:varchar(255);column:machine_id;index" json:"machine_id"`
	MachineName      string                           `gorm:"type:varchar(255);column:machine_name;NOT NULL;" json:"machine_name"` // 主机名称
	InstanceUUID     constant.InstanceUUIDType        `gorm:"type:varchar(255);column:instance_uuid;" json:"instance_uuid"`
	Online           constant.OnOffLine               `gorm:"type:int;column:online;" json:"online"` // 主机状态
	RegionSign       constant.RegionSignType          `gorm:"type:varchar(255);column:region_sign;" json:"region_sign"`
	RegionName       string                           `gorm:"type:varchar(255);column:region_name;NOT NULL;" json:"region_name"` // 地区
	Description      string                           `gorm:"type:text;column:description;" json:"description"`                  // 工单文字描述
	AttachmentInfo   datatypes.JSON                   `gorm:"type:json;column:attachment_info;" json:"attachment_info"`          // 附件图片
	AttachmentEntity AttachmentInfo                   `gorm:"-" json:"-"`
	Status           constant.CustomerWorkOrderStatus `gorm:"type:varchar(255);column:status;" json:"status"`                          // 工单状态
	Tag              string                           `gorm:"type:varchar(255);column:tag;index:idx_tag_fulltext,fulltext" json:"tag"` // 工单标签

	UserId       int `gorm:"type:int;column:user_id" json:"user_id"`               // 创建用户id
	AssignUserId int `gorm:"type:int;column:assign_user_id" json:"assign_user_id"` // 指派人id -->对应operate_user表的id
	CustomID     int `gorm:"type:int;column:custom_id" json:"custom_id"`           // 客户id
}

func (w *CustomerServiceWorkOrder) TableName() string {
	return TableNameCustomerServiceWorkOrder
}

func (w *CustomerServiceWorkOrder) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&CustomerServiceWorkOrder{})
}

type AttachmentInfo struct {
	Attachment []FileData `json:"attachment"`
}

func (w *CustomerServiceWorkOrder) BeforeCreate(db *gorm.DB) (err error) {
	w.AttachmentInfo, _ = json.Marshal(w.AttachmentEntity)
	return nil
}

func (w *CustomerServiceWorkOrder) AfterFind(db *gorm.DB) (err error) {
	if len(w.AttachmentInfo) != 0 {
		err = json.Unmarshal(w.AttachmentInfo, &w.AttachmentEntity)
		if err != nil {
			return
		}
	}
	return
}

func (w *CustomerServiceWorkOrder) CSWorkOrderCreate(tx *gorm.DB) error {
	return db_helper.InsertOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         w,
		InsertPayload:           w,
	}).GetError()
}

func (w *CustomerServiceWorkOrder) CSWorkOrderGet(filter db_helper.QueryFilters) (err error) {
	return db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: w,
		Filters:         filter,
	}, &w).GetError()
}

func (w *CustomerServiceWorkOrder) CSWorkOrderUpdate(tx *gorm.DB, filter *db_helper.QueryFilters, um map[string]interface{}) (err error) {
	um["updated_at"] = time.Now()
	return db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         w,
		Filters:                 *filter,
	}, um).GetError()
}
func (w *CustomerServiceWorkOrder) CSWorkOrderUpdateNoUpdateTime(tx *gorm.DB, filter *db_helper.QueryFilters, um map[string]interface{}) (err error) {
	return db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         w,
		Filters:                 *filter,
	}, um).GetError()
}

func (w *CustomerServiceWorkOrder) CSWorkOrderGetAll(filter db_helper.QueryFilters) (list []CustomerServiceWorkOrder, err error) {
	list = []CustomerServiceWorkOrder{}
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: w,
		Filters:         filter,
	}, &list).GetError()
	return
}
