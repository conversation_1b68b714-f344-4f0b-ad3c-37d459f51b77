package model

import (
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"time"
)

const TableNameWorkOrderRecord = "work_order_record"

type WorkOrderRecord struct {
	ID             int            `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	CreatedAt      time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt      time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt      gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`
	WorkOrderUUID  string         `gorm:"type:varchar(255);column:work_order_uuid;NOT NULL" json:"work_order_id"`
	Content        string         `gorm:"type:text;column:content" json:"content"`
	UserId         int            `gorm:"type:int;column:user_id" json:"user_id"`
	AttachmentInfo datatypes.JSON `gorm:"type:json;column:attachment_info;" json:"attachment_info"`
	CustomID       int            `gorm:"type:int;column:custom_id" json:"custom_id"` // 客户id
}

func (w *WorkOrderRecord) TableName() string {
	return TableNameWorkOrderRecord
}

func (w *WorkOrderRecord) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&WorkOrderRecord{})
}
