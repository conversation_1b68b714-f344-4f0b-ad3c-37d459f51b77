package model

import (
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"

	"gorm.io/gorm"
)

const TableNameWorkOrderOperateRecord = "work_order_operate_record"

type WorkOrderOperateRecord struct {
	ID            int                                 `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	CreatedAt     time.Time                           `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt     time.Time                           `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt     gorm.DeletedAt                      `gorm:"type:datetime;column:deleted_at;index" json:"-"`
	Operate       constant.WorkOrderOperateRecordType `gorm:"type:varchar(255);column:operate;" json:"operate"`
	UserID        int                                 `gorm:"column:user_id"`
	WorkOrderUUID string                              `gorm:"type:varchar(255);column:work_order_uuid;" json:"work_order_uuid"`
	RecordId      int                                 `gorm:"type:int;column:record_id;" json:"record_id"`          // 记录uuid
	AssignUserId  int                                 `gorm:"type:int;column:assign_user_id" json:"assign_user_id"` // 指派人id
	Status        constant.WorkOrderStatus            `gorm:"type:varchar(255);column:status;" json:"status"`       // 工单状态
}

func (w *WorkOrderOperateRecord) TableName() string {
	return TableNameWorkOrderOperateRecord
}

func (w *WorkOrderOperateRecord) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&WorkOrderOperateRecord{})
}

func (w *WorkOrderOperateRecord) WorkOrderOperateCreate() (err error) {
	return db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: w,
		InsertPayload:   w,
	}).GetError()
}
