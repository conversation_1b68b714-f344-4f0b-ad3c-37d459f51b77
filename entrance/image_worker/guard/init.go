package guard

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"github.com/docker/docker/api/types"
	"github.com/docker/docker/client"
	image_worker_guard "server/pkg-image-worker/guard"
	"server/pkg-image-worker/request"
	"server/pkg/logger"
	"time"
)

func RunImageWorkerGuard(ctx context.Context, panicChan chan<- error, serverHost, region, machineID string) {
	l := logger.NewLogger("RunImageWorkerGuard_" + region)
	l.Info("ready to init image worker guard...")
	defer func() {
		l.Info("image worker guard quit...")
	}()

	// init docker client
	l.Info("init docker cli...")
	dockerCli, err := newDockerClient()
	if err != nil {
		l.ErrorE(err, "connect docker deamon failed")
		panicChan <- err
		return
	}

	// docker login
	l.Info("docker login...")
	communityAuth := types.AuthConfig{
		Username:      "seetatech",
		Password:      "vaovfzpewllHMY1rwZduFOMV",
		ServerAddress: "registry.cn-beijing.aliyuncs.com",
	}
	community2Auth := types.AuthConfig{
		Username:      "<EMAIL>",
		Password:      "seetacloud123",
		ServerAddress: "registry.cn-beijing.aliyuncs.com",
	}
	community3Auth := types.AuthConfig{
		Username:      "拓朴思",
		Password:      "vaovfzpewllHMY1rwZduFOMV",
		ServerAddress: "registry.cn-zhangjiakou.aliyuncs.com",
	}
	_, err = dockerCli.RegistryLogin(context.Background(), communityAuth)
	if err != nil {
		l.ErrorE(err, "community docker login failed")
		panicChan <- err
		return
	}
	_, err = dockerCli.RegistryLogin(context.Background(), community2Auth)
	if err != nil {
		l.ErrorE(err, "community2 docker login failed")
		panicChan <- err
		return
	}
	_, err = dockerCli.RegistryLogin(context.Background(), community3Auth)
	if err != nil {
		l.ErrorE(err, "community3 docker login failed")
		panicChan <- err
		return
	}

	dockerAuth, err := base64EncodeAuth(communityAuth)
	if err != nil {
		l.ErrorE(err, "docker encode auth failed")
		panicChan <- err
		return
	}

	docker2Auth, err := base64EncodeAuth(community2Auth)
	if err != nil {
		l.ErrorE(err, "docker2 encode auth failed")
		panicChan <- err
		return
	}
	docker3Auth, err := base64EncodeAuth(community3Auth)
	if err != nil {
		l.ErrorE(err, "docker2 encode auth failed")
		panicChan <- err
		return
	}

	dockerAuthMap := map[string]string{
		"registry.cn-beijing.aliyuncs.com/codewithgpu":      dockerAuth,
		"registry.cn-beijing.aliyuncs.com/codewithgpu2":     docker2Auth,
		"registry.cn-zhangjiakou.aliyuncs.com/codewithgpu3": docker3Auth,
	}

	// server init
	l.Info("ping server...")
	err = request.InitAndPing(serverHost)
	if err != nil {
		l.ErrorE(err, "ping server failed")
		panicChan <- err
		return
	}

	l.Info("run guard...")
	guard := image_worker_guard.NewGuard(dockerCli, "", docker2Auth, dockerAuthMap, region, machineID)
	guard.Run(ctx, panicChan)
}

func newDockerClient() (cli *client.Client, err error) {
	dockerSocketPath := "unix:///var/run/docker.sock"
	cli, err = client.NewClientWithOpts(
		client.WithHost(dockerSocketPath),
		// client.WithTimeout(time.Second*5),
		client.WithVersion("1.40"),
	)
	if err != nil {
		return
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()
	_, err = cli.Ping(ctx)
	return
}

func base64EncodeAuth(auth types.AuthConfig) (string, error) {
	var buf bytes.Buffer
	if err := json.NewEncoder(&buf).Encode(auth); err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(buf.Bytes()), nil
}
