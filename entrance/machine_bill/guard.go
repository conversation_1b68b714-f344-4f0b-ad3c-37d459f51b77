package machine_bill

import (
	"context"
	"fmt"
	"server/conf"
	billModel "server/pkg/billing_center/model"
	"server/pkg/common"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/libs"
	"server/pkg/logger"
	machineModel "server/pkg/machine/model"
	"server/plugin/mysql_plugin"
	"time"

	"github.com/pkg/errors"
	"gorm.io/gorm"
)

type fakeRedisTimer struct {
}

func (r fakeRedisTimer) Now() time.Time {
	return time.Now()
}

var l *logger.Logger

func RunSyncMachineBill(ctx context.Context, panicChan chan<- error) {
	l = logger.NewLogger("RunSyncMachineBill")
	l.Info("ready to init ...")
	defer func() {
		l.Info("Synchronize machine-level bills quit...")
	}()

	globalConf := conf.GetGlobalGsConfig()
	timer := fakeRedisTimer{} // fake redis record
	dbConn, err := mysql_plugin.MySQLProvider(&mysql_plugin.MySQLOptions{
		URL: fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			globalConf.MySQL.User,
			globalConf.MySQL.Password,
			globalConf.MySQL.Host,
			globalConf.MySQL.DBName,
		),
		Debug: globalConf.App.DebugLog,
		Timer: timer,
	})
	if err != nil {
		err = errors.Wrap(err, "init database rw failed")
		panicChan <- err
		return
	}
	dbConnRO, err := mysql_plugin.MySQLProvider(&mysql_plugin.MySQLOptions{
		URL: fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			globalConf.MySQL.User,
			globalConf.MySQL.Password,
			globalConf.MySQL.HostRO,
			globalConf.MySQL.DBName,
		),
		Debug: globalConf.App.DebugLog,
		Timer: timer,
	})
	if err != nil {
		err = errors.Wrap(err, "init database ro failed")
		panicChan <- err
		return
	}

	l.Info("db init success")
	db_helper.InitDatabaseConnection(dbConn, dbConnRO, l)

	billMachine := billModel.BillByMachine{}
	billRegion := billModel.BillByRegion{}
	_ = billMachine.Init(dbConn)
	_ = billRegion.Init(dbConn)

	l.Info("model migrate success")

	initMachineInfo(ctx)
	syncMachineBill(ctx)

	l.Info("quit Synchronize machine-level bills")
}

var machineInfoMap map[string]machineInfo

func initMachineInfo(ctx context.Context) {
	machineInfoMap = map[string]machineInfo{}
	listMachine := []globalMachineInfo{}

	machine := &machineModel.Machine{}
	err := machine.MachineGetAllWithSelectFromROUnscoped(
		"machine_id,region_sign,machine_name,gpu_name,gpu_number",
		&db_helper.QueryFilters{EqualFilters: map[string]interface{}{}},
		&listMachine,
	)
	if err != nil {
		l.WarnE(err, "initMachineInfo failed")
		return
	}

	for _, v := range listMachine {
		machineInfoMap[v.MachineID] = machineInfo{
			machineName: v.MachineName,
			regionSign:  v.RegionSign,
			gpuName:     v.GpuName,
			gpuNum:      int(v.GpuNumber),
		}
	}
}

func syncMachineBill(ctx context.Context) {
	runningTimeList := syncMachineBillDoGetRunningTimeList()
	syncMachineBillDo(runningTimeList)
	syncRegionBillDo(runningTimeList)
	t := time.NewTicker(time.Minute * 10)

	for {
		select {
		case <-ctx.Done():
			return
		case <-t.C:
			runningTimeList := syncMachineBillDoGetRunningTimeList()
			syncMachineBillDo(runningTimeList)
			syncRegionBillDo(runningTimeList)
		}
	}
}

func syncMachineBillDoGetRunningTimeList() (runningTimeList []time.Time) {
	// 如果上一次没记录, 那么不论此时是什么时候,都执行一次, 时间为当前时间的整时
	//  e.g: 当前时间为 2023-09-01 11:43:38, 那么执行同步的时间就是 2023-09-01 11:00:00
	// 如果上一次有记录, 距离当前时间一小时以内, 则是否执行以当前时间为准
	//  e.g: 记录时间为 2023-09-01 11:00:00, 当前时间为 2023-09-01 11:15:10, 则不执行
	// 如果上一次有记录, 距离当前时间较远, 那么以上一次时间的后一小时开始, 执行到当前时间的整时
	//  e.g: 记录时间为 2023-08-29 10:00:00 那么就会以一小时为跨度, 执行到 2023-09-01 11:00:00 共计 73 次
	now := time.Now()
	runningTimeList = []time.Time{}
	lastTimeStr := common.CommonGet(constant.CommonKeySyncMachineBillLastDoneAt)
	l.Info("CommonKeySyncMachineBillLastDoneAt is %s", lastTimeStr)

	if lastTimeStr == "" {
		t := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, time.Local)
		runningTimeList = append(runningTimeList, t)
	} else {
		lastDoneAt, _ := time.ParseInLocation(constant.FormatTimeString, lastTimeStr, time.Local)
		if now.Sub(lastDoneAt) >= time.Hour {
			// 大于等于一小时, 正常执行, 且上一次执行过的时间要再执行一次(防止因为入账时间原因导致的漏记)
			runningTimeList = append(runningTimeList, lastDoneAt)
			for {
				lastDoneAt = lastDoneAt.Add(time.Hour)
				if lastDoneAt.After(now) {
					break
				}
				runningTimeList = append(runningTimeList, lastDoneAt)
			}
		}
	}
	return
}

// 地区和机器都一小时一次
func syncMachineAndRegionBillDo(runningTimeList []time.Time) {
	now := time.Now()
	for k := range runningTimeList {
		var err error
		createdAt := runningTimeList[k]
		machineBillMap := map[string]*billModel.BillByMachine{}
		regionBillMap := map[constant.RegionSignType]*billModel.BillByRegion{}
		billList := make([]billModel.BillShardingSync, 0)

		_ = db_helper.GlobalDBConnForRead().
			Where("created_at > ? and created_at <= ?", createdAt.Add(-time.Hour), createdAt).
			Where("bill_type in (?)", []constant.BillType{constant.BillTypeCharge, constant.BillTypeRefund}).
			FindInBatches(&billList, 1000, func(tx *gorm.DB, batch int) error {
				for _, bill := range billList {
					// 当前bill所属的machineID, regionSign
					var machineID string
					var regionSign constant.RegionSignType

					if bill.ProductUUID != "" {
						// productUUID 不为空时, 正常情况下,machineID和regionSign都不会为空
						machineID = libs.ParseMachineIDFromProductID(bill.ProductUUID)
						if _, ok := machineInfoMap[machineID]; !ok {
							machine := machineModel.Machine{}
							err = machine.MachineGet(&db_helper.QueryFilters{EqualFilters: map[string]interface{}{"machine_id": machineID}})
							if err != nil {
								l.WithField("machine_id", machineID).WarnE(err, "get machine by id failed")
							}
							machineInfoMap[machineID] = machineInfo{
								machineName: machine.MachineName,
								regionSign:  machine.RegionSign,
								gpuName:     machine.GpuName,
								gpuNum:      int(machine.GpuNumber),
							}
						}

						regionSign = machineInfoMap[machineID].regionSign

						// 允许 machineInfoMap[machineID]不存在,也就是 regionSign 为空的情况
						if machineBillMap[machineID] == nil {
							machineBillMap[machineID] = &billModel.BillByMachine{
								RegionSign: regionSign,
								MachineID:  machineID,
								CreatedAt:  createdAt,
								UpdatedAt:  now,
							}
						}
					} else {
						// productUUID 为空时, 默认为文件存储的计费记录, machineID为空, regionSign不为空
						regionSign = bill.DetailsEntity.RegionSign
					}

					if _, ok := regionBillMap[regionSign]; !ok {
						regionBillMap[regionSign] = &billModel.BillByRegion{
							RegionSign: regionSign,
							CreatedAt:  createdAt,
							UpdatedAt:  now,
						}
					}

					switch bill.SubType {
					case constant.BillSubTypeCreateContainer,
						constant.BillSubTypeCharge,
						constant.BillSubTypeChargeShutdown,
						constant.BillSubTypeShutdown,
						constant.BillSubTypeRenewalContainer,
						constant.BillSubTypeChangeChargeType,
						constant.BillSubTypeUpdateInstance:
						machineBillMap[machineID].InstanceIncome += bill.PayByBalance
						machineBillMap[machineID].PayByVoucher += bill.PayByVoucher
						regionBillMap[regionSign].InstanceIncome += bill.PayByBalance
						regionBillMap[regionSign].PayByVoucher += bill.PayByVoucher

					case constant.BillSubTypeChargeDC,
						constant.BillSubTypeChargeShutdownDC,
						constant.BillSubTypeShutdownDC:
						machineBillMap[machineID].DCIncome += bill.PayByBalance + bill.DetailsEntity.DDPConvertedIncome
						regionBillMap[regionSign].DCIncome += bill.PayByBalance + bill.DetailsEntity.DDPConvertedIncome
						machineBillMap[machineID].PayByVoucher += bill.PayByVoucher + bill.DetailsEntity.DDPConvertedVoucherIncome
						regionBillMap[regionSign].PayByVoucher += bill.PayByVoucher + bill.DetailsEntity.DDPConvertedVoucherIncome

					case constant.BillSubTypeDataDiskSettle,
						constant.BillSubTypeDataDiskChange,
						constant.BillSubTypeDataDiskExpandForPrepay:
						machineBillMap[machineID].DataDiskIncome += bill.PayByBalance
						regionBillMap[regionSign].DataDiskIncome += bill.PayByBalance
						machineBillMap[machineID].PayByVoucher += bill.PayByVoucher
						regionBillMap[regionSign].PayByVoucher += bill.PayByVoucher

					case constant.BillSubTypeFileStorage,
						constant.BillSubTypeAutoFs,
						constant.BillSubTypeHSFSInit,
						constant.BillSubTypeHSFSReinit,
						constant.BillSubTypeHSFSRenewal,
						constant.BillSubTypeHSFSExpand:
						regionBillMap[regionSign].FileStorageIncome += bill.PayByBalance
						regionBillMap[regionSign].PayByVoucher += bill.PayByVoucher

					case constant.BillSubTypeNetDiskExpand,
						constant.BillSubTypeNetDiskRenewal:
						regionBillMap[regionSign].NetDiskIncome += bill.PayByBalance
						regionBillMap[regionSign].PayByVoucher += bill.PayByVoucher

					case constant.BillSubTypeRefundToPayg:
						machineBillMap[machineID].InstanceOutlay -= bill.PayByBalance
						regionBillMap[regionSign].InstanceOutlay -= bill.PayByBalance

					case constant.BillSubTypePrivateImage:
						// 私有镜像计费单独处理
						if _, ok := regionBillMap[constant.FakeRegionSignForPrivateImageCharge]; !ok {
							regionBillMap[constant.FakeRegionSignForPrivateImageCharge] = &billModel.BillByRegion{
								RegionSign: constant.FakeRegionSignForPrivateImageCharge,
								CreatedAt:  createdAt,
								UpdatedAt:  now,
							}
						}
						regionBillMap[constant.FakeRegionSignForPrivateImageCharge].PrivateImageIncome += bill.PayByBalance
						regionBillMap[regionSign].PayByVoucher += bill.PayByVoucher
					}
				}
				return nil
			}).Error

		for machineID, billByMachine := range machineBillMap {
			if !billByMachine.Valid() {
				continue
			}
			err = db_helper.InsertOrUpdateOne(db_helper.QueryDefinition{
				ModelDefinition: billByMachine,
				InsertPayload:   machineBillMap[machineID],
				Filters: db_helper.QueryFilters{
					EqualFilters: map[string]interface{}{
						"machine_id": machineBillMap[machineID].MachineID,
						"created_at": machineBillMap[machineID].CreatedAt,
					}},
			}, &billModel.BillByMachine{}).GetError()
			if err != nil {
				l.WithField("entity", libs.IndentString(machineBillMap[machineID])).ErrorE(err, "billByMachine insertOrUpdate failed")
			}
		}

		for regionSign, billByRegion := range regionBillMap {
			if !billByRegion.Valid() {
				continue
			}
			err = db_helper.InsertOrUpdateOne(db_helper.QueryDefinition{
				ModelDefinition: billByRegion,
				InsertPayload:   regionBillMap[regionSign],
				Filters: db_helper.QueryFilters{
					EqualFilters: map[string]interface{}{
						"region_sign": regionBillMap[regionSign].RegionSign,
						"created_at":  regionBillMap[regionSign].CreatedAt,
					}},
			}, &billModel.BillByRegion{}).GetError()
			if err != nil {
				l.WithField("entity", libs.IndentString(regionBillMap[regionSign])).ErrorE(err, "billByRegion insertOrUpdate failed")
			}
		}

		l.Info("set common %s", createdAt.Format(constant.FormatTimeString))
		err = common.CommonSet(constant.CommonKeySyncMachineBillLastDoneAt, createdAt.Format(constant.FormatTimeString))
		if err != nil {
			l.ErrorE(err, "common set key:SyncMachineBillLastDownAt, value:%s failed", runningTimeList[k].Format(constant.FormatTimeString))
		}
	}
}

// 按机器调整成24小时一次
func syncMachineBillDo(runningTimeList []time.Time) {
	now := time.Now()
	for k := range runningTimeList {
		var err error
		createdAt := runningTimeList[k]
		if createdAt.Hour() != 0 {
			continue
		}
		machineBillMap := map[string]*billModel.BillByMachine{}
		billList := make([]billModel.BillShardingSync, 0)

		_ = db_helper.GlobalDBConnForRead().
			Where("created_at > ? and created_at <= ?", createdAt.AddDate(0, 0, -1), createdAt).
			Where("bill_type in (?)", []constant.BillType{constant.BillTypeCharge, constant.BillTypeRefund}).
			FindInBatches(&billList, 1000, func(tx *gorm.DB, batch int) error {
				for _, bill := range billList {
					// 当前bill所属的machineID, regionSign
					var machineID string
					var regionSign constant.RegionSignType

					if bill.ProductUUID != "" {
						// productUUID 不为空时, 正常情况下,machineID和regionSign都不会为空
						machineID = libs.ParseMachineIDFromProductID(bill.ProductUUID)
						if _, ok := machineInfoMap[machineID]; !ok {
							machine := machineModel.Machine{}
							err = machine.MachineGet(&db_helper.QueryFilters{EqualFilters: map[string]interface{}{"machine_id": machineID}})
							if err != nil {
								l.WithField("machine_id", machineID).WarnE(err, "get machine by id failed")
							}
							machineInfoMap[machineID] = machineInfo{
								machineName: machine.MachineName,
								regionSign:  machine.RegionSign,
								gpuName:     machine.GpuName,
								gpuNum:      int(machine.GpuNumber),
							}
						}

						regionSign = machineInfoMap[machineID].regionSign

						// 允许 machineInfoMap[machineID]不存在,也就是 regionSign 为空的情况
						if machineBillMap[machineID] == nil {
							machineBillMap[machineID] = &billModel.BillByMachine{
								RegionSign:  regionSign,
								MachineID:   machineID,
								MachineName: machineInfoMap[machineID].machineName,
								GpuName:     machineInfoMap[machineID].gpuName,
								GpuNum:      machineInfoMap[machineID].gpuNum,
								CreatedAt:   createdAt,
								UpdatedAt:   now,
							}
						}
					} else {
						// productUUID 为空时, 默认为文件存储的计费记录, machineID为空, regionSign不为空
						regionSign = bill.DetailsEntity.RegionSign
					}

					switch bill.SubType {
					case constant.BillSubTypeCreateContainer,
						constant.BillSubTypeCharge,
						constant.BillSubTypeChargeShutdown,
						constant.BillSubTypeShutdown,
						constant.BillSubTypeRenewalContainer,
						constant.BillSubTypeChangeChargeType,
						constant.BillSubTypeUpdateInstance:
						machineBillMap[machineID].InstanceIncome += bill.PayByBalance
						machineBillMap[machineID].PayByVoucher += bill.PayByVoucher

					case constant.BillSubTypeChargeDC,
						constant.BillSubTypeChargeShutdownDC,
						constant.BillSubTypeShutdownDC:
						machineBillMap[machineID].DCIncome += bill.PayByBalance
						machineBillMap[machineID].PayByVoucher += bill.PayByVoucher

					case constant.BillSubTypeDataDiskSettle,
						constant.BillSubTypeDataDiskChange,
						constant.BillSubTypeDataDiskExpandForPrepay:
						machineBillMap[machineID].DataDiskIncome += bill.PayByBalance
						machineBillMap[machineID].PayByVoucher += bill.PayByVoucher

					case constant.BillSubTypeRefundToPayg:
						machineBillMap[machineID].InstanceOutlay -= bill.PayByBalance
					}
				}
				return nil
			}).Error

		for machineID, billByMachine := range machineBillMap {
			machineBillMap[machineID].InstanceNetWorth = machineBillMap[machineID].InstanceIncome + machineBillMap[machineID].InstanceOutlay
			machineBillMap[machineID].NetWorth = machineBillMap[machineID].InstanceIncome + machineBillMap[machineID].InstanceOutlay + machineBillMap[machineID].DataDiskIncome + machineBillMap[machineID].DCIncome

			if !machineBillMap[machineID].Valid() {
				continue
			}

			err = db_helper.InsertOrUpdateOne(db_helper.QueryDefinition{
				ModelDefinition: billByMachine,
				InsertPayload:   machineBillMap[machineID],
				Filters: db_helper.QueryFilters{
					EqualFilters: map[string]interface{}{
						"machine_id": machineBillMap[machineID].MachineID,
						"created_at": machineBillMap[machineID].CreatedAt,
					}},
			}, &billModel.BillByMachine{}).GetError()
			if err != nil {
				l.WithField("entity", libs.IndentString(machineBillMap[machineID])).ErrorE(err, "billByMachine insertOrUpdate failed")
			}
		}
	}
}

// 按地区每小时更新一次
func syncRegionBillDo(runningTimeList []time.Time) {
	now := time.Now()
	for k := range runningTimeList {
		var err error
		createdAt := runningTimeList[k]
		regionBillMap := map[constant.RegionSignType]*billModel.BillByRegion{}
		billList := make([]billModel.BillShardingSync, 0)

		_ = db_helper.GlobalDBConnForRead().
			Where("created_at > ? and created_at <= ?", createdAt.Add(-time.Hour), createdAt).
			Where("bill_type in (?)", []constant.BillType{constant.BillTypeCharge, constant.BillTypeRefund}).
			FindInBatches(&billList, 1000, func(tx *gorm.DB, batch int) error {
				for _, bill := range billList {
					// 当前bill所属的machineID, regionSign
					var machineID string
					var regionSign constant.RegionSignType

					if bill.ProductUUID != "" {
						// productUUID 不为空时, 正常情况下,machineID和regionSign都不会为空
						machineID = libs.ParseMachineIDFromProductID(bill.ProductUUID)
						if _, ok := machineInfoMap[machineID]; !ok {
							machine := machineModel.Machine{}
							err = machine.MachineGet(&db_helper.QueryFilters{EqualFilters: map[string]interface{}{"machine_id": machineID}})
							if err != nil {
								l.WithField("machine_id", machineID).WarnE(err, "get machine by id failed")
							}
							machineInfoMap[machineID] = machineInfo{
								machineName: machine.MachineName,
								regionSign:  machine.RegionSign,
								gpuName:     machine.GpuName,
								gpuNum:      int(machine.GpuNumber),
							}
						}

						regionSign = machineInfoMap[machineID].regionSign

					} else {
						// productUUID 为空时, 默认为文件存储的计费记录, machineID为空, regionSign不为空
						regionSign = bill.DetailsEntity.RegionSign
					}

					if _, ok := regionBillMap[regionSign]; !ok {
						regionBillMap[regionSign] = &billModel.BillByRegion{
							RegionSign: regionSign,
							CreatedAt:  createdAt,
							UpdatedAt:  now,
						}
					}

					switch bill.SubType {
					case constant.BillSubTypeCreateContainer,
						constant.BillSubTypeCharge,
						constant.BillSubTypeChargeShutdown,
						constant.BillSubTypeShutdown,
						constant.BillSubTypeRenewalContainer,
						constant.BillSubTypeChangeChargeType,
						constant.BillSubTypeUpdateInstance:
						regionBillMap[regionSign].InstanceIncome += bill.PayByBalance
						regionBillMap[regionSign].PayByVoucher += bill.PayByVoucher

					case constant.BillSubTypeChargeDC,
						constant.BillSubTypeChargeShutdownDC,
						constant.BillSubTypeShutdownDC:
						regionBillMap[regionSign].DCIncome += bill.PayByBalance
						regionBillMap[regionSign].PayByVoucher += bill.PayByVoucher

					case constant.BillSubTypeDataDiskSettle,
						constant.BillSubTypeDataDiskChange,
						constant.BillSubTypeDataDiskExpandForPrepay:
						regionBillMap[regionSign].DataDiskIncome += bill.PayByBalance
						regionBillMap[regionSign].PayByVoucher += bill.PayByVoucher

					case constant.BillSubTypeFileStorage,
						constant.BillSubTypeAutoFs,
						constant.BillSubTypeHSFSInit,
						constant.BillSubTypeHSFSReinit,
						constant.BillSubTypeHSFSRenewal,
						constant.BillSubTypeHSFSExpand:
						regionBillMap[regionSign].FileStorageIncome += bill.PayByBalance
						regionBillMap[regionSign].PayByVoucher += bill.PayByVoucher

					case constant.BillSubTypeNetDiskExpand,
						constant.BillSubTypeNetDiskRenewal:
						regionBillMap[regionSign].NetDiskIncome += bill.PayByBalance
						regionBillMap[regionSign].PayByVoucher += bill.PayByVoucher

					case constant.BillSubTypeRefundToPayg:
						regionBillMap[regionSign].InstanceOutlay -= bill.PayByBalance

					case constant.BillSubTypePrivateImage:
						// 私有镜像计费单独处理
						if _, ok := regionBillMap[constant.FakeRegionSignForPrivateImageCharge]; !ok {
							regionBillMap[constant.FakeRegionSignForPrivateImageCharge] = &billModel.BillByRegion{
								RegionSign: constant.FakeRegionSignForPrivateImageCharge,
								CreatedAt:  createdAt,
								UpdatedAt:  now,
							}
						}
						regionBillMap[constant.FakeRegionSignForPrivateImageCharge].PrivateImageIncome += bill.PayByBalance
						regionBillMap[constant.FakeRegionSignForPrivateImageCharge].PayByVoucher += bill.PayByVoucher
					}
				}
				return nil
			}).Error

		for regionSign, billByRegion := range regionBillMap {
			regionBillMap[regionSign].InstanceNetWorth = regionBillMap[regionSign].InstanceIncome + regionBillMap[regionSign].InstanceOutlay
			regionBillMap[regionSign].NetWorth = regionBillMap[regionSign].InstanceIncome + regionBillMap[regionSign].DCIncome + regionBillMap[regionSign].NetDiskIncome + regionBillMap[regionSign].PrivateImageIncome + regionBillMap[regionSign].InstanceOutlay + regionBillMap[regionSign].DataDiskIncome + regionBillMap[regionSign].FileStorageIncome

			if !regionBillMap[regionSign].Valid() {
				continue
			}
			err = db_helper.InsertOrUpdateOne(db_helper.QueryDefinition{
				ModelDefinition: billByRegion,
				InsertPayload:   regionBillMap[regionSign],
				Filters: db_helper.QueryFilters{
					EqualFilters: map[string]interface{}{
						"region_sign": regionBillMap[regionSign].RegionSign,
						"created_at":  regionBillMap[regionSign].CreatedAt,
					}},
			}, &billModel.BillByRegion{}).GetError()
			if err != nil {
				l.WithField("entity", libs.IndentString(regionBillMap[regionSign])).ErrorE(err, "billByRegion insertOrUpdate failed")
			}
		}

		l.Info("set common %s", createdAt.Format(constant.FormatTimeString))
		err = common.CommonSet(constant.CommonKeySyncMachineBillLastDoneAt, createdAt.Format(constant.FormatTimeString))
		if err != nil {
			l.ErrorE(err, "common set key:SyncMachineBillLastDownAt, value:%s failed", runningTimeList[k].Format(constant.FormatTimeString))
		}
	}
}
