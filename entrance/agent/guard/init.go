package guard

import (
	"context"
	"github.com/pkg/errors"
	"net/http"
	"server/pkg-agent/agent_booter"
	constant "server/pkg-agent/agent_constant"
	"server/pkg-agent/agent_container"
	"server/pkg-agent/agent_container_monitor"
	"server/pkg-agent/agent_files"
	"server/pkg-agent/agent_guard"
	"server/pkg-agent/agent_machine_monitor"
	"server/pkg-agent/diff_cache"
	"server/pkg-agent/hardware/resource/cpu_set"
	"server/pkg-agent/messenger"
	storageConstant "server/pkg-storage-agent/storage_agent_constant"
	"server/pkg/logger"
	"time"
)

func RunGuard(ctx context.Context, panicChan chan<- error, serverURL, token, region, localRedisHost string, cpuCoreNumsPerUnit, maxCpuUnitNum int, ossFileChecker bool) {
	l := logger.NewLogger("RunGuard")
	l.Info("ready to init guard...")
	defer func() {
		l.Info("guard quit...")
	}()

	url := constant.ParseUrl(serverURL)
	l.<PERSON>("host", url.GetHost()).Info("server host parsed")

	dockerClient, err := constant.NewDockerClient()
	if err != nil {
		err = errors.Wrap(err, "failed to new docker client")
		panicChan <- err
		return
	}

	checkFilesError := agent_files.CheckBootFilesIsPrepared()
	if checkFilesError != nil {
		panicChan <- checkFilesError
		return
	}

	diff_cache.InitLocalRedis(ctx, l, localRedisHost, ossFileChecker)

	booter := agent_booter.NewBooter(dockerClient)
	params := &constant.NewContainerParam{}
	container := agent_container.NewContainer(params, dockerClient)
	containerMonitor := agent_container_monitor.NewContainerFinder(dockerClient, container)

	// 如果 cpuCoreNumsPerUnit为0, 不会报错
	err = cpu_set.CpuSetHardwareDriverInit(l, cpuCoreNumsPerUnit, maxCpuUnitNum)
	if err != nil {
		err = errors.Wrap(err, "CpuSetHardwareDriverInit failed")
		panicChan <- err
		return
	}

	machineMonitor := agent_machine_monitor.NewMachineMonitor(dockerClient, region)
	machineHardWareInfo, err := machineMonitor.GetMachineHardwareInfo()
	if err != nil {
		err = errors.Wrap(err, "failed to get machine hardware info")
		panicChan <- err
		return
	}

	l.WithField("machine id", machineHardWareInfo.MachineID).Info("machine id detected")

	//err = logger.InitHookToSendLogToAPI(ctx, machineHardWareInfo.MachineID, token, url.GetSaveLogUrls())
	//if err != nil {
	//	l.WarnE(err, "can not connect to log server")
	//	err = nil
	//}

	m, err := tryConnectToServer(url, token, machineHardWareInfo.MachineID, region)
	if err != nil {
		err = errors.Wrap(err, "failed to exec messenger dial")
		l.E(err)
		panicChan <- err
		return
	}

	guard := agent_guard.NewGuard(machineHardWareInfo.MachineID, machineHardWareInfo.MachineName, region, m, booter, containerMonitor, machineMonitor, dockerClient)
	guard.Run(ctx, panicChan)
	return
}

func tryConnectToServer(url *constant.URL, token string, machineID string, region string) (m *messenger.Messenger, err error) {
	header := &http.Header{}
	header.Set(constant.AgentAuthorizationHeaderKey, token)
	header.Set(constant.AgentMachineIDHeaderKey, machineID)

	if len(region) != 0 {
		header.Set(storageConstant.AgentRegionSignHeaderKey, region)
	}

	for _, u := range url.GetWebsocketConnectUrls() {
		m, err = connectToServer(u, header)
		if err == nil {
			constant.ServerConnectedURL = url
			return
		}
	}
	return
}

func connectToServer(url string, h *http.Header) (m *messenger.Messenger, err error) {
	m = messenger.NewMessengerForClient(url, *h)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	err = m.Dial(ctx)
	return
}
