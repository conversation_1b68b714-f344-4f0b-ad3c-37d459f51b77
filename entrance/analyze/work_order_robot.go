package analyze

import (
	"context"
	"fmt"
	"github.com/levigross/grequests"
	"github.com/pkg/errors"
	"server/pkg-work-order/work_order/model"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/logger"
	"time"
)

var (
	testWorkOrderRobotWebHook = "https://open.feishu.cn/open-apis/bot/v2/hook/ed1aa385-fa7b-4371-bb9e-4e22710d44f2"
	workOrderRobotWebHook     = "https://open.feishu.cn/open-apis/bot/v2/hook/a473a57b-b4dc-4f59-bf9e-7db593fab463"
)

func workOrderInfo(ctx context.Context, panicChan chan<- error) {
	l := logger.NewLogger("workOrderRobotInfo")
	l.Info("ready to run workOrderRobotInfo...")
	defer func() {
		l.Info("workOrderRobotInfo quit...")
	}()

	// 查询原始工单数据
	var originalWorkOrders []model.WorkOrder

	// 工单新建 和状态更新 发送飞书机器人消息
	run := func() {
		var nowWorkOrders []model.WorkOrder
		err := db_helper.GetAll(db_helper.QueryDefinition{
			ModelDefinition: &model.WorkOrder{},
			Filters:         db_helper.QueryFilters{},
			Unscoped:        true,
			NoLimit:         true,
		}, &nowWorkOrders).GetError()
		if err != nil {
			err = errors.Wrap(err, "get work order list failed .")
			panicChan <- err
			return
		}

		if len(originalWorkOrders) == 0 {
			originalWorkOrders = nowWorkOrders
		}

		// 获取新建的工单和状态更新的工单详情
		addWorkOrders, changeStatusWorkOrders := changeWorkOrders(originalWorkOrders, nowWorkOrders)

		originalWorkOrders = nowWorkOrders

		for _, workOrder := range addWorkOrders {
			l.Info("work order %s is new", workOrder.WorkOrderUUID)
			// 获取操作人姓名
			operateUser := &model.WorkOrderOperateUser{}
			err = db_helper.GetFirst(db_helper.QueryDefinition{
				ModelDefinition: &model.WorkOrderOperateUser{},
				Filters: db_helper.QueryFilters{
					EqualFilters: map[string]interface{}{
						"id": workOrder.UserId,
					},
				},
			}, operateUser).GetError()
			if err != nil {
				err = errors.Wrap(err, "get work order operate user failed .")
				panicChan <- err
				return
			}
			sendWorkOrderMsg(l, fmt.Sprintf("【自动消息】工单新建\n主机ID：%s\n主机名称：%s\n当前状态：%s\n操作人：%s\n工单描述：%s", workOrder.MachineId, workOrder.MachineName, constant.WorkOrderStatusString(workOrder.Status), operateUser.Username, workOrder.Description))
		}

		for _, workOrder := range changeStatusWorkOrders {
			l.Info("work order %s status is changed", workOrder.WorkOrderUUID)
			// 查找操作人
			operateUser, err := getOperateUserByOperateRecord(workOrder.WorkOrderUUID)
			if err != nil {
				err = errors.Wrap(err, "get work order operate user failed .")
				panicChan <- err
				return
			}
			sendWorkOrderMsg(l, fmt.Sprintf("【自动消息】工单状态更新\n主机ID：%s\n主机名称：%s\n操作人：%s\n新状态：%s", workOrder.MachineId, workOrder.MachineName, operateUser.Username, constant.WorkOrderStatusString(workOrder.Status)))
		}

	}

	run()
	ticker := time.NewTicker(time.Second * 10)
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			run()
		}
	}
}

func sendWorkOrderMsg(l *logger.Logger, msg string) {
	resp, err := grequests.Post(workOrderRobotWebHook, &grequests.RequestOptions{
		JSON: map[string]interface{}{
			"msg_type": "text",
			"content": map[string]string{
				"text": msg,
			},
		},
	})

	l.Info("send webhook resp %s", resp.String())
	if err != nil {
		l.ErrorE(err, "send webhook resp failed")
		return
	}
	return
}

func changeWorkOrders(originalWorkOrders, nowWorkOrders []model.WorkOrder) (addWorkOrders, changeStatusWorkOrders []model.WorkOrder) {
	originalWorkOrdersMap := make(map[string]model.WorkOrder)
	for _, originalWorkOrder := range originalWorkOrders {
		originalWorkOrdersMap[originalWorkOrder.WorkOrderUUID] = originalWorkOrder
	}

	for _, nowWorkOrder := range nowWorkOrders {
		if originalWorkOrder, ok := originalWorkOrdersMap[nowWorkOrder.WorkOrderUUID]; !ok {
			addWorkOrders = append(addWorkOrders, nowWorkOrder)
		} else {
			if nowWorkOrder.Status != originalWorkOrder.Status {
				changeStatusWorkOrders = append(changeStatusWorkOrders, nowWorkOrder)
			}
		}
	}
	return
}

func getOperateUserByOperateRecord(workOrderUUID string) (operateUser model.WorkOrderOperateUser, err error) {
	// 查找操作记录
	var operateRecord model.WorkOrderOperateRecord
	err = db_helper.GetLast(db_helper.QueryDefinition{
		ModelDefinition: &model.WorkOrderOperateRecord{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"work_order_uuid": workOrderUUID,
				"operate":         constant.SetStatus,
			},
		},
	}, &operateRecord).GetError()
	if err != nil {
		err = errors.Wrap(err, "get last work order operate record failed .")
		return model.WorkOrderOperateUser{}, err
	}

	// 查找操作人
	err = db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: &model.WorkOrderOperateUser{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"id": operateRecord.UserID,
			},
		},
	}, &operateUser).GetError()
	if err != nil {
		err = errors.Wrap(err, "get work order operate user failed .")
		return model.WorkOrderOperateUser{}, err
	}
	return operateUser, nil
}
