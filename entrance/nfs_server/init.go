package nfs_server

import (
	"context"
	"errors"
	"fmt"
	log "github.com/sirupsen/logrus"
	"os"
	"os/exec"
	agentConstant "server/pkg-agent/agent_constant"
	server_api "server/pkg/api"
	"server/pkg/constant"
	"server/pkg/libs"
	"server/pkg/logger"
	"server/pkg/region/model"
	"strconv"
	"syscall"
	"time"
)

func RunNfsServer(ctx context.Context, nfsAddr, mountPoint string, panicChan chan<- error) {
	l := logger.NewLogger("RunNfsServer")
	l.Info("ready to init nfs server...")
	defer func() {
		l.Info("nfs server quit...")
	}()

	//参数前置校验, 如果参数为空,直接返回错误
	if nfsAddr == "" || mountPoint == "" {
		err := errors.New(fmt.Sprintf(
			"nfsAddr:%v, mountPoint:%v can not be empty", nfsAddr, mountPoint))
		panic<PERSON>han <- err
		return
	}

	url := os.Getenv(constant.ContainerEnvUrlForAdfs)
	uidEnv := os.Getenv(constant.ContainerEnvUidForAdfs)
	containerRuntimeType := constant.ContainerRuntimeType(os.Getenv(constant.ContainerEnvContainerRuntimeTypeForAdfs))
	regionSign := constant.RegionSignType(os.Getenv(constant.ContainerEnvRegionSignForAdfs))

	if url == "" ||
		uidEnv == "" ||
		containerRuntimeType == "" ||
		regionSign == "" {
		err := errors.New("env: remote_url, uid, containerRuntimeType, regionSign can not be empty")
		panicChan <- err
		return
	}

	uid, err := strconv.Atoi(uidEnv)
	if uid == 0 || err != nil {
		err := errors.New("uid err")
		panicChan <- err
		return
	}

	parseUrl := agentConstant.ParseUrl(url)

	n := NfsMonitor{
		HOSTNAME:             os.Getenv("HOSTNAME"),
		Uid:                  uid,
		ContainerRuntimeType: containerRuntimeType,
		RegionSign:           regionSign,
		Api:                  server_api.NewApi(parseUrl),
	}

	n.monitorAdfsContainer(ctx, nfsAddr, mountPoint, panicChan)
	<-ctx.Done()
}

type NfsMonitor struct {
	HOSTNAME             string
	Uid                  int
	ContainerRuntimeType constant.ContainerRuntimeType
	RegionSign           constant.RegionSignType
	*server_api.Api
}

var NfsDisconnectedAlertTemplate = `NFS 挂载异常,稍后会重新挂载 🚨🚨
region: %v
containerId: %v
containerType: %v
nfsAddr: %v
`

// 监测nfs地址,如果nfs地址失效,这里会重新启动挂载服务容器,避免挂载失效
// nfs *************:/srv/nfs_share,第一个参数
// mountPoint 第二个参数
// runtimeType 第三个参数
func (n *NfsMonitor) monitorAdfsContainer(ctx context.Context, nfsAddr string, mountPoint string, panicChan chan<- error) {
	log := logger.NewLogger("Guard.monitorAdfsContainer")

	defer func() {
		err := umountNfs(mountPoint)
		if err != nil {
			log.Error("umountNfs mountPoint:%v err:%v", mountPoint, err)
		}
		log.Info("umountNfs success:%v", mountPoint)
	}()

	log.Info("nfs service...nfsAddr: %v", nfsAddr)
	lastMountStatus := false

	err := n.ReMountNfsService(log, nfsAddr, mountPoint)
	if err == nil {
		log.Info("nfs mount service success")
		lastMountStatus = true
	} else {
		log.Info("nfs mount service failed")
	}

	ticker := time.NewTicker(time.Minute * 1)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			panicChan <- errors.New("GotPanicSig")
			return

		case <-ticker.C:
			err := checkNFSMount(mountPoint)
			if err == nil {
				if lastMountStatus {
					continue
				} else {
					log.Info("checkNFSMountPoints success mountPoint:%v", mountPoint) //上次失败,这次成功挂载,也要重新进行一次挂载
				}
			}
			lastMountStatus = false //上次失败,这次成功挂载,也要重新进行一次挂载
			//log.Info("checkNFSMountPoints success mountPoint:%v err:%v", mountPoint, err)
			//执行挂载,需要把之前的挂载点先下掉
			err = n.ReMountNfsService(log, nfsAddr, mountPoint)
			if err == nil {
				log.Info("nfs remount success")
				lastMountStatus = true
				continue
			}

			message := fmt.Sprintf(NfsDisconnectedAlertTemplate, n.RegionSign, n.HOSTNAME, n.ContainerRuntimeType, nfsAddr)
			if err = libs.FeiShuRobotSendMSg(libs.NfsDisconnectedAlert, message); err != nil {
				log.WithError(err).Error("FeiShuRobotSendMSg")
				continue
			}

			//通过接口获取远程地址
			nfs, err := n.Api.GetExclusiveNfs(model.ExclusiveNfsInfoParams{
				Uid:                  n.Uid,
				ContainerRuntimeType: n.ContainerRuntimeType,
				RegionSignType:       n.RegionSign,
			})
			if err != nil {
				log.ErrorE(err, "get GetExclusiveNfs failed")
				continue
			}
			log.Info("get nfs server:%v", nfs.Data.NfsAddr)

			//执行重新挂载逻辑
			//如果执行挂载继续失败,过个3秒钟重新获取
			if nfs.Data.NfsAddr != "" { // 执行重新挂载
				nfsAddr = nfs.Data.NfsAddr
			} else {
				log.ErrorE(err, "get nfs result failed")
			}
		}
	}
}
func (n *NfsMonitor) ReMountNfsService(log *logger.Logger, nfsServerPath, mountPoint string) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)
	defer cancel()

	// 使用 channel 接收结果
	resultChan := make(chan bool)
	errChan := make(chan error)

	go func() {
		// 检查目录是否存在
		_, err := os.Stat(mountPoint)
		if os.IsNotExist(err) { // 目录不存在，则创建
			errDir := os.MkdirAll(mountPoint, os.ModePerm) // 使用0755权限创建目录
			if errDir != nil {
				errChan <- errDir
				return
			}
		}
		resultChan <- true
	}()

	select {
	case <-ctx.Done():
		err := errors.New("mount operation timed out")
		log.WithError(err).Error("mkdir mountPoint:%v timed out for NFS :%v\n", mountPoint, nfsServerPath)
		return err

	case err := <-errChan:
		return err

	case <-resultChan:
		// 挂载点创建成功
	}

	log.Info(fmt.Sprintf("mount -t nfs %v %v", nfsServerPath, mountPoint))

	cmd := exec.Command("mount", "-t", "nfs", nfsServerPath, mountPoint)
	if err := cmd.Start(); err != nil {
		log.WithError(err).Error("Failed to start mount command for NFS share from %s: %v\n", nfsServerPath, err)
		return err
	}

	done := make(chan error, 1)
	go func() {
		done <- cmd.Wait()
	}()

	select {
	case <-ctx.Done():
		cmd.Process.Kill() // 终止挂载进程
		err := errors.New("mount operation timed out")
		log.WithError(err).Error("Mount operation timed out for NFS share from %s at %s\n", nfsServerPath, mountPoint)
		return err

	case err := <-done:
		if err != nil {
			log.WithError(err).Error("Failed to mount NFS share from %s: %v\n", nfsServerPath, err)
			return err
		}

		log.Info("Successfully mounted NFS share from %s at %s\n", nfsServerPath, mountPoint)
		return nil
	}
}

func umountNfs(mountPoint string) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()

	// 使用 channel 接收结果
	resultChan := make(chan bool)
	errChan := make(chan error)

	go func() {
		// 使用 -l 选项进行懒惰卸载，-f 强制卸载
		cmd := exec.Command("umount", "-fl", mountPoint)
		if err := cmd.Run(); err != nil {
			errChan <- fmt.Errorf("failed to unmount %s: %w", mountPoint, err)
			return
		}
		fmt.Printf("Successfully unmounted %s\n", mountPoint)
		resultChan <- true
	}()
	select {
	case <-ctx.Done():
		err := errors.New("mount operation timed out")
		log.WithError(err).Error("umount mountPoint:%v timed out for NFS\n", mountPoint)
		return err
	case err := <-errChan:
		return err
	case <-resultChan:
		// 卸载挂载点成功
	}
	return nil
}
func checkNFSMount(mountPoint string) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()

	// 使用 channel 接收结果
	resultChan := make(chan bool)
	errChan := make(chan error)

	go func() {
		// 尝试打开挂载点
		file, err := os.Open(mountPoint)
		if err != nil {
			errChan <- fmt.Errorf("无法打开挂载点: %v", err)
			return
		}
		defer file.Close()

		// 获取文件系统状态
		var stat syscall.Statfs_t
		err = syscall.Statfs(mountPoint, &stat)
		if err != nil {
			errChan <- fmt.Errorf("无法获取文件系统状态: %v", err)
			return
		}

		// 检查文件系统是否正常
		if stat.Blocks == 0 {
			errChan <- fmt.Errorf("NFS挂载已掉线")
			return
		}

		resultChan <- true
	}()

	select {
	case <-ctx.Done():
		// 超时
		return fmt.Errorf("检测NFS挂载超时")
	case err := <-errChan:
		// 发生错误
		return err
	case <-resultChan:
		// 检测成功
		return nil
	}
}
