package work_order

import (
	"server/entrance/initialize"
	"server/pkg/constant"
	"server/pkg/middleware"

	"github.com/gin-gonic/gin"
)

var (
	Adm = constant.Administrator   // 管理员
	PS  = constant.ProductionStaff // 生产员
	AS  = constant.AfterSales      // 售后
	Ops = constant.Operation       // 运维
	CS  = constant.CustomerService // 客服
)

func initRouter(r *gin.Engine, ctrl *initialize.ControllerFactory, mw *middleware.Middleware) {
	r.GET("/health", func(context *gin.Context) {
		context.JSON(200, map[string]string{
			"status": "red",
			"code":   "ok",
		})
	})

	api := r.Group("/api/v1")
	{
		workOrder := api.Group("/work_order") // 运维工单
		workOrder.Use(mw.TenantLoginRequired(), mw.WOAuth(Ops, CS, Adm))
		{
			workOrder.POST("/create", ctrl.WorkOrder.CreateWorkOrder)
			workOrder.POST("/list", ctrl.WorkOrder.GetWorkOrderList)
			workOrder.POST("/detail", ctrl.WorkOrder.GetWorkOrderDetail)           // 获取单个工单处理过程详细信息
			workOrder.POST("/update", ctrl.WorkOrder.UpdateWorkOrder)              // 编辑
			workOrder.POST("/status/update", ctrl.WorkOrder.UpdateWorkOrderStatus) // 设置状态
			workOrder.POST("/assign/update", ctrl.WorkOrder.UpdateWorkOrderAssign) // 指派经办人
			workOrder.POST("/record/create", ctrl.WorkOrder.CreateWorkOrderRecord)
			workOrder.POST("/finish/subscribe", ctrl.WorkOrder.UpdateFinishSubscribeUser)            // 更新工单订阅工单结束事件
			workOrder.POST("/machine/idle/subscribe", ctrl.WorkOrder.UpdateMachineIdleSubscribeUser) // 更新工单主机空闲事件

			workOrder.POST("/region/list", ctrl.Region.GetRegionList)                            // 获取region 下拉列表
			workOrder.POST("/user/list", ctrl.WorkOrder.GetWorkOrderUserList)                    // 获取经办人列表
			workOrder.POST("/user/operation/list", ctrl.WorkOrder.GetWorkOrderOperationUserList) // 获取运维工单经办人列表
			workOrder.POST("/remind/feishu", ctrl.WorkOrder.SendFeishuRemind)                    // 发送飞书提醒
		}

		customerWorkOrder := api.Group("/customer_service_work_order") // 客服工单
		customerWorkOrder.Use(mw.TenantLoginRequired(), mw.WOAuth(Ops, CS, Adm))
		{
			customerWorkOrder.POST("/create", ctrl.WorkOrder.CreateCSWorkOrder)
			customerWorkOrder.POST("/list", ctrl.WorkOrder.GetCSWorkOrderList)
			customerWorkOrder.POST("/detail", ctrl.WorkOrder.GetCSWorkOrderDetail)
			customerWorkOrder.POST("/update", ctrl.WorkOrder.UpdateCSWorkOrder)
			customerWorkOrder.POST("/status/update", ctrl.WorkOrder.UpdateCSWorkOrderStatus)     // 设置状态
			customerWorkOrder.POST("/assign/update", ctrl.WorkOrder.UpdateCSWorkOrderAssign)     // 指派经办人
			customerWorkOrder.POST("/record/create", ctrl.WorkOrder.CreateCSWorkOrderRecord)     // 添加记录
			customerWorkOrder.POST("/instance/region/info", ctrl.WorkOrder.GetMachineRegionInfo) // 根据instance_uuid获取主机地区和编号
		}

		machineMonitor := api.Group("/machine_monitor")
		machineMonitor.Use(mw.TenantLoginRequired(), mw.WOAuth(Ops, CS, Adm))
		{
			machineMonitor.POST("/create", ctrl.WorkOrder.CreateMachineMonitor)
			machineMonitor.POST("/dispose", ctrl.WorkOrder.DisposeMachineMonitor) // 操作主机监控记录
			machineMonitor.POST("/list", ctrl.WorkOrder.GetMachineMonitorList)    // 主机监控列表
		}

		smsManagement := api.Group("/sms")
		smsManagement.Use(mw.TenantLoginRequired())
		{
			smsManagement.POST("/template/create", mw.WOAuth(Adm), ctrl.WorkOrder.CreateSmsTemplate)             // 创建的短信模板
			smsManagement.DELETE("/template/delete", mw.WOAuth(Adm), ctrl.WorkOrder.DeleteSmsTemplate)           // 删除短信模板
			smsManagement.POST("/template/list", mw.WOAuth(Adm), ctrl.WorkOrder.GetSmsTemplateList)              // 短信模板列表
			smsManagement.POST("/template/detail", mw.WOAuth(Ops, CS, Adm), ctrl.WorkOrder.GetSmsTemplateDetail) // 获取短信模板详情
			smsManagement.POST("/template/info", mw.WOAuth(Ops, CS, Adm), ctrl.WorkOrder.GetSmsTemplateInfo)     // 获取短信模板下拉列表
			smsManagement.POST("/send", mw.WOAuth(Ops, CS, Adm), ctrl.WorkOrder.SendSms)                         // 发送短信
			smsManagement.POST("/send/list", mw.WOAuth(Ops, CS, Adm), ctrl.WorkOrder.GetSendSmsList)             // 发送短信列表
			smsManagement.POST("/instance/info", mw.WOAuth(Ops, CS, Adm), ctrl.WorkOrder.GetSmsInstanceIDInfo)   // 查找实例ID 列表
			smsManagement.POST("/instance/detail", ctrl.WorkOrder.GetInstanceInfo)                               // 查询instance_uuid对应手机号
			smsManagement.POST("/instance/name", ctrl.WorkOrder.GetInstanceName)                                 // 查询多个instance_uuid对应的name
			smsManagement.POST("/phone/detail", ctrl.WorkOrder.GetUserPhoneDetail)                               // 查询手机号对应的区号

			smsManagement.POST("/agency/notify/create", ctrl.WorkOrder.CreateAgencyNotify)   // 创建代办通知
			smsManagement.DELETE("/agency/notify/delete", ctrl.WorkOrder.DeleteAgencyNotify) // 删除代办通知
			smsManagement.POST("/agency/notify/list", ctrl.WorkOrder.GetAgencyNotifyList)    // 获取代办通知列表

		}

		notifyManagement := api.Group("/notify")
		notifyManagement.Use(mw.TenantLoginRequired())
		{
			notifyManagement.POST("/instance/create", ctrl.WorkOrder.CreateInstanceNotify) // 创建实例通知
			notifyManagement.POST("/instance/update", ctrl.WorkOrder.UpdateInstanceNotify) // 编辑实例通知
			notifyManagement.POST("/instance/list", ctrl.WorkOrder.GetInstanceNotifyList)  // 实例通知列表
			notifyManagement.POST("/instance/finish", ctrl.WorkOrder.FinishInstanceNotify) // 实例通知结束

			notifyManagement.POST("/receipt/list", ctrl.WorkOrder.GetInstanceReceiptList) // 回执列表
		}

		user := api.Group("/user")
		user.Use(mw.LoginRequired())
		{
			user.GET("/detail", ctrl.User.GetDetail)                   // 获取用户详情
			user.GET("/role/detail", ctrl.WorkOrder.GetUserRoleDetail) // 获取用户角色信息
		}

		api.POST("/file", mw.LoginRequired(), ctrl.File.Upload) // 上传附件

		// 租户
		tenant := api.Group("/tenant")
		{
			tenant.POST("/init", mw.LoginRequired(), ctrl.WorkOrder.InitCreateWorkOrderTenant)

			tenant.POST("/create", mw.LoginRequired(), mw.ALR(false), ctrl.WorkOrder.CreateWorkOrderTenant)
			tenant.POST("/list", mw.LoginRequired(), mw.ALR(false), ctrl.WorkOrder.GetWorkOrderTenantList) // 系统人员列表，供应商客户人员管理列表
			tenant.POST("/user/create", mw.TenantLoginRequired(), mw.WOAuth(Adm), ctrl.WorkOrder.CreateWorkerOrderUser)
			tenant.POST("/user/list", mw.TenantLoginRequired(), mw.WOAuth(Adm), ctrl.WorkOrder.GetWorkOrderTenantUserList)
			tenant.POST("/autodl/user/create", mw.LoginRequired(), mw.ALR(false), ctrl.WorkOrder.CreateWorkerOrderUser)
			tenant.POST("/autodl/user/list", mw.LoginRequired(), mw.ALR(false), ctrl.WorkOrder.GetWorkOrderTenantUserList)
			tenant.POST("/relationship/create", mw.TenantLoginRequired(), mw.WOAuth(Adm), ctrl.WorkOrder.CreateWorkOrderTenantRelationship)
			tenant.POST("/relationship/list", mw.TenantLoginRequired(), mw.WOAuth(Adm), ctrl.WorkOrder.GetWorkOrderRelationshipList)

			tenant.POST("/custom/info", mw.TenantLoginRequired(), ctrl.WorkOrder.GetWorkOrderCustomInfo)      // 下拉客户列表
			tenant.POST("/custom/list", mw.TenantLoginRequired(), ctrl.WorkOrder.GetWorkOrderCustomList)      // 客户列表
			tenant.POST("/provider/list", mw.TenantLoginRequired(), ctrl.WorkOrder.GetWorkOrderProviderList)  // 供应商列表
			tenant.POST("/switch/list", mw.LoginRequired(), ctrl.WorkOrder.GetWorkOrderTenantSwitchList)      // 导航租户切换列表
			tenant.POST("/switch", mw.LoginRequired(), ctrl.WorkOrder.SwitchTenant)                           // 生成新的token
			tenant.POST("/detail", mw.TenantLoginRequired(), ctrl.WorkOrder.GetWorkOrderTenantDetail)         // 获取租户信息
			tenant.POST("/user/role/info", mw.TenantLoginRequired(), ctrl.WorkOrder.GetWorkOrderUserRoleInfo) // 获取租户下用户角色信息
		}

		// 物料
		material := api.Group("/material")
		material.Use(mw.TenantLoginRequired(), mw.WOAuth(Adm, PS, AS, Ops))
		{
			material.POST("/create", ctrl.WorkOrder.CreateWorkOrderMaterial)
			material.POST("/list", ctrl.WorkOrder.GetWorkOrderMaterialList)
			material.POST("/update", ctrl.WorkOrder.UpdateWorkOrderMaterial) // 编辑
			material.POST("/info", ctrl.WorkOrder.GetWorkOrderMaterialInfo)  // 获取机器物料
		}

		// 机型
		machineType := api.Group("/machine_type")
		machineType.Use(mw.TenantLoginRequired(), mw.WOAuth(Adm, PS, AS, Ops))
		{
			machineType.POST("/create", ctrl.WorkOrder.CreateWorkOrderMachineType)
			machineType.POST("/list", ctrl.WorkOrder.GetWorkOrderMachineTypeList)
			machineType.POST("/update", ctrl.WorkOrder.UpdateWorkOrderMachineType) // 编辑
			machineType.POST("/info", ctrl.WorkOrder.GetWorkOrderMachineTypeInfo)  // 下拉机型列表
		}

		// 机器
		workOrderMachine := api.Group("/machine")
		workOrderMachine.Use(mw.TenantLoginRequired(), mw.WOAuth(Adm, PS, AS, Ops))
		{
			workOrderMachine.POST("/create", ctrl.WorkOrder.CreateWorkOrderMachine)                    // 创建
			workOrderMachine.POST("/provider/list", ctrl.WorkOrder.GetWorkOrderProviderMachineList)    // 供应商机器列表
			workOrderMachine.POST("/status/update", ctrl.WorkOrder.UpdateWorkOrderMachineStatus)       // 供应商修改状态
			workOrderMachine.POST("/custom/update", ctrl.WorkOrder.UpdateWorkOrderMachineCustom)       // 供应商修改客户
			workOrderMachine.POST("/provider/update", ctrl.WorkOrder.UpdateWorkOrderMachineByProvider) // 供应商编辑机器
			workOrderMachine.POST("/custom/list", ctrl.WorkOrder.GetWorkOrderCustomMachineList)        // 客户机器列表
			workOrderMachine.POST("/update", ctrl.WorkOrder.UpdateWorkOrderMachine)                    // 客户编辑机器
			workOrderMachine.POST("/import", ctrl.WorkOrder.WorkOrderMachineImport)                    // 客户导入机器csv
			workOrderMachine.POST("/detail", ctrl.WorkOrder.GetWorkOrderMachineDetail)                 // 客户创建返修工单时对应的machineDetail
		}

		// 备件
		sparePart := api.Group("/spare_part")
		sparePart.Use(mw.TenantLoginRequired(), mw.WOAuth(Adm, PS, AS, Ops))
		{
			sparePart.POST("/work_order/create", ctrl.WorkOrder.CreateWorkOrderSparePart)                  // 供应商创建备件工单
			sparePart.POST("/work_order/provider/list", ctrl.WorkOrder.GetWorkOrderSparePartProviderList)  // 供应商备件工单列表
			sparePart.POST("/work_order/provider/update", ctrl.WorkOrder.UpdateWorkOrderSparePartProvider) // 供应商编辑备件工单
			sparePart.POST("/work_order/status/update", ctrl.WorkOrder.UpdateWorkOrderSparePartStatus)     // 供应商修改备件工单 状态（未绑定发货单）

			sparePart.POST("/dispatch_order/create", ctrl.WorkOrder.CreateDispatchOrderSparePart)                          // 供应商创建备件发货单
			sparePart.POST("/dispatch_order/list", ctrl.WorkOrder.GetDispatchOrderSparePartList)                           // 供应商备件发货单列表
			sparePart.POST("/dispatch_order/provider/update", ctrl.WorkOrder.UpdateDispatchOrderSparePartUpdateByProvider) // 供应商编辑备件发货单

			sparePart.POST("/dispatch_order_id/bind", ctrl.WorkOrder.BindDispatchOrderID)                      // 供应商备货工单批量绑定发货单id
			sparePart.POST("/express_num/bind", ctrl.WorkOrder.BindExpressNumber)                              // 供应商录入快递单号
			sparePart.POST("/dispatch_order/status/update", ctrl.WorkOrder.UpdateDispatchOrderSparePartStatus) // 供应商修改 备件发货单 状态

			sparePart.POST("/work_order/custom/list", ctrl.WorkOrder.GetWorkOrderSparePartCustomList) // 客户备件工单列表
		}

		// 返修
		repair := api.Group("/repair")
		repair.Use(mw.TenantLoginRequired(), mw.WOAuth(Adm, PS, AS, Ops))
		{
			repair.POST("/work_order/create", ctrl.WorkOrder.CreateWorkOrderRepair)                 // 客户创建返修工单
			repair.POST("/work_order/custom/list", ctrl.WorkOrder.GetWorkOrderCustomRepairList)     // 客户 返修工单列表
			repair.POST("/work_order/provider/list", ctrl.WorkOrder.GetWorkOrderProviderRepairList) // 供应商 返修工单列表

			repair.POST("/dispatch_order/create", ctrl.WorkOrder.CreateDispatchOrderRepair)                       // 客户创建返修发货单
			repair.POST("/dispatch_order/custom/list", ctrl.WorkOrder.GetDispatchOrderCustomRepairList)           // 客户返修发货单列表
			repair.POST("/dispatch_order/provider/list", ctrl.WorkOrder.GetDispatchOrderProviderRepairList)       // 供应商返修发货单列表
			repair.POST("/work_order/no_bind_dispatch/list", ctrl.WorkOrder.GetWorkOrderRepairNoBindDispatchList) // 没有绑定返修发货单的返修工单列表

			repair.POST("/work_order/status/update", ctrl.WorkOrder.UpdateWorkOrderRepairStatus)         // 客户修改 返修工单状态（未绑定发货单）
			repair.POST("/dispatch_order/status/update", ctrl.WorkOrder.UpdateDispatchOrderRepairStatus) // 客户修改 返修发货单状态
			repair.POST("/express_num/bind", ctrl.WorkOrder.RepairBindExpressNumber)                     // 客户录入返修发货单的快递单
		}

		// gpu 型号
		gpu_type := api.Group("/gpu_type")
		gpu_type.Use(mw.LoginRequired())
		{
			gpu_type.POST("/list", ctrl.GpuType.List) // 获取Gpu型号列表
		}
	}
}
