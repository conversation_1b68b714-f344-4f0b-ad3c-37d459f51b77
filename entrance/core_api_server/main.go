package core_api_server

import (
	"context"
	"fmt"
	"net/http"
	"server/entrance/initialize"
	h "server/pkg/http"
	"server/pkg/libs"
	"server/pkg/logger"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func RunCoreApiServer(ctx context.Context, panic<PERSON>han chan<- error, port int, l *logger.Logger) {
	l.Info("ready to init core api server...")
	defer func() {
		l.Info("core api server quit...")
	}()

	/****************************************************************************************************************/
	// 构建依赖，依赖注入
	l.Info("core api server build dependency start...")

	dependency, err := initialize.InitCoreBasicDependency(l)
	if err != nil {
		panicChan <- err
		return
	}
	defer dependency.Cancel()

	_, controllerFactory, mw, err := initialize.InitCoreDependency(dependency)
	if err != nil {
		panicChan <- err
		return
	}

	l.Info("core api server build dependency finished...")
	/****************************************************************************************************************/

	gin.SetMode(gin.DebugMode)
	r := gin.New()
	r.Use(libs.LoggerWithConfig())
	r.Use(h.GetGinPanicHandler(l))
	r.Use(cors.New(cors.Config{
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Length", "Content-Type", "Authorization", "AppVersion"},
		AllowCredentials: false,
		MaxAge:           12 * time.Hour,
		AllowAllOrigins:  true,
	}))
	initRouter(r, controllerFactory, mw)

	srv := &http.Server{
		Addr:              fmt.Sprintf("0.0.0.0:%d", port),
		Handler:           r,
		ReadTimeout:       30 * time.Second,
		ReadHeaderTimeout: 30 * time.Second,
		WriteTimeout:      30 * time.Second,
	}
	go func() {
		for {
			select {
			case <-ctx.Done():
				ctx, _ := context.WithTimeout(context.Background(), 10*time.Second)
				if err := srv.Shutdown(ctx); err != nil {
					l.ErrorE(err, "shutdown server failed")
				}
				return
			}
		}
	}()
	panicChan <- srv.ListenAndServe() // 阻塞
	return
}
