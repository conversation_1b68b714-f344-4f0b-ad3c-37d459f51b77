package guard

import (
	"bytes"
	"context"
	"fmt"
	"github.com/minio/minio-go/v7"
	log "github.com/sirupsen/logrus"
	"net/http"
	"os/exec"
	"server/conf"
	agentConstant "server/pkg-agent/agent_constant"
	"server/pkg-agent/messenger"
	"server/pkg-storage-agent/storage_agent_adfs"
	"server/pkg-storage-agent/storage_agent_auth"
	"server/pkg-storage-agent/storage_agent_autofs"
	constant "server/pkg-storage-agent/storage_agent_constant"
	"server/pkg-storage-agent/storage_agent_guard"
	"server/pkg-storage-agent/storage_agent_health"
	"server/pkg-storage-agent/storage_agent_middleware"
	"server/pkg-storage-agent/storage_agent_nfs"
	server_api "server/pkg/api"
	serverConstant "server/pkg/constant"
	"server/pkg/libs"
	"server/pkg/logger"
	"server/plugin/redis_plugin"
	"strings"
	"time"

	"github.com/pkg/errors"
)

type RunGuardConfig struct {
	Region          string                `mapstructure:"region"`
	FsType          serverConstant.FsType `mapstructure:"fs_type"`
	FsSign          string                `mapstructure:"fs_sign"`
	FsConfigVersion string                `mapstructure:"fs_config_version"`

	ServerURL     string `mapstructure:"server_url"`
	ApiPort       int    `mapstructure:"api_port"`
	Token         string `mapstructure:"token"`
	NeedFrpcProxy bool   `mapstructure:"need_frpc_proxy"`

	RedisClusterAddr string `mapstructure:"redis_cluster_addr"`
	RedisClusterPwd  string `mapstructure:"redis_cluster_pwd"`
	BlobClusterAddr  string `mapstructure:"blob_cluster_addr"`

	S3Address         string `mapstructure:"s3_address"`
	MinioRootUser     string `mapstructure:"minio_root_user"`
	MinioRootPassword string `mapstructure:"minio_root_password"`

	//CenterRedisAddr  string `mapstructure:"center_redis_addr"`
	//CenterRedisPwd   string `mapstructure:"center_redis_pwd"`
	CenterServerAddr string `mapstructure:"center_server_addr"`

	AutoFSAlertURL *string `mapstructure:"autofs_alert_url"` // juicefs 告警地址
}

func (r *RunGuardConfig) CorrectAndValid() bool {
	if r.Region == "" {
		log.Error("region should be specified; Start storage agent failed... exit.")
		return false
	}

	if r.ApiPort == 0 {
		log.Error("api_port should be specified")
		return false
	}

	if r.ServerURL == "" {
		r.ServerURL = "https://agent-server-security.seetacloud.com"
		log.Warnf("[server_url] is empty, use default [%s]", r.ServerURL)
	}

	if r.Token == "" {
		r.Token = "seetatech666"
		log.Warnf("[token] is empty, use default [%s]", r.Token)
	}

	if r.FsType == "" {
		r.FsType = constant.RegionFlagNFS
		log.Warnf("[fs_type] is empty, use default [%s]", r.FsSign)
	}

	if r.FsType == serverConstant.ADFS || r.FsType == serverConstant.AutoFS {
		if r.RedisClusterAddr == "" {
			log.Error("redis_cluster_addr should be specified")
			return false
		}
		if r.RedisClusterPwd == "" {
			log.Error("redis_cluster_pwd should be specified")
			return false
		}

		if r.CenterServerAddr == "" {
			r.CenterServerAddr = "https://api.autodl.com"
			log.Warnf("[center_server_addr] is empty, use default [%s]", r.CenterServerAddr)
		}
	}
	if r.FsSign == "" {
		r.FsSign = fmt.Sprintf("%s-%s", r.Region, r.FsType)
		log.Warnf("[fs_sign] is empty, use default [%s]", r.FsSign)
	}

	if r.FsType == constant.RegionFlagNFS {
		err := initLocalMount()
		if err != nil {
			log.Errorf("nfs type assined, but initLocalMount failed: %v", err)
			return false
		}
	}

	if r.FsType == serverConstant.AutoFS {
		if r.BlobClusterAddr == "" {
			log.Error("blob_cluster_addr should be specified")
			return false
		}
		if len(r.MinioRootUser) < 3 {
			log.Error("minio_root_user should be specified with at least 3 characters")
			return false
		}
		if len(r.MinioRootPassword) < 8 {
			log.Error("minio_root_password should be specified with at least 3 characters")
			return false
		}
		if r.S3Address == "" {
			log.Error("s3_address should be specified")
			return false
		}

		if !libs.ExistPath("/bin/juicefs") {
			log.Error("Panic! Running in autofs mode with no /bin/juicefs")
			return false
		}
	}

	if r.NeedFrpcProxy {
		if !libs.ExistPath("/bin/frpc") {
			log.Error("config set need_frpc_proxy but cannot find frpc in path: /bin/frpc")
			return false
		}
	}

	return true
}

func RunStorageAgentGuard(ctx context.Context, panicChan chan<- error, c *RunGuardConfig) {
	l := logger.NewLogger(fmt.Sprintf("RunStorageGuard_%s_%s", c.Region, c.FsType))
	l.Info("ready to init storage guard...")
	defer func() {
		l.Info("storage guard quit...")
	}()

	// 一些依赖注入初始化
	conf.SetGlobalGsConfig()
	rs := serverConstant.NewRegionSignType(c.Region)
	authKeeper := storage_agent_auth.NewAuthKeeper(rs, constant.AuthUserTokenTimeout)

	// save token. e.g. seetatech666
	authKeeper.SetAgentToken(c.Token)

	healthMonitor := storage_agent_health.NewHealthMonitor(
		rs, authKeeper, constant.HeartbeatInterval, constant.RegisterInterval,
	)
	l.Info("healthMonitor initialized...")

	var err error
	var adfsPlugin *redis_plugin.ADFSPlugin
	var autoFsPlugin *redis_plugin.AutoFsPlugin
	var spac *storage_agent_guard.SPAC
	if c.FsType != serverConstant.NFS {
		rcc, err := redis_plugin.RedisClusterClientProvider(&redis_plugin.RedisClusterOption{
			Addr:     strings.Split(c.RedisClusterAddr, ","),
			Password: c.RedisClusterPwd,
		})
		if err != nil {
			l.E(err)
			panicChan <- err
			return
		}

		l.Info("redis cluster client initialized...")
		adfsPlugin = redis_plugin.NewADFSPluginProvider(rcc)
		autoFsPlugin = redis_plugin.NewAutoFsPluginProvider(rcc)

		//rc, err := redis_plugin.RedisClientProvider(&redis_plugin.RedisClientOption{
		//	Addr:     c.CenterRedisAddr,
		//	Password: c.CenterRedisPwd,
		//})
		//if err != nil {
		//	l.E(err)
		//	panicChan <- err
		//	return
		//}
		//l.Info("redis client for spac initialized...")
		//
		//spacPlugin = redis_plugin.NewSPacPluginProvider(rc)
		url := agentConstant.ParseUrl(c.CenterServerAddr)
		l.Info("center server is parsed... host: %s", url.GetHost())

		serverApi := server_api.NewApi(url)
		spac = storage_agent_guard.NewSpac(nil, serverApi)
	}

	var minIOCore *minio.Core
	if c.FsType == serverConstant.AutoFS {
		minIOCore, err = storage_agent_autofs.AutoFsNewMinioClient(c.MinioRootUser, c.MinioRootPassword, c.S3Address)
		if err != nil {
			l.E(err)
			panicChan <- err
			return
		}
		l.Info("minio client initialized...")
	}

	nfsMonitor := storage_agent_nfs.NewNFSMonitor(rs, authKeeper)
	adfsMonitor := storage_agent_adfs.NewADFSMonitor(rs, authKeeper, adfsPlugin, c.ServerURL)
	autoFsMonitor := storage_agent_autofs.NewAutoFsMonitor(rs, authKeeper, autoFsPlugin, c.ServerURL, c.BlobClusterAddr, c.RedisClusterAddr, c.AutoFSAlertURL, minIOCore)

	mw := storage_agent_middleware.NewAgentStorageMiddleware(rs, authKeeper, c.ServerURL)

	// url
	url := agentConstant.ParseUrl(c.ServerURL)

	// connect socket
	l.WithField("host", url.GetHost()).Info("server host parsed")

	m, err := tryConnectToServer(url, c.Token, rs, c.FsType, c.FsConfigVersion)
	if err != nil {
		err = errors.Wrap(err, "failed to exec messenger dial")
		l.E(err)
		panicChan <- err
		return
	}
	l.Info("connected to server success...")

	// run
	guard := storage_agent_guard.NewGuard(
		rs, c.ServerURL, c.ApiPort,
		m, healthMonitor, authKeeper, nfsMonitor,
		adfsMonitor, adfsPlugin, autoFsMonitor,
		autoFsPlugin, mw, c.FsType, spac, c.NeedFrpcProxy)
	guard.Run(ctx, panicChan)
}

func tryConnectToServer(url *agentConstant.URL, token string, rs serverConstant.RegionSignType, regionFlag serverConstant.FsType, fsConfigVersion string) (m *messenger.Messenger, err error) {
	header := &http.Header{}
	header.Set(constant.AgentAuthorizationHeaderKey, token)
	header.Set(constant.AgentRegionSignHeaderKey, rs.String())
	header.Set(constant.AgentRegionFlagHeaderKey, regionFlag.String())
	header.Set(constant.AgentFsConfigVersionHeaderKey, strings.TrimSpace(fsConfigVersion))

	for _, u := range url.GetStorageAgentWebsocketConnectUrls() {
		m, err = connectToServer(u, header)
		if err == nil {
			return
		}
	}
	return
}

func connectToServer(url string, h *http.Header) (m *messenger.Messenger, err error) {
	m = messenger.NewMessengerForClient(url, *h)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	err = m.Dial(ctx)
	return
}

func initLocalMount() (err error) {
	cmdStr := fmt.Sprintf(`mkdir -p %s`, constant.StorageDirDataMount)
	cmdStr += fmt.Sprintf(` && mount -t nfs 127.0.0.1:/data %s`, constant.StorageDirDataMount)

	cmd := exec.Command("/bin/bash", "-c", cmdStr)
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	// 执行命令
	if err := cmd.Run(); err != nil {
		return err
	}

	return nil
}
