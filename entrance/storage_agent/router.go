package storage_agent

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"server/pkg-storage-agent/storage_agent_middleware"
	serverConstant "server/pkg/constant"
)

type AgentStorageController interface {
	/*
	 * File
	 */

	GetUploadProgressController(c *gin.Context)
	UploadFileController(c *gin.Context)
	BeforeUploadFileController(c *gin.Context)
	RemoveFileController(c *gin.Context)
	RenameFileController(c *gin.Context)
	GetFileTokenController(c *gin.Context)

	/*
	 * Dir
	 */

	GetPagedFilesController(c *gin.Context)
	MakeDirController(c *gin.Context)
	RemoveDirController(c *gin.Context)
	RenameDirController(c *gin.Context)

	/*
	 * Common Download
	 */

	DownloadFileController(c *gin.Context)

	// adfs below

	/*
	 * File
	 */

	ADFSGetUploadProgressController(c *gin.Context)
	ADFSUploadFileController(c *gin.Context)
	ADFSBeforeUploadFileController(c *gin.Context)
	ADFSRemoveFileController(c *gin.Context)
	ADFSRenameFileController(c *gin.Context)
	ADFSGetFileTokenController(c *gin.Context)

	/*
	 * Dir
	 */

	ADFSGetPagedFilesController(c *gin.Context)
	ADFSMakeDirController(c *gin.Context)
	ADFSRemoveDirController(c *gin.Context)
	ADFSRenameDirController(c *gin.Context)
	ADFSClearUploadingCacheController(c *gin.Context)

	/*
	 * Common Download
	 */

	ADFSDownloadFileController(c *gin.Context)

	ADFSGetUserUsageController(c *gin.Context)
	ADFSGetUserDetailController(c *gin.Context)

	// autoFs
	AutoFsGetUsage(c *gin.Context)
	AutoFsGetList(c *gin.Context)
	AutoFsGetListV2(c *gin.Context)
	AutoFsGetUploadProgressController(c *gin.Context)
	AutoFsUploadFileController(c *gin.Context)
	AutoFsBeforeUploadFileController(c *gin.Context)
	AutoFsRemoveFileController(c *gin.Context)
	AutoFsMakeDirController(c *gin.Context)
	AutoFsRemoveDirController(c *gin.Context)
	AutoFsGetFileTokenController(c *gin.Context)
	AutoFsDownloadFileController(c *gin.Context)
	AutoFsGetUserDetailController(c *gin.Context)
	AutoFsClearUploadingCacheController(c *gin.Context)
}

func InitRouter(r *gin.Engine, ctrl AgentStorageController, amw *storage_agent_middleware.AgentStorageMiddleware, fsType serverConstant.FsType) {
	r.GET("/health", func(context *gin.Context) {
		context.JSON(http.StatusOK, map[string]string{"status": "green", "code": "ok"})
	})

	api := r.Group("/api/v1/")
	api.Use(amw.AuthRequired()) // 用户验证
	{
		file := api.Group("/net_disk")
		{
			file.GET("/file/progress/:file_uuid", ctrl.GetUploadProgressController) // 获取文件上传进度, id 由前端指定, 仅作为区分进度用, 不同于 file_id
			file.POST("/file", ctrl.UploadFileController)                           // 分片上传
			file.GET("/file/before_upload", ctrl.BeforeUploadFileController)        // 断点续传
			file.DELETE("/file", ctrl.RemoveFileController)                         // 用户删除文件

			// 此路由命名是为了兼容以后更改 file 的其他属性, 就可以追加为例如 [PUT] /file/location 等.
			file.PUT("/file/name", ctrl.RenameFileController) // 用户重命名文件

			file.POST("/file/download_token", ctrl.GetFileTokenController) // 下载文件 token

			file.GET("/dir", ctrl.GetPagedFilesController) // 获取当前用户的文件列表
			file.POST("/dir", ctrl.MakeDirController)      // mkdir
			file.DELETE("/dir", ctrl.RemoveDirController)  // rm dir
			file.PUT("/dir", ctrl.RenameDirController)     // 开始下载文件
		}

		api.GET("/download/file/:token", ctrl.DownloadFileController)

		if fsType.IsAdfs() {
			adfs := api.Group("/file_storage")
			{
				adfs.GET("/file/progress/:file_uuid", ctrl.ADFSGetUploadProgressController) // 获取文件上传进度, id 由前端指定, 仅作为区分进度用, 不同于 file_id
				adfs.POST("/file", ctrl.ADFSUploadFileController)                           // 分片上传
				adfs.GET("/file/before_upload", ctrl.ADFSBeforeUploadFileController)        // 断点续传
				adfs.DELETE("/file", ctrl.ADFSRemoveFileController)                         // 用户删除文件

				// 此路由命名是为了兼容以后更改 file 的其他属性, 就可以追加为例如 [PUT] /file/location 等.
				adfs.PUT("/file/name", ctrl.ADFSRenameFileController) // 用户重命名文件

				adfs.POST("/file/download_token", ctrl.ADFSGetFileTokenController) // 下载文件 token

				adfs.GET("/dir", ctrl.ADFSGetPagedFilesController)            // 获取当前用户的文件列表
				adfs.POST("/dir", ctrl.ADFSMakeDirController)                 // mkdir
				adfs.DELETE("/dir", ctrl.ADFSRemoveDirController)             // rm dir
				adfs.PUT("/dir", ctrl.ADFSRenameDirController)                // 开始下载文件
				adfs.DELETE("/cache", ctrl.ADFSClearUploadingCacheController) // 清理上传缓存

				adfs.GET("/usage", ctrl.ADFSGetUserUsageController)
				adfs.GET("/detail", ctrl.ADFSGetUserDetailController)

				adfs.GET("/download/file/:token", ctrl.ADFSDownloadFileController)
			}
		} else if fsType.IsAutoFs() {
			autoFs := api.Group("/file_storage")
			{
				autoFs.GET("/file/progress/:file_uuid", ctrl.ADFSGetUploadProgressController) // 获取文件上传进度, id 由前端指定, 仅作为区分进度用, 不同于 file_id
				autoFs.POST("/file", ctrl.AutoFsUploadFileController)                         // 分片上传
				autoFs.GET("/file/before_upload", ctrl.AutoFsBeforeUploadFileController)      // 断点续传
				autoFs.DELETE("/file", ctrl.AutoFsRemoveFileController)                       // 用户删除文件

				// 此路由命名是为了兼容以后更改 file 的其他属性, 就可以追加为例如 [PUT] /file/location 等.
				//autoFs.PUT("/file/name", ctrl.ADFSRenameFileController) // 用户重命名文件

				autoFs.POST("/file/download_token", ctrl.AutoFsGetFileTokenController) // 下载文件 token

				autoFs.GET("/dir", ctrl.AutoFsGetList)                            // 获取当前用户的文件列表
				autoFs.GET("/dir/v2", ctrl.AutoFsGetListV2)                       // 获取当前用户的文件列表
				autoFs.POST("/dir", ctrl.AutoFsMakeDirController)                 // mkdir
				autoFs.DELETE("/dir", ctrl.AutoFsRemoveDirController)             // rm dir
				autoFs.DELETE("/cache", ctrl.AutoFsClearUploadingCacheController) // 清理上传缓存
				//autoFs.PUT("/dir", ctrl.ADFSRenameDirController)    // 开始下载文件

				autoFs.GET("/usage", ctrl.AutoFsGetUsage)
				autoFs.GET("/detail", ctrl.AutoFsGetUserDetailController)

				autoFs.GET("/download/file/:token", ctrl.AutoFsDownloadFileController)
			}
		}

	}
}
