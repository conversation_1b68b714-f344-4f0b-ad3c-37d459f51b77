package api_server

import (
	"server/entrance/initialize"
	"server/pkg/constant"
	"server/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func subUserRouter(api *gin.RouterGroup, ctrl *initialize.ControllerFactory, mw *middleware.Middleware) {
	subUser := api.Group("/sub_user")
	subUser.POST("/login", ctrl.User.SubUserLogin)
	subUser.POST("/login_failed/count", ctrl.User.LoginFailedCount) // 用户登录错误次数
	subUser.POST("/passport", ctrl.User.SubUserPassport)
	subUser.GET("/time_sync", ctrl.User.SyncTime) // 时钟
	subUser.GET("/common", ctrl.Common.Common)
	subUser.GET("/phone_area", ctrl.Common.PhoneAreaGetAll)
	subUser.GET("/region/list", ctrl.Region.GetRegionListForIndex)
	subUser.GET("/promotion/invite", ctrl.Promotion.InvitePromotion)
	subUser.GET("/captcha/block_puzzle", ctrl.User.GetBlockPuzzleCaptcha) // 获取拼图验证码
	subUser.POST("/captcha/check", ctrl.User.CaptchaCheck)                // 验证码校验
	subUser.GET("/captcha", ctrl.User.GetCaptcha)
	subUser.POST("/simply/send_message", ctrl.User.SimplySendMessage) // 不需要滑块验证发送短信
	subUser.POST("/send_message", ctrl.User.SendVCode)                // 发送短信
	subUser.POST("/message/check", ctrl.User.CheckVCode)              // 校验短信验证码
	//subUser.POST("/send_email", ctrl.User.SendEmail)                  // 发送邮件
	subUser.POST("/email/check", ctrl.User.CheckEmail)
	subUser.POST("/logout", ctrl.User.SubUserLogout)                                  // 用户退出登录
	subUser.GET("/login/logo", ctrl.User.GetLoginLogo)                                // 登录logo
	subUser.GET("/user/image_file/:feed_back_id", ctrl.User.GetUserFeedBackImageFile) // 获取用户问题截图

	su := subUser.Group("")
	su.Use(mw.SubUserLoginRequired())
	{
		su.GET("", ctrl.User.SubUserDetail)
		su.POST("/send_message/inner", ctrl.User.SendVCodeInner) // 发送短信（替换手机号为sub_name）

	}

	// user
	user := subUser.Group("/user")
	user.Use(mw.SubUserLoginRequired())
	{
		user.POST("/pub_key", ctrl.User.CreatePubKey)
		user.GET("/pub_key", ctrl.User.GetPubKeyList)
		user.DELETE("/pub_key", ctrl.User.DeletePubKey)
		user.POST("/machine/list", ctrl.User.GetUserMachineList)
		user.GET("/member/detail", ctrl.User.UserMemberInfo) // 获取用户会员详情
		user.POST("/feedback", ctrl.User.UserFeedBack)       // 用户反馈与建议
		user.GET("/agreements", ctrl.User.UserAgreementSignedGet)
		user.POST("/agreements", ctrl.User.UserAgreementSign)
		user.POST("/uuid/detail", ctrl.User.GetUserByUUID)
		user.PUT("/password", ctrl.User.SubUserUpdatePassword)
		user.POST("/uuid/username", ctrl.User.GetUserNameByUUID)
	}

	// bc
	bc := subUser.Group("")
	bc.Use(mw.SubUserLoginRequired())
	{
		bc.GET("/wallet/balance", ctrl.BC.SubUserWalletGetForCreateOrder) // 下单获取余额
		bc.GET("/wallet", ctrl.BC.SubUserWalletGet)                       // 钱包详情, 余额

		bc.GET("/wallet/recharge", ctrl.BC.RechargeQueryLocal)                    // 轮循查询
		bc.GET("/wallet/recharge/alipay/query", ctrl.BC.RechargeAliQuery)         // 主动查询
		bc.GET("/wallet/recharge/alipay/qrcode", ctrl.BC.RechargeAliUpdateQrCode) // 主动查询
		bc.GET("/wallet/recharge/wx/query", ctrl.BC.RechargeWxQuery)              // 主动查询

		// voucher
		bc.POST("/voucher/list", ctrl.BC.VoucherUserGetList)                   // 获取代金券列表
		bc.POST("/voucher/issue/exchange", ctrl.BC.VoucherIssueExchangeTicket) // 用户领取兑换券
		bc.GET("/voucher/register", ctrl.BC.VoucherUserGetRegisterTicketInfo)  // 用户领取兑换券
		bc.POST("/voucher/set", ctrl.BC.UserSetVoucherIsAutoUse)               // 用户设置代金券是否自动抵扣使用

		// coupon
		bc.GET("/coupon_market", ctrl.BC.CouponListForCouponMarket)             // 优惠券市场
		bc.GET("/coupon", ctrl.BC.CouponDetail)                                 // 优惠券详情
		bc.GET("/coupon/receive", ctrl.BC.CouponReceiveByUser)                  // 用户领取优惠券
		bc.POST("/user_coupon", ctrl.BC.UserCouponList)                         // 用户领取优惠券
		bc.POST("/user_coupon/exchange", ctrl.BC.UserCouponIssueByExchangeCode) // 使用兑换码兑换优惠券

		bc.POST("/order/deployment/ddp/preview", ctrl.BC.CreateOrderForDeploymentDuration)               // 创建弹性部署时长包 - 预览
		bc.POST("/order/deployment/ddp", ctrl.BC.CreateOrderForDeploymentDuration)                       // 创建弹性部署时长包
		bc.DELETE("/order/deployment/ddp/preview", ctrl.BC.CreateOrderForCancelDeploymentDurationRefund) // 退订弹性部署时长包 - 预览
		bc.DELETE("/order/deployment/ddp", ctrl.BC.CreateOrderForCancelDeploymentDurationRefund)         // 退订弹性部署时长包

	}

	bcRole := subUser.Group("")
	bcRole.Use(mw.SubUserLoginRequired(constant.RoleBillingCenter))
	{
		bcRole.POST("/wallet/recharge/wx", ctrl.BC.SubUserRechargeWxCreate)              // 微信充值
		bcRole.POST("/wallet/recharge/alipay", ctrl.BC.SubUserRechargeAliPayCreate)      // 支付宝充值
		bcRole.POST("/wallet/recharge/wx/v2", ctrl.BC.SubUserRechargeWxCreateV2)         // 微信充值V2
		bcRole.POST("/wallet/recharge/alipay/v2", ctrl.BC.SubUserRechargeAliPayCreateV2) // 支付宝充值V2
	}

	// machine
	machine := subUser.Group("/machine")
	machine.GET("/tag", ctrl.Machine.GetMachineTag)             // 获取所有机器标签
	machine.GET("/gpu_type", ctrl.Machine.GetAllMachineGpuType) // 获取所有机器的gpu号型
	machine.Use(mw.SubUserLoginRequired())
	{
		machine.POST("/detail", ctrl.Machine.Detail) // 获取机器信息详情
		machine.POST("/region/gpu_type", ctrl.Machine.GetMachineRegionGpuType)
		machine.POST("/simple/detail", ctrl.Machine.SimpleDetail)              // 检查机器简单信息
		machine.POST("/check_machine_online", ctrl.Machine.CheckMachineOnline) // 检查机器是否上架
	}

	// role: instance
	subUser.GET("/order", mw.SubUserLoginRequired(constant.RoleInstance, constant.RoleBillingCenter), ctrl.BC.OrderGetDetail)
	roleInstance := subUser.Group("").Use(mw.SubUserLoginRequired(constant.RoleInstance))
	{
		roleInstance.POST("/order/price/preview", ctrl.BC.GetPricePreview)                                             // 获取订单详情
		roleInstance.PUT("/order/pay", ctrl.BC.SubUserOrderPay)                                                        // 付款
		roleInstance.PUT("/order/cancel", ctrl.BC.SubUserOrderCancel)                                                  // 取消
		roleInstance.POST("/order/instance/create/prepay", ctrl.BC.SubUserCreateOrderForCreateInstancePrepay)          // 创建实例
		roleInstance.POST("/order/instance/create/payg", ctrl.BC.SubUserCreateOrderForCreateInstancePayg)              // 创建实例
		roleInstance.POST("/order/instance/clone/prepay", ctrl.BC.SubUserCreateOrderForCloneInstancePrepay)            // 迁移实例预付费
		roleInstance.POST("/order/instance/clone/payg", ctrl.BC.SubUserCreateOrderForCloneInstancePayg)                // 实例迁移
		roleInstance.POST("/order/instance/renewal", ctrl.BC.SubUserCreateOrderForRenewalInstance)                     // 实例续费
		roleInstance.POST("/order/instance/change", ctrl.BC.SubUserCreateOrderForChangeInstance)                       // 实例调整配置
		roleInstance.POST("/order/data_disk/change", ctrl.BC.SubUserCreateOrderForDataDiskChangeSize)                  // 数据盘扩缩容
		roleInstance.POST("/order/data_disk/change/preview", ctrl.BC.SubUserDataDiskChangeSizePreview)                 // 数据盘扩缩容 - 预览
		roleInstance.POST("/order/instance/charge_type/prepay", ctrl.BC.SubUserCreateOrderForPaygToPrepay)             // 按量付费转包卡
		roleInstance.GET("/order/instance/charge_type/payg/preview", ctrl.BC.SubUserCreateOrderForPrepayToPaygPreview) // 包卡转按量付费 - 预览
		roleInstance.GET("/order/instance/charge_type/check", ctrl.BC.SubUserCheckForChangeChargeType)                 // 包卡转按量付费 - 预览
		roleInstance.POST("/order/instance/charge_type/payg", ctrl.BC.SubUserCreateOrderForPrepayToPayg)               // 包卡转按量付费 - 执行
		roleInstance.POST("/order/instance/change_protocol", ctrl.BC.ChangeProtocol)                                   // 修改协议

		roleInstance.GET("/instance", ctrl.Instance.SubUserGetPaged)                              // 获取实例列表
		roleInstance.POST("/instance", ctrl.Instance.SubUserGetPaged)                             // 获取实例列表
		roleInstance.GET("/instance/snapshot", ctrl.Instance.SubUserGetInstanceSnapshot)          // 获取实例的机器等详情 (快照)
		roleInstance.GET("/instance/detail", ctrl.Instance.SubUserGetInstanceDetail)              // 获取实例的机器等详情
		roleInstance.GET("/instance/exist", ctrl.Instance.SubUserCheckInstanceExist)              // 检查实例存在与否
		roleInstance.POST("/instance/power_on", ctrl.Instance.SubUserPowerOnInstance)             // 开机
		roleInstance.GET("/instance/power_on/can_non_gpu", ctrl.Instance.CanPowerOnByNonGPUMode)  // 查询开机限制
		roleInstance.POST("/instance/power_off", ctrl.Instance.SubUserPowerOffInstance)           // 关机
		roleInstance.POST("/instance/release", ctrl.Instance.SubUserReleaseInstance)              // 释放
		roleInstance.POST("/instance/init", ctrl.Instance.SubUserInitInstance)                    // 初始化
		roleInstance.POST("/instance/change_image", ctrl.Instance.SubUserChangeImage)             // 更换镜像
		roleInstance.POST("/instance/image/save", ctrl.Instance.SubUserSaveImage)                 // 保存镜像
		roleInstance.GET("/instance/count", ctrl.Instance.CountInstance)                          // 统计用户实例数量
		roleInstance.GET("/instance/count/v1", ctrl.Instance.CountUserInstanceInstance)           // 统计用户实例数量排除了(abort状态的)
		roleInstance.PUT("/instance/name", ctrl.Instance.SubUserUpdateInstanceName)               // 更新名称
		roleInstance.PUT("/instance/description", ctrl.Instance.SubUserUpdateInstanceDescription) // 更新描述 (提前写在这预防改需求)
		roleInstance.POST("/instance/timed/shutdown", ctrl.Instance.SubUserScheduledShutdown)     // 定时任务: 关机
		roleInstance.GET("/instance/monitor", ctrl.Instance.SubUserInstanceMonitor)               // 实例监控
		roleInstance.POST("/instance/clone/retry", ctrl.Instance.CloneRetry)                      // 实例克隆重试
		roleInstance.POST("/clone/file_transfer/done", ctrl.Instance.CloneFileTransferDone)       // 实例克隆, 拷贝数据盘
		roleInstance.POST("/instance/file_transfer", ctrl.Instance.FileTransfer)
		roleInstance.POST("/instance/file_transfer/cancel", ctrl.Instance.FileTransferCancel)
		roleInstance.POST("/instance/file_transfer/list", ctrl.Instance.TransferInstanceList)
		roleInstance.POST("/instance/setting", ctrl.Instance.UpdateSetting)
		roleInstance.POST("/instance/restart", ctrl.Instance.RestartInstance) // 开机
		roleInstance.PUT("/instance/ssh_pwd", ctrl.Instance.SSHPwdUpdate)
		roleInstance.POST("/instance/unpayg/renewal", ctrl.Instance.SubUserGetUnExpireList)
		roleInstance.POST("/instance/receipt/create", ctrl.WorkOrder.CreateInstanceReceipt) // 生成回执

		roleInstance.POST("/instance/tag/list", ctrl.Instance.V2GetTagList)
		roleInstance.POST("/instance/tag", ctrl.Instance.V2AddTag)
		roleInstance.POST("/instance/tag/del", ctrl.Instance.V2DeleteTag)
	}

	// role: deployment
	roleDeployment := subUser.Group("").Use(mw.SubUserLoginRequired(constant.RoleDeployment))
	{
		roleDeployment.POST("/deployment", ctrl.Deployment.DeploymentCreate)                  // create
		roleDeployment.POST("/deployment/overview", ctrl.Deployment.DeploymentCreateOverview) // create overview
		roleDeployment.PUT("/deployment", ctrl.Deployment.DeploymentUpdate)                   // update
		roleDeployment.PUT("/deployment/overview", ctrl.Deployment.DeploymentUpdateOverview)  // update overview
		roleDeployment.GET("/deployment", ctrl.Deployment.DeploymentDetail)                   // detail
		roleDeployment.GET("/deployment/overview", ctrl.Deployment.DeploymentOverview)        // detail overview
		roleDeployment.POST("/deployment/list", ctrl.Deployment.DeploymentList)               // list
		roleDeployment.POST("/deployment/container/list", ctrl.Deployment.DCList)             // list instance
		roleDeployment.PUT("/deployment/container/power_off", ctrl.Deployment.DCPowerOff)     // list instance
		roleDeployment.GET("/deployment/container/off_reason", ctrl.Deployment.DCOffReason)   // 停止原因
		roleDeployment.PUT("/deployment/operate", ctrl.Deployment.DeploymentStatusOperate)    // operate
		roleDeployment.PUT("/deployment/refresh", ctrl.Deployment.DeploymentRefresh)          // refresh
		roleDeployment.DELETE("/deployment", ctrl.Deployment.DeploymentDelete)
		roleDeployment.GET("/deployment/ddp/overview", ctrl.Deployment.DeploymentDurationPkgOverview) // 时长包概览
		roleDeployment.POST("/deployment/ddp/list", ctrl.Deployment.GetDeploymentDurationList)        // 弹性部署时长包列表

		roleDeployment.DELETE("/order/deployment/ddp/single/preview", ctrl.BC.CreateOrderForCancelDeploymentDurationRefundSingle) // 退订单个弹性部署时长包 - 预览
		roleDeployment.DELETE("/order/deployment/ddp/single", ctrl.BC.CreateOrderForCancelDeploymentDurationRefundSingle)         // 退订单个弹性部署时长包
	}

	// role: file_storage
	roleFileStorage := subUser.Group("").Use(mw.SubUserLoginRequired(constant.RoleFileStorage))
	{
		roleFileStorage.POST("/file_storage/init", ctrl.BC.InitUserFileStorage)          // 初始化文件存储
		roleFileStorage.GET("/file_storage/list", ctrl.Region.GetFileStorageListForUser) // 获取用户文件存储列表
		roleFileStorage.GET("/file_storage/mount/list", ctrl.Region.FileStorageMountAccessList)
		roleFileStorage.POST("/file_storage/mount", ctrl.Region.FileStorageMountCtrl)
		roleFileStorage.GET("/file_storage/autofs/max_usage", ctrl.Region.AutoFsGetUsage)
	}

	// role: billing_center
	roleBillingCenter := subUser.Group("").Use(mw.SubUserLoginRequired(constant.RoleBillingCenter))
	{
		roleBillingCenter.POST("/order/list", ctrl.BC.OrderGetList)                                  // 获取订单列表
		roleBillingCenter.POST("/bill/list", ctrl.BC.BillGetList)                                    // 账单列表（账单明细）
		roleBillingCenter.GET("/bill/confirm", ctrl.BC.BillConfirm)                                  // 查看账单是否确认
		roleBillingCenter.GET("/bill", ctrl.BC.BillGet)                                              // 查看账单详情/bill
		roleBillingCenter.POST("/bill/balance_statement/list", ctrl.BC.GetBalanceStatementList)      // 收支明细
		roleBillingCenter.POST("/bill/daily_bill_statement/list", ctrl.BC.GetDailyBillStatementList) // 按日的账单明细
		roleBillingCenter.GET("/bill/daily_bill_statement/export", ctrl.BC.DailyBillStatementExport) //日结账单导出csv

		// invoice
		roleBillingCenter.POST("/invoice/list", ctrl.Invoice.UserInvoiceList)                       // 用户发票列表
		roleBillingCenter.POST("/invoice/create", ctrl.Invoice.CreateUserInvoice)                   // 创建用户发票
		roleBillingCenter.POST("/invoice/detail", ctrl.Invoice.GetUserInvoiceDetail)                // 用户获取发票明细
		roleBillingCenter.GET("/invoice/amount/detail", ctrl.Invoice.GetInvoiceAmountInfo)          // 获取用户开票金额信息
		roleBillingCenter.POST("/invoice/order/list", ctrl.Invoice.GetInvoiceOrderList)             // 用户获取所有能开发票的订单
		roleBillingCenter.POST("/invoice/daily_bill/list", ctrl.Invoice.GetInvoiceDailyBillList)    // 用户获取所有能开发票的日结账单
		roleBillingCenter.POST("/invoice/title_info/create", ctrl.Invoice.CreateInvoiceTitleInfo)   // 创建发票抬头信息
		roleBillingCenter.POST("/invoice/title_info/update", ctrl.Invoice.UpdateInvoiceTitleInfo)   // 更新发票抬头信息
		roleBillingCenter.POST("/invoice/title_info/list", ctrl.Invoice.InvoiceTitleInfoList)       // 发票抬头信息列表
		roleBillingCenter.GET("/invoice/title_info/get", ctrl.Invoice.GetInvoiceTitleInfo)          // 获取发票抬头信息
		roleBillingCenter.DELETE("/invoice/title_info/delete", ctrl.Invoice.DeleteInvoiceTitleInfo) // 删除发票抬头信息
		roleBillingCenter.POST("/invoice/mail_info/create", ctrl.Invoice.CreateInvoiceMailInfo)     // 创建发票邮寄信息
		roleBillingCenter.POST("/invoice/mail_info/update", ctrl.Invoice.UpdateInvoiceMailInfo)     // 更新发票邮寄信息
		roleBillingCenter.POST("/invoice/mail_info/list", ctrl.Invoice.InvoiceMailInfoList)         // 发票邮寄信息列表
		roleBillingCenter.GET("/invoice/mail_info/get", ctrl.Invoice.GetInvoiceMailInfo)            // 获取发票邮寄信息
		roleBillingCenter.DELETE("/invoice/mail_info/delete", ctrl.Invoice.DeleteInvoiceMailInfo)   // 删除发票邮寄信息

		// credit wallet
		bc.POST("/credit_wallet/history", ctrl.BC.CreditWalletHistoryGetListForUser)
	}

	// sys_notice
	sysNotice := subUser.Group("/sys_notice")
	sysNotice.Use(mw.SubUserLoginRequired())
	{
		sysNotice.PUT("", ctrl.SysNotice.SubUserRead)
		sysNotice.GET("", ctrl.SysNotice.SubUserGetNotice)
	}

	// image
	imageSubUser := subUser.Group("/image")
	imageSubUser.Use(mw.SubUserLoginRequired())
	{
		imageSubUser.POST("/all", ctrl.Image.GetImageCompleteInfo)                                  // 获取镜像完整信息
		imageSubUser.POST("/codewithgpu/popular", ctrl.PrivateImage.CodeWithGpuGetImagePopularList) // 获取社区镜像
		imageSubUser.POST("/codewithgpu/list", ctrl.PrivateImage.CodeWithGpuGetImageList)
		imageSubUser.POST("/private/get", ctrl.PrivateImage.GetAll) // 获取用户所有镜像
		imageSubUser.POST("/get", ctrl.Image.GetOnShelvesImages)    // 改动: 仅能看到上架的
	}
	image := subUser.Group("/image")
	image.Use(mw.SubUserLoginRequired(constant.RoleImage))
	{
		// image.GET("/all", ctrl.Image.GetImageCompleteInfo)                         // 获取镜像完整信息
		image.GET("/get_framework", ctrl.Image.GetOnShelvesImageFramework)         // 改动: 仅能看到上架的
		image.POST("/private/list", ctrl.PrivateImage.List)                        // 获取用户镜像列表
		image.POST("/private/update", ctrl.PrivateImage.UpdateImageName)           // 更新镜像名称
		image.DELETE("/private/delete", ctrl.PrivateImage.Delete)                  // 删除用户镜像
		image.POST("/private/check_name", ctrl.PrivateImage.CheckPrivateImageName) // 校验用户镜像名称
		image.GET("/private/num", ctrl.PrivateImage.GetImageNum)                   // 获取用户镜像数量
		image.POST("/private/share", ctrl.PrivateImage.CreateShareImage)           // 用户共享镜像
		image.POST("/private/share/cancel", ctrl.PrivateImage.CancelShareImage)    // 用户取消共享镜像
		image.POST("/private/share/user", ctrl.PrivateImage.GetShareImageUser)     // 获取共享镜像用户
		image.GET("/codewithgpu", ctrl.PrivateImage.CodeWithGpuValidImage)
		image.POST("/private/usage", ctrl.PrivateImage.GetUserUsageInfo) // 获取镜像使用信息
	}

	// public data
	publicData := subUser.Group("/public_data")
	publicData.Use(mw.SubUserLoginRequired())
	{
		publicData.POST("/list", ctrl.PublicData.RedisGetListLogin)
		publicData.POST("", ctrl.PublicData.Create)
		publicData.DELETE("", ctrl.PublicData.Delete)
		publicData.PUT("", ctrl.PublicData.Update)
		publicData.GET("", ctrl.PublicData.Get)
		publicData.GET("/share", ctrl.PublicData.MyShare)
		publicData.PUT("/star", ctrl.PublicData.Star)
		publicData.GET("/star", ctrl.PublicData.MyStar)
		publicData.DELETE("/star", ctrl.PublicData.StarCancel)
		publicData.GET("/name_exist", ctrl.PublicData.NameCheck)

	}

	// common data
	commonData := subUser.Group("common_data")
	{
		commonData.GET("/list", ctrl.CommonData.CommonDataUserList) // 前台用户公共数据列表
	}

	netDisk := subUser.Group("/net_disk")
	netDisk.Use(mw.SubUserLoginRequired())
	{
		netDisk.POST("/init", ctrl.Region.InitUserNetDisk)      // 初始化网盘
		netDisk.GET("/list", ctrl.Region.GetNetDiskListForUser) // 获取用户网盘列表
	}

	kv := subUser.Group("/internal/kv")
	kvAuth := kv.Group("")
	kvAuth.Use(mw.KVRequired())
	{
		kv.GET("/key/:key", ctrl.Kv.Get)
		kvAuth.POST("/key/:key", ctrl.Kv.Set)
	}

	subUser.POST("/server/sku/list", ctrl.Server.GetSKUList)
	subUser.POST("/server/sku/get", ctrl.Server.GetSKU)
	server := subUser.Group("", mw.SubUserLoginRequired())
	{
		server.POST("/server/cart_product/list", ctrl.Server.GetCartProductList)
		server.POST("/server/cart_product/get", ctrl.Server.GetCartProduct)
		server.POST("/server/cart_product/create", ctrl.Server.CreateCartProduct)
		server.POST("/server/cart_product/delete", ctrl.Server.DeleteCartProduct)
		server.POST("/server/cart_product/share", ctrl.Server.ShareCartProduct)
	}
	subUser.POST("/server/cart_product/get_shared", ctrl.Server.GetSharedCartProduct)

	sso := subUser.Group("/sso", mw.SubUserLoginRequired())
	{
		// 生成用于sso的ticket
		sso.POST("/ticket", ctrl.User.GenerateSSOTicket)
	}
	return
}
