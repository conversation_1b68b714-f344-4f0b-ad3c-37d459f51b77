package initialize

import (
	"context"
	"fmt"
	"os"
	"server/conf"
	"server/pkg-core/api/coreapi"
	coreContainerService "server/pkg-core/container_runtime/service"
	coreMachineService "server/pkg-core/machine/service"
	message_model "server/pkg-core/message/model"
	coreRegionService "server/pkg-core/region/service"
	workOrder "server/pkg-work-order/module_definition"
	workOrderCtrl "server/pkg-work-order/work_order/controller"
	workOrderService "server/pkg-work-order/work_order/service"
	"server/pkg/billing_center/bc_server"
	bcCtrl "server/pkg/billing_center/controller"
	bcService "server/pkg/billing_center/service"
	commonCtrl "server/pkg/common"
	commonDataCtrl "server/pkg/common_data/controller"
	commonDataService "server/pkg/common_data/service"
	containerService "server/pkg/container_runtime/service"
	contractCtrl "server/pkg/contract/controller"
	contractService "server/pkg/contract/service"
	ddsService "server/pkg/data_disk_stock/service"
	"server/pkg/db_helper"
	"server/pkg/db_helper/model_init"
	deploymentCtrl "server/pkg/deployment/controller"
	deploymentService "server/pkg/deployment/service"
	fileCtrl "server/pkg/file/controller"
	fileService "server/pkg/file/service"
	kvCtrl "server/pkg/global_kv/controller"
	ft_service "server/pkg/global_kv/service"
	gpuStockCtrl "server/pkg/gpu_stock/controller"
	gpuStockService "server/pkg/gpu_stock/service"
	"server/pkg/guard"
	imageCtrl "server/pkg/image/controller"
	imageService "server/pkg/image/service"
	instanceCtrl "server/pkg/instance/controller"
	instanceService "server/pkg/instance/service"
	invoiceCtrl "server/pkg/invoice/controller"
	invoiceService "server/pkg/invoice/service"
	"server/pkg/logger"
	machineCtrl "server/pkg/machine/controller"
	machineService "server/pkg/machine/service"
	"server/pkg/message/model"
	"server/pkg/middleware"
	"server/pkg/module_definition"
	sysNoticeCtrl "server/pkg/notice/controller"
	sysNoticeService "server/pkg/notice/service"
	notify1 "server/pkg/notify"
	notifyCtrl "server/pkg/notify/controller"
	notifyService "server/pkg/notify/service"
	portService "server/pkg/port/service"
	promotionCtrl "server/pkg/promotion/controller"
	promotionService "server/pkg/promotion/service"
	publicDataCtrl "server/pkg/public_data/controller"
	publicDataService "server/pkg/public_data/service"
	regionCtrl "server/pkg/region/controller"
	regionService "server/pkg/region/service"
	serverCtrl "server/pkg/server/controller"
	serverService "server/pkg/server/service"
	userCtrl "server/pkg/user/controller"
	userService "server/pkg/user/service"
	"server/plugin/influxdb_plugin"
	"server/plugin/ip_plugin"
	"server/plugin/kv_plugin"
	"server/plugin/mysql_plugin"
	"server/plugin/payment"
	"server/plugin/queue"
	"server/plugin/redis_plugin"
	tsc "server/plugin/redis_plugin/time_service_center"
	"server/plugin/tencent_oss"
	"time"

	influxdb2 "github.com/influxdata/influxdb-client-go/v2"
	sharding "github.com/liangjunmo/gorm-sharding"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/notify"
	"gorm.io/gorm"
)

type InitServerDependencyResp struct {
	DbConn          *gorm.DB
	RedisClient     *redis.Client
	Q               *queue.Q
	CoreQ           *queue.Q
	WxpayClient     *core.Client
	NotifyHandler   *notify.Handler
	AliPayClientMap map[string]*payment.AliPayClient
	CoreApi         *coreapi.Api
	Clean           func()
	Cmb             *payment.CmbBank
}

func InitServerDependency() (resp *InitServerDependencyResp, err error) {
	l := logger.NewLogger("initialize")
	l.Info("begin initializing...")
	l.Debug("debug mod: on")

	var (
		globalConf      = conf.GetGlobalGsConfig()
		dbConn          *gorm.DB
		dbConnRO        *gorm.DB
		redisClient     *redis.Client
		q               *queue.Q
		coreQ           *queue.Q
		wxpayClient     *core.Client
		notifyHandler   *notify.Handler
		aliPayClientMap map[string]*payment.AliPayClient
		coreApi         *coreapi.Api
	)

	redisClient, err = redis_plugin.RedisClientProvider(&redis_plugin.RedisClientOption{
		Addr:     globalConf.Redis.Host,
		Password: globalConf.Redis.Password,
	})
	if err != nil {
		err = errors.Wrap(err, "init redis failed")
		return
	}

	tsc.Init(redisClient)

	timer := redis_plugin.NewTimerProvider(redisClient)
	dbConn, err = mysql_plugin.MySQLProvider(&mysql_plugin.MySQLOptions{
		URL: fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			globalConf.MySQL.User,
			globalConf.MySQL.Password,
			globalConf.MySQL.Host,
			globalConf.MySQL.DBName,
		),
		Debug: globalConf.App.DebugLog,
		// Debug: false, // NOTE: touch 热更新有时会出现 data race
		Timer: timer,
	})
	if err != nil {
		err = errors.Wrap(err, "init database failed")
		return
	}
	dbConnRO, err = mysql_plugin.MySQLProvider(&mysql_plugin.MySQLOptions{
		URL: fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			globalConf.MySQL.User,
			globalConf.MySQL.Password,
			globalConf.MySQL.HostRO,
			globalConf.MySQL.DBName,
		),
		Debug: globalConf.App.DebugLog,
		// Debug: false, // NOTE: touch 热更新有时会出现 data race
		Timer: timer,
	})
	if err != nil {
		err = errors.Wrap(err, "init database failed")
		return
	}

	db_helper.InitDatabaseConnection(dbConn, dbConnRO, l)

	mu := redis_plugin.NewMutexRedisProvider(redisClient)
	lock := mu.NewRedisMutex(redis_plugin.InitModelsMutex, "init")
	if ok, _ := lock.LockWithNoRetry(time.Second * 10); ok {
		defer lock.UnLock()
		err = model_init.InitModels(dbConn)
		if err != nil {
			err = errors.Wrap(err, "init models failed")
			return
		}
	}

	dbConn.Use(sharding.Register(sharding.Config{
		ShardingKey:    "uid",
		NumberOfShards: 64,
		// 使用自增ID
		PrimaryKeyGenerator: sharding.PKCustom,
		PrimaryKeyGeneratorFn: func(tableIdx int64) int64 {
			return 0
		},
	}, "bill"))
	dbConnRO.Use(sharding.Register(sharding.Config{
		ShardingKey:    "uid",
		NumberOfShards: 64,
		// 使用自增ID
		PrimaryKeyGenerator: sharding.PKCustom,
		PrimaryKeyGeneratorFn: func(tableIdx int64) int64 {
			return 0
		},
	}, "bill"))

	q, err = queue.NewQ(&queue.MQOption{
		Host:       globalConf.RabbitMQ.Host,
		User:       globalConf.RabbitMQ.User,
		Password:   globalConf.RabbitMQ.Password,
		GetMsgList: model.GetMsgList,
		Handling:   model.Handling,
	})
	if err != nil {
		err = errors.Wrap(err, "init rabbitmq failed")
		return
	}

	// 因为core与上层数据库的分离，导致持久化的message表不同，不能通用一个queue
	coreQ, err = queue.NewQ(&queue.MQOption{
		Host:       globalConf.RabbitMQ.Host,
		User:       globalConf.RabbitMQ.User,
		Password:   globalConf.RabbitMQ.Password,
		GetMsgList: message_model.GetMsgList,
		Handling:   message_model.Handling,
	})
	if err != nil {
		err = errors.Wrap(err, "init core rabbitmq failed")
		return
	}

	// 微信支付
	wxpayClient, notifyHandler, err = payment.WxPayClientProvider()
	if err != nil {
		err = errors.Wrap(err, "init wxpay client failed")
		return
	}
	if notifyHandler == nil {
		err = errors.Wrap(err, "init wxpay notifyHandler failed")
		return
	}
	// 支付宝支付
	aliPayClientMap, err = payment.AlipayClientProvider()
	if err != nil {
		err = errors.Wrap(err, "init alipay client provider")
		return
	}

	// coreApi
	coreApi = coreapi.New(globalConf.App.CoreHost)
	err = coreApi.Health()
	if err != nil {
		l.WarnE(err, "ping core failed")
		//err = errors.Wrap(err, "ping core failed")
		//return
	}

	tencentOss := globalConf.TencentOss
	err = tencent_oss.NewOss(tencentOss.BucketHost, tencentOss.Host, tencentOss.AccessID, tencentOss.AccessSecret)
	if err != nil {
		l.WarnE(err, "init tencent cos failed")
		return
	}

	cmb := payment.NewCmbBank(false, globalConf.CmbBank.UID, globalConf.CmbBank.Sm2, globalConf.CmbBank.Sm4, globalConf.CmbBank.CardNbr)

	// 热加载, 方便debug
	ctx, cancel := context.WithCancel(context.Background())
	go hotReloadDebugLog(ctx)

	resp = &InitServerDependencyResp{
		DbConn:          dbConn,
		RedisClient:     redisClient,
		Q:               q,
		CoreQ:           coreQ,
		WxpayClient:     wxpayClient,
		NotifyHandler:   notifyHandler,
		AliPayClientMap: aliPayClientMap,
		CoreApi:         coreApi,
		Cmb:             cmb,
		Clean: func() {
			cancel()
			_ = redisClient.Close()
			_ = q.Close()
		},
	}
	return resp, nil
}

func InitAnalyzeDependency() (
	dbConn *gorm.DB,
	redisClient *redis.Client,
	influxdbWriter *influxdb_plugin.AnaLogWriter,
	clean func(),
	err error,
) {
	l := logger.NewLogger("initialize")
	l.Info("begin initializing...")
	l.Debug("debug mod: on")

	globalConf := conf.GetGlobalGsConfig()
	redisClient, err = redis_plugin.RedisClientProvider(&redis_plugin.RedisClientOption{
		Addr:     globalConf.Redis.Host,
		Password: globalConf.Redis.Password,
	})
	if err != nil {
		err = errors.Wrap(err, "init redis failed")
		return
	}

	timer := redis_plugin.NewTimerProvider(redisClient)
	dbConn, err = mysql_plugin.MySQLProvider(&mysql_plugin.MySQLOptions{
		URL: fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			globalConf.MySQL.User,
			globalConf.MySQL.Password,
			globalConf.MySQL.Host,
			globalConf.MySQL.DBName,
		),
		Debug: globalConf.App.DebugLog,
		// Debug: false, // NOTE: touch 热更新有时会出现 data race
		Timer: timer,
	})
	if err != nil {
		err = errors.Wrap(err, "init database failed")
		return
	}

	db_helper.InitDatabaseConnection(dbConn, nil, l)

	var influxdbClient influxdb2.Client
	influxdbClient, err = influxdb_plugin.InfluxDBProvider(&influxdb_plugin.InfluxDBOptions{
		Host:  globalConf.InfluxDB.Host,
		Token: globalConf.InfluxDB.Token,
	})
	if err != nil {
		l.WarnE(err, "init influxdb failed")
		return
	}

	influxdbWriter, err = influxdb_plugin.NewAnaLogWriter(l, influxdbClient, influxdb_plugin.LogWriterBucketOfAnalyze)
	if err != nil {
		l.WarnE(err, "new log writer failed")
	}

	// 热加载, 方便debug
	ctx, cancel := context.WithCancel(context.Background())
	go hotReloadDebugLog(ctx)

	return dbConn, redisClient, influxdbWriter, func() {
		cancel()
		_ = redisClient.Close()
		if influxdbWriter != nil {
			influxdbWriter.Flush()
		}
		influxdbClient.Close()
	}, nil
}

type ControllerFactory struct {
	Instance     *instanceCtrl.InstanceController
	User         *userCtrl.UserController
	Machine      *machineCtrl.MachineController
	GpuType      *machineCtrl.GpuTypeController
	Image        *imageCtrl.ImageController
	Deployment   *deploymentCtrl.DeploymentController
	PrivateImage *imageCtrl.PrivateImageController
	BC           *bcCtrl.BCController
	Region       *regionCtrl.RegionController
	Promotion    *promotionCtrl.PromotionController
	File         *fileCtrl.FileController
	Server       *serverCtrl.ServerController

	AgentGuard *guard.Controller

	// Admin
	AdminInstance *instanceCtrl.InstanceAdminController

	UserGroup        *userCtrl.UserGroupController
	MachineUserGroup *machineCtrl.MachineUserGroupController
	Invoice          *invoiceCtrl.UserInvoiceController
	CommonData       *commonDataCtrl.CommonDataController
	Common           *commonCtrl.CommonController
	PublicData       *publicDataCtrl.PDController
	SysNotice        *sysNoticeCtrl.SysNoticeController
	Kv               *kvCtrl.KVController
	FT               *kvCtrl.FTController
	Notify           *notifyCtrl.NotifyController
	GpuStock         *gpuStockCtrl.GpuStockCtrl
	Contract         *contractCtrl.ContractController

	// Interior work order system
	WorkOrder *workOrderCtrl.WorkOrderController
	// and so on...

}

type ServiceFactory struct {
	// 接口
	BC            module_definition.BCInterface
	Instance      module_definition.InstanceInterface
	Container     module_definition.ContainerRuntimeInterface
	Port          module_definition.PortInterface
	Machine       module_definition.MachineInference
	User          module_definition.UserInterface
	GpuType       module_definition.GpuTypeInference
	Image         module_definition.ImageInference
	GpuStock      module_definition.GpuStockInterface
	Deployment    module_definition.DeploymentInterface
	Region        module_definition.RegionInterface
	CommonData    module_definition.CommonDataInterface
	PublicData    module_definition.PublicDataInterface
	PrivateImage  module_definition.PrivateImageInterface
	DataDiskStock module_definition.DataDiskStockInterface
	Promotion     module_definition.PromotionInterface
	Notify        module_definition.NotifyInterface
	WorkOrder     workOrder.WorkOrderInterface
	Contract      module_definition.ContractInterface

	Queue     *queue.Q
	CoreQueue *queue.Q
	// 实体
	Countdown *redis_plugin.OrderTimeoutPlugin
	Mutex     *redis_plugin.MutexRedis
	// and so on...
}

// InitInjectedServices 初始化各个包之间的 service 依赖, 这个函数名还有待推敲
func InitInjectedServices(
	pkg *InitServerDependencyResp,
	panicChan chan<- error,
) (ctrl *ControllerFactory, svc *ServiceFactory, mw *middleware.Middleware) {
	var (
		re        = pkg.RedisClient
		m         = pkg.Q
		coreQueue = pkg.CoreQ
		wxpay     = pkg.WxpayClient
		nh        = pkg.NotifyHandler
		ali       = pkg.AliPayClientMap
		coreApi   = pkg.CoreApi
	)

	// core
	var coreMachineSvcEntity = new(coreMachineService.McService)
	var coreRegionSvcEntity = new(coreRegionService.RegionService)
	var coreContainerSvcEntity = new(coreContainerService.CR)

	// 第一部分: 可以正常 new 的
	var userPlugin = redis_plugin.NewUserPluginProvider(re)
	var tokenBucketPlugin = redis_plugin.NewTokenBucket(re)
	var captchaPlugin = redis_plugin.NewUserCaptchaPluginProvider(re)
	var mutexRedis = redis_plugin.NewMutexRedisProvider(re)
	// var stopInstancePlugin = redis_plugin.NewStopInstancePluginProvider(re)
	var orderTimeoutPlugin = redis_plugin.NewOrderTimeoutPluginProvider(re)
	var machineMonitorPlugin = redis_plugin.NewMachineStatusPluginProvider(re)
	var billWarningPlugin = redis_plugin.NewBillWarningPluginProvider(re)
	var bcRechargeQuery = redis_plugin.NewBCRechargeQueryPluginProvider(re)
	var aliRechargePlugin = redis_plugin.NewAliRechargeTimeoutPluginProvider(re)
	var portPlugin = redis_plugin.NewPortPlugin(re)
	var regionPlugin = redis_plugin.NewRegionPlugin(re)
	var userInfoCachePlugin = redis_plugin.NewUserInfoCachePluginProvider(re)
	var publicDataPlugin = redis_plugin.NewPublicDataCacheProvider(re)
	var SysNoticePlugin = redis_plugin.NewSysNoticePluginProvider(re)
	var kvPlugin = kv_plugin.NewKVPluginProvider(re)
	var bcCache = redis_plugin.NewBCCacheProvider(re)
	var refundDelay = redis_plugin.NewWalletRefundDelayProvider(re)
	var deploymentLock = redis_plugin.NewDeploymentLockPluginProvider(re)
	var spacPlugin = redis_plugin.NewSPacPluginProvider(re)
	var deploymentVersionCtrlPlugin = redis_plugin.NewDeploymentVersionProvider(re)
	var deploymentStopRedis = redis_plugin.NewDeploymentStopProvider(re)
	var instanceLastPowerOnTimePlugin = redis_plugin.NewInstanceLastPowerOnTimeProvider(re)
	var SMSCountCachePlugin = redis_plugin.NewSMSCountCachePluginProvider(re)
	var smsErrorSendFeishuPlugin = redis_plugin.NewSmsErrorSendFeishuProvider(re)

	var redisTimer = redis_plugin.NewTimerProvider(re)

	var imageSvcEntity = imageService.NewImageServiceProvider()
	var wxpayPlugin = payment.NewWxPayPluginProvider(wxpay, nh)
	var aliPayPlugin = payment.NewAliPayPluginProvider(ali)
	var gpuStockEntity = gpuStockService.NewGpuStockServiceProvider(mutexRedis, m, coreApi)
	var userCloneExpireTimePlugin = redis_plugin.NewUserCloneExpireTimePluginProvider(re)

	// var UserFeedBackSvcEntity = userService.NewUserFeedBackServiceProvider()
	var containerUsagePlugin = redis_plugin.NewContainerUsageProvider(re)
	var privateImagePlugin = redis_plugin.NewPrivateImagePluginProvider(re)
	var instanceTimeoutPlugin = redis_plugin.NewInstanceRunningTimeoutPlugin(re)
	var ipPlugin = ip_plugin.NewIpPluginProvider()

	// 第二部分: circle wire, 只能手动 bind
	//

	coreRegionSvcEntity = coreRegionService.NewRegionServiceProvider(coreRegionSvcEntity, coreQueue, regionPlugin, kvPlugin, mutexRedis)
	var bcSvcEntity = new(bcService.BCService)
	var bcServerEntity = new(bc_server.BCServer)
	var userSvcEntity = new(userService.UserService)
	var instanceSvcEntity = new(instanceService.Service)
	var gpuTypeSvcEntity = new(machineService.GpuService)
	var machineSvcEntity = new(machineService.McService)
	var containerSvcEntity = new(containerService.CR)
	var deploymentEntity = new(deploymentService.DeploymentService)
	var invoiceEntity = new(invoiceService.InvoiceService)
	var commonDataEntity = new(commonDataService.CommonDataService)
	var ddsSvcEntity = new(ddsService.DataDiskService)
	var notifySvcEntity = new(notifyService.Service)
	var ftSvcEntity = new(ft_service.FTService)
	var regionSvcEntity = regionService.NewRegionServiceProvider(m, regionPlugin, userSvcEntity, coreApi, bcSvcEntity, notifySvcEntity, notify1.NewSmsChannel())
	var portSvcEntity = portService.NewPortServiceProvider(portPlugin, regionSvcEntity, mutexRedis, coreApi)
	var sysNoticeEntity = sysNoticeService.NewSysNoticeServiceProvider(SysNoticePlugin, mutexRedis)
	var privateImageEntity = new(imageService.PrivateImageService)
	var fileSvcEntity = fileService.NewFileFileServiceProvider()
	var workOrderSvcEntity = new(workOrderService.WorkOrderService)
	var promotionSvcEntity = promotionService.NewPromotionServiceProvider(userSvcEntity, re)
	var contractSvcEntity = new(contractService.ContractService)
	var publicDataSvcEntity = new(publicDataService.PDService)

	// 循环依赖的首尾
	var UserGroupSvcEntity = userService.NewUserGroupServiceProvider(userSvcEntity)
	var GroupSvcEntity = userService.NewGroupServiceProvider(UserGroupSvcEntity)
	var machineUserGroupEntity = machineService.NewMachineUserGroupServiceProvider(GroupSvcEntity)

	notifyService.NewNotifyServiceProvider(notifySvcEntity, userSvcEntity, notify1.NewSmsChannel(), smsErrorSendFeishuPlugin)
	ddsSvcEntity = ddsService.NewDataDiskServiceProvider(mutexRedis, coreApi, machineSvcEntity)
	gpuTypeSvcEntity = machineService.NewGpuServiceProvider(machineSvcEntity, coreApi)
	bcService.NewBCServiceProvider(bcSvcEntity, mutexRedis, orderTimeoutPlugin, billWarningPlugin, m, wxpayPlugin, gpuStockEntity, bcRechargeQuery, aliRechargePlugin, aliPayPlugin, ddsSvcEntity, bcCache, refundDelay, userSvcEntity, regionSvcEntity, kvPlugin, privateImageEntity, instanceSvcEntity, redisTimer, pkg.Cmb, notify1.NewSmsChannel(), bcServerEntity, notifySvcEntity, SMSCountCachePlugin, machineSvcEntity, gpuTypeSvcEntity)
	privateImageEntity = imageService.NewPrivateImageServiceProvider(privateImageEntity, mutexRedis, m, userSvcEntity, regionSvcEntity, imageSvcEntity, deploymentEntity, privateImagePlugin, kvPlugin)
	containerSvcEntity = containerService.NewContainerRuntime(machineSvcEntity, portSvcEntity, gpuStockEntity, gpuTypeSvcEntity, bcSvcEntity, containerUsagePlugin, deploymentEntity, privateImageEntity, regionSvcEntity, userSvcEntity, ddsSvcEntity, redisTimer, m, mutexRedis, coreApi)
	instanceService.NewInstanceService(instanceSvcEntity, containerSvcEntity, machineSvcEntity, regionSvcEntity, imageSvcEntity, portSvcEntity, userSvcEntity, bcSvcEntity, bcServerEntity, ddsSvcEntity, privateImageEntity, notifySvcEntity, notify1.NewSmsChannel(), redisTimer, m, containerUsagePlugin, mutexRedis, coreApi, gpuStockEntity, kvPlugin, ftSvcEntity, privateImagePlugin, instanceTimeoutPlugin, gpuTypeSvcEntity)
	bc_server.NewBCServerProvider(bcServerEntity, m, mutexRedis, bcSvcEntity, userSvcEntity, machineSvcEntity, gpuTypeSvcEntity, instanceSvcEntity, gpuStockEntity, regionSvcEntity, privateImageEntity, ddsSvcEntity, invoiceEntity, containerSvcEntity, deploymentEntity, coreApi, kvPlugin)
	deploymentService.NewDeploymentServiceProvider(deploymentEntity, regionSvcEntity, machineSvcEntity, gpuStockEntity, containerSvcEntity, userSvcEntity, gpuTypeSvcEntity, bcSvcEntity, bcServerEntity, imageSvcEntity, privateImageEntity, m, mutexRedis, deploymentLock, deploymentVersionCtrlPlugin, deploymentStopRedis)
	userSvcEntity = userService.NewUserServiceProvider(userSvcEntity, userPlugin, mutexRedis, captchaPlugin, bcSvcEntity, m, aliPayPlugin, machineSvcEntity, machineUserGroupEntity, UserGroupSvcEntity, userInfoCachePlugin, instanceSvcEntity, deploymentEntity, privateImageEntity, notifySvcEntity, userCloneExpireTimePlugin, spacPlugin, ipPlugin) // √
	invoiceService.NewInvoiceServiceProvider(invoiceEntity, bcSvcEntity, userSvcEntity, userInfoCachePlugin)
	publicDataService.NewPublicDataServiceProvider(publicDataSvcEntity, mutexRedis, publicDataPlugin, userSvcEntity)
	ft_service.NewFTControllerProvider(ftSvcEntity, kvPlugin, instanceSvcEntity, containerSvcEntity, m)
	workOrderSvcEntity = workOrderService.NewWorkOrderServiceProvider(mutexRedis, machineSvcEntity, userSvcEntity, instanceSvcEntity, notify1.NewSmsChannel(), gpuStockEntity, notifySvcEntity)
	machineSvcEntity = machineService.NewMachineServiceProvider(machineSvcEntity, gpuTypeSvcEntity, instanceSvcEntity, containerSvcEntity, userSvcEntity, machineMonitorPlugin, gpuStockEntity, machineUserGroupEntity, regionSvcEntity, ddsSvcEntity, containerUsagePlugin, coreApi, bcSvcEntity, deploymentEntity, workOrderSvcEntity)
	contractService.NewContractServiceProvider(contractSvcEntity, userSvcEntity)

	// 第三部分: 使用以上 service 构建 controller
	promotionController := promotionCtrl.NewPromotionControllerProvider(promotionSvcEntity)
	bcController := bcCtrl.NewBCControllerProvider(m, mutexRedis, bcSvcEntity, bcServerEntity, userSvcEntity, machineSvcEntity, gpuTypeSvcEntity, instanceSvcEntity, gpuStockEntity, regionSvcEntity, deploymentEntity)
	instanceController := instanceCtrl.NewInstanceControllerProvider(instanceSvcEntity, userSvcEntity, machineSvcEntity, kvPlugin, bcSvcEntity, bcServerEntity, privateImageEntity, instanceLastPowerOnTimePlugin)
	userController := userCtrl.NewUserController(userSvcEntity, userPlugin, captchaPlugin, spacPlugin, ipPlugin)
	machineController := machineCtrl.NewMachineControllerProvider(machineSvcEntity, coreApi, regionSvcEntity)
	gpuTypeController := machineCtrl.NewGpuTypeControllerProvider(gpuTypeSvcEntity)
	imageController := imageCtrl.NewImageControllerProvider(imageSvcEntity)
	agentGuardController := guard.NewController(coreMachineSvcEntity, coreContainerSvcEntity, coreRegionSvcEntity, redisTimer, m, containerUsagePlugin)
	deploymentController := deploymentCtrl.NewDeploymentProvider(userSvcEntity, bcSvcEntity, deploymentEntity, privateImageEntity, deploymentStopRedis)
	regionController := regionCtrl.NewRegionControllerProvider(regionSvcEntity, userSvcEntity)
	commonDataController := commonDataCtrl.NewCommonDataController(commonDataEntity)
	commonController := commonCtrl.NewCommonControllerProvider(regionSvcEntity, machineSvcEntity, sysNoticeEntity)
	sysNoticeController := sysNoticeCtrl.NewSysNoticeControllerProvider(sysNoticeEntity)
	kvController := kvCtrl.NewKVControllerProvider(kvPlugin)
	ftController := kvCtrl.NewFTControllerProvider(kvPlugin, instanceSvcEntity, containerSvcEntity, m, ftSvcEntity)
	fileController := fileCtrl.NewFileControllerProvider(fileSvcEntity)
	workOrderController := workOrderCtrl.NewWorkOrderControllerProvider(workOrderSvcEntity, machineSvcEntity, userPlugin, userSvcEntity, gpuTypeSvcEntity, instanceSvcEntity, containerSvcEntity)
	contractController := contractCtrl.NewContractControllerProvider(contractSvcEntity, userSvcEntity)

	// 建议 admin 单独写一个 ctrl.
	instanceAdminController := instanceCtrl.NewInstanceAdminControllerProvider(instanceSvcEntity, instanceSvcEntity, bcServerEntity)
	privateImageController := imageCtrl.NewPrivateImageControllerProvider(privateImageEntity, userSvcEntity)
	userGroupController := userCtrl.NewUserGroupController(userSvcEntity, UserGroupSvcEntity, GroupSvcEntity)
	machineUserGroupController := machineCtrl.NewMachineUserGroupControllerProvider(machineUserGroupEntity)
	invoiceController := invoiceCtrl.NewUserInvoiceControllerProvider(userSvcEntity, invoiceEntity)
	notifyController := notifyCtrl.NewNotifyControllerProvider(notifySvcEntity, instanceSvcEntity, regionSvcEntity)
	publicDataController := publicDataCtrl.NewPDControllerProvider(publicDataSvcEntity)
	gpuStockController := gpuStockCtrl.NewGpuStockCtrl(coreApi)

	serverSvcEntity := serverService.NewServiceProvider()
	serverController := serverCtrl.NewServerControllerProvider(serverSvcEntity, userSvcEntity)

	// 给 router 用的 ctrl 集合.
	ctrl = &ControllerFactory{
		Instance:     instanceController,
		User:         userController,
		Machine:      machineController,
		GpuType:      gpuTypeController,
		Image:        imageController,
		Deployment:   deploymentController,
		PrivateImage: privateImageController,
		AgentGuard:   agentGuardController,
		BC:           bcController,
		Region:       regionController,
		CommonData:   commonDataController,
		Common:       commonController,
		PublicData:   publicDataController,
		SysNotice:    sysNoticeController,
		Kv:           kvController,
		FT:           ftController,
		Promotion:    promotionController,
		File:         fileController,
		Server:       serverController,
		WorkOrder:    workOrderController,
		Contract:     contractController,

		// Admin
		AdminInstance:    instanceAdminController,
		UserGroup:        userGroupController,
		MachineUserGroup: machineUserGroupController,
		Invoice:          invoiceController,
		Notify:           notifyController,
		GpuStock:         gpuStockController,
	}

	// 给 worker 用的 svc 集合.
	svc = &ServiceFactory{
		BC:            bcSvcEntity,
		Instance:      instanceSvcEntity,
		Container:     containerSvcEntity,
		Port:          portSvcEntity,
		Machine:       machineSvcEntity,
		User:          userSvcEntity,
		GpuType:       gpuTypeSvcEntity,
		Image:         imageSvcEntity,
		GpuStock:      gpuStockEntity,
		Deployment:    deploymentEntity,
		Queue:         m,
		CoreQueue:     coreQueue,
		Countdown:     orderTimeoutPlugin,
		Mutex:         mutexRedis,
		Region:        regionSvcEntity,
		CommonData:    commonDataEntity,
		PublicData:    publicDataSvcEntity,
		PrivateImage:  privateImageEntity,
		DataDiskStock: ddsSvcEntity,
		Promotion:     promotionSvcEntity,
		WorkOrder:     workOrderSvcEntity,
		Contract:      contractSvcEntity,
		Notify:        notifySvcEntity,
	}

	// 初始化中间件
	mw = middleware.NewMiddlewareProvider(userPlugin, userSvcEntity, tokenBucketPlugin, workOrderSvcEntity)

	// 依赖注入步骤完成. 开始调用函数做各自的具体的初始化.
	err := doInitForServices(svc)
	if err != nil {
		panicChan <- err // panic
		return
	}

	return ctrl, svc, mw
}

// 完成一些 service 的计划任务.
func doInitForServices(svc *ServiceFactory) error {
	// 注册每个模块各自的钩子.

	return nil
}

func hotReloadDebugLog(ctx context.Context) {
	check := time.NewTicker(time.Second * 10)
	defer check.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-check.C:
			if _, err := os.Stat("/tmp/GSDEBUG1"); err == nil {
				conf.HotReloadSet(true)
				db_helper.Debug(true)
			} else if _, err = os.Stat("/tmp/GSDEBUG0"); err == nil {
				conf.HotReloadSet(false)
				db_helper.Debug(false)
			}
		}
	}
}
