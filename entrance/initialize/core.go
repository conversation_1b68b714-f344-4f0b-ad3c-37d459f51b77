package initialize

import (
	"fmt"
	"server/conf"
	containerController "server/pkg-core/container_runtime/container"
	containerService "server/pkg-core/container_runtime/service"
	dataDiskController "server/pkg-core/data_disk_stock/controller"
	dataDiskService "server/pkg-core/data_disk_stock/service"
	gpuStockController "server/pkg-core/gpu_stock/controller"
	gpuStockService "server/pkg-core/gpu_stock/service"
	machineController "server/pkg-core/machine/controller"
	machineService "server/pkg-core/machine/service"
	message_model "server/pkg-core/message/model"
	"server/pkg-core/module_definition"
	portController "server/pkg-core/port/controller"
	portService "server/pkg-core/port/service"
	regionController "server/pkg-core/region/controller"
	regionService "server/pkg-core/region/service"
	"server/pkg/db_helper"
	kvCtrl "server/pkg/global_kv/controller"
	"server/pkg/guard"
	"server/pkg/logger"
	"server/pkg/middleware"
	"server/plugin/kv_plugin"
	"server/plugin/mysql_plugin"
	"server/plugin/queue"
	"server/plugin/redis_plugin"
	tsc "server/plugin/redis_plugin/time_service_center"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

type CoreBasicDependency struct {
	RedisClient *redis.Client
	MutexRedis  *redis_plugin.MutexRedis
	DB          *gorm.DB
	MQ          *queue.Q
	Cancel      func()
}

func InitCoreBasicDependency(l *logger.Logger) (*CoreBasicDependency, error) {
	globalConf := conf.GetGlobalGsConfig()

	// Redis Client
	redisClient, err := redis_plugin.RedisClientProvider(&redis_plugin.RedisClientOption{
		Addr:     globalConf.Redis.Host,
		Password: globalConf.Redis.Password,
	})
	if err != nil {
		return nil, fmt.Errorf("redis: %v", err)
	}

	tsc.Init(redisClient)

	// Mutex
	mutexRedis := redis_plugin.NewMutexRedisProvider(redisClient)

	// DB
	db, err := mysql_plugin.MySQLProvider(&mysql_plugin.MySQLOptions{
		URL: fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			globalConf.Core.MySQL.User,
			globalConf.Core.MySQL.Password,
			globalConf.Core.MySQL.Host,
			globalConf.Core.MySQL.DBName,
		),
		Debug: globalConf.App.DebugLog,
		Timer: redis_plugin.NewTimerProvider(redisClient),
	})
	if err != nil {
		return nil, fmt.Errorf("db: %v", err)
	}
	db_helper.InitDatabaseConnection(db, db, l)
	err = autoMigrateModels(db, mutexRedis)
	if err != nil {
		return nil, fmt.Errorf("db automigrate: %v", err)
	}

	// InfluxDB
	//influxdbClient, err := influxdb_plugin.InfluxDBProvider(&influxdb_plugin.InfluxDBOptions{
	//	Host:  globalConf.InfluxDB.Host,
	//	Token: globalConf.InfluxDB.Token,
	//})
	//if err != nil {
	//	l.WarnE(err, "init influxdb failed")
	//}
	//
	//var logWriter *influxdb_plugin.LogWriter
	//if influxdbClient != nil && err == nil {
	//	logWriter, err = influxdb_plugin.NewLogWriter(l, influxdbClient, influxdb_plugin.LogWriterBucketOfServer)
	//	if err != nil {
	//		l.WarnE(err, "new log writer failed")
	//	}
	//	err = logger.InitHookToWriteLogToInfluxDB(logWriter)
	//	if err != nil {
	//		l.ErrorE(err, "init hook to write log to influx db failed")
	//	}
	//}

	mq, err := queue.NewQ(&queue.MQOption{
		Host:       globalConf.RabbitMQ.Host,
		User:       globalConf.RabbitMQ.User,
		Password:   globalConf.RabbitMQ.Password,
		GetMsgList: message_model.GetMsgList,
		Handling:   message_model.Handling,
	})
	if err != nil {
		return nil, fmt.Errorf("rabbitmq: %v", err)
	}

	cancel := func() {
		if mq != nil {
			mq.Close()
		}
		db, _ := db.DB()
		if db != nil {
			db.Close()
		}
		if redisClient != nil {
			redisClient.Close()
		}
	}

	return &CoreBasicDependency{
		RedisClient: redisClient,
		MutexRedis:  mutexRedis,
		DB:          db,
		MQ:          mq,
		Cancel:      cancel,
	}, nil
}

func autoMigrateModels(db *gorm.DB, mutexRedis *redis_plugin.MutexRedis) error {
	lock := mutexRedis.NewRedisMutex(redis_plugin.InitModelsMutex, "init_core")
	if ok, _ := lock.LockWithNoRetry(time.Second * 10); ok {
		defer lock.UnLock()
		return initModels(db)
	}
	return nil
}

type Definition interface {
	Init(dbConn *gorm.DB) error
	TableName() string
}

var ModelSet = []Definition{
	//&machineModel.GPUType{},
	//&machineModel.Machine{},
	//&gs.GpuStock{},
	//&portModel.Port{},
	//&containerModel.Container{},
	//&containerModel.StatusHistory{},
	//&containerModel.OperateHistory{},
	//&containerModel.MigrationHistory{},
	//&regionModel.Region{},
	//&regionModel.StorageOSS{},
	//&regionModel.RegionStorageOSS{},
	//&machineModel.MachineTag{},
	//&machineModel.MachineRelationTag{},
	//&machineModel.MachineWorkOrder{},
	//&machineModel.MachineWorkOrderRecord{},
	//&ddsModel.DataDiskStock{},
	//&ddsModel.DataDiskStockRecord{},
	//&message_model.Message{},
	//&regionModel.NetDisk{},
	//&regionModel.FileStorage{},
	//&regionModel.FileStorageConfig{},
}

func initModels(db *gorm.DB) (err error) {
	for _, v := range ModelSet {
		err = v.Init(db)
		if err != nil {
			return
		}
	}
	return
}

type CoreServiceFactory struct {
	GpuStockService  module_definition.GpuStockInterface
	RegionService    module_definition.RegionInterface
	PortService      module_definition.PortInterface
	DataDiskService  module_definition.DataDiskStockInterface
	MachineService   module_definition.MachineInference
	GpuTypeService   module_definition.GpuTypeInference
	ContainerService module_definition.ContainerRuntimeInterface
}

type CoreControllerFactory struct {
	GpuStockController   *gpuStockController.GpuStockController
	RegionController     *regionController.RegionController
	PortController       *portController.PortController
	DataDiskController   *dataDiskController.DataDiskController
	MachineController    *machineController.MachineController
	GpuTypeController    *machineController.GpuTypeController
	ContainerController  *containerController.ContainerRuntimeController
	AgentGuardController *guard.Controller
	KvController         *kvCtrl.KVController
}

type CoreMiddlewareFactory struct {
	StorageAgentMiddleware *middleware.StorageAgentMiddleware
	KVMiddleware           *middleware.KVMiddleware
	RequestIDMiddleware    gin.HandlerFunc
}

func InitCoreDependency(dependency *CoreBasicDependency) (*CoreServiceFactory, *CoreControllerFactory, *CoreMiddlewareFactory, error) {
	var machineSvc = new(machineService.McService)
	var containerSvc = new(containerService.CR)

	regionPlugin := redis_plugin.NewRegionPlugin(dependency.RedisClient)
	machineMonitorPlugin := redis_plugin.NewMachineStatusPluginProvider(dependency.RedisClient)
	containerUsagePlugin := redis_plugin.NewContainerUsageProvider(dependency.RedisClient)
	redisTimerPlugin := redis_plugin.NewTimerProvider(dependency.RedisClient)
	kvPlugin := kv_plugin.NewKVPluginProvider(dependency.RedisClient)
	mutexRedis := redis_plugin.NewMutexRedisProvider(dependency.RedisClient)

	gpuStockSvc := gpuStockService.NewGpuStockServiceProvider(dependency.MutexRedis, dependency.MQ)
	gpuStockCtrl := gpuStockController.NewGpuStockControllerProvider(gpuStockSvc)

	var regionSvc = new(regionService.RegionService)
	regionSvc = regionService.NewRegionServiceProvider(regionSvc, dependency.MQ, regionPlugin, kvPlugin, mutexRedis)
	regionCtrl := regionController.NewRegionControllerProvider(regionSvc)

	portPlugin := redis_plugin.NewPortPlugin(dependency.RedisClient)
	portSvc := portService.NewPortServiceProvider(portPlugin, regionSvc, dependency.MutexRedis)
	portCtrl := portController.NewPortControllerProvider(portSvc)

	dataDiskSvc := dataDiskService.NewDataDiskServiceProvider(dependency.MutexRedis, dependency.MQ)
	dataDiskCtrl := dataDiskController.NewDataDiskControllerProvider(dataDiskSvc)

	gpuTypeSvc := machineService.NewGpuServiceProvider(machineSvc, dependency.MQ)
	gpuTypeCtrl := machineController.NewGpuTypeControllerProvider(gpuTypeSvc)

	machineSvc = machineService.NewMachineServiceProvider(machineSvc, gpuTypeSvc, containerSvc, machineMonitorPlugin, gpuStockSvc, regionSvc, dataDiskSvc, dependency.MQ)
	machineCtrl := machineController.NewMachineControllerProvider(machineSvc)

	containerService.NewContainerRuntime(containerSvc, machineSvc, portSvc, gpuStockSvc, gpuTypeSvc, containerUsagePlugin, regionSvc, dataDiskSvc, redisTimerPlugin, dependency.MQ, dependency.MutexRedis, kvPlugin)
	containerCtrl := containerController.NewContainerRuntimeControllerProvider(containerSvc)

	agentGuardController := guard.NewController(machineSvc, containerSvc, regionSvc, redisTimerPlugin, dependency.MQ, containerUsagePlugin)

	serviceFactory := &CoreServiceFactory{
		GpuStockService:  gpuStockSvc,
		RegionService:    regionSvc,
		PortService:      portSvc,
		DataDiskService:  dataDiskSvc,
		MachineService:   machineSvc,
		GpuTypeService:   gpuTypeSvc,
		ContainerService: containerSvc,
	}
	controllerFactory := &CoreControllerFactory{
		GpuStockController:   gpuStockCtrl,
		RegionController:     regionCtrl,
		PortController:       portCtrl,
		DataDiskController:   dataDiskCtrl,
		MachineController:    machineCtrl,
		GpuTypeController:    gpuTypeCtrl,
		ContainerController:  containerCtrl,
		AgentGuardController: agentGuardController,
		KvController:         kvCtrl.NewKVControllerProvider(kvPlugin),
	}
	middlewareFactory := &CoreMiddlewareFactory{
		StorageAgentMiddleware: middleware.NewStorageAgentMiddlewareProvider(),
		KVMiddleware:           middleware.NewKVMiddlewareProvider(),
		RequestIDMiddleware:    middleware.NewRequestIDMiddleware(),
	}
	// 依赖注入步骤完成. 开始调用函数做各自的具体的初始化.
	serviceFactory.ContainerService.InitOwnHooks()
	return serviceFactory, controllerFactory, middlewareFactory, nil
}
