package others_model_020

import (
	"gorm.io/gorm"
	"server/pkg/user/model"
	"time"
)

const TableNameUser string = "user"

type User struct {
	ID        int            `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`

	// 用户基本信息
	UUID             string               `gorm:"column:uuid" json:"uuid"` // 第三方用户登录的凭证(多个三方登录绑定同一个uuid)
	Username         string               `gorm:"column:username" json:"username"`
	Password         string               `gorm:"column:password" json:"-"`
	Nickname         string               `gorm:"column:nickname" json:"nickname"`
	Status           model.UserStatusType `gorm:"column:status;" json:"status"` // 用户当前状态
	LoggedInAt       *time.Time           `gorm:"column:logged_in_at;" json:"logged_in_at"`
	Email            string               `gorm:"column:email" json:"email"`
	Phone            string               `gorm:"column:phone" json:"phone"`
	IsAdmin          bool                 `gorm:"column:is_admin;type:tinyint(1)" json:"is_admin"`                       // 普通管理员
	IsSuperAdmin     bool                 `gorm:"column:is_super_admin;type:tinyint(1)" json:"is_super_admin"`           // 是否是超级管理员
	IsIdleTaskOpened bool                 `gorm:"column:is_idle_task_opened;type:tinyint(1)" json:"is_idle_task_opened"` // 用户是否开始闲时作业
	Sha1Password     string               `gorm:"column:sha1_password;" json:"-"`
	FirstLogin       bool                 `json:"-"`                                                             // 是否是第一次登录
	EmailConfirmed   bool                 `gorm:"column:email_confirmed;type:tinyint(1)" json:"email_confirmed"` // 邮箱是否绑定过

	InvitationCode string `gorm:"column:invitation_code" json:"invitation_code"` // 用户邀请码
}

func (u *User) TableName() string {
	return TableNameUser
}

// Init 实现 db_helper 接口.
func (u *User) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&User{})
}
