package others_model_020

import (
	"gorm.io/gorm"
	"server/pkg/constant"
	"time"
)

const TableNameGpuStock = "gpu_stock"

type GpuStock struct {
	ID        int            `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index;index:machine_deleted_idx;" json:"-"`

	Index     int    `gorm:"type:int;column:index;" json:"index"`
	GpuName   string `gorm:"type:varchar(255);column:gpu_name;NOT NULL;" json:"gpu_name"`
	GpuUUID   string `gorm:"type:varchar(255);column:gpu_uuid;NOT NULL;" json:"gpu_uuid"`
	GpuMemory int64  `gorm:"type:bigint(20);column:gpu_memory;" json:"gpu_memory"` //单位为byte
	MachineID string `gorm:"type:varchar(255);column:machine_id;NOT NULL;index:machine_deleted_idx;" json:"machine_id"`
	Uuid      string `gorm:"type:varchar(255);column:uuid;NOT NULL;" json:"uuid"` // instanceUUID
	//优先级用来判断此任务的优先级, 级别高的任务可以抢占级别低的任务的gpu资源
	Priority constant.PriorityType `gorm:"type:int;column:priority;" json:"priority"`
	// OccupationType constant.OrderType           `gorm:"type:varchar(255);column:occupation_type;NOT NULL;" json:"occupation_type"`
}

func (m *GpuStock) TableName() string {
	return TableNameGpuStock
}

// Init 实现 db_helper 接口.
func (m *GpuStock) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&GpuStock{})
}
