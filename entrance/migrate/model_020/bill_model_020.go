package model_020

import (
	"encoding/json"
	bc_020 "server/entrance/migrate/model_020/bc_model_020"
	bcm "server/pkg/billing_center/model"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/libs"
	"server/pkg/logger"
	userModel "server/pkg/user/model"
	"time"

	"gorm.io/gorm"
)

func MigrateBC(oldDBConn, newDBConn *gorm.DB, l *logger.Logger) error {
	var billList []bc_020.Bill
	var chargingList []bc_020.Charging
	var orderList []bc_020.Order

	var subtype constant.BillSubType
	err := oldDBConn.Model(bc_020.Bill{}).FindInBatches(&billList, 25, func(tx *gorm.DB, batch int) error {
		for _, v := range billList {
			// switch v.SubType {
			// case 10:
			//	subtype = constant.BillSubTypeAdminRecharge
			// case 11:
			//	subtype = constant.BillSubTypeRecharge
			// case 20:
			//	subtype = constant.BillSubTypeCreateContainer
			// case 21:
			//	subtype = constant.BillSubTypeRenewalContainer
			// case 22:
			//	subtype = constant.BillSubTypeCharge
			// case 23:
			//	subtype = constant.BillSubTypeChargeShutdown
			// case 24:
			//	subtype = constant.BillSubTypeShutdown
			// }

			err := db_helper.InsertOne(db_helper.QueryDefinition{
				DBTransactionConnection: newDBConn,
				ModelDefinition:         &bcm.Bill{},
				InsertPayload: &bcm.Bill{
					UID:         v.UID,
					UUID:        v.UUID,
					OrderUUID:   v.OrderUUID,
					RuntimeUUID: constant.NewContainerRuntimeUUID(v.InstanceUUID),
					ProductUUID: v.InstanceUUID,
					Type:        v.Type,
					SubType:     subtype,
					Asset:       v.Asset,
					ChargeType:  v.ChargeType,
					Balance:     v.Balance,
					CreatedAt:   v.CreatedAt,
					UpdatedAt:   v.UpdatedAt,
					ConfirmAt:   v.ConfirmAt,
				},
			}).GetError()
			if err != nil {
				return err
			}
		}
		return nil
	}).Error
	if err != nil {
		return err
	}

	err = oldDBConn.Model(bc_020.Charging{}).FindInBatches(&chargingList, 25, func(tx *gorm.DB, batch int) error {
		for _, v := range chargingList {
			err = db_helper.InsertOne(db_helper.QueryDefinition{
				DBTransactionConnection: newDBConn,
				ModelDefinition:         &bcm.Charging{},
				InsertPayload: &bcm.Charging{
					UID:           v.UID,
					MachineID:     v.MachineID,
					ProductUUID:   v.InstanceUUID,
					RuntimeUUID:   constant.NewContainerRuntimeUUID(v.InstanceUUID),
					OrderUUID:     v.OrderUUID,
					Type:          v.Type,
					PaygPrice:     v.PaygPrice,
					Charging:      v.Charging,
					CreatedAt:     v.CreatedAt,
					StartedAt:     v.StartedAt,
					LastSettledAt: v.LastSettledAt,
					UpdatedAt:     v.UpdatedAt,
					StoppedAt:     v.StoppedAt,
				},
			}).GetError()
			if err != nil {
				return err
			}
		}
		return nil
	}).Error
	if err != nil {
		return err
	}

	err = oldDBConn.Model(bc_020.Order{}).FindInBatches(&orderList, 25, func(tx *gorm.DB, batch int) error {
		for _, v := range orderList {
			err = json.Unmarshal(v.InstanceInfo, &v.InstanceEntity)
			if err != nil {
				return err
			}

			runtimeEntity := &constant.CreateContainerTaskRequest{
				Name:         v.InstanceEntity.InstanceName,
				Description:  v.InstanceEntity.Description,
				RuntimeType:  constant.ContainerRuntimeOfInstance,
				Image:        v.InstanceEntity.Image,
				MachineID:    v.InstanceEntity.MachineID,
				UID:          v.UID,
				OrderUUID:    v.InstanceEntity.OrderUUID,
				ProductUUID:  v.InstanceUUID,
				RuntimeUUID:  constant.NewContainerRuntimeUUID(v.InstanceUUID),
				ReqGPUAmount: v.InstanceEntity.ReqGPUAmount,
				ChargeType:   v.InstanceEntity.ChargeType,
				ExpiredAt:    v.InstanceEntity.ExpiredAt,
				// LastExpiredAt: v.InstanceEntity.ExpiredAt,
				PaygPrice: v.InstanceEntity.PaygPrice,
			}

			runtimeJson, err := json.Marshal(runtimeEntity)
			if err != nil {
				return err
			}

			err = db_helper.InsertOne(db_helper.QueryDefinition{
				DBTransactionConnection: newDBConn,
				ModelDefinition:         &bcm.Order{},
				InsertPayload: &bcm.Order{
					UID:         v.UID,
					Username:    v.Username,
					UserPhone:   v.UserPhone,
					UUID:        v.UUID,
					ProductUUID: v.InstanceUUID,
					RuntimeUUID: constant.NewContainerRuntimeUUID(v.InstanceUUID),
					RuntimeType: constant.ContainerRuntimeOfInstance,
					Status:      v.Status,
					OrderType:   v.OrderType,
					ChargeType:  v.ChargeType,
					DealPrice:   v.DealPrice,
					PriceInfo:   v.PriceInfo,
					RuntimeInfo: runtimeJson,
					MachineID:   v.InstanceEntity.MachineID,
					MachineInfo: v.MachineInfo,
					CreatedAt:   v.CreatedAt,
					UpdatedAt:   v.UpdatedAt,
					PayAt:       v.PayAt,
				},
			}).GetError()
			if err != nil {
				return err
			}
		}
		return nil
	}).Error
	if err != nil {
		return err
	}

	return nil
}

func MigrateBillDiscountInfo(dbConn *gorm.DB) (err error) {
	err = dbConn.Exec("update bill set discount= json_set(ifnull(discount,\"{}\"), \"$.discount_sum\", 0, \"$.origin_price\", bill.asset) where discount is null;").Error
	if err != nil {
		return err
	}
	// var billList []bc_020.Bill
	// err := oldDBConn.TestModel(bc_020.Bill{}).FindInBatches(&billList, 25, func(tx *gorm.DB, batch int) error {
	//	for i, _ := range billList {
	//		if len(billList[i].DiscountJson) == 0 {
	//			discount := &bc_020.DiscountDetail{OriginPrice: billList[i].Asset, DiscountSum: 0}
	//			err := billList[i].JsonDiscount(discount)
	//			if err != nil {
	//				continue
	//			}
	//			err = db_helper.UpdateOne(db_helper.QueryDefinition{
	//				ModelDefinition: &bc_020.Bill{},
	//				Filters: db_helper.QueryFilters{
	//					EqualFilters: map[string]interface{}{
	//						"id": billList[i].CouponID,
	//					},
	//				},
	//			}, billList[i]).GetError()
	//			if err != nil {
	//				return err
	//			}
	//		}
	//	}
	//	return nil
	// }).Error
	// if err != nil {
	//	return err
	// }
	return
}

func MigrateRechargeRecord(oldDBConn, newDBConn *gorm.DB) (err error) {
	// 迁移管理员帮助充值的用户记录
	var billList []bcm.Bill
	err = oldDBConn.Model(bcm.Bill{}).FindInBatches(&billList, 25, func(tx *gorm.DB, batch int) error {
		for _, v := range billList {
			if v.Type == constant.BillTypeRecharge && v.SubType == constant.BillSubTypeAdminRecharge {
				phone := ""
				if v.UserPhone == "" {
					var user *userModel.User
					err = db_helper.GetOne(db_helper.QueryDefinition{
						ModelDefinition: &userModel.User{},
						Filters: db_helper.QueryFilters{
							EqualFilters: map[string]interface{}{
								"id": v.UID,
							},
						},
					}, &user).GetError()
					if err == nil {
						phone = user.Phone
					}
				} else {
					phone = v.UserPhone
				}
				err = db_helper.InsertOne(db_helper.QueryDefinition{
					DBTransactionConnection: newDBConn,
					ModelDefinition:         &bcm.RechargeRecord{},
					InsertPayload: &bcm.RechargeRecord{
						UID:       v.UID,
						UserPhone: phone,
						BillUUID:  v.UUID,
						Pathway:   constant.AdminPay,
						Assets:    v.Asset,
						CreatedAt: v.CreatedAt,
						UpdatedAt: v.CreatedAt,
					},
				}).GetError()
				if err != nil {
					return err
				}
			}
		}
		return nil
	}).Error
	if err != nil {
		return err
	}
	return nil
}

func MigrateOrderInvoiceFieldRecord(oldDBConn, newDBConn *gorm.DB) (err error) {
	// 迁移订单中新的字段值
	var billList []bcm.Bill
	err = oldDBConn.Model(bcm.Bill{}).FindInBatches(&billList, 25, func(tx *gorm.DB, batch int) error {
		// 第一步先更新正常包年包月或续费扣费订单的pay_by_balance字段
		for _, v := range billList {
			if v.Type == constant.BillTypeCharge && v.ChargeType.IsRentType() {
				var order *bcm.Order
				err = db_helper.GetOne(db_helper.QueryDefinition{
					ModelDefinition: &bcm.Order{},
					Filters: db_helper.QueryFilters{
						EqualFilters: map[string]interface{}{
							"uuid": v.OrderUUID,
						},
					},
				}, &order).GetError()
				if err != nil {
					if err == gorm.ErrRecordNotFound {
						continue
					}
					return err
				}
				err = db_helper.UpdateOne(db_helper.QueryDefinition{ModelDefinition: &bcm.Order{},
					Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uuid": v.OrderUUID}}},
					map[string]interface{}{"pay_by_balance": v.PayByBalance}).GetError()
				if err != nil {
					return err
				}
			}

		}
		// 第二步更新正常包年包月退费的refund_amount字段,里面case有一种是: 实例可能会被多次续费转包月等操作，会有一个退费订单需要修改多个续费或包月的订单
		for _, b := range billList {
			if b.Type == constant.BillTypeRefund {
				orders := make([]*bcm.Order, 0)
				// 筛选出所有不是按量计费的，且付费成功或续费成功，且不是退款的订单
				err = db_helper.GlobalDBConn().Table(bcm.TableNameOrder).Where("product_uuid", b.ProductUUID).
					Where("order_type not in (?)", []constant.OrderType{constant.OrderTypeRefund}).
					Where("status in (?)", []constant.OrderStatus{constant.OrderStatusSuccess, constant.OrderStatusUnused}).
					Where("charge_type not in (?)", []constant.ChargeType{constant.ChargeTypePayg}).
					Where("DATE_FORMAT(created_at,'%Y-%m-%d %H:%i:%s') <= ?", b.CreatedAt). // 针对一个实例被多次退款的情况
					Order("id desc").Find(&orders).Error
				if err != nil {
					if err == gorm.ErrRecordNotFound {
						continue
					}
					return err
				}
				balance := b.PayByBalance
				for _, o := range orders {
					// 一次更新所有订单中refund_amount字段值
					balance -= o.PayByBalance
					if balance > 0 {
						err = db_helper.UpdateOne(db_helper.QueryDefinition{ModelDefinition: &bcm.Order{},
							Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uuid": o.UUID}}},
							map[string]interface{}{"refund_amount": o.PayByBalance}).GetError()
						if err != nil {
							return err
						}
					} else {
						err = db_helper.UpdateOne(db_helper.QueryDefinition{ModelDefinition: &bcm.Order{},
							Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uuid": o.UUID}}},
							map[string]interface{}{"refund_amount": balance + o.PayByBalance}).GetError()
						if err != nil {
							return err
						}
						break
					}
				}

			}
		}
		return nil
	}).Error
	if err != nil {
		return err
	}
	return nil
}

func MigrateDailyBillRecord(oldDBConn, newDBConn *gorm.DB) (err error) {
	// 迁移日结账单(把按量付费和无卡的账单每天综合一下生成日结账单)
	var firstBill *bcm.Bill
	err = oldDBConn.Model(bcm.Bill{}).Where("id = 1").Find(&firstBill).Error
	if err != nil {
		return err
	}
	dateFrom := firstBill.CreatedAt
	dateFrom = time.Date(dateFrom.Year(), dateFrom.Month(), dateFrom.Day(), 0, 0, 45, 0, dateFrom.Location())
	dateTo := time.Now()
	dateTo = time.Date(dateTo.Year(), dateTo.Month(), dateTo.Day(), 0, 0, 45, 0, dateTo.Location())

	dayNum := int(dateTo.Sub(dateFrom).Hours() / 24)
	for i := 1; i <= dayNum; i++ {
		bills := make([]*bcm.Bill, 0)
		dateTo := dateFrom.Add(24 * time.Hour)
		bills, err = GetBillDayRecord(dateFrom, dateTo)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				continue
			}
			return err
		}
		err = BillToDailyBill(bills, dateTo)
		if err != nil {
			return err
		}
		dateFrom = dateFrom.Add(24 * time.Hour)

	}
	return nil
}

func GetBillDayRecord(dateFrom time.Time, dateTo time.Time) (bills []*bcm.Bill, err error) {
	bills = make([]*bcm.Bill, 0)
	err = db_helper.GlobalDBConn().Table(bcm.TableNameBill).Where("bill_type = ?", constant.BillTypeCharge).
		Where("DATE_FORMAT(created_at,'%Y-%m-%d %H:%i:%s') > ?", dateFrom).
		Where("DATE_FORMAT(created_at,'%Y-%m-%d %H:%i:%s') <= ?", dateTo).Find(&bills).Error
	if err != nil {
		return nil, err
	}
	return
}

func BillToDailyBill(bills []*bcm.Bill, dateTo time.Time) (err error) {
	// 按照订单维度来统计账单
	billProductUUIDMap := make(map[string]*bcm.InstanceDailyBillStatistics)
	for _, bill := range bills {
		if bill.ChargeType.IsRentType() {
			continue
		}
		_, ok := billProductUUIDMap[bill.OrderUUID]
		if ok {
			billProductUUIDMap[bill.OrderUUID].PayAmount += bill.PayByBalance // 仅加余额支付的钱
		} else {
			rightTime := time.Date(dateTo.Year(), dateTo.Month(), dateTo.Day(), dateTo.Hour(), 0, 0, 0, dateTo.Location())
			ibds := &bcm.InstanceDailyBillStatistics{
				UID:         bill.UID,
				ProductUUID: bill.ProductUUID,
				OrderUUID:   bill.OrderUUID,
				ChargeType:  bill.ChargeType,
				BillType:    bill.Type,
				PayAmount:   bill.PayByBalance,
				PayAt:       rightTime,
			}
			billProductUUIDMap[bill.OrderUUID] = ibds
		}
	}
	params := make([]*bcm.CreateOrUpdateDailyBillParams, 0)
	for _, instanceBillDailyStatistics := range billProductUUIDMap {
		if instanceBillDailyStatistics.PayAmount <= 0 {
			continue
		}
		params = append(params, &bcm.CreateOrUpdateDailyBillParams{
			UID:         instanceBillDailyStatistics.UID,
			OrderUUID:   instanceBillDailyStatistics.OrderUUID,
			ProductUUID: instanceBillDailyStatistics.ProductUUID,
			ChargeType:  instanceBillDailyStatistics.ChargeType,
			PayAmount:   instanceBillDailyStatistics.PayAmount,
			BillType:    instanceBillDailyStatistics.BillType,
			PayAt:       instanceBillDailyStatistics.PayAt,
		})
	}
	err = CreateDailyBill(params)
	if err != nil {
		return err
	}
	return
}

func CreateDailyBill(params []*bcm.CreateOrUpdateDailyBillParams) (err error) {
	if len(params) < 0 {
		return nil
	}
	err = db_helper.NewDBConn().Transaction(func(tx *gorm.DB) (errDB db_helper.ModelError) {
		for _, param := range params {
			dailyBill := &bcm.DailyBill{
				UID:         param.UID,
				ProductUUID: param.ProductUUID,
				OrderUUID:   param.OrderUUID,
				UUID:        libs.RandNumberString(),
				PayAmount:   param.PayAmount,
				PayAt:       param.PayAt,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			}
			errDB = db_helper.InsertOne(db_helper.QueryDefinition{ModelDefinition: &bcm.DailyBill{},
				DBTransactionConnection: tx,
				InsertPayload:           dailyBill})
			if errDB.IsNotNil() {
				return
			}
		}
		return
	}).GetError()
	if err != nil {
		return
	}
	return
}
