package module_definition

import (
	"server/pkg-core/api/coreapi"
	"server/pkg-core/gpu_stock/model"
	"server/pkg/constant"
	"server/pkg/db_helper"
)

type GpuStockInterface interface {
	Create(params []*model.RegisterGpuStockParam) error
	// 占用gpu
	Reserve(request *model.GpuReserve) (ok bool, err error)
	// 确认占用
	ReserveCommit(request *coreapi.GpuReserveCommitReq) (err error)
	// 占用gpu，外部事务
	//ReserveWithTx(tx *gorm.DB, request *model.GpuReserve) (queueTxMsgUUIDList []string, ok bool, err error)
	// 释放gpu
	Release(request *model.GpuRelease) (err error)
	// 获取机器上具体被使用的gpu
	GetGpuStockUsed(machineID string, priorities constant.PriorityType) (gpuStocks []*model.GpuStock, err error)
	CountMachineGpuStockUsed(machineID string, priorities constant.PriorityType) (count int64, err error)
	CountAllMachineGpuStockUsed(priorities constant.PriorityType) (res map[string]int64, err error)
	CountAllMachineGpuStock(machineID string) (count int64, err error)
	// 获取gpu占用详情
	GetReserveDetail(runtimeUUID, machineID string, priority constant.PriorityType) (gpuUUIDList, gpuCaps []string, err error)
	// 获取机器所有gpu
	GetGpuStockAll(machineID string) (gpuStocks []*model.GpuStock, err error)
	// 获取所有空闲gpu
	GetAllFreeGpuStock() (gpuStocks []*model.GpuStock, err error)
	// 通过runtime_uuid获取gpu_stock
	GetGpuStockByRuntimeUUID(runtimeUUIDs []constant.ContainerRuntimeUUID) (gpuStocks []*model.GpuStock, err error)
	// 通过uuid获取gpu_stock
	GetGpuStockByUUID(uuids []string, priority constant.PriorityType) (gpuStocks []*model.GpuStock, err error)
	// 删除gpu_stock
	DeleteGpuStock(machineID string) (err error)
	GpuStockListForAdmin(params *coreapi.GpuStockListForAdminReq) (paged *db_helper.PagedData, err error)
	GpuStockReleaseByAdmin(params *coreapi.GpuStockReleaseByAdminReq) (err error)
	GpuStockReserveByAdmin(params *coreapi.GpuStockReserveByAdminReq) (err error)
	GpuStockDeleteByAdmin(params *coreapi.GpuStockDeleteByAdminReq) (err error)
}
