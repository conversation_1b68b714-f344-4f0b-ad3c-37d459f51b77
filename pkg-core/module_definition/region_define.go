package module_definition

import (
	"context"
	"server/pkg-agent/agent_constant"
	"server/pkg-agent/messenger"
	"server/pkg-core/api/coreapi"
	"server/pkg-core/region/model"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/plugin/queue"
	"server/plugin/queue_interface"
)

type RegionInterface interface {
	/*
	 * crud
	 */

	CreateRegion(newRegion *model.Region) (*model.Region, error)
	GetRegionDetail(rs constant.RegionSignType) (*model.Region, error) // 供其他内部模块使用
	GetRegionList() ([]model.Region, error)                            // 供其他内部模块使用
	GetRegionDetailWithStorageInfo(rs constant.RegionSignType) (*model.Region, model.RegionStorageOSSDetailList, error)
	GetStorageDetail(rs agent_constant.StorageOSSSignType) (*model.StorageOSS, error)

	CheckRegionSignExist(rs constant.RegionSignType) (exist bool, err error) // 地区标识不可重复, 也可以用于 agent-server 检查连接来的机器是否合法.
	CheckRegionNameExist(name string) (exist bool, err error)                // 地区名称不可重复

	/*
	 * agent
	 */

	AuthAgent(token string) (err error)                           // 验证 agent 携带的 token 是否正确.
	RegisterAgent(rs constant.RegionSignType, token string) error // 为 agent 注册, 需要支持重复进行. 具体参数和返回值还要考虑使用什么注册方法.
	CheckAgentRealHealth(rs constant.RegionSignType) bool         // 仅需要检查真实状态, NFS 机器不可以下架.
	SetStorageAgentOnlineOnce(rs constant.RegionSignType)         // 健康状态, 连接状态. 相当于一次心跳. 存入 redis 中.
	ConfirmRegionUserNetDiskInDB(rs constant.RegionSignType, uid int, isSuccess bool, errInfo string, quota int64)
	ConfirmAdminSetQuota(rs constant.RegionSignType, uid int, isSuccess bool, errInfo string, quota int64)

	/*
	 * net disk
	 */

	InitNetDiskWithRegionForUser(uid int, rs constant.RegionSignType, tenant string) (ru *model.NetDisk, err error)              // 初始化网盘
	GetNetDiskListForUser(uid int) (res []model.RegionUsageInfo, err error)                                                      // 前端显示网盘列表
	GetRegionListForIndex() (res []model.GetRegionListForIndexRes, err error)                                                    // 前端gpu市场筛选
	GetUserNetDiskMountForInstance(uid int, rs constant.RegionSignType) (res model.GetUserNetDiskMountForInstanceRes, err error) // 供其他内部模块使用, get /data/#{uid}
	NetDiskSetQuota(Sign constant.RegionSignType, uid int, quota int64) error
	GetSetQuotaMessage(sign constant.RegionSignType, uid int, quota int64, operateType messenger.MessageType) (msg *queue_interface.NewQueueForStorageAgentOnMachine, err error)
	GetUserNetDiskQuotaList(req coreapi.GetUserNetDiskQuotaListReq) (paged *db_helper.PagedData, list []model.UserNetDiskQuotaForAdminInfo, err error)
	GetNetDisk(uid int, rs constant.RegionSignType) (netDisk *model.NetDisk, err error)

	// file storage
	InitFileStorageWithRegionForUser(uid int, rs constant.RegionSignType, tenant string) (ru *model.FileStorage, err error) // 初始化文件存储
	GetFileStorageListForUser(uid int) (res []model.RegionFileStorageInfo, err error)                                       // 前端显示网盘列表
	GetFileStorageByUids(uids []int, rs constant.RegionSignType) (fs []model.FileStorage, err error)
	GetFileStorageForCharge(params *coreapi.GetFileStorageForChargeReq) (data *coreapi.GetFileStorageForChargeData, err error)
	InitFileStorageFinal(result *constant.FSInitResult)
	GetFileStorageAdminList(req coreapi.GetFileStorageAdminListReq) (coreapi.GetFileStorageAdminListData, error)
	UpdateFileStorageSetting(req coreapi.UpdateFileStorageSettingReq) error
	AutoFsSyncUsageInfo(req *constant.AutoFsSyncUsage) (err error)
	FsGetDetail(params *coreapi.FsGetDetailReq) (fs *model.FileStorage, err error)
	AdfsMkdir(uid int, rs constant.RegionSignType, subPath string) error

	/*
	 * cron
	 */

	CronJobRegister(ctx context.Context)
	MsgRegister() []queue.RegisterInfo

	GuardCronJobRegister(ctx context.Context, regionSign constant.RegionSignType, writer chan<- messenger.Message)

	/*
	 * other
	 */

	SyncStorageUsage(msg *constant.StorageSyncUsageModel)
	SyncDiskAlerts(msg *constant.StorageSyncDiskAlertsModel)
	DistributeFrpcProxy(rs constant.RegionSignType) (proxyHost string, proxyPort, proxyApiPort int, proxyToken string, region *model.Region, err error)

	GetFsConfigs(signs []constant.RegionSignType, versions []string) (map[constant.RegionSignType]map[string]model.FileStorageConfig, error)
	GetFsConfig(sign constant.RegionSignType, version string) (config model.FileStorageConfig, ok bool, err error)
}

type AdminRegionInterface interface {
	/*
	 * crud
	 */

	//AddRegion 地区名称不可重复, 如果有修改的需求，需要刷DB修改记录, frps端口和NFS的管理端口由系统做约定
	AddRegion(params model.AddRegionParams) error
	ListRegionForFrontend(filter model.RegionFilter, pagedReq db_helper.GetPagedRangeRequest) (*db_helper.PagedData, []model.Region, error)

	GetRegionDetail(rs constant.RegionSignType) (model.Region, error) // 供其他内部模块使用
	GetAllRegionDetail() ([]model.Region, error)                      // 供其他内部模块使用

	CheckRegionSignExist(rs constant.RegionSignType) (exist bool, err error) // 地区标识不可重复, 也可以用于 agent-server 检查连接来的机器是否合法.
	CheckRegionNameExist(name string) (exist bool, err error)                // 地区名称不可重复
}
