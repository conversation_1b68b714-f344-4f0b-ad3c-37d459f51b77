package module_definition

import (
	"context"
	"server/pkg-agent/agent_constant"
	"server/pkg-core/api/coreapi"
	"server/pkg-core/machine/model"
	"server/pkg/constant"
	"server/pkg/db_helper"
)

type MachineInference interface {
	// 注册机器
	Create(createMachineParams *agent_constant.MachineRegisterParam) error
	MachineBasicInfoUpdate(params *model.UpdateMachineInfoParams) (err error)
	// 获取机器
	Get(machineID string) (machine *model.Machine, err error)
	GetMachineByIDs(machinesIDs []string) (machines model.MachineList, err error)
	// 删除gou号型记录
	Delete(machineID string) error
	// 机器列表
	//List(params *model.GetMachineListParams, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []*model.Machine, err error)
	// 给业务层使用的列表，表里有的都查出来
	GetListForBusiness(pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []model.Machine, err error)
	// 管理获取机器列表
	AdminMachineList(params *model.GetAdminMachineListParams, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []*model.Machine, err error)
	// 检查机器健康状态和上否上下架
	CheckMachineHealth(machineID string) (ok bool, err error)
	// 检查机器连接状态
	CheckMachineStatusHealth(machineID string) (ok bool, err error)
	GetMachineStatus(machineID string) (machineStatus *model.MachineStatusSave, err error)
	// redis存储机器健康状态
	SetMachineStatus(params *agent_constant.MachineStatusParam) (err error)
	MachineStatusConnect(ctx context.Context, machineID string)
	// 获取所有机器的machine_id
	GetAllMachineID() (machineIDs []string, err error)
	// 通过gpu_type_id 筛选machine
	GetMachineByGpuTypeID(gpuTypeID int) (machine *model.Machine, err error)
	// 更新机器上架，下架或强制下架
	UpdateOnline(updateOnlineParam *model.UpdateOnlineParam) (err error)

	//CheckMaxInstanceNum(machineID string) (ok bool, err error)
	// 获取所有机器gpu号型
	GetAllMachineGpuType(isAdmin bool) (gpuTypes []*model.GPUType, err error)
	// 监控机器的各项信息，包括状态信息，gpu数据同步等
	MachineMonitor(ctx context.Context)

	SyncMachineGpuInfo(machineIds ...string) (err error)
	SyncMachineStatus() (err error)
	// 用户层获取机器列表
	//GetUserMachineList(userGroupIDs []int64, params *model.GetMachineListParams, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, err error)
	// 设置用户是否可见
	//SetMachineUserVisibleLimit(params *model.SetMachineUserVisibleLimitParam) (err error)
	MachineAliasMap() (data map[string]string)
	GetMachineListForCommon() (list []model.MachineInfoForCommon)
	GetMachineStorageOssCredentials(machineID string) (credentials agent_constant.MinioCredentials, err error)

	// 设置机器地区
	SetMachineRegion(params *agent_constant.MachineRegionParam) (err error)
	GetMachineRegion(machineID string) (regionSign constant.RegionSignType, err error)
	IsNetDiskAvailable(machineID string) (available, adfsAvailable bool)

	UpdateMachineSetting(req coreapi.UpdateMachineSettingReq) error

	// 主机维护工单

	WorkOrderCreate(params *model.WorkOrderCreateParams) (workOrder *model.MachineWorkOrder, err error)
	WorkOrderUpdate(params *model.WorkOrderUpdateParams) (err error)
	WorkOrderList(params *model.WorkOrderListParams) (paged *db_helper.PagedData, err error)
	WorkOrderRecordCreate(params *model.WorkOrderRecordCreateParams) (err error)
	WorkOrderRecordList(params *model.WorkOrderRecordListParams) (paged *db_helper.PagedData, err error)
}

type GpuTypeInference interface {
	// 创建gpu号型
	Create(params *model.CreateGpuTypeParams) error
	// 更新gpu号型
	Update(params *model.GpuTypeInfo) error
	// 获取gpu列表
	List(gpuName string, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []*model.GPUType, err error)
	// 删除gpu号型记录
	Delete(gpuTypeID int) (err error)
	// 检测gpu号型是否存在
	CheckGpuTypeName(gpuTypName string, gpuTypeID int) (existed bool, err error)
	// 筛选gpu号型
	GetGpuID(puTypName []string) (ids []int, err error)
	// 获取gpu号型
	GetGpu(gpuID int) (gpuType *model.GPUType, err error)
	// 获取gpuType通过id数组
	GetGpuByIDs(gpuIDs []int) (gpuTypes []*model.GPUType, err error)
}

//type MachineUserGroup interface {
//	Create(tx *gorm.DB, params *model.CreateMachineUserGroupParam) (err error)
//	GetGroupNameByMachineID(machineID string) (groups []*userModel.Group, err error)
//	DeleteByMachineID(tx *gorm.DB, machineID string) (err error)
//}
