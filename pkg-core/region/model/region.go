package model

import (
	"encoding/json"
	"fmt"
	"gorm.io/datatypes"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"

	"gorm.io/gorm"
)

const TableNameRegion = "core_region"

// Region 地区 Model。添加新的字段也需要修改 Region.Map 方法。
type Region struct {
	ID        int            `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	CreatedAt time.Time      `gorm:"column:created_at;type:datetime" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`

	Sign constant.RegionSignType `gorm:"column:sign;type:varchar(255);index" json:"sign"` // 地区标识
	Name string                  `gorm:"column:name;type:varchar(255)" json:"name"`       // 地区名称
	// 数据中心名称， 相同的数据中心具有相同的此字段，表明内网是互通的
	DataCenter string `gorm:"column:data_center;type:varchar(255)" json:"data_center"`

	PortRangeMin int `gorm:"column:port_range_min" json:"port_range_min"` // 端口分配范围 min (包含)
	PortRangeMax int `gorm:"column:port_range_max" json:"port_range_max"` // 端口分配范围 max (包含)

	SSLDomain   string `gorm:"type:varchar(255);column:ssl_domain;" json:"ssl_domain"`
	ExportAddr1 string `gorm:"column:export_addr_1;" json:"export_addr_1"` // 出口地址
	ExportAddr2 string `gorm:"column:export_addr_2;" json:"export_addr_2"` // 出口地址

	ExportAddr3 string `gorm:"column:export_addr_3;" json:"export_addr_3"` // 出口地址（热备机器的地址）

	ProxyPort  int    `gorm:"column:proxy_port" json:"proxy_port"`                      // 为空默认为7000
	ProxyToken string `gorm:"type:varchar(255);column:proxy_token;" json:"proxy_token"` // frpc token 不参与逻辑. default: 'seetatech666'

	/*  nfs
	// IsNFSAvailable 是否开启 nfs, 决定下列字段是否有意义.
	// NFSAddr NFS 地址, 现与出口地址相同, 且不参与逻辑 (agent 不自动挂载). create 时, 后端自动填写为与出口地址相同.
	// NFSPort NFS agent 端口, 即管理网盘的 storage agent api, https
	// NFSHealth NFS 健康状态, 如果填写新的 nfs, 默认为 unknown, 等待真正的状态同步.
	// DefaultUserQuotaInByte NFS 用户配额
	// NFSVisibleForFrontend 控制用户主页, 网盘列表是否显示当前地区
	*/
	IsNFSAvailable         bool                      `gorm:"column:is_nfs_available;type:tinyint(1)" json:"is_nfs_available"`
	NFSAddr                string                    `gorm:"column:nfs_addr;type:varchar(255)" json:"nfs_addr"`
	NFSPort                int                       `gorm:"column:nfs_port;" json:"nfs_port"`
	NFSHealth              constant.NFSMachineStatus `gorm:"column:nfs_health;type:varchar(255)" json:"nfs_health"`
	DefaultUserQuotaInByte int64                     `gorm:"column:default_user_quota_in_byte" json:"default_user_quota_in_byte"`
	NFSVisibleForFrontend  bool                      `gorm:"column:nfs_visible_for_frontend;type:tinyint(1)" json:"nfs_visible_for_frontend"`

	/*  adfs
	// IsADFSAvailable 是否开启 adfs, 决定下列字段是否有意义.
	// ADFSPubAddr adfs agent 地址, 即管理网盘的 storage agent api, https
	// ADFSPubPort adfs agent 端口, 与上面的addr配合使用, 返回给前端, 用以前端访问storage agent
	// ADFSFilerAddr 用户实例启动挂载容器所需参数, 自动挂载ADFS所需的server地址
	// ADFSImageName 用户实例启动挂载容器的镜像
	// ADFSVisibleForFrontend 控制用户主页, 文件存储列表是否显示当前地区(adfs与autofs公用此字段)
	*/
	IsADFSAvailable        bool   `gorm:"column:is_adfs_available;type:tinyint(1)" json:"is_adfs_available"`
	ADFSPubAddr            string `gorm:"column:adfs_pub_addr;type:varchar(255)" json:"adfs_pub_addr"`
	ADFSPubPort            int    `gorm:"column:adfs_pub_port;" json:"adfs_pub_port"`
	ADFSFilerAddr          string `gorm:"column:adfs_filer_addr;type:varchar(255)" json:"adfs_filer_addr"`
	ADFSImageName          string `gorm:"column:adfs_image_name;type:varchar(255)" json:"adfs_image_name"`
	ADFSVisibleForFrontend bool   `gorm:"column:adfs_visible_for_frontend;type:tinyint(1)" json:"adfs_visible_for_frontend"`

	/*  autofs
	// IsAutoFsAvailable 是否开启autofs, 决定下列字段是否有意义
	// AutoFSPubAddr autofs agent 地址, 即管理网盘的 storage agent api, https
	// AutoFSPubPort autofs agent 端口, 与上面的addr配合使用, 返回给前端, 用以前端访问storage agent
	// AutoFSFilerAddr autofs 用户实例启动挂载容器所需参数, 自动挂载ADFS所需的server地址
	// AutoFSImageName 用户实例启动挂载容器的镜像
	*/
	IsAutoFsAvailable bool             `gorm:"column:is_autofs_available;type:tinyint(1)" json:"is_autofs_available"`
	AutoFSPubAddr     string           `gorm:"column:autofs_pub_addr;type:varchar(255)" json:"autofs_pub_addr"`
	AutoFSPubPort     int              `gorm:"column:autofs_pub_port;" json:"autofs_pub_port"`
	AutoFSFilerAddr   string           `gorm:"column:autofs_filer_addr;type:varchar(255)" json:"autofs_filer_addr"`
	AutoFSImageName   string           `gorm:"column:autofs_image_name;type:varchar(255)" json:"autofs_image_name"`
	Rank              int              `gorm:"column:rank" json:"rank"` // 排序权重 用于控制前端地区列表的地区顺序
	AdditionalJson    datatypes.JSON   `gorm:"column:additional;type:json" json:"-"`
	Additional        RegionAdditional `gorm:"-" json:"additional"`

	// FsConfigVersion 表示此地区当前使用的文件存储的配置版本。
	// 当值为空时，表示使用 core_region 表中的配置。当值不为空时，表示使用 core_region_file_storage_config 表中的配置。
	FsConfigVersion string `gorm:"column:fs_config_version;type:varchar(255);not null;default:'';" json:"fs_config_version"`
}

type RegionAdditional struct {
	AutoFsPrefix string `json:"autofs_prefix"`
}

func (r *Region) TableName() string {
	return TableNameRegion
}

func (r *Region) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&Region{})
}

func (r *Region) InspectionPass() bool {
	if len(r.Name) == 0 || r.PortRangeMin == 0 || r.PortRangeMax == 0 ||
		len(r.ExportAddr1) == 0 || r.ExportAddr1 == r.ExportAddr2 {
		return false
	}

	// 允许 [35000,35000] 只给一个的极端情况, 但不允许 [35010,35009]
	if r.PortRangeMin > r.PortRangeMax {
		return false
	}

	if r.IsNFSAvailable {
		// nfs_addr 不校验, 后端自动填写
		if r.NFSPort == 0 || r.DefaultUserQuotaInByte == 0 {
			return false
		}
	}

	return true
}

func (r *Region) BeforeCreate(db *gorm.DB) error {
	r.AdditionalJson, _ = json.Marshal(r.Additional)
	return nil
}

func (r *Region) AfterFind(db *gorm.DB) error {
	if len(r.AdditionalJson) != 0 {
		_ = json.Unmarshal(r.AdditionalJson, &r.Additional)
	}
	return nil
}

func (r *Region) FillNFSAddrIfAvailable() {
	if r.IsNFSAvailable {
		r.NFSAddr = r.ExportAddr1
	}
}

func (r *Region) NFSUrl() string {
	r.FillNFSAddrIfAvailable()
	return fmt.Sprintf("%s:%d", r.NFSAddr, r.NFSPort)
}

func (r *Region) Map() map[string]interface{} {
	return map[string]interface{}{
		//"id":                         r.ID,
		"created_at":                 r.CreatedAt.Format(constant.FormatTimeString),
		"updated_at":                 r.UpdatedAt.Format(constant.FormatTimeString),
		"sign":                       r.Sign,
		"name":                       r.Name,
		"data_center":                r.DataCenter,
		"port_range_min":             r.PortRangeMin,
		"port_range_max":             r.PortRangeMax,
		"export_addr_1":              r.ExportAddr1,
		"export_addr_2":              r.ExportAddr2,
		"export_addr_3":              r.ExportAddr3,
		"proxy_port":                 r.ProxyPort,
		"proxy_token":                r.ProxyToken,
		"is_nfs_available":           r.IsNFSAvailable,
		"nfs_addr":                   r.NFSAddr,
		"nfs_port":                   r.NFSPort,
		"nfs_health":                 r.NFSHealth,
		"default_user_quota_in_byte": r.DefaultUserQuotaInByte,
		"rank":                       r.Rank,
		"adfs_filer_addr":            r.ADFSFilerAddr,
		"adfs_pub_addr":              r.ADFSPubAddr,
		"adfs_pub_port":              r.ADFSPubPort,
		"is_adfs_available":          r.IsADFSAvailable,
		"is_autofs_available":        r.IsAutoFsAvailable,
	}
}

type RegionUsageInfo struct {
	Sign                   constant.RegionSignType        `json:"sign"`
	Name                   string                         `json:"name"`
	ExportAddr1            string                         `json:"export_addr_1"`
	ExportAddr2            string                         `json:"export_addr_2"`
	DefaultUserQuotaInByte int64                          `json:"default_user_quota_in_byte"`
	NfsStatus              constant.NetDiskInitStatusType `json:"nfs_status"`
	NFSPort                int                            `json:"nfs_port"`
	Usage                  string                         `json:"usage"`
}

// CheckRegionIsInSameDataCenter 确定多个地区之间是否位于同一个数据中心，目前用于判断有没有内网链接
// 以启动实力迁移
// 判断方法： name相同
func CheckRegionIsInSameDataCenter(regionSignList ...constant.RegionSignType) (same bool, err error) {
	var res []Region
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &Region{},
		Filters: db_helper.QueryFilters{
			InFilters: []db_helper.In{
				{
					Key:   "sign",
					InSet: regionSignList,
				},
			},
		},
	}, &res).GetError()
	if err != nil {
		return
	}

	if len(res) == 0 {
		return
	}
	lastRegionDataCenter := res[0].DataCenter
	for _, r := range res {
		if r.DataCenter != lastRegionDataCenter {
			return
		}
	}

	same = true
	return
}

func (r *Region) FsType() constant.FsType {
	if r.IsAutoFsAvailable {
		return constant.AutoFS
	}
	if r.IsADFSAvailable {
		return constant.ADFS
	}
	return ""
}

func (r *Region) RegionGet(filter *db_helper.QueryFilters) error {
	return db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: r,
		Filters:         *filter,
	}, &r).GetError()
}

func (r *Region) GetAll(filter *db_helper.QueryFilters) (list []Region, err error) {
	list = []Region{}
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: r,
		Filters:         *filter,
	}, &list).GetError()
	return
}
