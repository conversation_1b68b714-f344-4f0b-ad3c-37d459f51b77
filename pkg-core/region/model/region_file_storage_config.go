package model

import (
	"encoding/json"
	"gorm.io/datatypes"
	"server/pkg/constant"
	"time"

	"gorm.io/gorm"
)

const TableNameFileStorageConfig = "core_region_file_storage_config"

type FileStorageConfig struct {
	ID        int            `gorm:"column:id;AUTO_INCREMENT;PRIMARY KEY;" json:"id"`
	CreatedAt time.Time      `gorm:"column:created_at;type:datetime;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"column:updated_at;type:datetime;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;type:datetime;index:idx_deleted_at;" json:"deleted_at"`

	Sign            constant.RegionSignType `gorm:"column:sign;type:varchar(255);index:idx_sign;" json:"sign"`
	FsConfigVersion string                  `gorm:"column:fs_config_version;type:varchar(255);not null;index:idx_sign;" json:"fs_config_version"`

	AutoFSPubAddr   string           `gorm:"column:autofs_pub_addr;type:varchar(255);" json:"autofs_pub_addr"`
	AutoFSPubPort   int              `gorm:"column:autofs_pub_port;type:int;" json:"autofs_pub_port"`
	AutoFSFilerAddr string           `gorm:"column:autofs_filer_addr;type:varchar(255);" json:"autofs_filer_addr"`
	AutoFSImageName string           `gorm:"column:autofs_image_name;type:varchar(255);" json:"autofs_image_name"`
	AdditionalJson  datatypes.JSON   `gorm:"column:additional;type:json" json:"-"`
	Additional      RegionAdditional `gorm:"-" json:"additional"`
}

func (c *FileStorageConfig) TableName() string {
	return TableNameFileStorageConfig
}

func (c *FileStorageConfig) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&FileStorageConfig{})
}

func (c *FileStorageConfig) BeforeCreate(db *gorm.DB) error {
	c.AdditionalJson, _ = json.Marshal(c.Additional)
	return nil
}

func (c *FileStorageConfig) AfterFind(db *gorm.DB) error {
	if len(c.AdditionalJson) != 0 {
		_ = json.Unmarshal(c.AdditionalJson, &c.Additional)
	}
	return nil
}
