package model

import (
	"server/pkg/constant"
	"server/pkg/db_helper"
)

type InitUserNetDiskParams struct {
	Sign constant.RegionSignType `json:"sign"`
}

type CreateRegionParams struct {
	Sign constant.RegionSignType `form:"sign" json:"sign"` // 地区标识
	Name string                  `form:"name" json:"name"` // 地区名称

	PortRangeMin int    `form:"port_range_min" json:"port_range_min"` // 端口分配范围 min (包含)
	PortRangeMax int    `form:"port_range_max" json:"port_range_max"` // 端口分配范围 max (包含)
	ExportAddr1  string `form:"export_addr_1" json:"export_addr_1"`   // 出口地址
	ExportAddr2  string `form:"export_addr_2" json:"export_addr_2"`   // 出口地址

	NFSPort                int   `form:"nfs_port" json:"nfs_port"`
	IsNFSAvailable         bool  `form:"is_nfs_available" json:"is_nfs_available"`
	DefaultUserQuotaInByte int64 `form:"default_user_quota_in_byte" json:"default_user_quota_in_byte"` // 用户配额
}

func (r *CreateRegionParams) Check() bool {
	if len(r.Name) == 0 || r.PortRangeMin == 0 || r.PortRangeMax == 0 ||
		len(r.ExportAddr1) == 0 || r.ExportAddr1 == r.ExportAddr2 {
		return false
	}

	// 允许 [35000,35000] 只给一个的极端情况, 但不允许 [35010,35009]
	if r.PortRangeMin > r.PortRangeMax {
		return false
	}

	if r.IsNFSAvailable {
		// nfs_addr 不校验, 后端自动填写
		if r.NFSPort == 0 || r.DefaultUserQuotaInByte == 0 {
			return false
		}
	}

	return true
}

type NetDiskSetQuotaParams struct {
	Sign  constant.RegionSignType `json:"sign"`
	UID   int                     `json:"uid"`
	Quota int64                   `json:"quota"` // 单位 GB
}

type UserNetDiskQuotaParams struct {
	Phone string `form:"phone" json:"phone"`
	db_helper.GetPagedRangeRequest
}
