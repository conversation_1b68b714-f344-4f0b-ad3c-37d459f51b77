package model

import (
	"server/pkg/constant"
	"time"
)

type AddRegionParams struct {
}

type RegionFilter struct {
}

type GetRegionListForIndexRes struct {
	RegionSign constant.RegionSignType `json:"region_sign"`
	RegionName string                  `json:"region_name"`
	DataCenter string                  `json:"data_center"`
}

type UserNetDiskQuotaForAdminInfo struct {
	UID           int                     `json:"uid"`
	RegionSign    constant.RegionSignType `json:"region_sign"`
	UsedQuota     int64                   `json:"used_quota"`
	TotalQuota    int64                   `json:"total_quota"`
	UsedQuotaRate int                     `json:"used_quota_rate"`

	UserPhone       string    `json:"user_phone"`
	ExpandExpiredAt time.Time `json:"expand_expired_at"`
	IsExpanded      bool      `json:"is_expanded"`
}

type GetUserNetDiskMountForInstanceRes struct {
	Path    string `json:"path"`
	Exist   bool   `json:"exist"`
	QuotaOK bool   `json:"quota_ok"`
}

type FsForChargeResult struct {
	UID       int    `json:"uid"`
	OrderUUID string `json:"order_uuid"`
}
