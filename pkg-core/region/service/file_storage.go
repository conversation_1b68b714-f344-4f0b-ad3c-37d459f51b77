package service

import (
	"errors"
	"fmt"
	"server/pkg-agent/messenger"
	"server/pkg-core/api/coreapi"
	message_model "server/pkg-core/message/model"
	"server/pkg-core/region/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/libs"
	"server/plugin/queue_interface"
	redis "server/plugin/redis_plugin"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

// 文件存储相关

func (svc *RegionService) InitFileStorageWithRegionForUser(uid int, rs constant.RegionSignType, tenant string) (ru *model.FileStorage, err error) {
	ru = &model.FileStorage{}
	err = ru.FileStorageGet(&db_helper.QueryFilters{EqualFilters: map[string]interface{}{"sign": rs, "uid": uid}})
	if err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			err = nil
		} else {
			svc.log.WithError(err).WithField("sign", rs).WithField("uid", uid).Error("get file stroage error")
			err = businesserror.ErrDatabaseError
			return
		}
	} else {
		if ru.Status != constant.FileStorageCreateFailed {
			return
		}
	}

	// 没创建过或者创建失败
	region, err := svc.GetRegionDetail(rs)
	if err != nil {
		return
	}

	fsType := region.FsType()
	if fsType == "" {
		err = businesserror.ErrRegionFSNotAvailable
		return
	}

	var (
		autofsPrefix = region.Additional.AutoFsPrefix
	)

	// region 表的 fs_config_version 是手动插入的，所以这里处理下，防止有多余的空字符，后面不用处理了。
	region.FsConfigVersion = strings.TrimSpace(region.FsConfigVersion)

	// 初始化的时候一定要确认文件存储配置是否存在。
	if region.FsConfigVersion != "" {
		fsConfig, ok, err := svc.GetFsConfig(region.Sign, region.FsConfigVersion)
		if err != nil {
			svc.log.WithError(err).WithField("sign", region.Sign).WithField("version", region.FsConfigVersion).Error("GetFsConfig failed")
			return nil, err
		}
		if !ok {
			err = businesserror.ErrRegionFileStorageConfigNotFound
			svc.log.WithError(err).WithField("sign", region.Sign).WithField("version", region.FsConfigVersion).Error("GetFsConfig failed")
			return nil, err
		}

		autofsPrefix = fsConfig.Additional.AutoFsPrefix
	}

	fs := &model.FileStorage{
		UID:             uid,
		RegionSign:      rs,
		Status:          constant.FileStorageCreating,
		FsType:          fsType,
		Tenant:          tenant,
		QuotaTotal:      constant.ADFSDefaultSize,
		QuotaInodeTotal: constant.ADFSDefaultInode,
	}
	if fsType == constant.AutoFS {
		fs.ConcurrentLimit = constant.AutoFsDefaultRateLimit
		fs.MaxUploads = 2
		fs.CacheSize = constant.AutoFsCacheSize
		fs.BufferSize = constant.AutoFsBufferSize
		fs.AutoFsPrefix = autofsPrefix
		fs.FsConfigVersion = region.FsConfigVersion
	}

	err = db_helper.InsertOrUpdateOne(db_helper.QueryDefinition{
		ModelDefinition: &model.FileStorage{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"sign": rs, "uid": uid}},
		InsertPayload:   fs,
	}, ru).GetError()
	if err != nil {
		svc.log.WithError(err).WithField("sign", rs).WithField("uid", uid).Error("get file storage error")
		return nil, businesserror.ErrDatabaseError
	}
	fs.Region = region

	param := constant.FSInit{
		UID:        uid,
		RegionSign: rs,
	}

	var payload queue_interface.ElementPayloadContent
	payload = &queue_interface.NewQueueForADFSStorageAgentOnMachine{
		RegionSign: rs,
		Message: messenger.Message{
			MsgID:   time.Now().Format(time.RFC3339Nano),
			Type:    messenger.FileStorageInitType,
			Payload: param.String(),
		},
	}
	if fsType == constant.AutoFS {
		autoFsParams := constant.AutoFsInitParams{
			UID:             uid,
			Prefix:          autofsPrefix,
			RegionSign:      rs,
			Capacity:        constant.AutoFsDefaultSizeInGB,
			Inodes:          constant.AutoFsDefaultInode,
			ConcurrentLimit: constant.AutoFsDefaultRateLimit,
		}
		payload = &queue_interface.NewQueueForAutoFsStorageAgentWithRegion{
			RegionSign:      rs,
			FsConfigVersion: region.FsConfigVersion,
			Message: messenger.Message{
				MsgID:   time.Now().Format(time.RFC3339Nano),
				Type:    messenger.AutoFsInitType,
				Payload: autoFsParams.String(),
			},
		}
	}

	err = message_model.SimplePubMessage(svc.log, svc.queue, payload)
	if err != nil {
		svc.log.WithError(err).Error("Pub message failed.")
		return nil, businesserror.ErrInternalError
	}

	return fs, nil
}

func (svc *RegionService) InitFileStorageFinal(params *constant.FSInitResult) {
	l := svc.log.WithField("params", params)
	lock := svc.mutex.NewRedisMutex(redis.MutexInitFileStorageFinal, "init_"+strconv.Itoa(params.UID))
	locked, err := lock.LockWithNoRetry(constant.LockTimeout)
	if err != nil || !locked {
		svc.log.WithError(err).WithField("is locked", locked).WithField("uid", params.UID).Warn("InitFileStorageFinal get redis lock failed.")
		err = businesserror.ErrServerBusy
		return
	}
	defer lock.UnLock()

	fs := model.FileStorage{}
	err = fs.FileStorageGet(&db_helper.QueryFilters{EqualFilters: map[string]interface{}{"sign": params.RegionSign, "uid": params.UID}})
	if err != nil {
		l.ErrorE(err, "update file storage error")
		return
	}
	if fs.Status == constant.FileStorageCreated {
		return
	}

	orderUUID := libs.RandNumberString()
	update := make(map[string]interface{})

	if !params.IsSucceed {
		update = map[string]interface{}{"status": constant.FileStorageCreateFailed}
	} else {
		update = map[string]interface{}{"status": constant.FileStorageCreated, "order_uuid": orderUUID}
	}

	err = fs.FileStorageUpdate(nil, &db_helper.QueryFilters{EqualFilters: map[string]interface{}{"sign": params.RegionSign, "uid": params.UID}}, update)
	if err != nil {
		l.ErrorE(err, "update file storage error")
		return
	}

	if !params.IsSucceed {
		return
	}

	payload := constant.MQCoreBusinessInitFileStoragePayload{
		RS:        params.RegionSign,
		UID:       params.UID,
		IsSuccess: params.IsSucceed,
		ErrInfo:   params.Msg,
		OrderUUID: orderUUID,
		FsType:    fs.FsType,
	}

	// 成功了的话，再给业务层发送消息生成订单
	err = message_model.SimplePubMessage(svc.log, svc.queue, &queue_interface.NewQueueForCoreToBusinessModifyData{
		Tenant: fs.Tenant,
		Req: constant.MQCoreToBusinessModifyDataReq{
			Tenant:  fs.Tenant,
			OptType: constant.MQCoreToBusinessInitFileStorageOpt,
			Payload: payload.String(),
			Caller:  constant.OptCallerInitFileStorage,
		},
	})
	if err != nil {
		l.ErrorE(err, "pub mq error")
		return
	}
}

func (svc *RegionService) GetFileStorageListForUser(uid int) (res []model.RegionFileStorageInfo, err error) {
	var (
		regions   []model.Region
		fsList    []model.FileStorage
		statusMap = make(map[constant.RegionSignType]*model.FileStorage)

		signs            []constant.RegionSignType
		fsConfigVersions []string
		fsConfigMap      map[constant.RegionSignType]map[string]model.FileStorageConfig
	)

	// region 列表
	regions, err = svc.GetRegionList()
	if err != nil {
		return
	}
	// 各地区网盘初始化情况
	fsList, err = svc.getUserFileStorageInfo(uid)
	if err != nil {
		return
	}

	for k, v := range fsList {
		statusMap[v.RegionSign] = &fsList[k]

		if v.FsConfigVersion != "" {
			signs = append(signs, v.RegionSign)
			fsConfigVersions = append(fsConfigVersions, v.FsConfigVersion)
		}
	}

	if len(fsConfigVersions) != 0 {
		fsConfigMap, err = svc.GetFsConfigs(signs, fsConfigVersions)
		if err != nil {
			svc.log.WithField("signs", signs).WithField("fsConfigVersions", fsConfigVersions).WithError(err).Error("get file storage config error")
			return nil, err
		}
	}

	for _, v := range regions {
		if v.FsType() == "" || !v.ADFSVisibleForFrontend {
			continue
		}

		// 整理初始化状态
		var initializedAt *time.Time
		var fsType = v.FsType()
		var status constant.FileStorageStatusType
		if s, ok := statusMap[v.Sign]; ok {
			status = s.Status
			initializedAt = &s.CreatedAt
			fsType = s.FsType
		} else {
			status = constant.FileStorageUninitialized
		}

		pubAddr := v.ADFSPubAddr
		pubPort := v.ADFSPubPort

		// AutoFS 比较特殊。如果是 AutoFS，则有可能支持多个配置版本。
		if fsType == constant.AutoFS {
			pubAddr = v.AutoFSPubAddr
			pubPort = v.AutoFSPubPort

			if fs, ok := statusMap[v.Sign]; ok {
				if config, ok := fsConfigMap[fs.RegionSign][fs.FsConfigVersion]; ok {
					pubAddr = config.AutoFSPubAddr
					pubPort = config.AutoFSPubPort
				}
			}
		}

		res = append(res, model.RegionFileStorageInfo{
			Sign:          v.Sign,
			Name:          v.Name,
			ExportAddr1:   pubAddr,
			ExportAddr2:   v.ExportAddr2,
			Port:          pubPort,
			Status:        status,
			FsType:        fsType,
			FreeSize:      constant.ADFSFreeSize,
			MaxSize:       constant.ADFSMaxSize,
			DefaultSize:   constant.ADFSDefaultSize,
			InitializedAt: initializedAt,
		})
	}

	return
}

func (svc *RegionService) getUserFileStorageInfo(uid int) (fileStorage []model.FileStorage, err error) {
	// 不超过 100 个
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.FileStorage{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uid": uid}},
	}, &fileStorage).GetError()
	if err != nil {
		svc.log.WithError(err).Error("get fileStorage failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *RegionService) GetFileStorageForCharge(params *coreapi.GetFileStorageForChargeReq) (data *coreapi.GetFileStorageForChargeData, err error) {
	if params.Tenant == "" || params.RegionSign == "" {
		return
	}

	region, err := svc.GetRegionDetail(params.RegionSign)
	if err != nil {
		return
	}
	switch params.FsType {
	case constant.ADFS:
		if !region.IsADFSAvailable {
			return
		}
	case constant.AutoFS:
		if !region.IsAutoFsAvailable {
			return
		}
	default:
		return
	}

	data = &coreapi.GetFileStorageForChargeData{
		List:        make([]model.FsForChargeResult, 0),
		FsType:      params.FsType,
		MaxUsageMap: nil,
	}

	// todo: 后续可以考虑在FileStorage里记载多一点信息,比如记录前一天的最高使用量
	//  这样可以不用去redis中查询,同时可以根据这个字段筛选, 减少数据传输

	fs := &model.FileStorage{}
	err = fs.FileStorageGetAllWithSelect(&db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"tenant":  params.Tenant,
			"sign":    params.RegionSign,
			"fs_type": params.FsType,
			"status":  "created",
		}}, "uid,order_uuid", &data.List)
	if err != nil {
		err = businesserror.ErrDatabaseError
		return
	}

	if params.FsType == constant.AutoFS {
		data.MaxUsageMap, err = svc.storagePlugin.AutoFsRegionDailyMaxUsageHGetAll(params.RegionSign, &params.SettleDate)
		if err != nil {
			svc.log.WithField("sign", params.RegionSign).ErrorE(err, "AutoFsRegionDailyMaxUsageHGetAll failed")
			err = businesserror.ErrDatabaseError
			return
		}
	}

	return
}

func (svc *RegionService) GetFileStorageAdminList(req coreapi.GetFileStorageAdminListReq) (coreapi.GetFileStorageAdminListData, error) {
	if req.Tenant == "" {
		return coreapi.GetFileStorageAdminListData{}, nil
	}

	var (
		err   error
		count int64
		fs    = make([]model.FileStorage, 0)
	)

	db := db_helper.GlobalDBConn().
		Table(model.TableNameFileStorage).
		Where("tenant = ?", req.Tenant).
		Where("status = ?", constant.FileStorageCreated)

	if req.RegionSignType != "" {
		db = db.Where("sign = ?", req.RegionSignType)
	}
	if len(req.UIDs) != 0 {
		db = db.Where("uid in (?)", req.UIDs)
	}
	if req.FsType != "" {
		db = db.Where("fs_type = ?", req.FsType)
	}
	if req.MinQuotaUsage != 0 {
		db = db.Where("quota_usage >= ?", req.MinQuotaUsage)
	}

	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Error("count file storage failed")
		return coreapi.GetFileStorageAdminListData{}, businesserror.ErrDatabaseError
	}

	paged := db_helper.BuildPagedDataUtil(req.PageIndex, req.PageSize, int(count), 0)

	if len(req.Sorts) != 0 {
		for field, order := range req.Sorts.ParseSorts([]string{"quota_usage", "quota_usage_rate"}) {
			db = db.Order(field + " " + order)
		}
	} else {
		db = db.Order("id desc")
	}

	err = db.Offset(paged.Offset).Limit(paged.PageSize).Find(&fs).Error
	if err != nil {
		svc.log.WithError(err).Error("find file storage failed")
		return coreapi.GetFileStorageAdminListData{}, businesserror.ErrDatabaseError
	}

	res := coreapi.GetFileStorageAdminListData{
		Total:        int(count),
		FileStorages: fs,
	}

	return res, nil
}

func (svc *RegionService) GetFileStorageByUids(uids []int, rs constant.RegionSignType) (fs []model.FileStorage, err error) {
	// fix bug: 同一个数据中心的两个地区A和B，用户只初始化了A，但是在B地区可以使用文件存储

	// 获取地区信息
	region, err := svc.GetRegionDetail(rs)
	if err != nil {
		return nil, err
	}

	// 根据数据中心获取所有关联的地区
	regions := make([]model.Region, 0)
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.Region{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{"data_center": region.DataCenter},
		},
	}, &regions).GetError()
	if err != nil {
		err = businesserror.ErrDatabaseError
		return
	}

	// 保存地区sign
	signs := make([]constant.RegionSignType, 0)
	for _, r := range regions {
		signs = append(signs, r.Sign)
	}

	// 查询所有的用户和地区sign的文件存储信息，如果某个用户一条文件存储记录都没有那说明这个用户没有初始化过任何一个同数据中心的地区文件存储
	fs = make([]model.FileStorage, 0, len(uids))
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.FileStorage{},
		Filters: db_helper.QueryFilters{
			InFilters: []db_helper.In{
				{
					Key:   "uid",
					InSet: uids,
				},
				{
					Key:   "sign",
					InSet: signs,
				},
			},
		},
	}, &fs).GetError()
	if err != nil {
		err = businesserror.ErrDatabaseError
		return
	}

	// 每个用户可能会查出多条记录（一般不会），选最大的
	box := make(map[int]model.FileStorage)
	for _, s := range fs {
		if ss, ok := box[s.UID]; !ok {
			box[s.UID] = s
		} else {
			if s.QuotaTotal > ss.QuotaTotal {
				box[s.UID] = s
			}
		}
	}

	result := make([]model.FileStorage, 0)
	for _, b := range box {
		result = append(result, b)
	}

	return result, nil
}

// UpdateFileStorageSetting 设置文件存储的 quota 值
// NOTE：设置 quota 值要设置某个地区的文件存储的 Redis 集群中的 quota 值，
// 要实现这一逻辑需要走消息队列再通过 WebSocket 发送给 storage-agent，
// 在 storage-agent 的 handler 中设置 Redis 的值。
// 这里先更新 DB，再发送消息，即使 storage-agent 的 handler 处理设置 quota 的逻辑失败了也会因为存在同步 quota 和使用信息的逻辑，
// 最终 DB 中的值也会和 Redis 中的值相同，最后都已 Redis 中的值为准。
func (svc *RegionService) UpdateFileStorageSetting(req coreapi.UpdateFileStorageSettingReq) error {
	if req.UID == 0 || req.RegionSignType == "" {
		return nil
	}

	var (
		err error
		fs  model.FileStorage
		l   = svc.log.WithField("uid", req.UID).WithField("region", req.RegionSignType)
	)

	err = fs.FileStorageGet(&db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uid": req.UID, "sign": req.RegionSignType}})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return businesserror.ErrRecordNotFoundError
		}
		l.WithError(err).Error("get fs failed")
		return businesserror.ErrDatabaseError
	}

	// 只有created状态的记录才会在后台展示并更新，如果不是created状态的话直接返回
	if fs.Status != constant.FileStorageCreated {
		return nil
	}

	// autoFs GB
	if req.Settings.QuotaTotal < constant.AutoFsDefaultSizeInGB {
		req.Settings.QuotaTotal = constant.AutoFsDefaultSizeInGB
	}
	if req.Settings.QuotaTotal > constant.AutoFsMaxSizeInGB {
		req.Settings.QuotaTotal = constant.AutoFsMaxSizeInGB
	}
	quotaTotalInByte := req.Settings.QuotaTotal * 1024 * 1024 * 1024

	if req.Settings.QuotaInodeTotal < constant.ADFSDefaultInode {
		req.Settings.QuotaInodeTotal = constant.ADFSDefaultInode
	}
	if req.Settings.QuotaInodeTotal > constant.ADFSMaxInode {
		req.Settings.QuotaInodeTotal = constant.ADFSMaxInode
	}

	// 更新quota后，rate需要重新计算
	rate, _ := strconv.ParseFloat(fmt.Sprintf("%0.2f", float64(fs.QuotaUsage)/float64(quotaTotalInByte)), 64)
	update := map[string]interface{}{
		"quota_total":       quotaTotalInByte,
		"quota_inode_total": req.Settings.QuotaInodeTotal,
		"quota_usage_rate":  int(rate * 100),
	}

	if req.Settings.ConcurrentLimit != 0 {
		update["concurrent_limit"] = req.Settings.ConcurrentLimit
	}
	if req.Settings.MaxUploads != 0 {
		update["max_uploads"] = req.Settings.MaxUploads
	}
	if req.Settings.CacheSize != 0 {
		update["cache_size"] = req.Settings.CacheSize
	}
	if req.Settings.BufferSize != 0 {
		update["buffer_size"] = req.Settings.BufferSize
	}

	err = fs.FileStorageUpdate(nil, &db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": fs.ID}}, update)
	if err != nil {
		l.WithError(err).Error("update file storage failed")
		return businesserror.ErrDatabaseError
	}

	svc.log.Info("updateFileStorageQuotaSetting: uid %d, region %s, update successfully %+v", req.UID, req.RegionSignType, update)

	var pubPayload queue_interface.ElementPayloadContent
	if fs.FsType == constant.ADFS {
		payload := constant.ADFSSetQuota{
			UID:        req.UID,
			RegionSign: req.RegionSignType,
			QuotaSize:  int64(quotaTotalInByte),
			QuotaInode: int64(req.Settings.QuotaInodeTotal),
		}
		pubPayload = &queue_interface.NewQueueForADFSStorageAgentOnMachine{
			RegionSign: req.RegionSignType,
			Message: messenger.Message{
				MsgID:   time.Now().Format(time.RFC3339Nano),
				Type:    messenger.FileStorageSetQuotaType,
				Payload: payload.Marshal(),
			},
		}
	} else {
		payload := constant.AutoFsInitParams{
			UID:             req.UID,
			Prefix:          fs.AutoFsPrefix,
			RegionSign:      req.RegionSignType,
			Capacity:        req.Settings.QuotaTotal,
			Inodes:          req.Settings.QuotaInodeTotal,
			ConcurrentLimit: req.Settings.ConcurrentLimit,
		}
		pubPayload = &queue_interface.NewQueueForAutoFsStorageAgentWithRegion{
			RegionSign:      req.RegionSignType,
			FsConfigVersion: fs.FsConfigVersion,
			Message: messenger.Message{
				MsgID:   time.Now().Format(time.RFC3339Nano),
				Type:    messenger.AutoFsSetQuotaType,
				Payload: payload.String(),
			},
		}

	}

	err = message_model.SimplePubMessage(svc.log, svc.queue, pubPayload)
	if err != nil {
		l.WithError(err).Error("Pub message failed.")
		return businesserror.ErrInternalError
	}

	svc.log.Info("updateFileStorageQuotaSetting: uid %d, region %s, pub message successfully %+v", req.UID, req.RegionSignType, pubPayload)

	return nil
}

func (svc *RegionService) adfsUpdateUserFileStorageUsageInfo(data constant.ADFSSyncUsageData, regionSign constant.RegionSignType) (err error) {
	if data.UID < 1 {
		return nil
	}

	rate := libs.GetRate(float64(data.CurrentUsage), float64(data.QuotaSize))

	fs := &model.FileStorage{}
	err = fs.FileStorageUpdate(nil, &db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"uid":     data.UID,
			"sign":    regionSign,
			"fs_type": constant.ADFS,
		}}, map[string]interface{}{
		"quota_usage":       data.CurrentUsage,
		"quota_max_usage":   data.MaxUsage,
		"quota_usage_rate":  int(rate * 100),
		"quota_inode_usage": data.Inode,
	})
	if err != nil {
		svc.log.WithError(err).Error("update file storage failed")
		return businesserror.ErrDatabaseError
	}

	return nil
}

func (svc *RegionService) AutoFsSyncUsageInfo(req *constant.AutoFsSyncUsage) (err error) {
	t := time.Now()
	oldUsageMap, err := svc.storagePlugin.AutoFsRegionDailyMaxUsageHGetAll(req.RegionSign, &t)
	if err != nil {
		svc.log.WithField("sign", req.RegionSign).ErrorE(err, "AutoFsRegionDailyMaxUsageHGetAll failed")
		return
	}

	newMaxUsageMap := map[string]interface{}{}

	fs := &model.FileStorage{}
	for uid, usageInfo := range req.UsageInfo {
		uidStr := strconv.Itoa(uid)
		var maxUsage int64 = 0

		// redis
		if u, ok := oldUsageMap[uidStr]; ok {
			maxUsage, _ = strconv.ParseInt(u, 10, 64)
		}
		if usageInfo.Size > maxUsage {
			maxUsage = usageInfo.Size
			newMaxUsageMap[uidStr] = maxUsage
		}

		// mysql
		err = fs.FileStorageUpdate(nil, &db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uid":     uid,
				"sign":    req.RegionSign,
				"fs_type": constant.AutoFS,
			}},
			map[string]interface{}{
				"quota_usage":       usageInfo.Size,
				"quota_max_usage":   maxUsage,
				"quota_usage_rate":  gorm.Expr("?/quota_total", usageInfo.Size*100),
				"quota_inode_usage": usageInfo.Inode,
			})
		if err != nil {
			svc.log.WithField("uid", uid).ErrorE(err, "update file storage autofs failed")
			continue
		}
	}

	if len(newMaxUsageMap) == 0 {
		return
	}

	// ToDo: 批量更新, 如果此处更新redis出现阻塞, 记得分批更新
	err = svc.storagePlugin.AutoFsRegionDailyMaxUsageSet(req.RegionSign, newMaxUsageMap)
	if err != nil {
		svc.log.ErrorE(err, "AutoFsRegionDailyMaxUsageSet failed.region: %+v, um: %+v", req.RegionSign, newMaxUsageMap)
		return
	}

	return nil
}

func (svc *RegionService) FsGetDetail(params *coreapi.FsGetDetailReq) (fs *model.FileStorage, err error) {
	fs = &model.FileStorage{}
	err = fs.FileStorageGet(&db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uid": params.UID, "sign": params.RegionSign}})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = businesserror.ErrRecordNotFoundError
			return nil, err
		}
		svc.log.WithField("params", params).ErrorE(err, "get fs failed")
		return nil, err
	}

	return
}

func (svc *RegionService) AdfsMkdir(uid int, rs constant.RegionSignType, subPath string) error {
	fs, err := svc.GetFileStorageByUids([]int{uid}, rs)
	if err != nil {
		svc.log.ErrorE(err, "get fs failed, uid:%d, rs:%s", uid, rs)
		return err
	}

	if len(fs) != 0 && fs[0].Status == constant.FileStorageCreated {
		err = message_model.SimplePubMessage(svc.log, svc.queue, &queue_interface.NewQueueForADFSStorageAgentOnMachine{
			RegionSign: fs[0].RegionSign,
			Message: messenger.Message{
				MsgID: time.Now().Format(time.RFC3339Nano),
				Type:  messenger.AdfsMkdirType,
				Payload: constant.ADFSMkdir{
					UID:        uid,
					RegionSign: fs[0].RegionSign,
					DirPath:    subPath,
				}.String(),
			},
		})
		if err != nil {
			svc.log.ErrorE(err, "AdfsMkdir Pub message failed. uid:%d, rs:%s, subPath:%s.  correct rs is:%s", uid, rs, subPath, fs[0].RegionSign)
			return businesserror.ErrInternalError
		}

		svc.log.Info("AdfsMkdir pub cmt to storage agent; get param, uid:%d, rs：%s,subPath:%s. correct rs is:%s", uid, rs, subPath, fs[0].RegionSign)
	}

	return nil
}

func (svc *RegionService) GetFsConfigs(signs []constant.RegionSignType, versions []string) (map[constant.RegionSignType]map[string]model.FileStorageConfig, error) {
	if len(versions) == 0 {
		return nil, nil
	}

	var configs []model.FileStorageConfig

	err := db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.FileStorageConfig{},
		Filters: db_helper.QueryFilters{
			InFilters: []db_helper.In{
				{
					Key:   "sign",
					InSet: signs,
				},
				{
					Key:   "fs_config_version",
					InSet: versions,
				},
			},
		},
	}, &configs).GetError()
	if err != nil {
		svc.log.WithError(err).Error("db get failed")
		err = businesserror.ErrDatabaseError
		return nil, err
	}

	fsConfigMap := make(map[constant.RegionSignType]map[string]model.FileStorageConfig)

	for _, config := range configs {
		if _, ok := fsConfigMap[config.Sign]; !ok {
			fsConfigMap[config.Sign] = make(map[string]model.FileStorageConfig)
		}
		fsConfigMap[config.Sign][config.FsConfigVersion] = config
	}

	return fsConfigMap, nil
}

func (svc *RegionService) GetFsConfig(sign constant.RegionSignType, version string) (config model.FileStorageConfig, ok bool, err error) {
	err = db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: &model.FileStorageConfig{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"sign":              sign,
				"fs_config_version": version,
			},
		},
	}, &config).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
			return
		}
		svc.log.WithError(err).Error("db get failed")
		err = businesserror.ErrDatabaseError
		return
	}
	ok = true
	return
}
