package service

import (
	"context"
	"encoding/json"
	"fmt"
	"server/pkg-core/region/model"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/libs"
	"strconv"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"

	"gorm.io/gorm"
)

func (svc *RegionService) CronJobRegister(ctx context.Context) {
	go svc.CronSyncNetDiskUsage(ctx)
	go svc.CronSyncFileStorageUsage(ctx)
}

func (svc *RegionService) CronSyncNetDiskUsage(ctx context.Context) {
	svc.cronSyncNetDiskUsage()

	ticker := time.NewTicker(time.Minute)

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			svc.cronSyncNetDiskUsage()
		}
	}
}

func (svc *RegionService) cronSyncNetDiskUsage() {
	log := svc.log.WithField("method", "cronSyncNetDiskUsage")

	var sync = func(netDisks []model.NetDisk) error {

		for _, netDisk := range netDisks {
			l := log.WithField("uid", netDisk.UID).WithField("region", netDisk.RegionSign)

			usage, err := svc.storagePlugin.UsageGet(netDisk.UID, netDisk.RegionSign)
			if err != nil && err != redis.Nil {
				return err
			}
			if usage == "" {
				continue
			}

			// 解析使用量
			segs := strings.Split(usage, "/")
			var quotaInfo []int64
			for i := 0; i < len(segs); i++ {
				quota, err := strconv.ParseInt(segs[i], 10, 64)
				if err != nil {
					return err
				}
				quotaInfo = append(quotaInfo, quota)
			}
			if len(quotaInfo) < 2 {
				continue
			}

			rate := libs.GetRate(float64(quotaInfo[0]), float64(quotaInfo[1]))
			update := map[string]interface{}{
				"quota_usage":      quotaInfo[0], // 上面已经校验数组长度了，这里不会 panic
				"quota_total":      quotaInfo[1], // 上面已经校验数组长度了，这里不会 panic
				"quota_usage_rate": int(rate * 100),
			}
			err = db_helper.GlobalDBConn().Model(&model.NetDisk{}).Where("id = ?", netDisk.ID).Updates(update).Error
			if err != nil {
				l.ErrorE(err, "update net_disk failed")
				return err
			}
		}

		return nil
	}

	var (
		err      error
		netDisks []model.NetDisk
	)

	db := db_helper.GlobalDBConn().Where("tenant = ?", constant.TenantAutoDL).Where("status = ?", constant.NetDiskNFSCreated)

	err = db.FindInBatches(&netDisks, 100, func(tx *gorm.DB, batch int) error {
		return sync(netDisks)
	}).Error
	if err != nil {
		svc.log.WithError(err).Error("find netDisks and sync failed")
		return
	}

}

// CronSyncFileStorageUsage adfs 定时同步用量, adfs没有使用storage-agent上报的方式,而是自己用redis作为媒介
func (svc *RegionService) CronSyncFileStorageUsage(ctx context.Context) {
	svc.cronSyncFileStorageUsage()

	ticker := time.NewTicker(time.Minute * 5)

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			svc.cronSyncFileStorageUsage()
		}
	}
}

func (svc *RegionService) cronSyncFileStorageUsage() {
	log := svc.log.WithField("method", "cronSyncFileStorageUsage")

	regions, err := svc.GetRegionList()
	if err != nil {
		log.ErrorE(err, "get region list failed.")
		return
	}

	for _, region := range regions {
		log = log.WithField("region", region.Sign)
		key := fmt.Sprintf(constant.ADFSKVAllUserUsageInfoKey, region.Sign)
		log.Info("key:%+v", key)
		val, err := svc.kv.Get(key)
		if err != nil {
			log.ErrorE(err, "kv get failed.")
			continue
		}
		if val == "" {
			continue
		}
		kvArr := make([]constant.ADFSSyncUsageData, 0)
		err = json.Unmarshal([]byte(val), &kvArr)
		if err != nil {
			log.ErrorE(err, "unmarshal %+v failed.", val)
			continue
		}

		// 处理每个用户
		for _, kv := range kvArr {
			err = svc.adfsUpdateUserFileStorageUsageInfo(kv, region.Sign)
			if err != nil {
				log.ErrorE(err, "adfsUpdateUserFileStorageUsageInfo failed.")
				continue
			}
		}

		// fix 这里处理完一个地区的数据后要立即清理kv的key。
		// 因为当前定时任务和storage-agent上报数据可能会有时间间隔，
		// 如果后台设置文件存储的quota值到storage-agent上报数据之间当前所在定时任务执行的话，那会更新成之前的值，等下一个5分钟后才会变为修改后的值
		err = svc.kv.Del(key)
		if err != nil {
			log.ErrorE(err, "del kv redis key [%s] failed", key)
			continue
		}
	}
}
