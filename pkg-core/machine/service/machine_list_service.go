package service

import (
	"server/pkg-agent/agent_constant"
	"server/pkg-core/machine/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"
)

func (svc *McService) AdminMachineList(params *model.GetAdminMachineListParams, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []*model.Machine, err error) {
	list = make([]*model.Machine, 0)
	var gpuTypeIDs []int
	if len(params.GpuTypeName) > 0 {
		gpuTypeIDs, err = svc.gpuType.GetGpuID(params.GpuTypeName)
		if err != nil {
			svc.log.WithField("err", err).WithField("gpu_type name:", params.GpuTypeName).Error("get gpu_type name failed")
			err = businesserror.ErrDatabaseError
			return
		}
		if len(gpuTypeIDs) < 1 {
			paged = db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, 0, 0)
			return
		}
	}
	db := db_helper.GlobalDBConn().Table(model.TableNameMachine).Where("deleted_at is null").Order("id desc")

	if len(params.MachineName) > 0 {
		name := "%" + params.MachineName + "%"
		db = db.Where("machine_name like ?", name)
	}
	if len(params.MachineID) > 0 {
		db = db.Where("machine_id = ?", params.MachineID)
	}
	if len(gpuTypeIDs) > 0 {
		db = db.Where("gpu_type_id in (?)", gpuTypeIDs)
	}
	if len(params.RegionSign) > 0 {
		db = db.Where("region_sign in (?)", params.RegionSign)
	}
	if params.OnlineStatus != nil {
		db = db.Where("on_line = ?", *params.OnlineStatus)
	}
	if pageReq.DateFrom != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') >= ?", pageReq.DateFrom)
	}
	if pageReq.DateTo != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') <= ?", pageReq.DateTo)
	}
	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Warn("Count failed.")
		return
	}
	paged = db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, int(count), 0)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Find(&list).Error
	if err != nil {
		svc.log.WithField("err", err).Error("get machine failed")
		err = businesserror.ErrDatabaseError
		return
	}
	paged = svc.processListData(true, paged, list)
	return paged, list, nil
}

type MachineInfoOrderList []*model.MachineInfo

func (m MachineInfoOrderList) Len() int           { return len(m) }
func (m MachineInfoOrderList) Swap(i, j int)      { m[i], m[j] = m[j], m[i] }
func (m MachineInfoOrderList) Less(i, j int) bool { return m[i].PaygPrice < m[j].PaygPrice }

func (svc *McService) processListData(isAdmin bool, oldPaged *db_helper.PagedData, oldList []*model.Machine) (paged *db_helper.PagedData) {
	machineInfoList := make([]*model.MachineInfo, 0)
	var gpuTypeIDs []int
	for _, m := range oldList {
		if m.GPUTypeID > 0 {
			gpuTypeIDs = append(gpuTypeIDs, m.GPUTypeID)
		}
	}
	machineAllStatus, err := svc.machineRedis.ConnectionInfoHGetAll()
	if err != nil {
		svc.log.Error("redis get all machine status failed")
		return
	}

	gpuTypeMap := svc.GetGpuTypeInfoByIds(gpuTypeIDs)
	for _, machine := range oldList {
		// 获取gpu_type号型
		gpuName := machine.GpuName
		gpuMemory := machine.GpuMemory
		floatingPointHashRate := ""
		_, ok := gpuTypeMap[machine.GPUTypeID]
		if ok {
			gpuName = gpuTypeMap[machine.GPUTypeID].GpuName
			gpuMemory = gpuTypeMap[machine.GPUTypeID].GpuMemory
			floatingPointHashRate = gpuTypeMap[machine.GPUTypeID].FloatingPointHashRate
		}
		machine.MachineBaseContent.IpAddress = ""

		machineTags := []string{}
		machineTags, err = svc.GetMachineTagName(machine.MachineID)
		if err != nil {
			svc.log.WithField("machine_id", machine.MachineID).Warn("get machine tag failed")
		}
		heartStatus := agent_constant.HeartException
		_, exist := machineAllStatus[machine.MachineID]
		if exist {
			createAt, err := time.Parse(time.RFC3339, machineAllStatus[machine.MachineID])
			if err != nil {
				svc.log.WithField("machine_id", machine.MachineID).WarnE(err, "time.Parse machine connection active at failed")
			} else {
				if time.Now().Sub(createAt) < constant.MachineHeartStatusTime {
					heartStatus = agent_constant.HeartNormal
				}
			}
		}

		m := &model.MachineInfo{
			MachineID:             machine.MachineID,
			MachineName:           machine.MachineName,
			MachineAlias:          machine.MachineAlias,
			HealthStatus:          machine.Status,
			HeartStatus:           heartStatus,
			OnLine:                machine.Online,
			RegionName:            machine.RegionName,
			GpuName:               gpuName,
			GpuNumber:             machine.GpuNumber,
			GpuUsed:               machine.GpuUsed,
			GpuIdleNum:            machine.GPUIdleNum,
			GpuMemory:             gpuMemory,
			DiskType:              machine.DiskType,
			Description:           "",
			RentDeadline:          machine.RentDeadline,
			MaxInstanceNum:        machine.MaxInstanceNum,
			MaxInstanceDiskSize:   machine.MaxInstanceDiskSize,
			BindingInstanceNum:    machine.BindingInstanceNum,
			FloatingPointHashRate: floatingPointHashRate,
			HighestCudaVersion:    machine.HighestCudaVersion,
			CpuPerGpu:             machine.CpuPerGpu,
			MemPerGpu:             machine.MemPerGpu,
			CreatedAt:             machine.CreatedAt,
			UserVisibleLimit:      machine.UserVisibleLimit,
			MachineBaseInfo:       machine.MachineBaseContent,
			MachineSkuInfo:        machine.NewMachineSkuContent,
			RegionSign:            machine.RegionSign,
			MountNetDisk:          machine.MountNetDisk,
			MachineTagInfo:        machineTags,
			RentMode:              machine.RentMode,
		}
		if isAdmin {
			machineSaveInfo, _ := svc.GetMachineStatus(machine.MachineID)
			if machineSaveInfo != nil {
				m.DiskInfo = machineSaveInfo.DiskInfo
			}
		}
		machineInfoList = append(machineInfoList, m)

	}
	oldPaged.List = machineInfoList
	return oldPaged
}

// GetListForBusiness 给业务层使用的列表，表里有的都查出来
func (svc *McService) GetListForBusiness(pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []model.Machine, err error) {
	list = make([]model.Machine, 0)
	db := db_helper.GlobalDBConn().Table(model.TableNameMachine).Unscoped()

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Warn("machine count failed.")
		err = businesserror.ErrDatabaseError
		return
	}

	paged = db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, int(count), 0)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Warn("machine finds failed.")
		err = businesserror.ErrDatabaseError
		return
	}

	//paged.List = list
	return
}
