package controller

import (
	"github.com/gin-gonic/gin"
	"server/pkg-core/api/coreapi"
	"server/pkg-core/module_definition"
	"server/pkg/businesserror"
	"server/pkg/http"
	"server/pkg/logger"
)

const GpuTypeModuleName = "gpu_type_controller"

type GpuTypeController struct {
	gpuTypeService module_definition.GpuTypeInference
	logger         *logger.Logger
}

func NewGpuTypeControllerProvider(gpuTypeService module_definition.GpuTypeInference) *GpuTypeController {
	return &GpuTypeController{
		gpuTypeService: gpuTypeService,
		logger:         logger.NewLogger(GpuTypeModuleName),
	}
}

func (ctrl *GpuTypeController) Create(c *gin.Context) {
	var req coreapi.CreateGpuTypeReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	err := ctrl.gpuTypeService.Create(req)
	if err != nil {
		ctrl.logger.ErrorE(err, "create gpu stock failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)
}

func (ctrl *GpuTypeController) Update(c *gin.Context) {
	var req coreapi.UpdateGpuTypeReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	err := ctrl.gpuTypeService.Update(req)
	if err != nil {
		ctrl.logger.ErrorE(err, "create gpu stock failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)
}

func (ctrl *GpuTypeController) GetList(c *gin.Context) {
	var req coreapi.GetGpuTypeListReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	paged, _, err := ctrl.gpuTypeService.List(req.GpuName, req.Paged)
	if err != nil {
		ctrl.logger.ErrorE(err, "create gpu stock failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, paged)
}

func (ctrl *GpuTypeController) Delete(c *gin.Context) {
	var req coreapi.DeleteGpuTypeReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	err := ctrl.gpuTypeService.Delete(req.GpuTypeID)
	if err != nil {
		ctrl.logger.ErrorE(err, "create gpu stock failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)
}

func (ctrl *GpuTypeController) CheckGpuTypeName(c *gin.Context) {
	var req coreapi.CheckGpuTypeNameReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	existed, err := ctrl.gpuTypeService.CheckGpuTypeName(req.GpuTypeName, req.GpuTypeID)
	if err != nil {
		ctrl.logger.ErrorE(err, "create gpu stock failed")
		http.SendError(c, err)
		return
	}
	data := coreapi.CheckGpuTypeNameData{
		Existed: existed,
	}
	http.SendOK(c, data)
}

func (ctrl *GpuTypeController) GetGpuID(c *gin.Context) {
	var req coreapi.GetGpuIDReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	ids, err := ctrl.gpuTypeService.GetGpuID(req.GpuTypeNames)
	if err != nil {
		ctrl.logger.ErrorE(err, "create gpu stock failed")
		http.SendError(c, err)
		return
	}
	data := coreapi.GetGpuIDData{
		IDs: ids,
	}
	http.SendOK(c, data)
}

func (ctrl *GpuTypeController) GetGpu(c *gin.Context) {
	var req coreapi.GetGpuReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	gpuType, err := ctrl.gpuTypeService.GetGpu(req.GpuID)
	if err != nil {
		ctrl.logger.ErrorE(err, "create gpu stock failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, gpuType)
}

func (ctrl *GpuTypeController) GetGpuByIDs(c *gin.Context) {
	var req coreapi.GetGpuByIDsReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	gpuTypes, err := ctrl.gpuTypeService.GetGpuByIDs(req.GpuIDs)
	if err != nil {
		ctrl.logger.ErrorE(err, "create gpu stock failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, gpuTypes)
}
