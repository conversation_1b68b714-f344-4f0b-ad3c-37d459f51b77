package model

import (
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"server/pkg/businesserror"
	"server/pkg/db_helper"
	"time"
)

const TableNameMachineTagType = "core_machine_tag"

type MachineTag struct {
	ID        int            `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`

	TagName string `gorm:"type:varchar(255);column:tag_name;" json:"tag_name"`
}

func (m *MachineTag) TableName() string {
	return TableNameMachineTagType
}

// Init 实现 db_helper 接口.
func (m *MachineTag) Init(dbConn *gorm.DB) error {
	err := dbConn.AutoMigrate(&MachineTag{})
	if err != nil {
		log.WithField("err", err).Error("db migrate image failed")
		return err
	}
	for _, tag := range machineTags {
		notExisted := db_helper.GetOne(db_helper.QueryDefinition{ModelDefinition: &MachineTag{},
			Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{"tag_name": tag.TagName}}},
			&MachineTag{}).IsRecordNotFoundError()
		if notExisted {
			tag := &MachineTag{
				TagName:   tag.TagName,
				CreatedAt: time.Now()}
			err = db_helper.InsertOne(db_helper.QueryDefinition{ModelDefinition: &MachineTag{}, InsertPayload: tag}).GetError()
			if err != nil {
				log.WithField("err", err).Error("create machine tag failed.")
				err = businesserror.ErrDatabaseError
				return err
			}
		}
	}
	return nil
}

var machineTags = []MachineTag{
	{TagName: "通用型"},
	{TagName: "大显存"},
	{TagName: "高主频CPU"},
	{TagName: "多核心CPU"},
	{TagName: "1元GPU"},
	{TagName: "自主可控"},
}
