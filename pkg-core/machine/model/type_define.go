package model

import (
	"server/pkg/constant"
	"server/pkg/db_helper"
)

type WorkOrderCreateParams struct {
	MachineID string `json:"machine_id"`
	Describe  string `json:"describe"`
}

type WorkOrderUpdateParams struct {
	WorkOrderUUID string `json:"work_order_uuid"`
	Describe      string `json:"describe"`
}

type WorkOrderListParams struct {
	MachineID  string                          `json:"machine_id"`
	RegionSign string                          `json:"region_sign"`
	Status     constant.MachineWorkOrderStatus `json:"status"`

	db_helper.GetPagedRangeRequest
}

type WorkOrderRecordCreateParams struct {
	WorkOrderUUID string `json:"work_order_uuid"`
	Describe      string `json:"describe"`

	WorkOrderStatus constant.MachineWorkOrderStatus `json:"work_order_status"`
}

type WorkOrderRecordListParams struct {
	MachineID     string `json:"machine_id"`
	WorkOrderUUID string `json:"work_order_uuid"`
	db_helper.GetPagedRangeRequest
}
