package model

import (
	"encoding/json"
	"sort"
	"time"

	"server/pkg-agent/agent_constant"
	gs "server/pkg-core/gpu_stock/model"
	"server/pkg/constant"
	"server/pkg/db_helper"

	"gorm.io/datatypes"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

const TableNameMachine = "core_machine"

type BaseMachineInfo struct {
	CpuNum               int64                       `json:"cpu_num"`
	CpuName              string                      `json:"cpu_name"`
	CpuBasicFrequency    string                      `json:"cpu_basic_frequency"`
	DiskSize             int64                       `json:"disk_size"` // 单位为byte
	DiskType             string                      `json:"disk_type"`
	Memory               int64                       `json:"memory"` // 单位为byte
	IpAddress            string                      `json:"ip_address"`
	OsName               string                      `json:"os_name"`
	DockerVersion        string                      `json:"docker_version"`
	KernelVersion        string                      `json:"kernel_version"`
	NvidiaDocker         bool                        `json:"nvidia_docker"`
	NetworkDownloadSpeed int64                       `json:"network_download_speed"`
	NetworkUploadSpeed   int64                       `json:"network_upload_speed"`
	AgentVersion         string                      `json:"agent_version"`
	RestartTime          time.Time                   `json:"restart_time"`
	MachineFeature       agent_constant.AgentFeature `json:"machine_feature"`
}

func (bm *BaseMachineInfo) Marshal() []byte {
	b, _ := json.Marshal(bm)
	return b
}

type EditMachineInfo struct {
	DiskType             string
	NetworkDownloadSpeed int
	NetworkUploadSpeed   int
	GPUTypeSale          string
	GPUTypeSaleMemory    int
	MaxInstanceNum       int
	MaxInstanceDiskSize  int
	RentDeadline         time.Time
	Description          string
	MachineSkuInfo       []SkuInfo
}

type LevelInfo struct {
	LevelName       constant.MemberLevelName `json:"level_name"`
	Discount        int64                    `json:"discount"`
	DiscountedPrice int64                    `json:"discounted_price"`
	Index           int                      `json:"index"`
}

type SkuInfo struct {
	Type         constant.ChargeType `json:"type"`
	OriginPrice  int64               `json:"origin_price"`
	CurrentPrice int64               `json:"current_price"`
	Discount     int64               `json:"discount"`
	LevelConfig  []*LevelInfo        `json:"level_config"`
}

// Machine 机器 Model。添加新的字段也需要修改 Machine.Map 方法。
type Machine struct {
	ID           int                                `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	Status       agent_constant.MachineHealthStatus `gorm:"type:int;column:status;" json:"status"`
	Online       constant.OnOffLine                 `gorm:"type:int;column:on_line;" json:"online"`
	OnlineBackup constant.OnOffLine                 `gorm:"type:int;column:online_backup" json:"online_backup"`
	FakeRecord   bool                               `gorm:"type:bool;column:fake_record;" json:"fake_record"`
	MachineID    string                             `gorm:"type:varchar(255);column:machine_id;NOT NULL;" json:"machine_id"`
	MachineName  string                             `gorm:"type:varchar(255);column:machine_name;NOT NULL;" json:"machine_name"`
	MachineAlias string                             `gorm:"type:varchar(255);column:machine_alias;" json:"machine_alias"`
	RegionName   string                             `gorm:"type:varchar(255);column:region_name;NOT NULL;" json:"region_name"`
	RegionSign   constant.RegionSignType            `gorm:"type:varchar(255);column:region_sign;" json:"region_sign"`
	GpuName      string                             `gorm:"type:varchar(255);column:gpu_name;NOT NULL;" json:"gpu_name"`
	GpuNumber    int64                              `gorm:"type:bigint(20);column:gpu_number;" json:"gpu_number"`
	Source       string                             `gorm:"type:varchar(255);column:source" json:"source"`
	// gpu_memory 为GB的整数byte单位

	GpuMemory           int64  `gorm:"type:bigint(20);column:gpu_memory;" json:"gpu_memory"` // 单位为byte
	GpuUsed             int64  `gorm:"type:bigint(20);column:gpu_used;" json:"gpu_used"`
	GpuUsed5            int64  `gorm:"type:bigint(20);column:gpu_used_5;default:0;" json:"gpu_used_5"`
	GPUIdleNum          int64  `gorm:"type:bigint(20);column:gpu_idle_num;NOT NULL;" json:"gpu_idle_num"`
	GpuUsedOnly10       int64  `gorm:"type:bigint(20);column:gpu_used_only_10;" json:"gpu_used_only_10"`
	MaxInstanceNum      int64  `gorm:"type:bigint(20);column:max_instance_num;" json:"max_instance_num"`
	BindingInstanceNum  int64  `gorm:"type:bigint(20);column:binding_instance_num;" json:"binding_instance_num"`
	MaxInstanceDiskSize int64  `gorm:"type:bigint(20);column:max_instance_disk_size;" json:"max_instance_disk_size"` // 单位为byte
	DiskType            string `gorm:"type:varchar(255);column:disk_type;NOT NULL;default:'';" json:"disk_type"`
	GPUTypeID           int    `gorm:"type:int;column:gpu_type_id;NOT NULL;default:0;" json:"gpu_type_id"`
	//Description         string     `gorm:"type:text;column:description;NOT NULL;" json:"description"`
	RentDeadline       *time.Time `gorm:"type:datetime;column:rent_deadline;" json:"rent_deadline"`
	HighestCudaVersion string     `json:"type:varchar(255);column:highest_cuda_version;" json:"highest_cuda_version"` // 支持的最高cuda版本
	GPUType            string     `gorm:"type:varchar(255);column:gpu_type;NOT NULL;default:'';" json:"gpu_type"`
	ChipCorp           string     `gorm:"type:varchar(255);column:chip_corp;NOT NULL;default:'';" json:"chip_corp"`
	CpuArch            string     `gorm:"type:varchar(255);column:cpu_arch;NOT NULL;default:'';" json:"cpu_arch"`

	MachineCC            int64           `gorm:"type:bigint(20);column:machine_cc;" json:"-"` // machine字段的版本控制
	MachineBaseInfo      []byte          `gorm:"type:json;column:machine_base_info;" json:"machine_base_info"`
	MachineBaseContent   BaseMachineInfo `gorm:"-" json:"machine_base_content"`
	NewMachineSkuInfo    datatypes.JSON  `gorm:"type:json;column:new_machine_sku_info;" json:"new_machine_sku_info"`
	NewMachineSkuContent []*SkuInfo      `gorm:"-" json:"new_machine_sku_content"`

	RentMode constant.RentMode `gorm:"type:varchar(255);column:rent_mode;default:''" json:"rent_mode"`

	// 网盘挂载
	MountNetDisk  bool `gorm:"column:mount_net_disk;type:tinyint(1)" json:"mount_net_disk"`
	MountADFSDisk bool `gorm:"column:mount_adfs_disk;type:tinyint(1)" json:"mount_adfs_disk"`

	// 添加cpu.memory分配单元， 按照gpu数量分配的字段
	CpuPerGpu int64 `gorm:"type:bigint(20);column:cpu_per_gpu;" json:"cpu_per_gpu"`
	MemPerGpu int64 `gorm:"type:bigint(20);column:mem_per_gpu;" json:"mem_per_gpu"`
	// 真实的gpu_memory大小
	GpuOriginMemory int64 `gorm:"type:bigint(20);column:gpu_origin_memory;" json:"gpu_origin_memory"`
	// 用户可见限制(部分用户可见或无限制)
	UserVisibleLimit bool `gorm:"type bool;column:user_visible_limit;" json:"user_visible_limit"`
	//用于限制机器是否可用于闲时作业
	IdleJobLimit bool `gorm:"type bool;column:idle_job_limit;NOT NULL;default:false" json:"idle_job_limit"`
	// 机器是否接入了地区内网
	PrivateNetAccess bool   `gorm:"column:private_net_access;type:tinyint(1);NOT NULL;default:false" json:"private_net_access"`
	PrivateNetIP     string `gorm:"type:varchar(255);column:private_net_ip;default:''" json:"private_net_ip"`

	// time
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"deleted_at"`
}

func (m *Machine) TableName() string {
	return TableNameMachine
}

func (m *Machine) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&Machine{})
}

func (m *Machine) BeforeCreate(db *gorm.DB) (err error) {
	m.MachineBaseInfo = m.MachineBaseContent.Marshal()

	if m.NewMachineSkuContent != nil {
		for i, _ := range m.NewMachineSkuContent {
			for j, _ := range m.NewMachineSkuContent[i].LevelConfig {
				m.NewMachineSkuContent[i].LevelConfig[j].DiscountedPrice = m.NewMachineSkuContent[i].LevelConfig[j].Discount * m.NewMachineSkuContent[i].CurrentPrice / 100 // 非整数时往下取整
			}
		}
		m.NewMachineSkuInfo, err = json.Marshal(m.NewMachineSkuContent)
		if err != nil {
			return
		}
	}

	return nil
}

func (m *Machine) AfterFind(db *gorm.DB) (err error) {
	if len(m.MachineBaseInfo) > 0 {
		err = json.Unmarshal(m.MachineBaseInfo, &m.MachineBaseContent)
		if err != nil {
			return
		}
	}

	m.NewMachineSkuContent = []*SkuInfo{}
	if len(m.NewMachineSkuInfo) > 0 {
		err = json.Unmarshal(m.NewMachineSkuInfo, &m.NewMachineSkuContent)
		if err != nil {
			return err
		}
	}
	return
}

func (m *Machine) MachineCreate(tx *gorm.DB) error {
	return db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition:         m,
		DBTransactionConnection: tx,
		InsertPayload:           m,
	}).GetError()
}

func (m *Machine) MachineUpdate(tx *gorm.DB, filter db_helper.QueryFilters, um map[string]interface{}) error {
	return db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         m,
		Filters:                 filter,
	}, um).GetError()
}

func (m *Machine) GetAgentFeature() agent_constant.AgentFeature {
	return m.MachineBaseContent.MachineFeature
}

func (m *Machine) Map() map[string]interface{} {
	machineBaseInfo := string(m.MachineBaseInfo)
	if machineBaseInfo == "" {
		machineBaseInfo = "{}"
	}
	var rentDeadline interface{}
	if m.RentDeadline != nil {
		rentDeadline = m.RentDeadline.Format(constant.FormatTimeString)
	}
	var deletedAt interface{}
	if m.DeletedAt.Valid {
		deletedAt = m.DeletedAt.Time.Format(constant.FormatTimeString)
	}
	return map[string]interface{}{
		//"id":                     m.ID, // todo：不能指定id，会报错

		// core_machine表的id，用于业务层machine表关联core_machine表
		// 如果用machine_id的话表中的数据可能不是唯一的，例如删除的记录和没有删除的记录，这样的话业务层的machine会与core不一致
		// 如果用表id的话，core和业务层的machine表id必须一致，一旦不一致就会导致严重的问题
		// 所以这里改成业务层machine.core_machine_id <=> core的machine.id
		// core的machine.id通过字段core_machine_id向外传递，不能通过id因为业务层会直接使用Map()方法的返回值做update操作
		"core_machine_id": m.ID,

		"status":                 m.Status,
		"on_line":                m.Online,
		"online_backup":          m.OnlineBackup,
		"fake_record":            m.FakeRecord,
		"machine_id":             m.MachineID,
		"machine_name":           m.MachineName,
		"machine_alias":          m.MachineAlias,
		"region_name":            m.RegionName,
		"region_sign":            m.RegionSign,
		"mount_net_disk":         m.MountNetDisk,
		"gpu_name":               m.GpuName,
		"gpu_number":             m.GpuNumber,
		"gpu_memory":             m.GpuMemory,
		"gpu_used":               m.GpuUsed,
		"gpu_idle_num":           m.GPUIdleNum,
		"chip_corp":              m.ChipCorp,
		"cpu_arch":               m.CpuArch,
		"max_instance_num":       m.MaxInstanceNum,
		"max_instance_disk_size": m.MaxInstanceDiskSize,
		"disk_type":              m.DiskType,
		"gpu_type_id":            m.GPUTypeID,
		//"description":            m.Description,
		"source":               m.Source,
		"rent_deadline":        rentDeadline,
		"highest_cuda_version": m.HighestCudaVersion,
		"machine_base_info":    machineBaseInfo,
		"rent_mode":            m.RentMode,
		"mount_adfs_disk":      m.MountADFSDisk,
		"cpu_per_gpu":          m.CpuPerGpu,
		"mem_per_gpu":          m.MemPerGpu,
		"gpu_origin_memory":    m.GpuOriginMemory,
		"private_net_access":   m.PrivateNetAccess,
		"private_net_ip":       m.PrivateNetIP,
		"created_at":           m.CreatedAt.Format(constant.FormatTimeString),
		"updated_at":           m.UpdatedAt.Format(constant.FormatTimeString),
		"deleted_at":           deletedAt,
		"gpu_type":             m.GPUType,
		"gpu_used_only_10":     m.GpuUsedOnly10,
	}
}

type CreateMachineParams struct {
	MachineID    string
	MachineName  string
	HealthStatus int

	CpuNum            int
	CpuName           string
	CpuBasicFrequency string
	GpuName           string
	GpuNumber         int
	GpuMemory         int
	GpuUsed           int
	Memory            int
	DiskSize          int

	OnLine       int
	RegionName   string
	RegionSign   constant.RegionSignType
	MountNetDisk bool
	IpAddress    string
	OsName       string
}

type UpdateMachineInfoParams struct {
	MachineID            string     `json:"machine_id"`
	MachineAlias         string     `json:"machine_alias"`
	DiskType             string     `json:"disk_type"`
	NetworkDownloadSpeed int64      `json:"network_download_speed"`
	NetworkUploadSpeed   int64      `json:"network_upload_speed"`
	GPUTypeID            int        `json:"gpu_type_id"`
	GPUType              string     `json:"gpu_type"`
	MaxInstanceNum       int64      `json:"max_instance_num"`
	MaxInstanceDiskSize  int64      `json:"max_instance_disk_size"`
	CpuPerGpu            int64      `json:"cpu_per_gpu"`
	MemPerGpu            int64      `json:"mem_per_gpu"`
	RentDeadline         *time.Time `json:"rent_deadline"`
	HighestCudaVersion   string     `json:"highest_cuda_version"` // 支持的最高cuda版本
	Description          string     `json:"description"`
	MountNetDisk         bool       `json:"mount_net_disk"`
	MachineTagID         []int      `json:"machine_tag_id"`

	//DefaultMode 不限制租用量
	//Base2Mode 2卡起租
	//Base4Mode 4卡起租
	//Unit2Mode 以2卡为单位租
	//Unit4Mode 以4卡为单位租
	RentMode constant.RentMode `json:"rent_mode"`
}

type GetMachineListParams struct {
	RegionSign            constant.RegionSignType   `json:"region_sign"`
	RegionSignList        []constant.RegionSignType `json:"region_sign_list"`
	ChargeType            constant.ChargeQueryType  `json:"charge_type"`
	PayPriceOrder         constant.OrderValue       `json:"pay_price_order"`
	GpuTypeName           []string                  `json:"gpu_type_name"`
	MachineTagIDs         []int                     `json:"machine_tag_name"`
	GpuIdleType           constant.OrderValue       `json:"gpu_idle_type"`
	GpuIdleNum            int                       `json:"gpu_idle_num"`
	MountNetDisk          bool                      `json:"mount_net_disk"`
	InstanceDiskSizeOrder constant.OrderValue       `json:"instance_disk_size_order"`
	MachineID             string                    `json:"machine_id"`
	DefaultOrder          bool                      `json:"default_order"`
	MachineAlias          string                    `json:"machine_alias"`
}

type GetAdminMachineListParams struct {
	GpuTypeName  []string                  `json:"gpu_type_name"`
	RegionSign   []constant.RegionSignType `json:"region_sign"`
	OnlineStatus *int                      `json:"online_status"`
	MachineName  string                    `json:"machine_name"`
	MachineID    string                    `json:"machine_id"`
}

type MachineInfo struct {
	MachineID             string                             `json:"machine_id"`
	MachineName           string                             `json:"machine_name"`
	MachineAlias          string                             `json:"machine_alias"`
	HealthStatus          agent_constant.MachineHealthStatus `json:"health_status"`
	HeartStatus           agent_constant.MachineHeartStatus  `json:"heart_status"`
	OnLine                constant.OnOffLine                 `json:"on_line"`
	RegionName            string                             `json:"region_name"`
	GpuName               string                             `json:"gpu_name"`
	GpuNumber             int64                              `json:"gpu_number"`
	GpuMemory             int64                              `json:"gpu_memory"` // 单位为byte
	GpuUsed               int64                              `json:"gpu_used"`
	GpuIdleNum            int64                              `json:"gpu_idle_num"`
	MaxInstanceNum        int64                              `json:"max_instance_num"`
	MaxInstanceDiskSize   int64                              `json:"max_instance_disk_size"`   // 单位为byte
	BindingInstanceNum    int64                              `json:"binding_instance_num"`     // 机器当前绑定的实例数量
	FloatingPointHashRate string                             `json:"floating_point_hash_rate"` // 浮点算力
	HighestCudaVersion    string                             `json:"highest_cuda_version"`     // 支持的最高cuda版本
	CpuPerGpu             int64                              `json:"cpu_per_gpu"`
	MemPerGpu             int64                              `json:"mem_per_gpu"`
	DiskType              string                             `json:"disk_type"`
	Description           string                             `json:"description"`
	RentDeadline          *time.Time                         `json:"rent_deadline"`
	CreatedAt             time.Time                          `json:"created_at"`
	UpdateAt              time.Time                          `json:"update_at"`
	Payg                  bool                               `json:"payg"`
	PaygPrice             int64                              `json:"payg_price"` // 用于按照价格排序的字段
	PayDaily              bool                               `json:"pay_daily"`
	PayWeekly             bool                               `json:"pay_weekly"`
	PayMonthly            bool                               `json:"pay_monthly"`
	PayYearly             bool                               `json:"pay_yearly"`
	UserVisibleLimit      bool                               `json:"user_visible_limit"`
	MachineBaseInfo       BaseMachineInfo                    `json:"machine_base_info"`
	MachineSkuInfo        []*SkuInfo                         `json:"machine_sku_info"`
	RegionSign            constant.RegionSignType            `json:"region_sign"`
	MountNetDisk          bool                               `json:"mount_net_disk"`
	DiskInfo              *agent_constant.DiskInfo           `json:"disk_info"`
	MachineTagInfo        []string                           `json:"machine_tag_info"`

	// 硬盘扩容信息
	RentMode constant.RentMode `json:"rent_mode"`
}

type RentInfo struct {
	CpuUsed                int64           `json:"cpu_used"`
	MemoryUsed             int64           `json:"memory_used"` // 单位为byte
	GpuUsed                int64           `json:"gpu_used"`    // 单位为gpu数量
	InstanceOccupationInfo gs.GpuStockRank `json:"instance_occupation_info"`
}

type MachineDetailInfo struct {
	MachineID               string                             `json:"machine_id"`
	MachineName             string                             `json:"machine_name"`
	MachineAlias            string                             `json:"machine_alias"`
	HealthStatus            agent_constant.MachineHealthStatus `json:"health_status"`
	HeartStatus             agent_constant.MachineHeartStatus  `json:"heart_status"`
	OnLine                  constant.OnOffLine                 `json:"on_line"`
	RegionName              string                             `json:"region_name"`
	RegionSign              constant.RegionSignType            `json:"region_sign"`
	MountNetDisk            bool                               `json:"mount_net_disk"`
	GpuName                 string                             `json:"gpu_name"`
	GpuNumber               int64                              `json:"gpu_number"`
	GpuMemory               int64                              `json:"gpu_memory"` // 单位为byte
	GpuUsed                 int64                              `json:"gpu_used"`
	GpuIdleNum              int64                              `json:"gpu_idle_num"`
	MaxInstanceNum          int64                              `json:"max_instance_num"`
	MaxInstanceDiskSize     int64                              `json:"max_instance_disk_size"`
	BindingInstanceNum      int                                `json:"binding_instance_num"` // 机器当前绑定的实例数量
	CpuPerGpu               int64                              `json:"cpu_per_gpu"`
	MemPerGpu               int64                              `json:"mem_per_gpu"`
	DiskType                string                             `json:"disk_type"`
	Description             string                             `json:"description"`
	RentDeadline            *time.Time                         `json:"rent_deadline"`
	CreatedAt               time.Time                          `json:"created_at"`
	UpdateAt                time.Time                          `json:"update_at"`
	Payg                    bool                               `json:"payg"`
	PaygPrice               int64                              `json:"payg_price"` // 用于按照价格排序的字段
	PayDaily                bool                               `json:"pay_daily"`
	PayWeekly               bool                               `json:"pay_weekly"`
	PayMonthly              bool                               `json:"pay_monthly"`
	PayYearly               bool                               `json:"pay_yearly"`
	FloatingPointHashRate   string                             `json:"floating_point_hash_rate"` // 浮点算力
	HighestCudaVersion      string                             `json:"highest_cuda_version"`     // 支持的最高cuda版本
	MachineBaseInfo         *BaseMachineInfo                   `json:"machine_base_info"`
	MachineSkuInfo          []*SkuInfo                         `json:"machine_sku_info"`
	GpuTypeInfo             *GpuTypeInfo                       `json:"gpu_type_info"`
	RentInfo                *RentInfo                          `json:"rent_info"`
	MachineInstanceInfo     []*MachineInstanceInfo             `json:"machine_instance_info"`
	DiskInfo                *agent_constant.DiskInfo           `json:"disk_info"`
	MachineTagInfo          []string                           `json:"machine_tag_info"`
	DataDiskExpandTotalSize int64                              `json:"data_disk_expand_total_size"`
	DataDiskExpandPrice     *constant.DataDiskChargeInfo       `json:"data_disk_expand_price"`
	RentMode                constant.RentMode                  `json:"rent_mode"`
}

type MachineInstanceInfo struct {
	InstanceID     string                `json:"instance_id"`
	InstanceStatus string                `json:"instance_status"`
	ChargeType     constant.ChargeType   `json:"charge_type"`
	UserPhone      string                `json:"user_phone"`
	CreatedAt      time.Time             `json:"created_at"`
	ExpiredAt      *time.Time            `json:"expired_at"`
	ResourceInfo   *InstanceResourceInfo `json:"resource_info"`
}

type InstanceResourceInfo struct {
	CpuNum    int64   `json:"cpu_num"`
	Memory    int64   `json:"memory"`
	GpuNum    int64   `json:"gpu_num"`
	DiskUsage float64 `json:"disk_usage"`
}

type UpdateOnlineParam struct {
	MachineID   string             `json:"machine_id"`
	OnOffLine   constant.OnOffLine `json:"on_off_line"`
	ForceRemove bool               `json:"force_remove"`
}

type MachineStatusSave struct {
	HealthStatus agent_constant.MachineHealthStatus `json:"health_status"`
	DiskInfo     *agent_constant.DiskInfo           `json:"disk_info"`
	CreatedAt    time.Time                          `json:"created_at"`
}

type LevelInfoRank []*LevelInfo

func (g LevelInfoRank) Len() int           { return len(g) }
func (g LevelInfoRank) Swap(i, j int)      { g[i], g[j] = g[j], g[i] }
func (g LevelInfoRank) Less(i, j int) bool { return g[i].Index < g[j].Index }

// 校验会员级别的打折是否合理
func CheckLevel(levelInfo []*LevelInfo) (ok bool) {
	if len(levelInfo) != constant.LevelNum {
		return
	}
	var levelInfoRank LevelInfoRank // 按照discount值排序用的
	levelInfoRank = levelInfo
	sort.Sort(levelInfoRank)
	if levelInfoRank[0].LevelName != constant.NormalUser ||
		levelInfoRank[1].LevelName != constant.MemberUser {
		return
	}
	if levelInfoRank[0].DiscountedPrice >= levelInfoRank[1].DiscountedPrice {
		return true
	}
	return
}

func (s *SkuInfo) SetDiscountPrice() {
	for i := range s.LevelConfig {
		floatDiscountPrice := float64(s.LevelConfig[i].Discount*s.CurrentPrice/100) / 1000
		price, _ := decimal.NewFromFloat(floatDiscountPrice).Round(2).Float64()      // 四舍五入保留两位小数
		s.LevelConfig[i].DiscountedPrice = int64(price * 1000)                       // 非整数时往下取整
		s.LevelConfig[i].Index = int(constant.LevelRank[s.LevelConfig[i].LevelName]) // 设置下标
	}
	return
}

func (m *Machine) GetUserPaygPrice(chargeType constant.ChargeType, levelName constant.MemberLevelName) (price int64, err error) {
	for _, skuInfo := range m.NewMachineSkuContent {
		if skuInfo.Type == chargeType {
			for i := range skuInfo.LevelConfig {
				if skuInfo.LevelConfig[i].LevelName == levelName {
					price = skuInfo.LevelConfig[i].DiscountedPrice
					return
				}
			}
		}
	}
	return
}

type MachineInfoForCommon struct {
	MachineID    string                  `json:"machine_id"`
	MachineAlias string                  `json:"machine_alias"`
	RegionSign   constant.RegionSignType `json:"region_sign"`
}

func (m *Machine) GetDiscountRate() (discountInfo map[constant.ChargeType]map[constant.MemberLevelName]int64) {
	discountInfo = map[constant.ChargeType]map[constant.MemberLevelName]int64{}
	for _, sku := range m.NewMachineSkuContent {
		chargeType := sku.Type
		discountInfo[chargeType] = map[constant.MemberLevelName]int64{}
		for _, level := range sku.LevelConfig {
			if level == nil {
				continue
			}
			levelName := level.LevelName
			discountRate := level.Discount
			discountInfo[chargeType][levelName] = discountRate
		}
	}
	return
}

type MachineList []*Machine

func (ml MachineList) GetMachine(machineID string) Machine {
	for _, machine := range ml {
		if machine.MachineID == machineID {
			return *machine
		}
	}
	return Machine{}
}
