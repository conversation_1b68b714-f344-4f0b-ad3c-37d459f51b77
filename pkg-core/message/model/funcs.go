package model

import (
	"server/pkg/db_helper"
	"server/pkg/logger"
	"server/plugin/queue"
	"time"
)

func Handling(msgUUID string, statusType queue.MessageStatusType) {
	if db_helper.GlobalDBConn() == nil {
		return
	}

	if statusType == queue.MessageIsFinished {
		// 只删除Finished的，其余状态的message还是保留7天
		m := &Message{UUID: msgUUID}
		err := m.MessageDeleteUnscoped()
		if err == nil {
			return
		}

		logger.NewLogger("model message model Handling").ErrorE(err, "message [%s] Finished, but delete it failed", msgUUID)
	}

	updateErr := db_helper.UpdateOne(db_helper.QueryDefinition{
		ModelDefinition: &Message{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uuid": msgUUID,
			},
		},
		Unscoped:   true,
		DisableLog: true,
	}, &Message{
		Status:    statusType,
		UpdatedAt: time.Now(),
	}).GetError()
	if updateErr != nil {
		logger.NewLogger("model message model Handling").ErrorE(updateErr, "failed to update message in db")
	}
}

func GetMsgList(msgUUID ...string) (res []queue.ElementPayload, err error) {
	var msgs []Message

	var inFilter []db_helper.In
	if len(msgUUID) > 0 {
		inFilter = []db_helper.In{
			{
				Key:   "uuid",
				InSet: msgUUID,
			},
		}
	}

	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &Message{},
		Filters: db_helper.QueryFilters{
			InFilters: inFilter,
			EqualFilters: map[string]interface{}{
				"status": queue.MessageIsCreated,
			},
		},
		Unscoped:   true,
		DisableLog: true,
	}, &msgs).GetError()

	for _, msg := range msgs {
		res = append(res, queue.ElementPayload{
			Type:    msg.ObjectType,
			MsgUUID: msg.UUID,
			Payload: string(msg.Payload),
			ValidAt: msg.CreatedAt,
		})
	}
	return
}
