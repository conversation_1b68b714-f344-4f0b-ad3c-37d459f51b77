package controller

import (
	"server/pkg-core/api/coreapi"
	dataDiskService "server/pkg-core/data_disk_stock/service"
	"server/pkg/businesserror"
	"server/pkg/http"
	"server/pkg/logger"

	"github.com/gin-gonic/gin"
)

const ModuleName = "data_disk_controller"

type DataDiskController struct {
	logger          *logger.Logger
	dataDiskService *dataDiskService.DataDiskService
}

func NewDataDiskControllerProvider(dataDiskService *dataDiskService.DataDiskService) *DataDiskController {
	return &DataDiskController{
		logger:          logger.NewLogger(ModuleName),
		dataDiskService: dataDiskService,
	}
}

func (ctrl *DataDiskController) Create(c *gin.Context) {
	var req coreapi.CreateDataDiskReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	err := ctrl.dataDiskService.Create(nil, req.MachineID)
	if err != nil {
		ctrl.logger.ErrorE(err, "create gpu stock failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)
}

func (ctrl *DataDiskController) Update(c *gin.Context) {
	var req coreapi.UpdateDataDiskReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	err := ctrl.dataDiskService.Update(nil, req)
	if err != nil {
		ctrl.logger.ErrorE(err, "create gpu stock failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)
}

func (ctrl *DataDiskController) Get(c *gin.Context) {
	var req coreapi.GetDataDiskReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	dds, err := ctrl.dataDiskService.Get(nil, req.MachineID)
	if err != nil {
		ctrl.logger.ErrorE(err, "create gpu stock failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, dds)
}

func (ctrl *DataDiskController) GetByMachineIds(c *gin.Context) {
	var req coreapi.GetDataDiskByMachineIdsReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	dds, err := ctrl.dataDiskService.GetByMachineIds(req.MachineIds)
	if err != nil {
		ctrl.logger.ErrorE(err, "GetByMachineIds failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, dds)
}

func (ctrl *DataDiskController) GetList(c *gin.Context) {
	var req coreapi.GetDataDiskListReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	s, m, err := ctrl.dataDiskService.GetList(req.MachineIDs)
	if err != nil {
		ctrl.logger.ErrorE(err, "create gpu stock failed")
		http.SendError(c, err)
		return
	}
	data := coreapi.GetDataDiskListData{
		Slice: s,
		Map:   m,
	}
	http.SendOK(c, data)
}

func (ctrl *DataDiskController) Reserve(c *gin.Context) {
	var req coreapi.ReserveDataDiskReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	txUUID, err := ctrl.dataDiskService.Reserve(nil, req)
	if err != nil {
		ctrl.logger.ErrorE(err, "create gpu stock failed")
		http.SendError(c, err)
		return
	}
	data := coreapi.ReserveDataDiskData{
		TxUUID: txUUID,
	}
	http.SendOK(c, data)
}

func (ctrl *DataDiskController) TxCommit(c *gin.Context) {
	var req coreapi.DataDiskTxCommitReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	err := ctrl.dataDiskService.TxCommit(req)
	if err != nil {
		ctrl.logger.ErrorE(err, "create gpu stock failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)
}

func (ctrl *DataDiskController) TxRollback(c *gin.Context) {
	var req coreapi.DataDiskTxRollbackReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	err := ctrl.dataDiskService.TxRollback(req)
	if err != nil {
		ctrl.logger.ErrorE(err, "create gpu stock failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)
}

//func (ctrl *DataDiskController) ChangeSize(c *gin.Context) {
//	var req coreapi.DataDiskChangeSizeReq
//	if err := c.ShouldBind(&req); err != nil {
//		ctrl.logger.Warn(err.Error())
//		http.SendError(c, businesserror.ErrInvalidRequestParams)
//		return
//	}
//	err := ctrl.dataDiskService.ChangeSize(nil, req)
//	if err != nil {
//		ctrl.logger.ErrorE(err, "create gpu stock failed")
//		http.SendError(c, err)
//		return
//	}
//	http.SendOK(c, nil)
//}

func (ctrl *DataDiskController) Release(c *gin.Context) {
	var req coreapi.ReleaseDataDiskReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	txUUID, err := ctrl.dataDiskService.Release(nil, req)
	if err != nil {
		ctrl.logger.ErrorE(err, "create gpu stock failed")
		http.SendError(c, err)
		return
	}
	data := coreapi.ReleaseDataDiskData{
		TxUUID: txUUID,
	}
	http.SendOK(c, data)
}

func (ctrl *DataDiskController) Fix(c *gin.Context) {
	var req coreapi.FixDataDiskReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	err := ctrl.dataDiskService.Fix(req.MachineID, req.DiskExpandAllocated)
	if err != nil {
		ctrl.logger.ErrorE(err, "fix data disk stock failed")
		http.SendError(c, err)
		return
	}
	data := coreapi.FixDataDiskRes{}
	http.SendOK(c, data)
}
