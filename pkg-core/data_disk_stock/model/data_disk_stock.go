package model

import (
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"

	"gorm.io/gorm"
)

const TableNameMachineDataDisk = "core_data_disk_stock"

type DataDiskStock struct {
	ID        int    `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY;" json:"id"`
	MachineID string `gorm:"column:machine_id;type:varchar(255);uniqueIndex:machine_id;NOT NULL" json:"machine_id"`

	// 磁盘扩容分配情况
	DiskExpandTotal     int64 `gorm:"column:disk_expand_total;default:0" json:"disk_expand_total"`
	DiskExpandAllocated int64 `gorm:"column:disk_expand_allocated;default:0" json:"disk_expand_allocated"`
	DiskExpandAvailable int64 `gorm:"column:disk_expand_available;default:0" json:"disk_expand_available"`

	// 乐观锁
	StockLock int64 `gorm:"column:stock_lock;default:0" json:"stock_lock"`

	// 磁盘配置情况
	FreeDiskSize int64 `gorm:"column:free_disk_size;default:0" json:"free_disk_size"`

	UpdatedAt time.Time `gorm:"column:updated_at;type:datetime" json:"updated_at"`
}

func (m *DataDiskStock) TableName() string {
	return TableNameMachineDataDisk
}

func (m *DataDiskStock) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&DataDiskStock{})
}

func (m *DataDiskStock) Map() map[string]interface{} {
	return map[string]interface{}{
		"machine_id":            m.MachineID,
		"disk_expand_total":     m.DiskExpandTotal,
		"disk_expand_allocated": m.DiskExpandAllocated,
		"disk_expand_available": m.DiskExpandAvailable,
		"stock_lock":            m.StockLock,
		"free_disk_size":        m.FreeDiskSize,
		"updated_at":            m.UpdatedAt.Format(constant.FormatTimeString),
	}
}

func (m *DataDiskStock) GetAvailable() int64 {
	available := m.DiskExpandTotal - m.DiskExpandAllocated
	if available < 0 {
		return 0
	}
	return available
}

type UpdateDDSParams struct {
	ForMachineBasicUpdate bool
	MachineID             string
	DiskExpandTotal       int64
	FreeDiskSize          int64
}

type ExpandParams struct {
	MachineID      string
	ProductUUID    string
	ExpandDiskSize int64
}

type ChangeSizeParams struct {
	MachineID   string
	ProductUUID string
	OptType     constant.OrderType
	ChangeSize  int64
	FinallySize int64

	DiskAllocateSize int64
}

type ReleaseParams struct {
	MachineID   string
	ProductUUID string

	DiskAllocateSize int64
}

func (m *DataDiskStock) DDSGet(tx *gorm.DB, machineID string) (err error) {
	return db_helper.GetFirst(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         m,
		Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"machine_id": machineID}},
	}, &m).GetError()
}
