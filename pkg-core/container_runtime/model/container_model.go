package model

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const TableNameContainerCenter = "core_container"

/**
 * 此状态接近 agent 的状态, 与实例的业务状态分开. 后续实例中的状态可以彻底简化为原型图中的残缺状态了.
 */

type Container struct {
	ID                 int            `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	CreatedAt          time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt          time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt          gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index;index:idx_uuid_deleted_at" json:"-"`
	AssignedRemoveTime *time.Time     `gorm:"type:datetime;column:assigned_remove_time" json:"-"`

	// BASIC
	RuntimeUUID constant.ContainerRuntimeUUID `gorm:"type:varchar(255);column:runtime_uuid;index:idx_uuid_deleted_at" json:"runtime_uuid"`
	UID         int                           `gorm:"column:uid" json:"uid"`                                      // 创建人
	MachineID   string                        `gorm:"type:varchar(255);column:machine_id;" json:"machine_id"`     // machine_id
	RuntimeType constant.ContainerRuntimeType `gorm:"type:varchar(255);column:runtime_type;" json:"runtime_type"` // instance 还是闲时任务
	ProductUUID string                        `gorm:"type:varchar(255);column:product_uuid;" json:"product_uuid"` // 上层业务的 uuid
	RegionSign  constant.RegionSignType       `gorm:"type:varchar(255);column:region_sign;" json:"region_sign"`   // 地区

	// START MODE
	StartMode constant.ContainerStartMode `gorm:"type:varchar(255);column:start_mode;default:gpu;" json:"start_mode"` // 普通启动/无GPU启动/...

	// CONFIG
	ContainerParamContent datatypes.JSON  `gorm:"type:json;column:container_param_content;" json:"-"` // json in db
	ContainerParam        *ContainerParam `gorm:"-" json:"container_param"`                           // struct in memory

	// RUNTIME CONFIG
	RuntimeParamContent datatypes.JSON `gorm:"type:json;column:runtime_param_content;" json:"-"` // json in db
	RuntimeParam        *RuntimeParam  `gorm:"-" json:"runtime_param"`                           // struct in memory

	// LATEST STATUS & OPERATE
	StatusAt time.Time `gorm:"type:datetime;column:status_at;" json:"status_at"` // 更新 stat 的时间. 初始默认 RecordCreated
	StatusId int64     `gorm:"type:bigint(20);column:status_id;not null;default 0;" json:"status_id"`

	LatestStatusID int           `gorm:"column:latest_status_id" json:"latest_status_id"`
	LatestStatus   StatusHistory `gorm:"-" json:"latest_status"` // 简化 preload 写法. 避免外键和指针.

	LatestOperateID int            `gorm:"column:latest_operate_id" json:"latest_operate_id"`
	LatestOperate   OperateHistory `gorm:"-" json:"latest_operate"`

	// ORDER
	OrderUUID string `gorm:"type:varchar(255);column:order_uuid;" json:"order_uuid"` // 双向绑定订单 uuid

	// TIME
	StartedAt sql.NullTime `gorm:"type:datetime;column:started_at;" json:"started_at"` // 开机时间
	StoppedAt sql.NullTime `gorm:"type:datetime;column:stopped_at;" json:"stopped_at"` // 关机时间

	Tenant string `gorm:"type:varchar(100);default '';" json:"tenant"` // 租户，例如 autodl、海外站等
}

func (m *Container) TableName() string {
	return TableNameContainerCenter
}

// Init 实现 db_helper 接口.
func (m *Container) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&Container{})
}

// LoadAllJsonByStruct 将内存中的结构体转化为数据库中的 json 形式.
func (m *Container) LoadAllJsonByStruct() error {
	var err error
	err = m.LoadContainerParamJsonByStruct()
	if err != nil {
		return err
	}

	err = m.LoadRuntimeParamJsonByStruct()
	if err != nil {
		return err
	}

	return nil
}

// LoadAllStructByJson 从数据库中的 json 反序列化出结构体.
func (m *Container) LoadAllStructByJson() error {
	var err error
	err = m.LoadContainerParamStructByJson()
	if err != nil {
		return err
	}

	err = m.LoadRuntimeParamStructByJson()
	if err != nil {
		return err
	}

	return nil
}

// --------------------------------------------------------------------------------

func (m *Container) LoadContainerParamJsonByStruct() error {
	if m.ContainerParam == nil {
		return nil // nothing happened
	}

	var err error
	m.ContainerParamContent = make([]byte, 0)
	m.ContainerParamContent, err = json.Marshal(m.ContainerParam)
	if err != nil {
		return err
	}

	return nil
}

func (m *Container) LoadContainerParamStructByJson() error {
	if len(m.ContainerParamContent) == 0 {
		return nil // nothing happened
	}

	m.ContainerParam = new(ContainerParam)
	err := json.Unmarshal(m.ContainerParamContent, m.ContainerParam)
	if err != nil {
		return err
	}

	return nil
}

func (m *Container) LoadRuntimeParamJsonByStruct() error {
	if m.RuntimeParam == nil {
		return nil // nothing happened
	}

	var err error
	m.RuntimeParamContent = make([]byte, 0)
	m.RuntimeParamContent, err = json.Marshal(m.RuntimeParam)
	if err != nil {
		return err
	}

	return nil
}

func (m *Container) LoadRuntimeParamStructByJson() error {
	if len(m.RuntimeParamContent) == 0 {
		return nil // nothing happened
	}

	m.RuntimeParam = new(RuntimeParam)
	err := json.Unmarshal(m.RuntimeParamContent, m.RuntimeParam)
	if err != nil {
		return err
	}

	return nil
}

func (m *Container) String() string {
	s, _ := json.Marshal(m)
	return string(s)
}

func (m *Container) ParseFromContent(content string) error {
	return json.Unmarshal([]byte(content), m)
}

func (m *Container) ContainerUpdateByMap(filter db_helper.QueryFilters, um map[string]interface{}) error {
	return db_helper.UpdateOne(db_helper.QueryDefinition{
		ModelDefinition: m,
		Filters:         filter,
	}, um).GetError()
}

// For simple parse, 只获取少量字段，获得高效查询效率
type ContainerBasicInfo struct {
	ID int `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`

	// BASIC
	RuntimeUUID     constant.ContainerRuntimeUUID `gorm:"type:varchar(255);column:runtime_uuid;index:idx_uuid_deleted_at" json:"runtime_uuid"`
	ContainerStatus constant.ContainerStatusType  `gorm:"type:varchar(255);column:container_status;" json:"container_status"`
	StatusCreatedAt time.Time                     `gorm:"type:datetime;column:created_at;" json:"created_at"`
}

func getContainerAndStatusConn() *gorm.DB {
	return db_helper.GlobalDBConn().Model(&Container{}).
		Select(fmt.Sprintf("%s.id, %s.runtime_uuid, csh.container_status, csh.created_at", TableNameContainerCenter, TableNameContainerCenter)).
		Joins(fmt.Sprintf("inner join %s as csh on %s.latest_status_id = csh.id", TableNameContainerStatusHistory, TableNameContainerCenter)).
		Where(fmt.Sprintf("%s.deleted_at is null", TableNameContainerCenter))
}

func GetContainersBasicInfo(query map[string]interface{}, statIn []constant.ContainerStatusType) (containers []ContainerBasicInfo, err error) {
	conn := getContainerAndStatusConn()
	if query != nil {
		conn = conn.Where(query)
	}
	if len(statIn) > 0 {
		conn = conn.Where("csh.container_status in (?)", statIn)
	}

	err = conn.Find(&containers).Error
	return
}

func GetContainersBasicInfoInBatch(query map[string]interface{}, statIn []constant.ContainerStatusType) (containers []ContainerBasicInfo, err error) {
	conn := getContainerAndStatusConn().Where(query)
	if query != nil {
		conn = conn.Where(query)
	}
	if len(statIn) > 0 {
		conn = conn.Where("csh.container_status in (?)", statIn)
	}

	var batchContainers []ContainerBasicInfo
	err = conn.FindInBatches(&batchContainers, 25, func(tx *gorm.DB, batch int) error {
		for i := range batchContainers {
			containers = append(containers, batchContainers[i])
		}
		return nil
	}).Error
	return
}

func (m *Container) ContainerUpdate(tx *gorm.DB, filter *db_helper.QueryFilters, um map[string]interface{}) error {
	return db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         m,
		Filters:                 *filter,
	}, um).GetError()
}

func (m *Container) ContainerExec(tx *gorm.DB) *gorm.DB {
	if tx != nil {
		return tx
	}
	return db_helper.GlobalDBConn().Model(m)
}

func (m *Container) GetContainer(filter *db_helper.QueryFilters) error {
	return db_helper.GetFirst(db_helper.QueryDefinition{
		DBTransactionConnection: db_helper.GlobalDBConnForRead(),
		ModelDefinition:         m,
		Filters:                 *filter,
	}, &m).GetError()
}

func (m *Container) ContainerDelete(filter *db_helper.QueryFilters) error {
	return db_helper.Delete(db_helper.QueryDefinition{
		DBTransactionConnection: db_helper.GlobalDBConnForRead(),
		ModelDefinition:         m,
		Filters:                 *filter,
	}, &m).GetError()
}
