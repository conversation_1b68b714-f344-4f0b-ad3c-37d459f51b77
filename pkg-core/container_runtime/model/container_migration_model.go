package model

import (
	"database/sql"
	"gorm.io/gorm"
	"server/pkg/constant"
	"time"
)

const TableNameContainerMigration = "core_container_migration_history"

type MigrationHistory struct {
	ID        int            `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index;" json:"-"`

	UID         int                           `gorm:"column:uid" json:"uid"`                                      // 创建人
	RuntimeType constant.ContainerRuntimeType `gorm:"type:varchar(255);column:runtime_type;" json:"runtime_type"` // instance

	SourceRuntimeUUID constant.ContainerRuntimeUUID `gorm:"type:varchar(255);column:source_runtime_uuid;" json:"source_runtime_uuid"`
	SourceMachineID   string                        `gorm:"type:varchar(255);column:source_machine_id;" json:"source_machine_id"`
	SourceRegionSign  constant.RegionSignType       `gorm:"type:varchar(255);column:source_region_sign;" json:"source_region_sign"`   // 目前只能相同地区
	SourceProductUUID string                        `gorm:"type:varchar(255);column:source_product_uuid;" json:"source_product_uuid"` // instance uuid

	TargetRuntimeUUID constant.ContainerRuntimeUUID `gorm:"type:varchar(255);column:target_runtime_uuid;" json:"target_runtime_uuid"`
	TargetMachineID   string                        `gorm:"type:varchar(255);column:target_machine_id;" json:"target_machine_id"`
	TargetRegionSign  constant.RegionSignType       `gorm:"type:varchar(255);column:target_region_sign;" json:"target_region_sign"`
	TargetProductUUID string                        `gorm:"type:varchar(255);column:target_product_uuid;" json:"target_product_uuid"`

	StartedAt  sql.NullTime `gorm:"type:datetime;column:started_at;" json:"started_at"`   // 开始时间
	FinishedAt sql.NullTime `gorm:"type:datetime;column:finished_at;" json:"finished_at"` // 结束时间

	Status MigrateHistoryStatusType `gorm:"type:varchar(255);column:status;" json:"status"`
	Msg    string                   `gorm:"column:msg;" json:"msg"`
}

func (m *MigrationHistory) TableName() string {
	return TableNameContainerMigration
}

// Init 实现 db_helper 接口.
func (m *MigrationHistory) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&MigrationHistory{})
}

type MigrateHistoryStatusType string

const (
	Migrating      MigrateHistoryStatusType = "migrating" // 订单确定, 开始迁移
	MigrateSucceed MigrateHistoryStatusType = "succeed"
	MigrateFailed  MigrateHistoryStatusType = "failed"
	MigrateCancel  MigrateHistoryStatusType = "cancel"
)
