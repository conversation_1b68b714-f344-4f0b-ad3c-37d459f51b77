package service

import (
	"encoding/json"
	message "server/pkg-core/message/model"
	"server/pkg/constant"
	"server/pkg/libs"
	"server/pkg/logger"
	"server/plugin/queue_interface"
	"time"

	"github.com/pkg/errors"
	"gorm.io/gorm"
)

/**
 * 容器操作
 */

func (c *CR) releaseAllResourcesOnce(runtimeUUID constant.ContainerRuntimeUUID, timestamp int64) error {
	err := c.mod.FreePorts(runtimeUUID)
	if err != nil {
		return err
	}
	container, err := c.crud.GetContainerUnscoped(runtimeUUID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		}
		return err
	}

	err = c.mod.ReleaseGpus(runtimeUUID, container.MachineID, "releaseAllResourcesOnce", timestamp)
	if err != nil {
		return err
	}

	err = c.cu.DeleteContainerUsage(runtimeUUID.String())
	if err != nil {
		c.l.ErrorE(err, "del container usage failed")
	}

	var statusAt time.Time
	err = message.SimplePubMessage(c.l, c.q, &queue_interface.NewQueueForDDSRecharge{
		MachineID:    container.MachineID,
		InstanceUUID: libs.GenProductUUIDFromRuntimeUUID(runtimeUUID),
		Operate:      queue_interface.ChargingStop,
		T:            statusAt,
	})
	if err != nil {
		c.l.ErrorE(err, "Pub message data disk stop recharge failed.")
		return err
	}
	return nil
}

func (c *CR) updateSettingsByAdminOnce(runtimeUUID constant.ContainerRuntimeUUID, payload string) error {
	var req constant.ContainerUpdateSettingsByAdminReq

	err := json.Unmarshal([]byte(payload), &req)
	if err != nil {
		c.l.WithField("runtime_uuid", runtimeUUID).WarnE(err, "Unmarshal ContainerUpdateStatusByResultOptReq failed.")
		return err
	}

	container, err := c.crud.GetContainer(runtimeUUID)
	if err != nil {
		cl := logger.NewLogger("GetContainerFailed").WithField("runtime_uuid", runtimeUUID)
		cl.WarnE(err, "updateSettingsByAdminOnce() get container failed. payload: %+v", payload)
		return err
	}

	container.ContainerParam.UpdateCPUAndMemSettingsBy(req.ContainerParamCPUAndMemRequest)
	container.ContainerParam.UpdateAdditionalDisk(req.AdditionalDiskList)
	container.ContainerParam.UpdateLocalDiskSize(req.MaxLocalDiskSizeInByte)
	container.ContainerParam.UpdateAdditionalPort(req.AdditionalPortList)
	container.ContainerParam.UpdateShmSize(req.ShmSize)
	container.ContainerParam.CalculateAll()

	err = c.crud.UpdateContainerParam(runtimeUUID, *container.ContainerParam)
	if err != nil {
		c.l.WithField("runtime_uuid", runtimeUUID).WarnE(err, "UpdateContainerParam failed.")
		return err
	}

	c.l.WithField("runtime_uuid", runtimeUUID).Info("Updated container param by admin: %+v", payload)
	return nil
}
