package service

import (
	"database/sql"
	"fmt"
	"math"
	"server/conf"
	"server/pkg-agent/agent_constant"
	containerModel "server/pkg-core/container_runtime/model"
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/logger"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"gorm.io/gorm"
)

func (c *CR) beforeCtrlOperate(req constant.OptContainerReq) (isUseful bool, err error) {
	l := logger.NewLogger("ContainerBeforeOpt").WithFields(logger.Fields{
		"runtime_uuid": req.RuntimeUUID,
		"payload":      req.Payload,
		"opt":          req.Opt,
	})

	isUseful = true
	defer func() {
		isUseful = isUseful && err == nil // no err
	}()

	if !req.Opt.IsCtrlOpt() {
		return // omit
	}

	container, err := c.crud.GetContainer(req.RuntimeUUID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			isUseful = false
			err = nil
			return
		}
		return
	}

	// 检查机器健康状态. 由于存在强制下架之后关机的操作, 此处应该验证机器的真正心跳在线状态. 在 ctrl 层验证上架下架的业务状态.
	// 关机和释放时不检查状态.
	if req.Opt.NeedMachineRealHealth() {
		ok := c.mod.CheckMachineRealHealth(container.MachineID, container.RuntimeType)
		if !ok {
			// save the history
			l.WithField("machine_id", container.MachineID).Warn("Machine is not healthy.")
			err = biz.ErrMachineUnhealthy
			return
		}
	}

	containerStatus := container.LatestStatus.ContainerStatus

	//isUseful = containerStatus.IsUsefulCommandOpt(req.Opt)
	//if !isUseful {
	//	l.WithField("last_status", containerStatus).Info("Drop unuseful operation.")
	//	return
	//}

	// 对应操作的 ing 状态变更, no hook
	ingStat, exist := constant.NextContainerStatusByOpt(containerStatus, req.Opt)
	if exist {
		err = c.updateStatusWithHook(req.RuntimeUUID, constant.ContainerStatusUpdateReq{
			ValidAt:         c.t.Now(),
			ContainerStatus: ingStat,
			Operate:         req.Opt.String(),
		})
		if err != nil {
			l.WithField("xx_ing_status", ingStat).Info("Update xx_ing status to container failed.")
			return isUseful, err
		}
		l.Info("Updated xx-ing status to container runtime before publishing to agent: %s", ingStat)
	}

	return
}

func (c *CR) getAgentHandler(req constant.OptContainerReq, l *logger.Logger) (agentHandler AgentHandler, err error) {
	if req.Opt == constant.ContainerInsertRecordOpt {
		return
	}
	container, err := c.crud.GetContainer(req.RuntimeUUID)
	if err != nil {
		// 如果恰巧创建与查找同时发生，没有找到， 这里就会返回record not found， 这是正常现象，比如读写分离延迟导致
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 再找一次
			time.Sleep(time.Millisecond * 5)
			container, err = c.crud.GetContainer(req.RuntimeUUID)
			if err != nil {
				return agentHandler, err
			}
		} else {
			return
		}
	}

	// 根据不同 opt 对启动选项进行临时更改.
	err = c.optHookBeforeCtrlOperate(req, &container, l)
	if err != nil {
		l.Info("Mod container params with special opt failed.")
		return
	}

	l.Info("getAgentHandler:%+v", container.ContainerParam)

	agentHandler = NewAgentHandler(container.MachineID, *container.ContainerParam, *container.RuntimeParam, c.t, c.q)
	return
}

func (c *CR) optHookBeforeCtrlOperate(req constant.OptContainerReq, container *containerModel.Container, l *logger.Logger) (err error) {
	// NOTE: l 的上层已经有足够的 fields 了
	container.ContainerParam.SetEnv(constant.ContainerRegionEnvKey, container.RegionSign.String())
	container.ContainerParam.SetEnv(constant.ContainerUUID, container.ProductUUID)
	container.ContainerParam.SetEnv(constant.ContainerEnvAutodlAutoPanelToken, container.ContainerParam.JupyterToken)

	if container.RegionSign.String() != "" {
		region, err := c.mod.GetRegionDetail(container.RegionSign)
		if err != nil {
			return err
		}
		container.ContainerParam.SetEnv(constant.ContainerEnvAutodlDataCenter, region.DataCenter)
	}

	// 只有实例需要.
	if container.RuntimeType != constant.ContainerRuntimeOfInstance &&
		container.RuntimeType != constant.ContainerRuntimeOfDeployment {
		return nil
	}

	machine, err := c.mod.GetMachine(container.MachineID)
	if err != nil {
		return
	}
	if machine.PrivateNetAccess {
		container.ContainerParam.SetEnv(constant.ContainerEnvAgentHost, machine.PrivateNetIP)
	}

	switch req.Opt {
	case constant.ContainerStartOpt, constant.ContainerRestartOpt:
		// 此处改为有上层通过payload传过来
		startPayload := containerModel.ContainerStartPayload{}
		_ = startPayload.ParseFromContent(req.Payload)
		container.ContainerParam.SSHPublicKey = startPayload.SSHPublicKey
		container.ContainerParam.AutopanelToken = startPayload.AutopanelToken
		if container.RuntimeType == constant.ContainerRuntimeOfDeployment {
			container.ContainerParam.SetEnv(constant.AutoDLDeploymentUUID, startPayload.DeploymentUUID)
		}

		cpuCoreNum := "1"
		if startPayload.StartMode == constant.ContainerStartWithGPU {
			cpuCoreNum = strconv.Itoa(int(math.Ceil(container.ContainerParam.CpuLimit)))
		}
		container.ContainerParam.SetEnv(constant.ContainerEnv_OMP_NUM_THREADS, cpuCoreNum)
		container.ContainerParam.SetEnv(constant.ContainerEnv_MKL_NUM_THREADS, cpuCoreNum)
		if container.ContainerParam.TensorboardDomain != "" {
			// 根据 region sign 获取 port
			container.ContainerParam.SetEnv(constant.ContainerEnvDefineSvcAddr, "https://"+strings.TrimSpace(container.ContainerParam.TensorboardDomain)+startPayload.TensorboardDomainForVisit)
		} else if container.ContainerParam.ProxyHostPublic != "" {
			container.ContainerParam.SetEnv(constant.ContainerEnvDefineSvcAddr, "http://"+strings.TrimSpace(container.ContainerParam.GetContainerProxy())+":"+strconv.Itoa(container.ContainerParam.TensorboardPort))
		}

		// 监控设置
		container.ContainerParam.SetEnv(constant.ContainerEnvContainerMonitorSetting, startPayload.ContainerMonitorSetting)

		// 2. 每次开机之前检查一下 mount&proxy. 并更新到数据库中. 不影响管理员的特殊设置.
		err = c.startOptHookOfRefreshContainerParams(container, startPayload, l)
		if err != nil {
			return err
		}

		// 3. 启动模式的设定, 不更新
		err = c.startOptHookOfSetStartMode(container, startPayload, req.Opt, l)
		if err != nil {
			return err
		}

	case constant.ContainerRemoveOpt:
		// 如果还没有创建, 接到了移除请求, 则直接通过 update 状态的方式, 让实例 hook 删除字段
		err = c.removeOptHookOfInsertedStatus(container, l)
		if err != nil {
			return err
		}
	}
	return
}

func (c *CR) startOptHookOfSetStartMode(container *containerModel.Container, startPayload containerModel.ContainerStartPayload, opt constant.OptType, l *logger.Logger) error {
	if startPayload.StartMode.IsNonGPU() {
		// 此处判断逻辑移动到instance中

		// if opt != constant.ContainerRestartOpt {
		// 	can, err := c.CheckUserRunningNonGPULimit(container.UID, startPayload.UserNonGpuLimit)
		// 	if err != nil {
		// 		return err
		// 	}

		// 	if !can {
		// 		l.Info("User_%d cannot start over %d running non_gpu containers.", container.UID, startPayload.UserNonGpuLimit)
		// 		return biz.ErrContainerNonGPULimited.New().Format(startPayload.UserNonGpuLimit)
		// 	}
		// }

		// 1.2 根据无 GPU 启动, 分配资源. 无卡模式配置为: 0.5 核心 CPU, 2GB 内存, 无 GPU 卡.
		container.ContainerParam.ToMinimumSettings()
	}

	// add flag: start_mode
	err := c.crud.UpdateContainerStartMode(container.RuntimeUUID, startPayload.StartMode)
	if err != nil {
		l.WarnE(err, "UpdateContainerStartMode() to '%s' failed.", startPayload.StartMode)
		return err
	}

	l.Info("User_%d will start container in mode [%s].", container.UID, startPayload.StartMode)
	return nil
}

// 本操作会更新网盘, proxy, port, 地区. 地区以 machine 为准.
func (c *CR) startOptHookOfRefreshContainerParams(container *containerModel.Container, startPayload containerModel.ContainerStartPayload, l *logger.Logger) error {
	// NOTE: l 的上层已经有足够的 fields 了

	// 1. 更新地区
	rs, err := c.mod.GetMachineRegionSign(container.MachineID)
	if err != nil {
		return err
	}

	if container.RegionSign != rs {
		container.RegionSign = rs

		err = c.crud.UpdateContainerRegionSign(container.RuntimeUUID, container.RegionSign)
		if err != nil {
			l.WarnE(err, "Update container region sign failed.")
			return err
		}

		l.Info("Container region_sign: '%s' updated.", container.RegionSign)
	}

	region, err := c.mod.GetRegionDetail(rs)
	if err != nil {
		return err
	}

	// 2. 更新 mount， proxy， port, gpu. 这些资源都是可重复申请的, 第二次申请时还是之前分配好的资源.
	latestInfo := startPayload.LatestInfo

	var adfsChanged = true
	haveMount := false
	fsImageName := ""
	command := ""
	env := []string{}
	bindingPathList := []string{}
	if latestInfo.ExistExclusiveNfs {
		haveMount = true
		fsImageName = agent_constant.ExclusivenfsV2
		//command = fmt.Sprintf("bash /root/mount.sh %s", latestInfo.ExclusiveNfsAddr)
		command = fmt.Sprintf("/root/nfs_monitor --nfs_addr=%v --mount_point=%v", latestInfo.ExclusiveNfsAddr, "/storage/data/")
		bindingPathList = []string{fmt.Sprintf(agent_constant.ExclusiveNfsOnMachineMountFmt, container.UID, container.RuntimeUUID) + ":/storage:rw,shared"}
		env = []string{
			fmt.Sprintf("%s=%v", constant.ContainerEnvRegionSignForAdfs, container.RegionSign),
			fmt.Sprintf("%s=%v", constant.ContainerEnvUrlForAdfs, conf.GetGlobalGsConfig().App.BusinessCallbackDomain),
			fmt.Sprintf("%s=%v", constant.ContainerEnvUidForAdfs, container.UID),
			fmt.Sprintf("%s=%v", constant.ContainerEnvContainerRuntimeTypeForAdfs, container.RuntimeType),
		}
	} else if latestInfo.ExistMachineADFSDisk && latestInfo.UserADFSAuth {
		haveMount = true

		if latestInfo.FsType == constant.ADFS {
			fsImageName = agent_constant.Seaweedfs // "registry.cn-beijing.aliyuncs.com/codewithgpu/seaweedfs:3.34ss"
			command = constant.GetADFSCommand(container.UID, region.ADFSFilerAddr, latestInfo.UserADFSQuotaSize, latestInfo.UserADFSQuotaInode, latestInfo.UserADFSConcurrentLimit)
			if len(region.ADFSImageName) > 0 {
				fsImageName = region.ADFSImageName
			}
		} else {
			// 如果是 AutoFS 需要判断一下用的是哪一个文件存储配置。

			autofsFilerAddr := region.AutoFSFilerAddr

			fsImageName = agent_constant.Autofs
			if len(region.AutoFSImageName) > 0 {
				fsImageName = region.AutoFSImageName
			}

			// 如果用户使用了指定的文件存储配置，则根据版本号获取对应版本的文件存储配置。
			if latestInfo.FsConfigVersion != "" {
				if latestInfo.MountHSFS {
					config, ok, err := c.mod.GetHSFSConfig(rs, latestInfo.FsConfigVersion)
					if err != nil {
						l.WithField("sign", rs).WithField("hsfs_config_version", latestInfo.FsConfigVersion).WithError(err).Error("HSFSGetConfig failed")
						return err
					}
					if !ok {
						err = biz.ErrRegionFileStorageConfigNotFound
						l.WithField("sign", rs).WithField("hsfs_config_version", latestInfo.FsConfigVersion).WithError(err).Error("HSFSGetConfig failed")
						return err
					}
					if len(config.AutoFSFilerAddr) > 0 {
						autofsFilerAddr = config.AutoFSFilerAddr
					}
					if len(config.AutoFSImageName) > 0 {
						fsImageName = config.AutoFSImageName
					}
				} else {
					config, ok, err := c.mod.GetFsConfig(rs, latestInfo.FsConfigVersion)
					if err != nil {
						l.WithField("sign", rs).WithField("fs_config_version", latestInfo.FsConfigVersion).WithError(err).Error("GetFsConfig failed")
						return err
					}
					if !ok {
						err = biz.ErrRegionFileStorageConfigNotFound
						l.WithField("sign", rs).WithField("fs_config_version", latestInfo.FsConfigVersion).WithError(err).Error("GetFsConfig failed")
						return err
					}
					if len(config.AutoFSFilerAddr) > 0 {
						autofsFilerAddr = config.AutoFSFilerAddr
					}
					if len(config.AutoFSImageName) > 0 {
						fsImageName = config.AutoFSImageName
					}
				}
			}

			command, env = constant.GetAutoFsCommand(
				container.UID,
				latestInfo.UserAutoFSBufferSize,
				latestInfo.UserAutoFsFSCacheSize,
				latestInfo.UserADFSConcurrentLimit,
				latestInfo.UserFSMaxUploads,
				autofsFilerAddr,
				latestInfo.UserAutoFsNoBGJob,
				latestInfo.FsSubPath,
				latestInfo.MountHSFS,
			)
		}

		bindingPathList = []string{
			fmt.Sprintf(agent_constant.ADFSOnMachineCacheFmt, container.UID, container.RuntimeUUID) + ":/cache:rw",
			fmt.Sprintf(agent_constant.ADFSOnMachineMountFmt, container.UID, container.RuntimeUUID) + ":/storage:rw,shared",
		}
	}
	if haveMount {
		newPreStart := agent_constant.PreStartFunc{
			CreateContainer: &agent_constant.PreStartCreateADFSContainer{
				Image:           fsImageName,
				Command:         command,
				Privileged:      true,
				Network:         "host",
				BindingPathList: bindingPathList,
				WorkingDir:      "/root",
				Env:             env,
			},
		}

		if len(container.ContainerParam.PreStart) > 0 {
			if container.ContainerParam.PreStart[0].CreateContainer != nil &&
				container.ContainerParam.PreStart[0].CreateContainer.Image == newPreStart.CreateContainer.Image &&
				container.ContainerParam.PreStart[0].CreateContainer.Command == newPreStart.CreateContainer.Command {
				adfsChanged = false
			}
		}

		container.ContainerParam.PreStart = []agent_constant.PreStartFunc{newPreStart}

	}

	ports, err := c.mod.RequirePorts(container.RuntimeUUID, int64(container.UID), container.RegionSign, container.ContainerParam.ServicePortProtocol)
	if err != nil {
		l.WarnE(err, "RequirePorts failed before start container opt.")
		return err
	}

	// todo:此处临时加逻辑判断，后面的优先级，都由上层传过来
	priority := container.RuntimeType.ToPriorityType()
	if container.RuntimeType == constant.ContainerRuntimeOfDeployment {
		priority = startPayload.GpuPriority
	}

	gpuList, gpuCaps, err := c.mod.AvailableGpus(container.RuntimeUUID, container.MachineID, priority)
	if err != nil {
		l.WarnE(err, "AvailableGpus failed before start container opt.")
		return err
	}

	// 每次开机前，对adfs的子目录进行初始化
	if latestInfo.FsType == constant.ADFS &&
		latestInfo.FsSubPath != "" &&
		latestInfo.FsSubPath != container.ContainerParam.FsSubPath {
		_ = c.mod.AdfsMkdirBeforeStart(container.UID, region.Sign, latestInfo.FsSubPath)
		time.Sleep(time.Millisecond * 200)
	}

	changed := container.ContainerParam.UpdateBeforeStart(containerModel.ContainerParamUpdateBeforeStartRequest{
		RegionSign:           region.Sign,
		UID:                  container.UID,
		ExistUserNetDisk:     latestInfo.ExistUserNetDisk,
		UserNetDiskQuotaOK:   latestInfo.UserNetDiskQuotaOK,
		ExistMachineNetDisk:  latestInfo.ExistMachineNetDisk,
		ExistMachineADFSDisk: latestInfo.ExistMachineADFSDisk,
		ExistUserADFSDisk:    latestInfo.UserADFSAuth,
		ExistExclusiveNfs:    latestInfo.ExistExclusiveNfs,
		UserAutoFsNoBGJob:    latestInfo.UserAutoFsNoBGJob,
		FsType:               latestInfo.FsType,
		FsSubPath:            latestInfo.FsSubPath,
		ProxyHosts:           latestInfo.ProxyHosts,
		ProxyHost:            latestInfo.ProxyHost,
		ProxyHostPublic:      latestInfo.ProxyHostPublic,
		ProxyPort:            latestInfo.ProxyPort,
		ProxyToken:           latestInfo.ProxyToken,
		Ports:                ports,
		GPUListBeforeStart:   gpuList,
		GPUCapsBeforeStart:   gpuCaps,
	})

	if changed || adfsChanged {
		l.Info("Update latest container param before start opt: %+v", container.ContainerParam)
		err = c.crud.UpdateContainerParam(container.RuntimeUUID, *container.ContainerParam)
		if err != nil {
			l.WarnE(err, "Update latest container param failed.")
			return err
		}
	}

	return nil
}

func (c *CR) removeOptHookOfInsertedStatus(container *containerModel.Container, l *logger.Logger) error {
	if container.LatestStatus.ContainerStatus == constant.ContainerRecordInserted {
		err := c.updateStatusWithHook(container.RuntimeUUID, constant.ContainerStatusUpdateReq{
			ValidAt:         c.t.Now(),
			ContainerStatus: constant.ContainerRemoved,
			Operate:         constant.ContainerRemoveOpt.String(),
		})
		if err != nil {
			l.WarnE(err, "Update 'removed' status to container failed.")
			return err
		}

		l.Info("Removed an 'inserted' container by remove opt.")
	}
	return nil
}

// 结束异步控制类操作之后存入操作历史和 ing 状态
func (c *CR) afterOperate(
	req constant.OptContainerReq,
	dontAt time.Time,
	errMsg string, // "" = success
) (err error) {
	// 存入操作记录
	optHistory := &containerModel.OperateHistory{
		RuntimeUUID: req.RuntimeUUID,
		Operate:     req.Opt,
		Caller:      req.Caller,
		Payload:     req.Payload,
		OptAt:       req.OptAt,
		DoneAt: sql.NullTime{
			Time:  dontAt,
			Valid: true,
		},
		ErrMsg:     errMsg,
		StdMessage: "",
	}

	if req.StdMessage != nil {
		optHistory.StdMessage = req.StdMessage.GetByLang("")
	}

	err = c.crud.InsertContainerOperateHistory(optHistory)
	if err != nil {
		c.l.WarnE(err, "Do afterOperate() failed. req: %+v", req)
		return err
	}

	return nil
}
