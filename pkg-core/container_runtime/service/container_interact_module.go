package service

import (
	"server/pkg-agent/agent_constant"
	"server/pkg-core/api/coreapi"
	containerModel "server/pkg-core/container_runtime/model"
	gpuStockModel "server/pkg-core/gpu_stock/model"
	"server/pkg-core/machine/model"
	"server/pkg-core/module_definition"
	portModel "server/pkg-core/port/model"
	regionModel "server/pkg-core/region/model"
	bcm "server/pkg/billing_center/model"
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	imageModel "server/pkg/image/model"
	"server/pkg/logger"
	"server/plugin/queue"
	"server/plugin/redis_plugin"
	tsc "server/plugin/redis_plugin/time_service_center"
)

const secNameModuleHandler = "container_runtime_to_other_module"

type ModuleHandler interface {
	CheckMachineRealHealth(machineID string, runtimeType constant.ContainerRuntimeType) bool

	RequirePorts(runtimeUUID constant.ContainerRuntimeUUID, uid int64, regionSign constant.RegionSignType, protocol string) (instancePorts portModel.InstancePortDistributor, err error)
	FreePorts(runtimeUUID constant.ContainerRuntimeUUID) error
	ReplaceTensorboardPort(rs constant.RegionSignType, uid int64, runtimeUUID string, srcRuntimeUUID string) error

	AvailableGpus(runtimeUUID constant.ContainerRuntimeUUID, machineID string, level constant.PriorityType) (gpuList []string, gpuCaps []string, err error)
	ReleaseGpus(runtimeUUID constant.ContainerRuntimeUUID, machineID, debugMsg string, timeStamp int64) error
	ReserveGpus(runtimeUUID constant.ContainerRuntimeUUID, productUUID, machineID string, reqGpuAmount int, level constant.PriorityType, timeStamp int64) (ok bool, err error)

	ReserveGpuAndRefreshConfig(
		orderUUID string,
		runtimeType constant.ContainerRuntimeType,
		reserveGpu bool,
		order bcm.Order,
		timeStamp int64,
	) (gpuConfig containerModel.ContainerParamGPURequest, cpuMemConfig constant.ContainerParamCPUAndMemRequest, err error)

	GetMachineRegionSign(machineID string) (rs constant.RegionSignType, err error)

	GetMachine(machineID string) (machine *model.Machine, err error)

	GetMachineStorageOssCredentials(machineID string) (credentials agent_constant.MinioCredentials, err error)
	GetMachineImageStorageOssCredentials(machineID string, imageInfo imageModel.ImageCommonInfo) (bucket agent_constant.MinioBucketInfo, credentials agent_constant.MinioCredentials, err error)
	GetRegionDetail(rs constant.RegionSignType) (region *regionModel.Region, err error)
	AdfsMkdirBeforeStart(uid int, rs constant.RegionSignType, subPath string) error
	GetFsConfig(sign constant.RegionSignType, version string) (config regionModel.FileStorageConfig, ok bool, err error)
}

// ---------------------------------------------------------------------------

type MH struct {
	// 此处持有其他模块的 interface
	machine  module_definition.MachineInference
	port     module_definition.PortInterface
	gpuStock module_definition.GpuStockInterface
	gpuType  module_definition.GpuTypeInference
	region   module_definition.RegionInterface
	dds      module_definition.DataDiskStockInterface
	t        redis_plugin.SyncTimer
	q        *queue.Q
	l        *logger.Logger
}

func NewModuleHandler(
	machineInterface module_definition.MachineInference,
	portInterface module_definition.PortInterface,
	gpuStockInterface module_definition.GpuStockInterface,
	gpuType module_definition.GpuTypeInference,
	region module_definition.RegionInterface,
	dds module_definition.DataDiskStockInterface,
	t redis_plugin.SyncTimer,
	q *queue.Q,
) *MH {
	return &MH{
		machine:  machineInterface,
		port:     portInterface,
		gpuStock: gpuStockInterface,
		gpuType:  gpuType,
		region:   region,
		dds:      dds,
		t:        t,
		q:        q,
		l:        logger.NewLogger(secNameModuleHandler),
	}
}

// CheckMachineRealHealth 检查 false 之后返回 err = biz.ErrMachineUnhealthy. 此处只关心心跳.
func (m *MH) CheckMachineRealHealth(machineID string, runtimeType constant.ContainerRuntimeType) bool {
	machineStatus, err := m.machine.GetMachineStatus(machineID)
	if err != nil {
		m.l.WithField("machine_id", machineID).Error("get machine status from redis failed")
		return false
	}

	m.machine.Get(machineID)

	if machineStatus.HealthStatus == agent_constant.Normal ||
		machineStatus.HealthStatus == agent_constant.StorageMildException ||
		machineStatus.HealthStatus == agent_constant.StorageSeriousException {
		return true
	}

	m.l.WithField("machineID", machineID).WithField("machineStatus", machineStatus.HealthStatus).Error("CheckMachineStatusHealth: machine unhealthy")

	return false
}

// CheckMachineBusinessHealth 此处关心业务上是否上架下架.
func (m *MH) CheckMachineBusinessHealth(machineID string) bool {
	ok, err := m.machine.CheckMachineHealth(machineID)
	if err != nil || !ok {
		return false
	}

	return true
}

func (m *MH) RequirePorts(
	runtimeUUID constant.ContainerRuntimeUUID,
	uid int64,
	regionSign constant.RegionSignType,
	protocol string,
) (instancePorts portModel.InstancePortDistributor, err error) {

	m.l.WithField("runtime_uuid", runtimeUUID).Info("Try to require instance ports.")

	instancePorts, err = m.port.RequirePortsForInstance(regionSign, uid, runtimeUUID.String(), false, protocol)
	if err != nil {
		m.l.WarnE(err, "Container[%s] require ports in region:%s failed.", runtimeUUID, regionSign)
		err = biz.ErrInstanceCreateExceptionOccurred
		return
	}

	ok := instancePorts.Validate()
	if !ok {
		m.l.WarnE(err, "Container[%s] got wrong ports: %+v in region:%s.", runtimeUUID, instancePorts, regionSign)
		err = biz.ErrInternalError
		return
	}

	m.l.Info("Bind ports %+v for Container[%s] in region: %s.", instancePorts, runtimeUUID, regionSign)
	return
}

func (m *MH) FreePorts(runtimeUUID constant.ContainerRuntimeUUID) error {
	err := m.port.FreePorts(runtimeUUID.String())
	if err != nil {
		return err
	}

	m.l.WithField("container_runtime_uuid", runtimeUUID).Info("Free ports for ContainerRuntime.")
	return nil
}

func (m *MH) ReplaceTensorboardPort(rs constant.RegionSignType, uid int64, runtimeUUID string, srcRuntimeUUID string) error {
	err := m.port.ReplaceTensorboardPort(rs, uid, runtimeUUID, srcRuntimeUUID)
	if err != nil {
		return err
	}

	m.l.WithField("container_runtime_uuid", runtimeUUID).WithField("src_container_runtime_uuid", srcRuntimeUUID).Info("ReplaceTensorboardPort")
	return nil
}

func (m *MH) AvailableGpus(runtimeUUID constant.ContainerRuntimeUUID, machineID string, level constant.PriorityType) (gpuList []string, gpuCaps []string, err error) {
	gpuList, gpuCaps, err = m.gpuStock.GetReserveDetail(runtimeUUID.String(), machineID, level)
	if err != nil {
		return
	}

	m.l.WithField("runtime_uuid", runtimeUUID).Info("Get available gpus for ContainerRuntime: %+v", gpuList)
	return
}

func (m *MH) ReleaseGpus(runtimeUUID constant.ContainerRuntimeUUID, machineID, debugMsg string, timeStamp int64) error {
	err := m.gpuStock.Release(&gpuStockModel.GpuRelease{
		RuntimeUUID: runtimeUUID.String(),
		MachineID:   machineID,
		DebugMsg:    debugMsg,
		Timestamp:   timeStamp,
	})
	if err != nil {
		return err
	}

	m.l.WithField("runtime_uuid", runtimeUUID).Info("Unbound gpus for ContainerRuntime.")
	return nil
}

func (m *MH) ReserveGpus(runtimeUUID constant.ContainerRuntimeUUID, productUUID, machineID string, reqGpuAmount int, level constant.PriorityType, timeStamp int64) (ok bool, err error) {
	return m.gpuStock.Reserve(&gpuStockModel.GpuReserve{
		UUID:        productUUID,
		RuntimeUUID: runtimeUUID.String(),
		MachineID:   machineID,
		GpuNum:      reqGpuAmount,
		Priority:    level,
		Timestamp:   timeStamp,
	})
}

func (m *MH) ReserveGpuAndRefreshConfig(
	orderUUID string,
	runtimeType constant.ContainerRuntimeType,
	reserveGpu bool,
	order bcm.Order,
	timeStamp int64,
) (gpuConfig containerModel.ContainerParamGPURequest, cpuMemConfig constant.ContainerParamCPUAndMemRequest, err error) {
	l := logger.NewLogger("ContainerConfigRefresh")

	orderSettings := order.RuntimeEntity
	snapshot := order.MachineEntity
	if orderSettings == nil || snapshot == nil {
		l.Error("order.RuntimeEntity or order.MachineEntity is nil! order_uuid: %s, order: %+v", orderUUID, order)
		err = biz.ErrInternalError
		return
	}

	var regionSign = snapshot.RegionSign
	if len(regionSign) == 0 {
		regionSign, err = m.machine.GetMachineRegion(orderSettings.MachineID)
		if err != nil || len(regionSign) == 0 {
			l.Error("Get region from machine failed! order_uuid: %s, machine: %s.", orderUUID, orderSettings.MachineID)
			err = biz.ErrInternalError
			return
		}
	}

	switch runtimeType {
	case constant.ContainerRuntimeOfInstance:
		gpuPriorityLevel := runtimeType.ToPriorityType()

		req := order.RuntimeEntity
		if reserveGpu {
			ok, err := m.ReserveGpus(req.RuntimeUUID, order.ProductUUID, req.MachineID, req.ReqGPUAmount, gpuPriorityLevel, timeStamp)
			if err != nil || !ok {
				l.WithField("runtime_uuid", req.RuntimeUUID).WarnE(err, "ReserveGpus failed.")
				return gpuConfig, cpuMemConfig, biz.ErrMachineGpuNumNotEnough
			}
			err = m.gpuStock.ReserveCommit(&coreapi.GpuReserveCommitReq{
				RuntimeUUID: req.RuntimeUUID.String(),
				MachineID:   req.MachineID,
				Timestamp:   tsc.Timestamp(),
			})
			if err != nil {
				l.WithField("runtime_uuid", req.RuntimeUUID).WarnE(err, "ReserveGpus commit failed.")
				return gpuConfig, cpuMemConfig, biz.ErrMachineGpuNumNotEnough
			}
		}

		gpuList, gpuCaps, err := m.AvailableGpus(req.RuntimeUUID, req.MachineID, gpuPriorityLevel)
		if err != nil {
			l.WithField("runtime_uuid", req.RuntimeUUID).WarnE(err, "AvailableGpus failed.")
			return gpuConfig, cpuMemConfig, err
		}

		gpuConfig = containerModel.ContainerParamGPURequest{
			// gpu
			ReqGpuAmount: req.ReqGPUAmount,
			GpuList:      gpuList,
			GpuCaps:      gpuCaps,
		}

		cpuMemConfig = constant.ContainerParamCPUAndMemRequest{
			CpuLimit:       float64(snapshot.CpuUsed),
			MemLimitInByte: snapshot.MemUsed,
		}

	case constant.ContainerRuntimeOfDeployment:
		gpuPriorityLevel := order.RuntimeEntity.GetDeploymentContainerPriority()

		req := order.RuntimeEntity
		if reserveGpu {
			ok, err := m.ReserveGpus(req.RuntimeUUID, order.ProductUUID, req.MachineID, req.ReqGPUAmount, gpuPriorityLevel, timeStamp)
			if err != nil || !ok {
				l.WithField("runtime_uuid", req.RuntimeUUID).WarnE(err, "ReserveGpus failed.")
				return gpuConfig, cpuMemConfig, biz.ErrMachineGpuNumNotEnough
			}
			err = m.gpuStock.ReserveCommit(&coreapi.GpuReserveCommitReq{
				RuntimeUUID: req.RuntimeUUID.String(),
				MachineID:   req.MachineID,
				Timestamp:   tsc.Timestamp(),
			})
			if err != nil {
				l.WithField("runtime_uuid", req.RuntimeUUID).WarnE(err, "ReserveGpus commit failed.")
				return gpuConfig, cpuMemConfig, biz.ErrMachineGpuNumNotEnough
			}
		}

		gpuList, gpuCaps, err := m.AvailableGpus(req.RuntimeUUID, req.MachineID, gpuPriorityLevel)
		if err != nil {
			l.WithField("runtime_uuid", req.RuntimeUUID).WarnE(err, "AvailableGpus failed.")
			return gpuConfig, cpuMemConfig, err
		}

		gpuConfig = containerModel.ContainerParamGPURequest{
			// gpu
			ReqGpuAmount: req.ReqGPUAmount,
			GpuList:      gpuList,
			GpuCaps:      gpuCaps,
		}

		cpuMemConfig = constant.ContainerParamCPUAndMemRequest{
			CpuLimit:       float64(snapshot.CpuUsed),
			MemLimitInByte: snapshot.MemUsed,
		}

	default:
		m.l.Error("new case for runtimeType %s in func ReserveGpuAndRefreshConfig()", runtimeType)
		err = biz.ErrInternalError
		return gpuConfig, cpuMemConfig, err
	}

	m.l.WithField("order_uuid", orderUUID).Info("Pre-Build container param gpuConfig when payg starting by order.")
	return
}

func (m *MH) GetMachineRegionSign(machineID string) (rs constant.RegionSignType, err error) {
	rs, err = m.machine.GetMachineRegion(machineID)
	if err != nil {
		return
	}

	if len(rs) == 0 {
		return rs, biz.ErrInternalError
	}
	return
}

func (m *MH) GetMachine(machineID string) (machine *model.Machine, err error) {
	return m.machine.Get(machineID)
}

func (m *MH) GetMachineStorageOssCredentials(machineID string) (credentials agent_constant.MinioCredentials, err error) {
	return m.machine.GetMachineStorageOssCredentials(machineID)
}

func (m *MH) GetMachineImageStorageOssCredentials(machineID string, commonImage imageModel.ImageCommonInfo) (bucket agent_constant.MinioBucketInfo, credentials agent_constant.MinioCredentials, err error) {
	bucket = agent_constant.MinioBucketInfo{
		BucketName: commonImage.BucketName,
		ObjectName: commonImage.ObjectName,
		ObjectSize: commonImage.ImageSize,
	}
	credentials = agent_constant.MinioCredentials{
		Endpoint:        commonImage.StorageOSS.PublicAddr,
		AccessKeyID:     commonImage.StorageOSS.AccessKey,
		SecretAccessKey: commonImage.StorageOSS.SecretKey,
	}

	defer func() {
		m.l.WithFields(logger.Fields{
			"machine id": machineID,
			"image uuid": commonImage.ImageUUID,
			"oss addr":   credentials.Endpoint,
		}).Trace("finial addr in this case")
	}()

	machine, getMachineErr := m.GetMachine(machineID)
	if getMachineErr != nil {
		m.l.WithField("machine id", machine).ErrorE(err, "get machine failed")
		return
	}
	if !machine.PrivateNetAccess {
		return
	}

	var storageInfo regionModel.RegionStorageOSSDetailList
	_, storageInfo, err = m.region.GetRegionDetailWithStorageInfo(machine.RegionSign)
	if err != nil {
		m.l.WithField("machine id", machine).ErrorE(err, "GetRegionDetailWithStorageInfo failed")
		return
	}

	for _, v := range storageInfo {
		if v.StorageOSSSign == commonImage.StorageOssSign {
			if v.PrivateNetAccess {
				credentials.Endpoint = commonImage.StorageOSS.PrivateAddr
				m.l.WithFields(logger.Fields{
					"machine id": machineID,
					"image uuid": commonImage.ImageUUID,
					"oss addr":   credentials.Endpoint,
				}).Trace("use private addr in this case")
			}
		}
	}

	return
}

func (m *MH) GetRegionDetail(rs constant.RegionSignType) (region *regionModel.Region, err error) {
	return m.region.GetRegionDetail(rs)
}

func (m *MH) AdfsMkdirBeforeStart(uid int, rs constant.RegionSignType, subPath string) error {
	return m.region.AdfsMkdir(uid, rs, subPath)
}

func (m *MH) GetFsConfig(sign constant.RegionSignType, version string) (config regionModel.FileStorageConfig, ok bool, err error) {
	return m.region.GetFsConfig(sign, version)
}
