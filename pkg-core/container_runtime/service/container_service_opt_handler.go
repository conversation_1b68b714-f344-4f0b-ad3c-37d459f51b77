package service

import (
	"encoding/json"
	"errors"
	"server/conf"
	"server/pkg-agent/agent_constant"
	containerModel "server/pkg-core/container_runtime/model"
	"server/pkg-core/machine/model"
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	imageModel "server/pkg/image/model"
	"server/pkg/logger"
	"strconv"
	"time"

	"gorm.io/gorm"
)

type handler func(agent AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error)

/* TODO
 * 	case constant.ContainerRenewalOrderTakeEffect, constant.ContainerChangeChargeType:
		err = c.crud.UpdateContainerOrderUUID(req.RuntimeUUID, req.Payload)
		if err != nil {
			return err
		}

		err = c.crud.UpdateContainerStartMode(req.RuntimeUUID, constant.ContainerStartWithGPU)
		if err != nil {
			return err
		}
*/

func (c *CR) initHandlerMap() {
	if len(c.handlers) != 0 {
		return
	}

	c.handlers = map[constant.OptType]handler{
		// insert
		constant.ContainerInsertRecordOpt: c.handlerOfInsertRecord, // √

		// update
		constant.ContainerUpdateStatusOpt:           c.handlerOfUpdateStatus,                // √
		constant.ContainerUpdateStatusByResultOpt:   c.handlerOfUpdateStatusByResult,        // √
		constant.ContainerReleaseGpuOpt:             c.handlerOfReleaseGPU,                  // √
		constant.ContainerReserveGpuByOrderOpt:      c.handlerOfReserveGPUByPaygStart,       // √
		constant.ContainerUpdateByRenew:             c.handlerOfReserveGPUByRenew,           // √
		constant.ContainerUpdateSettingsByUser:      c.handlerOfUpdateSettingsByUser,        // √
		constant.ContainerUpdateSettingsByAdmin:     c.handlerOfUpdateSettingsByAdmin,       // √
		constant.ContainerChangeImageOpt:            c.handlerOfChangeImage,                 // √
		constant.ContainerDeleteRecordOpt:           c.handlerOfDeleteContainerRecord,       // √
		constant.ContainerRenewalOrderTakeEffect:    c.handlerOfUpdateContainerOrderUUID,    // √
		constant.ContainerChangeChargeType:          c.handlerOfUpdateContainerOrderUUID,    // √
		constant.ContainerSaveImageOpt:              c.handlerSaveImage,                     // √
		constant.ContainerCancelSaveImageOpt:        c.handlerCancelSaveImage,               // √
		constant.ContainerUpdateDataDiskSize:        c.handlerOfContainerUpdateDataDiskSize, // √
		constant.ContainerDCUpdateContainerParamOpt: c.handlerDCUpdateContainerParam,
		constant.ContainerUpdateStoppedAt:           c.handlerOfContainerUpdateStoppedAt, // √
		constant.ContainerUpdateSSHPwd:              c.handlerOfContainerUpdateSSHPwd,    // √
		constant.ContainerUpdateProtocol:            c.handlerOfContainerUpdateProtocol,
		constant.ContainerTransPrepay:               c.handlerOfTransPrepayByAdmin,

		//constant.ContainerCloneLockOpt: c.handlerOfAbort,     // √
		constant.ContainerCleanDiffOpt: c.handlerOfCleanDiff, // √

		// ctrl by user or mq
		constant.ContainerCreateOpt:  c.handlerOfCreate,  // √
		constant.ContainerStartOpt:   c.handlerOfStart,   // √
		constant.ContainerStopOpt:    c.handlerOfStop,    // √
		constant.ContainerReInitOpt:  c.handlerOfReInit,  // √
		constant.ContainerRemoveOpt:  c.handlerOfRemove,  // √
		constant.ContainerRestartOpt: c.handlerOfRestart, //
		constant.ContainerCloneOpt:   c.handlerOfClone,   // √
		//constant.ContainerUploadDiffOpt:  c.handlerOfUploadDiff,         // √
		constant.ContainerMergeDiffOpt:  c.handlerOfMergeDiff,          // √
		constant.DataTransferType:       c.handlerOfDataTransfer,       // √
		constant.DataTransferCancelType: c.handlerOfDataTransferCancel, // √
	}
}

func (c *CR) getHandler(opt constant.OptType) (handler, bool) {
	c.initHandlerMap()
	h, ok := c.handlers[opt]
	if !ok {
		return c.handlerOfTODO, false
	}
	return h, ok
}

func (c *CR) handlerOfTODO(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	l.WithField("opt", req.Opt).Error("Panic! implement me: TODO Handler")
	return false, nil
}

func (c *CR) handlerOfRestart(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true
	// 此处上层传了payload，但是payload不在此处解析，在 optHookBeforeCtrlOperate 这里解析使用

	err = agentHandler.PubRestartContainerCommand()
	if err != nil {
		l.WarnE(err, "Pub restart container cmd to agent failed.")
		return
	}

	l.Info("Pub restart container cmd to agent.")
	return
}

func (c *CR) handlerOfInsertRecord(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true

	var container containerModel.Container
	err = json.Unmarshal([]byte(req.Payload), &container)
	if err != nil {
		return
	}

	c.l.Info("[Container] Get insert opt: %+v", req)

	err = c.crud.InsertContainerRecord(&container)
	if err != nil {
		l.WithField("container", container).WarnE(err, "Insert container runtime record failed.")
		return
	}

	err = c.updateStatusWithHook(container.RuntimeUUID, constant.ContainerStatusUpdateReq{
		ValidAt:         time.Now(),
		ContainerStatus: constant.ContainerRecordInserted,
		Operate:         constant.ContainerInsertRecordOpt.String(),
		Message:         "",
	})
	if err != nil {
		l.WarnE(err, "updateStatusWithHook() to inserted failed.")
		return
	}

	l.Info("Container runtime record inserted. %s", container.RuntimeUUID)
	return true, nil
}

func (c *CR) handlerOfCreate(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true

	var downloadPrivateImageInfo *agent_constant.DownloadPrivateImageInfo
	if req.Payload != "" {
		downloadPrivateImageInfo = &agent_constant.DownloadPrivateImageInfo{}
		err = json.Unmarshal([]byte(req.Payload), &downloadPrivateImageInfo)
		if err != nil {
			return
		}
	}
	container, err := c.crud.GetContainer(req.RuntimeUUID)
	if err != nil {
		l.WarnE(err, "get container failed.")
		return
	}

	err = agentHandler.PubCreateContainerCommand(container.ContainerParam.Tags, downloadPrivateImageInfo)
	if err != nil {
		l.WarnE(err, "Pub create container cmd to agent failed.")
		return
	}

	l.Info("Pub create container cmd to agent.")
	return
}

func (c *CR) handlerOfStart(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true
	// 此处上层传了payload，但是payload不在此处解析，在 optHookBeforeCtrlOperate 这里解析使用

	err = agentHandler.PubStartContainerCommand()
	if err != nil {
		l.WarnE(err, "Pub start container cmd to agent failed.")
		return
	}

	l.Info("Pub start container cmd to agent.")
	return
}

func (c *CR) handlerOfStop(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true

	err = agentHandler.PubStopContainerCommand(req.Payload)
	if err != nil {
		l.WarnE(err, "Pub stop container cmd to agent failed.")
		return
	}

	l.Info("Pub stop container cmd to agent.")
	return
}

func (c *CR) handlerOfReInit(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true
	var imageInfo imageModel.ImageCommonInfo
	err = json.Unmarshal([]byte(req.Payload), &imageInfo)
	if err != nil {
		return
	}
	container, err := c.crud.GetContainer(req.RuntimeUUID)
	if err != nil {
		l.WarnE(err, "get container failed.")
		return
	}
	usage, exist := c.cu.GetContainerUsageInfo(req.RuntimeUUID.String())
	if exist {
		usage.DownloadImageProgress = 0
		usage.PullImageProgress = 0
		usage.DownloadOssFileProgress = 0
		err = c.cu.SetContainerUsageInfo(req.RuntimeUUID.String(), usage)
		if err != nil {
			l.WarnE(err, "set container usage failed, runtime_uuid: %s", req.RuntimeUUID.String())
			return saveHistory, err
		}
	}
	var downloadPrivateImageInfo *agent_constant.DownloadPrivateImageInfo
	if container.ContainerParam.PrivateImageUUID != "" {
		downloadPrivateImageInfo = &agent_constant.DownloadPrivateImageInfo{}
		downloadPrivateImageInfo.MinioBucketInfo, downloadPrivateImageInfo.MinioCredentials, err = c.mod.GetMachineImageStorageOssCredentials(container.MachineID, imageInfo)
		if err != nil {
			l.WarnE(err, "get private image failed")
			return saveHistory, err
		}
	}
	err = agentHandler.PubReInitContainerCommand(container.ContainerParam.Tags, downloadPrivateImageInfo)
	if err != nil {
		l.WarnE(err, "Pub re-init container cmd to agent failed.")
		return
	}

	l.Info("Pub re-init container cmd to agent.")
	return
}

func (c *CR) handlerOfRemove(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true

	// 1. release
	err = c.releaseAllResourcesOnce(req.RuntimeUUID, req.Timestamp)
	if err != nil {
		l.WarnE(err, "releaseAllResourcesOnce() failed.")
		return
	}
	l.Info("Released gpu and ports when publishing remove container cmd to agent.")

	// 2. get containerModel.
	container, err := c.crud.GetContainer(req.RuntimeUUID)
	if err != nil {
		l.ErrorE(err, "get container failed.")
		return false, err
	}

	if container.RuntimeType == constant.ContainerRuntimeOfInstance {
		// tag assigned_remove_time
		var assigned_remove_time time.Time
		if req.Caller == constant.OptCallerInstanceCron {
			assigned_remove_time = time.Now().Add(time.Hour * 48)
			if conf.GetGlobalGsConfig().App.DebugApi {
				assigned_remove_time = time.Now().Add(30 * time.Minute)
			}
		} else if req.Caller.IsUser() || req.Caller == constant.OptCallerAdmin {
			assigned_remove_time = time.Now().Add(time.Hour * 12)
			if conf.GetGlobalGsConfig().App.DebugApi {
				assigned_remove_time = time.Now().Add(15 * time.Minute)
			}
		}

		err = container.ContainerUpdate(nil, &db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"runtime_uuid": req.RuntimeUUID,
			},
			NullField: []string{"assigned_remove_time"},
		}, map[string]interface{}{
			"assigned_remove_time": assigned_remove_time,
		})
		if err != nil {
			l.ErrorE(err, "update container assigned_remove_time field failed")
			return
		}

		l.Info("container has been assigned remove time.")
		return
	}

	// 3. pub remove cmd to agent except instance
	err = agentHandler.PubRemoveContainerCommand()
	if err != nil {
		l.WarnE(err, "Pub remove container cmd to agent failed.")
		return
	}

	l.Info("Pub remove container cmd to agent.")
	return
}

// 接到克隆命令的容器为原容器
func (c *CR) handlerOfClone(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true
	c.l.Info("Get clone container req :%+v", req)

	err = agentHandler.PubDataTransferCommand(req.Payload)
	if err != nil {
		l.WarnE(err, "Pub clone container cmd to agent failed.")
		return
	}

	l.Info("Pub clone container cmd to agent.")
	return
}

func (c *CR) handlerOfUpdateStatus(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true

	c.l.Info("Get update status req: %+v", req)
	err = c.updateStatusByPayload(req.RuntimeUUID, req.Payload, req.OptAt, req.Caller)
	if err != nil {
		c.l.WarnE(err, "Handler of update status failed. req: %+v", req)
		return
	}
	return
}

func (c *CR) handlerOfUpdateStatusByResult(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true
	err = c.updateOptResultStatusByPayload(req.RuntimeUUID, req.Payload, req.OptAt)
	if err != nil {
		return
	}
	return
}

func (c *CR) handlerOfReleaseGPU(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true
	err = c.releaseGpuAndUpdateParamOnceAgain(req.RuntimeUUID, req.DebugMsg, req.Timestamp)
	if err != nil {
		return
	}
	return
}

// 按量付费开机. 尝试申请一遍 gpu.
func (c *CR) handlerOfReserveGPUByPaygStart(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true
	var param containerModel.ContainerPayloadParam
	err = json.Unmarshal([]byte(req.Payload), &param)
	if err != nil {
		return
	}
	err = c.reserveGpuByOrderAndUpdateContainerParam(req.RuntimeUUID, true, false, param.Order, req.Timestamp)
	if err != nil {
		return
	}
	return
}

// 包年包月续费, 同按量付费开机. 仅需要根据已申请 gpu 的数量申请一遍 gpu, 不需要更新 order uuid
func (c *CR) handlerOfReserveGPUByRenew(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true
	var param containerModel.ContainerPayloadParam
	err = json.Unmarshal([]byte(req.Payload), &param)
	if err != nil {
		return
	}
	err = c.reserveGpuByOrderAndUpdateContainerParam(req.RuntimeUUID, true, false, param.Order, req.Timestamp)
	if err != nil {
		return
	}
	return
}

// 用户升降配. 会生成新的 order.
func (c *CR) handlerOfUpdateSettingsByUser(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true
	var param containerModel.ContainerPayloadParam
	err = json.Unmarshal([]byte(req.Payload), &param)
	if err != nil {
		return
	}
	orderUUID := param.Order.UUID

	// 1. update order_uuid
	err = c.crud.UpdateContainerOrderUUID(req.RuntimeUUID, orderUUID)
	if err != nil {
		l.WarnE(err, "Update container order_uuid failed.")
		return
	}
	l.Info("The order_uuid of container was changed by user_update_settings. new_order: %s", orderUUID)

	// 2. update container params
	err = c.reserveGpuByOrderAndUpdateContainerParam(req.RuntimeUUID, false, true, param.Order, req.Timestamp)
	if err != nil {
		return
	}
	return
}

// 管理员为特定用户添加配置. 仅更改 container 中的参数, 不会生成 order, 直到用户升降配后, 用户配置会覆盖管理员设定的 cpu, mem，但不会覆盖 disk.
func (c *CR) handlerOfUpdateSettingsByAdmin(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true
	err = c.updateSettingsByAdminOnce(req.RuntimeUUID, req.Payload)
	if err != nil {
		return
	}
	return
}

func (c *CR) handlerOfChangeImage(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true
	imageInfo := constant.ImageInfo{}
	err = json.Unmarshal([]byte(req.Payload), &imageInfo)
	if err != nil {
		l.ErrorE(err, "json.Unmarshal imageInfo failed")
		return
	}

	container, err := c.crud.GetContainer(req.RuntimeUUID)
	if err != nil {
		cl := logger.NewLogger("GetContainerFailed").WithField("runtime_uuid", req.RuntimeUUID)
		cl.WarnE(err, "opt case of change img: get container failed. req: %+v", req)
		return
	}

	container.ContainerParam.UpdateImageSettingsBy(imageInfo.ImageName, imageInfo.SpecialImageUUID, imageInfo.BucketName, imageInfo.ObjectName)
	err = c.crud.UpdateContainerParam(req.RuntimeUUID, *container.ContainerParam)
	if err != nil {
		return
	}
	l.Info("The Image of container was changed. [old] %s, [new] %s", container.ContainerParam.Image, imageInfo.ImageName)

	return
}

func (c *CR) handlerOfDeleteContainerRecord(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true

	// release resources once again.
	err = c.releaseAllResourcesOnce(req.RuntimeUUID, req.Timestamp)
	if err != nil {
		return
	}

	err = c.crud.DeleteContainer(req.RuntimeUUID)
	if err != nil {
		l.WarnE(err, "Soft delete container record failed.")
		return
	}
	l.Info("The record of container was soft deleted.")

	return
}

func (c *CR) handlerOfUpdateContainerOrderUUID(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true

	orderUUID := req.Payload

	err = c.crud.UpdateContainerOrderUUID(req.RuntimeUUID, orderUUID)
	if err != nil {
		return
	}

	// 添加临时规则：在切换包年包月或按量计费后，无条件为关机时间赋值为现在时间。
	//  防止长期不开机的包年包月，转换为按量计费时，
	//  负责实例30天释放的定时任务瞬间以关机时间为标准，将此实例释放的情况。
	// 注意：这样就与 “开机后清零关机时间“ 存在并发问题了，
	//  其他根据关机时间做判断的逻辑需要结合状态一起进行判断。
	container, err := c.crud.GetContainer(req.RuntimeUUID)
	if err != nil {
		return
	}

	if container.LatestStatus.ContainerStatus.IsStop() {
		nowAt := c.t.Now()

		err = c.crud.UpdateContainerStoppedAt(req.RuntimeUUID, nowAt)
		if err != nil {
			return
		}

		l.Info("Container stopped_at was changed by: %+v", nowAt)
	}

	l.Info("Container order_uuid was changed by: %s", orderUUID)

	return
}

func (c *CR) handlerOfMergeDiff(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true

	mig, exist, err := c.CheckMigHistoryInMigratingStatus(req.RuntimeUUID)
	if err != nil {
		l.WarnE(err, "Check migration history failed.")
		return
	}

	if exist {
		err = agentHandler.PubMergeDiffCommand(mig, nil)
		if err != nil {
			l.WarnE(err, "Pub merge container diff cmd to agent failed.")
			return
		}

		l.Info("Pub merge container diff cmd to agent.")
		return
	}
	container, err := c.crud.GetContainer(req.RuntimeUUID)
	if err != nil {
		l.WarnE(err, "get container failed.")
		return
	}
	if container.ContainerParam.PrivateImageUUID == "" {
		l.Info("container DownloadPrivateImageInfo is null")
		err = errors.New("container DownloadPrivateImageInfo is null")
		return
	}

	minioBucketInfo := &agent_constant.MinioBucketInfo{
		BucketName: container.ContainerParam.BucketName,
		ObjectName: container.ContainerParam.ObjectName,
	}
	err = agentHandler.PubMergeDiffCommand(nil, minioBucketInfo)
	if err != nil {
		l.WarnE(err, "Pub merge container diff cmd to agent failed.")
		return
	}
	l.Info("Pub merge container diff cmd to agent.")
	return
}

func (c *CR) handlerOfCleanDiff(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true
	container, err := c.crud.GetContainer(req.RuntimeUUID)
	if err != nil {
		l.WarnE(err, "Check migration history failed.")
		return
	}

	err = agentHandler.SimplePubCleanDiffCommand("", container.ContainerParam.ObjectName)
	if err != nil {
		l.WarnE(err, "Pub merge container diff cmd to agent failed.")
		return
	}

	return
}

func (c *CR) handlerSaveImage(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true
	imageInfo := imageModel.ImageCommonInfo{}
	err = json.Unmarshal([]byte(req.Payload), &imageInfo)
	if err != nil {
		l.ErrorE(err, "json.Unmarshal imageInfo failed")
		return
	}

	privateImage := imageInfo
	minioBucketInfo := agent_constant.MinioBucketInfo{BucketName: privateImage.BucketName, ObjectName: privateImage.ObjectName}
	minioCredentials, err := c.mod.GetMachineStorageOssCredentials(agentHandler.GetMachineID())
	if err != nil {
		l.WarnE(err, "get private image failed")
		return saveHistory, err
	}

	if minioCredentials.StorageOSSRegionSign != privateImage.StorageOssSign {
		l.Error("machine %s private image sign %s not equal selector %s", agentHandler.GetMachineID(), privateImage.StorageOssSign, minioCredentials.StorageOSSRegionSign)
		return saveHistory, err
	}

	err = agentHandler.SaveImageCommand(privateImage.ImageUUID, minioBucketInfo, minioCredentials)
	if err != nil {
		l.WarnE(err, "save image message to agent failed.")
		return
	}
	return
}

func (c *CR) handlerCancelSaveImage(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true
	err = agentHandler.CancelSaveImageCommand(req.Payload)
	if err != nil {
		l.WarnE(err, "cancel save image message to agent failed.")
		return
	}
	return
}

func (c *CR) handlerDCUpdateContainerParam(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	l.Info("handlerDCUpdateContainerParam begin")

	payload := &constant.DCUpdateContainerParamPayload{}
	err = payload.ParseFromString(req.Payload)
	if err != nil {
		l.ErrorE(err, "handlerDCUpdateContainerParam parse container param from string failed")
		return false, err
	}

	container, err := c.crud.GetContainer(req.RuntimeUUID)
	if err != nil {
		l.ErrorE(err, "get container by runtimeUUID failed")
		return
	}

	container.ContainerParam.JupyterToken = payload.JupyterToken
	container.ContainerParam.RootPassword = payload.RootPassword
	container.ContainerParam.PreCmd = payload.PreCmd

	content, err := json.Marshal(container.ContainerParam)
	if err != nil {
		l.ErrorE(err, "handlerDCUpdateContainerParam marshal ContainerParam error")
		return
	}

	err = container.ContainerUpdateByMap(db_helper.QueryFilters{EqualFilters: map[string]interface{}{"runtime_uuid": req.RuntimeUUID}},
		map[string]interface{}{
			"order_uuid":              payload.OrderUUID,
			"product_uuid":            payload.ProductUUID,
			"container_param_content": content,
		})
	if err != nil {
		l.ErrorE(err, "handlerDCUpdateContainerParam update container failed")
		return
	}
	l.Info("handlerDCUpdateContainerParam end")
	return

}

func (c *CR) handlerOfContainerUpdateDataDiskSize(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	var (
		freeDisk int64
	)
	saveHistory = true

	var param containerModel.ContainerPayloadParam
	err = json.Unmarshal([]byte(req.Payload), &param)
	if err != nil {
		return
	}

	order := param.Order
	orderUUID := order.UUID

	// 预付费数据盘扩容订单的uuid绑定在实例的特殊字段中, 不在instance与container的orderUUID中
	if order.OrderType != constant.OrderTypeDataDiskExpandOfPrepay {
		err = c.crud.UpdateContainerOrderUUID(req.RuntimeUUID, orderUUID)
		if err != nil {
			return
		}
	}

	container, err := c.crud.GetContainer(order.RuntimeUUID)
	if err != nil {
		return
	}

	c.l.WithField("MaxLocalDiskSizeInByte", container.ContainerParam.MaxLocalDiskSizeInByte).WithField("orderUUID", orderUUID).Info("update containerParam local disk size begin")
	if order.MachineEntity != nil {
		freeDisk = order.MachineEntity.MaxInstanceDiskSize
	} else {
		var machine *model.Machine
		machine, err = c.mod.GetMachine(order.MachineID)
		if err != nil {
			return
		}
		freeDisk = machine.MaxInstanceDiskSize
	}

	container.ContainerParam.MaxLocalDiskSizeInByte = order.PriceEntity.ExpandDataDisk + freeDisk
	err = c.crud.UpdateContainerParam(req.RuntimeUUID, *container.ContainerParam)
	if err != nil {
		return
	}
	c.l.WithField("MaxLocalDiskSizeInByte", container.ContainerParam.MaxLocalDiskSizeInByte).Info("update containerParam local disk size end")
	return
}

func (c *CR) canCallerDoThisReq(req constant.OptContainerReq, l *logger.Logger) (ok bool) {
	l = l.WithField("step", "canCallerDoThisReq")

	var container containerModel.Container
	var err error

	var notOkButOmitLog bool
	defer func() {
		if !ok {
			if notOkButOmitLog {
				l.Info("Caller of this opt is not permitted! Omit this opt. req=%+v", req)
			} else {
				l.Error("Caller of this opt is not permitted! Omit this opt. req=%+v", req)
			}
		}
	}()

	if req.Opt != constant.ContainerCreateOpt && req.Opt != constant.ContainerInsertRecordOpt {
		container, err = c.crud.GetContainer(req.RuntimeUUID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				l.Info("Omit container opt-caller check. Container not found.")
				notOkButOmitLog = true
				return false
			}
			l.WarnE(err, "Get container failed in container opt-caller check.")
			return false
		}
	}

	// check of remove opt
	switch req.Opt {
	case constant.ContainerRemoveOpt:
		l.Info("Start check of remove opt. caller: %s, opt: %s", req.Caller, req.Opt)

		if req.Caller.IsUser() {
			l.Info("Container remove check passed. Caller is user.")
			return true
		}

		if req.Caller.IsAdmin() {
			l.Info("Container remove check passed. Caller is admin.")
			return true
		}

		switch req.Caller {
		case constant.OptCallerDeploymentRemover:
			if container.RuntimeType == constant.ContainerRuntimeOfDeployment {
				l.Info("Container remove check passed. Rules of deployment caller with deployment.")
				return true
			}
		case constant.OptCallerInstanceCron:
			if container.RegionSign == constant.WaveRegionDefault {
				if c.t.Now().Add(-constant.InstanceWaveAreaFreelyRetained3DaysTimeout).After(container.StatusAt) {
					l.WithFields(logger.Fields{
						"region sign": container.RegionSign,
						"status at":   container.StatusAt.String(),
					}).Info("Container remove check passed. Rules of wave instance cron: 3 days")
					return true
				}
			}

			if c.t.Now().Add(-constant.InstanceFreelyRetained30DaysTimeout).After(container.StoppedAt.Time) ||
				c.t.Now().Add(-constant.InstanceFreelyRetained30DaysTimeout).After(container.StatusAt) {
				// 30 days, status in NeverFreelyRetainedStatusSet
				// TODO: check status
				l.Info("Container remove check passed. Rules of instance cron: 30 days")
				return true
			}

			if c.t.Now().Add(-constant.InstanceFreelyRetained15DaysTimeout).After(container.StoppedAt.Time) ||
				c.t.Now().Add(-constant.InstanceFreelyRetained15DaysTimeout).After(container.StatusAt) {
				for _, tag := range container.ContainerParam.Tags {
					if tag == constant.ContainerTagInstanceAfter20230720 {
						// 15 days, status in NeverFreelyRetainedStatusSet
						// TODO: check status
						l.Info("Container remove check passed. Rules of instance cron: 15 days")
						return true
					}
				}
			}

			if c.t.Now().Add(-constant.InstanceAbortFreelyRetained7DaysTimeout).After(container.StatusAt) {
				// 7 days
				if container.LatestStatus.ContainerStatus == constant.ContainerCloneLocked ||
					container.LatestStatus.ContainerStatus == constant.ContainerCloneFailed {
					// 逻辑等于 svc.container.GetMigrateRecordNeedToRemove(days) by instance.updated_at
					// db = db.Where("status in (?)", []constant.InstanceStatusType{constant.InstanceAbort, constant.InstanceMigrateFailed})
					l.Info("Container remove check passed. Rules of instance cron: db = db.Where(\"status in (?)\", []constant.InstanceStatusType{constant.InstanceAbort, constant.InstanceMigrateFailed})")
					return true
				}
			}

			l.Warn("Container remove check failed. No rules for this case. req=%+v", req)
			return false
		}

		l.Warn("Container remove check failed. No rules for this caller. req=%+v", req)
		return false
	}

	// other type of opt to check...
	return true
}

func (c *CR) handlerOfDataTransfer(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true
	err = agentHandler.PubDataTransferCommand(req.Payload)
	if err != nil {
		l.WarnE(err, "Pub merge container diff cmd to agent failed.")
		return
	}

	return
}

func (c *CR) handlerOfDataTransferCancel(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true
	err = agentHandler.PubDataTransferCancelCommand(req.Payload)
	if err != nil {
		l.WarnE(err, "Pub merge container diff cmd to agent failed.")
		return
	}

	return
}

func (c *CR) handlerOfContainerUpdateStoppedAt(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	days, err := strconv.Atoi(req.Payload)
	if err != nil {
		l.WithField("payload", req.Payload).ErrorE(err, "handlerOfContainerUpdateStoppedAt parse days failed")
		return
	}
	if days <= 0 {
		return
	}

	container, err := c.crud.GetContainer(req.RuntimeUUID)
	if err != nil {
		l.ErrorE(err, "get container failed")
		return
	}
	if !container.StoppedAt.Valid {
		return
	}

	newStoppedAt := container.StoppedAt.Time.AddDate(0, 0, days)
	if newStoppedAt.After(time.Now()) {
		return false, biz.ErrInstanceDelayReleaseError
	}

	err = c.crud.UpdateContainerStoppedAt(req.RuntimeUUID, container.StoppedAt.Time.AddDate(0, 0, days))
	if err != nil {
		l.ErrorE(err, "handlerOfContainerUpdateStoppedAt update time failed")
	}

	return
}

func (c *CR) handlerOfContainerUpdateSSHPwd(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	container, err := c.crud.GetContainer(req.RuntimeUUID)
	if err != nil {
		l.ErrorE(err, "get container failed")
		return
	}
	if req.Payload == "" {
		return
	}

	container.ContainerParam.RootPassword = req.Payload
	err = c.crud.UpdateContainerParam(req.RuntimeUUID, *container.ContainerParam)
	if err != nil {
		l.ErrorE(err, "handlerOfContainerUpdateStoppedAt update time failed")
	}

	return
}

func (c *CR) handlerOfContainerUpdateProtocol(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true
	container, err := c.crud.GetContainer(req.RuntimeUUID)
	if err != nil {
		l.ErrorE(err, "get container failed")
		return
	}
	if req.Payload == "" {
		return
	}

	container.ContainerParam.ServicePortProtocol = req.Payload
	err = c.crud.UpdateContainerParam(req.RuntimeUUID, *container.ContainerParam)
	if err != nil {
		l.ErrorE(err, "handlerOfContainerUpdateProtocol update protocol failed")
	}
	return
}

// admin转移包卡订单，OrderUuid会更新
func (c *CR) handlerOfTransPrepayByAdmin(agentHandler AgentHandler, req constant.OptContainerReq, l *logger.Logger) (saveHistory bool, err error) {
	saveHistory = true

	err = c.crud.UpdateContainerOrderUUID(req.RuntimeUUID, req.Payload)
	if err != nil {
		l.WarnE(err, "Update container order_uuid failed.")
		return
	}
	l.Info("The order_uuid of container was changed. new_order_uuid: %s", req.Payload)

	return
}
