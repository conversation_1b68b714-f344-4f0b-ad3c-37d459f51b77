package controller

import (
	"github.com/gin-gonic/gin"
	containerModel "server/pkg-core/container_runtime/model"
	"server/pkg/db_helper"

	"server/pkg-core/api/coreapi"
	"server/pkg-core/module_definition"
	"server/pkg/businesserror"
	"server/pkg/http"
	"server/pkg/logger"
)

const ModuleName = "container_runtime_controller"

type ContainerRuntimeController struct {
	logger           *logger.Logger
	containerService module_definition.ContainerRuntimeInterface
}

func NewContainerRuntimeControllerProvider(containerService module_definition.ContainerRuntimeInterface) *ContainerRuntimeController {
	return &ContainerRuntimeController{
		logger:           logger.NewLogger(ModuleName),
		containerService: containerService,
	}
}

func (ctrl *ContainerRuntimeController) CheckExistUUID(c *gin.Context) {
	var req coreapi.CheckContainerExistUUIDReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	exist, err := ctrl.containerService.ExistUUID(req.RuntimeUUID)
	if err != nil {
		ctrl.logger.ErrorE(err, "CheckExistUUID failed")
		http.SendError(c, err)
		return
	}
	data := coreapi.CheckContainerExistUUIDData{
		Exist: exist,
	}
	http.SendOK(c, data)
}

func (ctrl *ContainerRuntimeController) GetContainerRecord(c *gin.Context) {
	var req coreapi.GetContainerRecordReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	container, err := ctrl.containerService.GetContainerRecord(req.RuntimeUUID)
	if err != nil {
		ctrl.logger.ErrorE(err, "GetContainerRecord failed，runtimeUUID: %s", req.RuntimeUUID)
		http.SendError(c, err)
		return
	}
	http.SendOK(c, container)
}

func (ctrl *ContainerRuntimeController) GetContainerRecordUnScoped(c *gin.Context) {
	var req coreapi.GetContainerRecordUnScopedReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	container, err := ctrl.containerService.GetContainerRecordUnScoped(req.RuntimeUUID)
	if err != nil {
		ctrl.logger.ErrorE(err, "GetContainerRecordUnScoped failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, container)
}

func (ctrl *ContainerRuntimeController) GetContainerRecords(c *gin.Context) {
	var req coreapi.GetContainerRecordsReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	containers, err := ctrl.containerService.GetContainerRecords(req.RuntimeUUIDs)
	if err != nil {
		ctrl.logger.ErrorE(err, "GetContainerRecords failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, containers)
}

func (ctrl *ContainerRuntimeController) GetContainerSimplifiedInfoList(c *gin.Context) {
	var req coreapi.GetContainerSimplifiedInfoListReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	containers, err := ctrl.containerService.GetContainerSimplifiedInfoList(req.RuntimeUUIDs, req.TaskType)
	if err != nil {
		ctrl.logger.ErrorE(err, "GetContainerSimplifiedInfoList failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, containers)
}

func (ctrl *ContainerRuntimeController) GetContainerSimplifiedInfo(c *gin.Context) {
	var req coreapi.GetContainerSimplifiedInfoReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	containers, err := ctrl.containerService.GetContainerSimplifiedInfo(req.RuntimeUUID, req.TaskType)
	if err != nil {
		ctrl.logger.ErrorE(err, "GetContainerSimplifiedInfo failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, containers)
}

func (ctrl *ContainerRuntimeController) GetContainerSimplifiedInfoListWithUsage(c *gin.Context) {
	var req coreapi.GetContainerSimplifiedInfoListReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	containers, err := ctrl.containerService.GetContainerSimplifiedInfoListWithUsage(req.RuntimeUUIDs, req.TaskType)
	if err != nil {
		ctrl.logger.ErrorE(err, "GetContainerSimplifiedInfoListWithUsage failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, containers)
}

func (ctrl *ContainerRuntimeController) CheckContainerStatusNum(c *gin.Context) {
	var req coreapi.CheckContainerStatusNumReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	count, err := ctrl.containerService.CheckContainerStatusNum(req.RuntimeUUID, req.Status)
	if err != nil {
		ctrl.logger.ErrorE(err, "CheckContainerStatusNum failed")
		http.SendError(c, err)
		return
	}
	data := coreapi.CheckContainerStatusNumData{
		Count: count,
	}
	http.SendOK(c, data)
}

func (ctrl *ContainerRuntimeController) GetContainerOperateHistory(c *gin.Context) {
	var req coreapi.GetContainerOperateHistoryReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	paged, list, err := ctrl.containerService.GetContainerOperateHistory(req.RuntimeUUIDs, req.OperateType, req.Paged)
	if err != nil {
		ctrl.logger.ErrorE(err, "GetContainerOperateHistory failed")
		http.SendError(c, err)
		return
	}
	data := coreapi.GetContainerOperateHistoryData{
		Paged: paged,
		List:  list,
	}
	http.SendOK(c, data)
}

func (ctrl *ContainerRuntimeController) CreateMigHistory(c *gin.Context) {
	var req coreapi.CreateContainerMigHistoryReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	migID, err := ctrl.containerService.CreateMigHistory(req)
	if err != nil {
		ctrl.logger.ErrorE(err, "CreateMigHistory failed")
		http.SendError(c, err)
		return
	}
	data := coreapi.CreateContainerMigHistoryData{
		MigID: migID,
	}
	http.SendOK(c, data)
}

func (ctrl *ContainerRuntimeController) CheckMigHistoryInMigratingStatus(c *gin.Context) {
	var req coreapi.CheckMigHistoryInMigratingStatusReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	history, exist, err := ctrl.containerService.CheckMigHistoryInMigratingStatus(req.RuntimeUUID)
	if err != nil {
		ctrl.logger.ErrorE(err, "CheckMigHistoryInMigratingStatus failed")
		http.SendError(c, err)
		return
	}
	data := coreapi.CheckMigHistoryInMigratingStatusData{
		Mig:   history,
		Exist: exist,
	}
	http.SendOK(c, data)
}

func (ctrl *ContainerRuntimeController) UpdateMigHistoryResult(c *gin.Context) {
	var req coreapi.UpdateContainerMigHistoryResultReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	err := ctrl.containerService.UpdateMigHistoryResult(req.MigID, req.Status, req.Msg)
	if err != nil {
		ctrl.logger.ErrorE(err, "UpdateMigHistoryResult failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)
}

func (ctrl *ContainerRuntimeController) RetryMigHistory(c *gin.Context) {
	var req coreapi.RetryContainerMigHistoryReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	err := ctrl.containerService.RetryMigHistory(req.MigID)
	if err != nil {
		ctrl.logger.ErrorE(err, "RetryMigHistory failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)
}

func (ctrl *ContainerRuntimeController) InsertMigHistory(c *gin.Context) {
	var req coreapi.InsertContainerMigHistoryReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	migID, err := ctrl.containerService.InsertMigHistory(req.Mig)
	if err != nil {
		ctrl.logger.ErrorE(err, "InsertMigHistory failed")
		http.SendError(c, err)
		return
	}
	data := coreapi.InsertContainerMigHistoryData{
		MigID: migID,
	}
	http.SendOK(c, data)
}

func (ctrl *ContainerRuntimeController) GetMigrateByTargetRuntimeUUID(c *gin.Context) {
	var req coreapi.GetContainerMigrateByTargetRuntimeUUIDReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	data, err := ctrl.containerService.GetMigrateByTargetRuntimeUUID(req.RuntimeUUID)
	if err != nil {
		ctrl.logger.ErrorE(err, "GetMigrateByTargetRuntimeUUID failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, data)
}

func (ctrl *ContainerRuntimeController) GetContainerMigrateList(c *gin.Context) {
	var req coreapi.GetContainerMigrateListReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	data, _, err := ctrl.containerService.GetContainerMigrateList(req.UID, &req.PageReq)
	if err != nil {
		ctrl.logger.ErrorE(err, "GetMigrateByTargetRuntimeUUID failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, data)
}

func (ctrl *ContainerRuntimeController) OperateContainer(c *gin.Context) {
	var req coreapi.OperateContainerReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	err := ctrl.containerService.OperateContainer(req.Opt...)
	if err != nil {
		ctrl.logger.WarnE(err, "operate container failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)
}

func (ctrl *ContainerRuntimeController) ForceOperateContainer(c *gin.Context) {
	var req coreapi.ForceOperateContainerReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	info, err := ctrl.containerService.ForceOperateContainer(req.RuntimeUUID, req.TaskType, req.IsStop)
	if err != nil {
		ctrl.logger.WarnE(err, "force operate container failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, info)
}

func (ctrl *ContainerRuntimeController) OperateContainerByPubMQ(c *gin.Context) {
	var req coreapi.OperateContainerByPubMQReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	err := ctrl.containerService.OperateContainerByPubMQ(req.Opt)
	if err != nil {
		ctrl.logger.ErrorE(err, "OperateContainerByPubMQ failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)
}

func (ctrl *ContainerRuntimeController) GetContainerUsageInfo(c *gin.Context) {
	var req coreapi.GetContainerUsageInfoReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	usage, err := ctrl.containerService.GetContainerUsageThenSetInRedis(req.RuntimeUUID)
	if err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, err)
		return
	}
	http.SendOK(c, usage)
}

func (ctrl *ContainerRuntimeController) GetContainerUsageInfoList(c *gin.Context) {
	var req coreapi.GetContainerUsageInfoListReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	usage, err := ctrl.containerService.GetContainerUsageList(req.RuntimeUUIDList)
	if err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, err)
		return
	}
	http.SendOK(c, usage)
}

func (ctrl *ContainerRuntimeController) GetContainerRunningErrRuntimeUuidList(c *gin.Context) {
	var req coreapi.GetContainerRunningErrRuntimeUuidListReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	data, err := ctrl.containerService.GetContainerRunningErrRuntimeUuidList(req.RuntimeUUIDList)
	if err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, err)
		return
	}
	http.SendOK(c, data)
}

func (ctrl *ContainerRuntimeController) RemoveContainerCache(c *gin.Context) {
	var req coreapi.RemoveContainerCacheReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.containerService.RemoveContainerCache(req)
	if err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *ContainerRuntimeController) ContainerCacheRecovery(c *gin.Context) {
	var req coreapi.ContainerCacheRecoveryReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.containerService.ContainerCacheRecovery(req)
	if err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *ContainerRuntimeController) ContainerDeleteByMachine(c *gin.Context) {
	var req coreapi.ContainerDeleteByMachineReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	container := &containerModel.Container{}
	err := container.ContainerDelete(&db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"machine_id": req.MachineID,
		}},
	)
	if err != nil {
		ctrl.logger.WithField("machine_id", req.MachineID).ErrorE(err, "ContainerDeleteByMachine failed")
		http.SendError(c, err)
	}

	http.SendOK(c, nil)
}
