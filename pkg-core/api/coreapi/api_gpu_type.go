package coreapi

import (
	"encoding/json"
	"net/http"
	machineModel "server/pkg-core/machine/model"
	"server/pkg/db_helper"
)

type CreateGpuTypeReq *machineModel.CreateGpuTypeParams

type CreateGpuTypeRes struct {
	ResBase
}

func (api *Api) CreateGpuType(req CreateGpuTypeReq, client *http.Client) (CreateGpuTypeRes, error) {
	body, err := api.post(req, "/api/v1/gpu_type/create", client)
	if err != nil {
		return CreateGpuTypeRes{}, err
	}
	var res CreateGpuTypeRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return CreateGpuTypeRes{}, err
	}
	return res, nil
}

//

type UpdateGpuTypeReq *machineModel.GpuTypeInfo

type UpdateGpuTypeRes struct {
	ResBase
}

func (api *Api) UpdateGpuType(req UpdateGpuTypeReq, client *http.Client) (UpdateGpuTypeRes, error) {
	body, err := api.post(req, "/api/v1/gpu_type/update", client)
	if err != nil {
		return UpdateGpuTypeRes{}, err
	}
	var res UpdateGpuTypeRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return UpdateGpuTypeRes{}, err
	}
	return res, nil
}

//

type GetGpuTypeListReq struct {
	GpuName string `json:"gpu_name"`
	Paged   *db_helper.GetPagedRangeRequest
}

type GetGpuTypeListRes struct {
	ResBase
	Data *db_helper.PagedData `json:"data"`
}

func (api *Api) GetGpuTypeList(req GetGpuTypeListReq, client *http.Client) (GetGpuTypeListRes, error) {
	body, err := api.post(req, "/api/v1/gpu_type/get_list", client)
	if err != nil {
		return GetGpuTypeListRes{}, err
	}
	var res GetGpuTypeListRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetGpuTypeListRes{}, err
	}
	return res, nil
}

//

type DeleteGpuTypeReq struct {
	GpuTypeID int `json:"gpu_type_id"`
}

type DeleteGpuTypeRes struct {
	ResBase
}

func (api *Api) DeleteGpuType(req DeleteGpuTypeReq, client *http.Client) (DeleteGpuTypeRes, error) {
	body, err := api.post(req, "/api/v1/gpu_type/delete", client)
	if err != nil {
		return DeleteGpuTypeRes{}, err
	}
	var res DeleteGpuTypeRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return DeleteGpuTypeRes{}, err
	}
	return res, nil
}

//

type CheckGpuTypeNameReq struct {
	GpuTypeName string `json:"gpu_type_name"`
	GpuTypeID   int    `json:"gpu_type_id"`
}

type CheckGpuTypeNameRes struct {
	ResBase
	Data CheckGpuTypeNameData `json:"data"`
}

type CheckGpuTypeNameData struct {
	Existed bool `json:"existed"`
}

func (api *Api) CheckGpuTypeName(req CheckGpuTypeNameReq, client *http.Client) (CheckGpuTypeNameRes, error) {
	body, err := api.post(req, "/api/v1/gpu_type/check_gpu_type_name", client)
	if err != nil {
		return CheckGpuTypeNameRes{}, err
	}
	var res CheckGpuTypeNameRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return CheckGpuTypeNameRes{}, err
	}
	return res, nil
}

//

type GetGpuIDReq struct {
	GpuTypeNames []string `json:"gpu_type_name"`
}

type GetGpuIDRes struct {
	ResBase
	Data GetGpuIDData `json:"data"`
}

type GetGpuIDData struct {
	IDs []int `json:"i_ds"`
}

func (api *Api) GetGpuID(req GetGpuIDReq, client *http.Client) (GetGpuIDRes, error) {
	body, err := api.post(req, "/api/v1/gpu_type/get_gpu_id", client)
	if err != nil {
		return GetGpuIDRes{}, err
	}
	var res GetGpuIDRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetGpuIDRes{}, err
	}
	return res, nil
}

//

type GetGpuReq struct {
	GpuID int `json:"gpu_id"`
}

type GetGpuRes struct {
	ResBase
	Data *machineModel.GPUType `json:"data"`
}

func (api *Api) GetGpu(req GetGpuReq, client *http.Client) (GetGpuRes, error) {
	body, err := api.post(req, "/api/v1/gpu_type/get", client)
	if err != nil {
		return GetGpuRes{}, err
	}
	var res GetGpuRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetGpuRes{}, err
	}
	return res, nil
}

//

type GetGpuByIDsReq struct {
	GpuIDs []int `json:"gpu_ids"`
}

type GetGpuByIDsRes struct {
	ResBase
	Data []*machineModel.GPUType `json:"data"`
}

func (api *Api) GetGpuByIDs(req GetGpuByIDsReq, client *http.Client) (GetGpuByIDsRes, error) {
	body, err := api.post(req, "/api/v1/gpu_type/get_gpu_by_ids", client)
	if err != nil {
		return GetGpuByIDsRes{}, err
	}
	var res GetGpuByIDsRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetGpuByIDsRes{}, err
	}
	return res, nil
}
