package coreapi

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"server/pkg/businesserror"
	"server/pkg/threadlocal"
	"strings"

	"github.com/pkg/errors"
)

type ResBase struct {
	Code string `json:"code"`
	Msg  string `json:"msg"`
}

func (r ResBase) success() bool {
	return r.Code == "Success"
}

func (r ResBase) GetErr() (err error) {
	if r.success() {
		return nil
	}
	return businesserror.ErrEmptyBox.New().Format(r.Msg)
}

type Api struct {
	host string
}

func New(host string) *Api {
	return &Api{
		host: host,
	}
}

func (api *Api) buildUrl(route string) string {
	return fmt.Sprintf("%s/%s", strings.Trim(api.host, "/"), strings.Trim(route, "/"))
}

// 没有用就可以删除可以删除
//func (api *Api) post(req interface{}, route string, client *http.Client) ([]byte, error) {
//	b, err := json.Marshal(req)
//	if err != nil {
//		return nil, err
//	}
//	reqBody := strings.NewReader(string(b))
//	if client == nil {
//		client = &http.Client{}
//	}
//	resp, err := client.Post(api.buildUrl(route), "application/json", reqBody)
//	if err != nil {
//		return nil, err
//	}
//	defer resp.Body.Close()
//	if resp.StatusCode != 200 {
//		return nil, errors.New(resp.Status)
//	}
//	b, err = ioutil.ReadAll(resp.Body)
//	if err != nil {
//		return nil, err
//	}
//	return b, nil
//}

func (api *Api) post(req interface{}, route string, client *http.Client) ([]byte, error) {
	b, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	reqBody := strings.NewReader(string(b))
	if client == nil {
		client = &http.Client{}
	}

	// 创建请求
	httpReq, err := http.NewRequest("POST", api.buildUrl(route), reqBody)
	if err != nil {
		return nil, err
	}
	httpReq.Header.Set("Content-Type", "application/json")

	// 从 ThreadLocal 获取 Request ID 并设置到请求头
	requestID := threadlocal.GetRequestID()
	if requestID != "" {
		httpReq.Header.Set(threadlocal.HttpHeaderXRequestIDKey, requestID)
	}

	// 发送请求
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, errors.New(resp.Status)
	}

	b, err = ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	return b, nil
}

func (api *Api) Health() (err error) {
	client := &http.Client{}
	resp, err := client.Get(api.host + "/health")
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return errors.Errorf("ping core failed! status code: %d", resp.StatusCode)
	}

	return
}
