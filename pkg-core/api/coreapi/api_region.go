package coreapi

import (
	"encoding/json"
	"net/http"
	"server/pkg-agent/agent_constant"
	regionModel "server/pkg-core/region/model"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/libs"
	"time"
)

type CreateRegionReq struct {
	Sign       constant.RegionSignType `json:"sign"`
	Name       string                  `json:"name"`
	DataCenter string                  `json:"data_center"`

	PortRangeMin int `json:"port_range_min"`
	PortRangeMax int `gjson:"port_range_max"`

	ExportAddr1 string `json:"export_addr_1"`
	ExportAddr2 string `json:"export_addr_2"`

	ProxyToken string `json:"proxy_token"`

	IsNFSAvailable bool `json:"is_nfs_available"`

	NFSAddr string `json:"nfs_addr"`

	NFSPort int `gorm:"column:nfs_port;" json:"nfs_port"`

	NFSHealth constant.NFSMachineStatus `json:"nfs_health"`

	DefaultUserQuotaInByte int64 `json:"default_user_quota_in_byte"`

	Rank int `gorm:"column:rank" json:"rank"`

	ADFSFilerAddr string `json:"adfs_filer_addr"`
	ADFSPubAddr   string `json:"adfs_pub_addr"`
	ADFSPubPort   int    `json:"adfs_pub_port"`
}

type CreateRegionRes struct {
	ResBase
	Data *regionModel.Region `json:"data"`
}

func (api *Api) CreateRegion(req CreateRegionReq, client *http.Client) (CreateRegionRes, error) {
	body, err := api.post(req, "/api/v1/region/create", client)
	if err != nil {
		return CreateRegionRes{}, err
	}
	var res CreateRegionRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return CreateRegionRes{}, err
	}
	return res, nil
}

//

type GetRegionDetailReq struct {
	RegionSignType constant.RegionSignType `json:"region_sign_type"`
}

type GetRegionDetailRes struct {
	ResBase
	Data *regionModel.Region `json:"data"`
}

func (api *Api) GetRegionDetail(req GetRegionDetailReq, client *http.Client) (GetRegionDetailRes, error) {
	body, err := api.post(req, "/api/v1/region/get_detail", client)
	if err != nil {
		return GetRegionDetailRes{}, err
	}
	var res GetRegionDetailRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetRegionDetailRes{}, err
	}
	return res, nil
}

//

type GetRegionListReq struct{}

type GetRegionListRes struct {
	ResBase
	Data []regionModel.Region `json:"data"`
}

func (api *Api) GetRegionList(req GetRegionListReq, client *http.Client) (GetRegionListRes, error) {
	body, err := api.post(req, "/api/v1/region/get_list", client)
	if err != nil {
		return GetRegionListRes{}, err
	}
	var res GetRegionListRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetRegionListRes{}, err
	}
	return res, nil
}

//

type GetRegionDetailWithStorageInfoReq struct {
	RegionSignType constant.RegionSignType `json:"region_sign_type"`
}

type GetRegionDetailWithStorageInfoRes struct {
	ResBase
	Data GetRegionDetailWithStorageInfoData `json:"data"`
}

type GetRegionDetailWithStorageInfoData struct {
	Region      *regionModel.Region                    `json:"region"`
	StorageList regionModel.RegionStorageOSSDetailList `json:"storage_list"`
}

func (api *Api) GetRegionDetailWithStorageInfo(req GetRegionDetailWithStorageInfoReq, client *http.Client) (GetRegionDetailWithStorageInfoRes, error) {
	body, err := api.post(req, "/api/v1/region/get_detail_with_storage_info", client)
	if err != nil {
		return GetRegionDetailWithStorageInfoRes{}, err
	}
	var res GetRegionDetailWithStorageInfoRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetRegionDetailWithStorageInfoRes{}, err
	}
	return res, nil
}

//

type GetStorageDetailReq struct {
	SignType agent_constant.StorageOSSSignType `json:"sign_type"`
}

type GetStorageDetailRes struct {
	ResBase
	Data *regionModel.StorageOSS `json:"data"`
}

func (api *Api) GetStorageDetail(req GetStorageDetailReq, client *http.Client) (GetStorageDetailRes, error) {
	body, err := api.post(req, "/api/v1/region/get_storage_detail", client)
	if err != nil {
		return GetStorageDetailRes{}, err
	}
	var res GetStorageDetailRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetStorageDetailRes{}, err
	}
	return res, nil
}

//

type CheckRegionSignExistReq struct {
	RegionSignType constant.RegionSignType `json:"region_sign_type"`
}

type CheckRegionSignExistRes struct {
	ResBase
	Data CheckRegionSignExistData `json:"data"`
}

type CheckRegionSignExistData struct {
	Exist bool `json:"exist"`
}

func (api *Api) CheckRegionSignExist(req CheckRegionSignExistReq, client *http.Client) (CheckRegionSignExistRes, error) {
	body, err := api.post(req, "/api/v1/region/check_region_sign_exist", client)
	if err != nil {
		return CheckRegionSignExistRes{}, err
	}
	var res CheckRegionSignExistRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return CheckRegionSignExistRes{}, err
	}
	return res, nil
}

//

type CheckRegionNameExistReq struct {
	Name string `json:"name"`
}

type CheckRegionNameExistRes struct {
	ResBase
	Data CheckRegionNameExistData `json:"data"`
}

type CheckRegionNameExistData struct {
	Exist bool `json:"exist"`
}

func (api *Api) CheckRegionNameExist(req CheckRegionNameExistReq, client *http.Client) (CheckRegionNameExistRes, error) {
	body, err := api.post(req, "/api/v1/region/check_region_name_exist", client)
	if err != nil {
		return CheckRegionNameExistRes{}, err
	}
	var res CheckRegionNameExistRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return CheckRegionNameExistRes{}, err
	}
	return res, nil
}

//

type InitNetDiskWithRegionForUserReq struct {
	UID            int                     `json:"uid"`
	RegionSignType constant.RegionSignType `json:"region_sign_type"`
	Tenant         string                  `json:"tenant"`
}

type InitNetDiskWithRegionForUserRes struct {
	ResBase
	Data *regionModel.NetDisk `json:"data"`
}

func (api *Api) InitNetDiskWithRegionForUser(req InitNetDiskWithRegionForUserReq, client *http.Client) (InitNetDiskWithRegionForUserRes, error) {
	body, err := api.post(req, "/api/v1/region/net_disk/init", client)
	if err != nil {
		return InitNetDiskWithRegionForUserRes{}, err
	}
	var res InitNetDiskWithRegionForUserRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return InitNetDiskWithRegionForUserRes{}, err
	}
	return res, nil
}

//

type GetNetDiskListForUserReq struct {
	UID int `json:"uid"`
}

type GetNetDiskListForUserRes struct {
	ResBase
	Data []regionModel.RegionUsageInfo `json:"data"`
}

func (api *Api) GetNetDiskListForUser(req GetNetDiskListForUserReq, client *http.Client) (GetNetDiskListForUserRes, error) {
	body, err := api.post(req, "/api/v1/region/net_disk/get_list", client)
	if err != nil {
		return GetNetDiskListForUserRes{}, err
	}
	var res GetNetDiskListForUserRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetNetDiskListForUserRes{}, err
	}
	return res, nil
}

//

type GetRegionListForIndexReq struct {
}

type GetRegionListForIndexRes struct {
	ResBase
	Data []regionModel.GetRegionListForIndexRes `json:"data"`
}

func (api *Api) GetRegionListForIndex(req GetRegionListForIndexReq, client *http.Client) (GetRegionListForIndexRes, error) {
	body, err := api.post(req, "/api/v1/region/get_list_for_index", client)
	if err != nil {
		return GetRegionListForIndexRes{}, err
	}
	var res GetRegionListForIndexRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetRegionListForIndexRes{}, err
	}
	return res, nil
}

//

type GetUserNetDiskMountForInstanceReq struct {
	UID            int                     `json:"uid"`
	RegionSignType constant.RegionSignType `json:"region_sign_type"`
}

type GetUserNetDiskMountForInstanceRes struct {
	ResBase
	Data regionModel.GetUserNetDiskMountForInstanceRes `json:"data"`
}

func (api *Api) GetUserNetDiskMountForInstance(req GetUserNetDiskMountForInstanceReq, client *http.Client) (GetUserNetDiskMountForInstanceRes, error) {
	body, err := api.post(req, "/api/v1/region/net_disk/get_user_mount_for_instance", client)
	if err != nil {
		return GetUserNetDiskMountForInstanceRes{}, err
	}
	var res GetUserNetDiskMountForInstanceRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetUserNetDiskMountForInstanceRes{}, err
	}
	return res, nil
}

type NetDiskSetQuotaReq struct {
	UID            int                     `json:"uid"`
	RegionSignType constant.RegionSignType `json:"region_sign_type"`
	Quota          int64                   `json:"quota"`
}

type NetDiskSetQuotaRes struct {
	ResBase
	Data interface{} `json:"data"`
}

func (api *Api) NetDiskSetQuota(req NetDiskSetQuotaReq, client *http.Client) (NetDiskSetQuotaRes, error) {
	body, err := api.post(req, "/api/v1/region/net_disk/set_quota", client)
	if err != nil {
		return NetDiskSetQuotaRes{}, err
	}
	var res NetDiskSetQuotaRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return NetDiskSetQuotaRes{}, err
	}
	return res, nil
}

//

type GetUserNetDiskQuotaListReq struct {
	UIDs     []int                           `json:"ui_ds"`
	PagedReq *db_helper.GetPagedRangeRequest `json:"paged_req"`
	Sorts    libs.SortsReq                   `json:"sorts"`
	Region   constant.RegionSignType         `json:"region"`
}

type GetUserNetDiskQuotaListRes struct {
	ResBase
	Data GetUserNetDiskQuotaListData `json:"data"`
}

type GetUserNetDiskQuotaListData struct {
	Paged *db_helper.PagedData                       `json:"paged"`
	List  []regionModel.UserNetDiskQuotaForAdminInfo `json:"list"`
}

func (api *Api) GetUserNetDiskQuotaList(req GetUserNetDiskQuotaListReq, client *http.Client) (GetUserNetDiskQuotaListRes, error) {
	body, err := api.post(req, "/api/v1/region/net_disk/get_quota_list", client)
	if err != nil {
		return GetUserNetDiskQuotaListRes{}, err
	}
	var res GetUserNetDiskQuotaListRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetUserNetDiskQuotaListRes{}, err
	}
	return res, nil
}

type GetNetDiskReq struct {
	UID int                     `json:"uid"`
	RS  constant.RegionSignType `json:"rs"`
}

type GetNetDiskRes struct {
	ResBase
	Data *regionModel.NetDisk
}

func (api *Api) GetNetDisk(req GetNetDiskReq, client *http.Client) (GetNetDiskRes, error) {
	body, err := api.post(req, "/api/v1/region/net_disk/get", client)
	if err != nil {
		return GetNetDiskRes{}, err
	}
	var res GetNetDiskRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetNetDiskRes{}, err
	}
	return res, nil
}

//

type InitFileStorageWithRegionForUserReq struct {
	UID            int                     `json:"uid"`
	RegionSignType constant.RegionSignType `json:"region_sign_type"`
	Tenant         string                  `json:"tenant"`
}

type InitFileStorageWithRegionForUserRes struct {
	ResBase
	Data *regionModel.FileStorage `json:"data"`
}

func (api *Api) InitFileStorageWithRegionForUser(req InitFileStorageWithRegionForUserReq, client *http.Client) (InitFileStorageWithRegionForUserRes, error) {
	body, err := api.post(req, "/api/v1/region/file_storage/init", client)
	if err != nil {
		return InitFileStorageWithRegionForUserRes{}, err
	}
	var res InitFileStorageWithRegionForUserRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return InitFileStorageWithRegionForUserRes{}, err
	}
	return res, nil
}

//

type GetFileStorageListForUserReq struct {
	UID int `json:"uid"`
}

type GetFileStorageListForUserRes struct {
	ResBase
	Data []regionModel.RegionFileStorageInfo `json:"data"`
}

func (api *Api) GetFileStorageListForUser(req GetFileStorageListForUserReq, client *http.Client) (GetFileStorageListForUserRes, error) {
	body, err := api.post(req, "/api/v1/region/file_storage/get_list", client)
	if err != nil {
		return GetFileStorageListForUserRes{}, err
	}
	var res GetFileStorageListForUserRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetFileStorageListForUserRes{}, err
	}
	return res, nil
}

//

type GetFileStorageByUidsReq struct {
	UIDs           []int                   `json:"uid"`
	RegionSignType constant.RegionSignType `json:"region_sign_type"`
}

type GetFileStorageByUidsRes struct {
	ResBase
	Data []regionModel.FileStorage `json:"data"`
}

func (api *Api) GetFileStorageByUids(req GetFileStorageByUidsReq, client *http.Client) (GetFileStorageByUidsRes, error) {
	body, err := api.post(req, "/api/v1/region/file_storage/get", client)
	if err != nil {
		return GetFileStorageByUidsRes{}, err
	}
	var res GetFileStorageByUidsRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetFileStorageByUidsRes{}, err
	}
	return res, nil
}

//

type GetFileStorageForChargeReq struct {
	RegionSign constant.RegionSignType `json:"region_sign_type"`
	Tenant     string                  `json:"tenant"`
	FsType     constant.FsType         `json:"fs_type"`
	SettleDate time.Time               `json:"settle_date"`
}

type GetFileStorageForChargeRes struct {
	ResBase
	Data GetFileStorageForChargeData `json:"data"`
}

type GetFileStorageForChargeData struct {
	List        []regionModel.FsForChargeResult `json:"list"`
	FsType      constant.FsType                 `json:"fs_type"`
	MaxUsageMap map[string]string               `json:"max_usage_list"`
}

func (api *Api) GetFileStorageForCharge(req GetFileStorageForChargeReq, client *http.Client) (GetFileStorageForChargeRes, error) {
	body, err := api.post(req, "/api/v1/region/file_storage/get_by_tenant", client)
	if err != nil {
		return GetFileStorageForChargeRes{}, err
	}
	var res GetFileStorageForChargeRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetFileStorageForChargeRes{}, err
	}
	return res, nil
}

//

type FsGetDetailReq struct {
	UID        int                     `json:"uid" binding:"required"`
	RegionSign constant.RegionSignType `json:"region_sign_type" binding:"required"`
	Tenant     string                  `json:"tenant"`
}

type FsGetDetailRes struct {
	ResBase
	Data *regionModel.FileStorage `json:"data"`
}

func (api *Api) FsGetDetail(req FsGetDetailReq, client *http.Client) (FsGetDetailRes, error) {
	body, err := api.post(req, "/api/v1/region/file_storage/get_detail", client)
	if err != nil {
		return FsGetDetailRes{}, err
	}
	var res FsGetDetailRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return FsGetDetailRes{}, err
	}
	return res, nil
}

//

type GetFileStorageAdminListReq struct {
	db_helper.GetPagedRangeRequest
	RegionSignType constant.RegionSignType `json:"region_sign_type"`
	Tenant         string                  `json:"tenant"`
	UIDs           []int                   `json:"uids"`
	Sorts          libs.SortsReq           `json:"sorts"`
	FsType         string                  `json:"fs_type"`
	MinQuotaUsage  int                     `json:"min_quota_usage"`
}

type GetFileStorageAdminListRes struct {
	ResBase
	Data GetFileStorageAdminListData `json:"data"`
}

type GetFileStorageAdminListData struct {
	Total        int                       `json:"total"`
	FileStorages []regionModel.FileStorage `json:"file_storages"`
}

func (api *Api) GetFileStorageAdminList(req GetFileStorageAdminListReq, client *http.Client) (GetFileStorageAdminListRes, error) {
	body, err := api.post(req, "/api/v1/region/file_storage/get_admin_list", client)
	if err != nil {
		return GetFileStorageAdminListRes{}, err
	}
	var res GetFileStorageAdminListRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetFileStorageAdminListRes{}, err
	}
	return res, nil
}

//

type UpdateFileStorageSettingReq struct {
	UID            int                     `json:"uid" binding:"required"`
	RegionSignType constant.RegionSignType `json:"region" binding:"required"`
	Settings       struct {
		ConcurrentLimit int `json:"concurrent_limit"`
		QuotaTotal      int `json:"quota_total"`
		QuotaInodeTotal int `json:"quota_inode_total"`
		MaxUploads      int `json:"max_uploads"` // autoFs 并发数
		CacheSize       int `json:"cache_size"`
		BufferSize      int `json:"buffer_size"`
	} `json:"settings"`
}

type UpdateFileStorageSettingRes struct {
	ResBase
	Data interface{} `json:"data"`
}

func (api *Api) UpdateFileStorageSetting(req UpdateFileStorageSettingReq, client *http.Client) (UpdateFileStorageSettingRes, error) {
	body, err := api.post(req, "/api/v1/region/file_storage/update_setting", client)
	if err != nil {
		return UpdateFileStorageSettingRes{}, err
	}
	var res UpdateFileStorageSettingRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return UpdateFileStorageSettingRes{}, err
	}
	return res, nil
}
