# ga&gs

[![build status](https://gitlab-gpuhub.autodl.com/poyekhali/server/badges/master/build.svg)](https://gitlab.seetatech.com/poyekhali/server/commits/master)
[![coverage report](https://gitlab-gpuhub.autodl.com/poyekhali/server/badges/master/coverage.svg)](https://gitlab.seetatech.com/poyekhali/server/commits/master)

## What is gs

gs是 gpu server缩写, 是一系列微服务的组合, 目前包括 api-server, agent-server, frps, worker等

## What is ga

ga是 gpu agent缩写, 是运行在共享硬件的物理主机上的agent程序, 支持direct/sysctl.service/docker/docker-compose/k8s deployment等方式运行

## Contribute

### Code path of gs

1. cmd/server/cmd/agent-server.go --> entrance of agent-server
2. cmd/server/cmd/api-server.go --> entrance of api-server
3. cmd/server/cmd/worker.go --> entrance of worker

### Deploy method of gs

```
cd cmd/server
make redeploy_all // build code & docker build image & delete deployment & deploy in kubernetes
make update // build code & docker build image & apply deployment
```

### Code path of ga

1. cmd/agent/cmd/run.go --> entrance of agent

### Deploy method of ga

```
cd cmd/agent
make // build code
```

## Architecture

![architecture](/doc/pic/architecture.png)
![dataFlow](/doc/pic/basic_data_flow.png)
