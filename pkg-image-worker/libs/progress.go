package image_libs

import (
	"context"
	"fmt"
	"github.com/dustin/go-humanize"
	"io"
	"math/big"
	"time"
)

func CopyWithProgress(ctx context.Context, dst io.Writer, object io.Reader, objSize int64) (int64, error) {
	var err error
	var written int64 = 0
	buf := make([]byte, 32*1024)
	begin := time.Now()
	for {
		// 读取
		select {
		case <-ctx.Done():
			return written, nil
		default:
			nr, er := object.Read(buf)
			if nr > 0 {
				nw, ew := dst.Write(buf[0:nr])
				if ew != nil {
					err = ew
					return written, err
				}
				if nw > 0 {
					written += int64(nw)
				}

				percent := float64(written) / float64(objSize)

				if time.Now().After(begin.Add(time.Second)) {
					begin = time.Now()
					fmt.Printf("\rdownloading... %.2f%% %s/%s", percent*100, Humanize(written), Humanize(objSize))
				}
				if nr != nw {
					err = io.ErrShortWrite
					return written, err
				}
			}
			if er != nil {
				if er == io.EOF {
					fmt.Printf("\rdownloading... 100%% %s/%s", Humanize(objSize), Humanize(objSize))
					er = nil
				}
				return written, er
			}
		}
	}
}

func Humanize(size int64) string {
	bigInt := &big.Int{}
	return humanize.BigIBytes(bigInt.SetInt64(size))
}
