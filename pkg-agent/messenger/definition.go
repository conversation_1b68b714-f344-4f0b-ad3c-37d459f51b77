package messenger

import "encoding/json"

type MessageType string

const (
	// ContainerCreateType ContainerStartType ContainerStopType ContainerRemoveType
	// server send, agent receive, single step, don't need to freeze status
	ContainerCreateType MessageType = "ContainerCreateType"
	ContainerStartType  MessageType = "ContainerStartType"
	ContainerStopType   MessageType = "ContainerStopType"
	ContainerRemoveType MessageType = "ContainerRemoveType"

	//  ContainerMergeDiffType:
	// exchange container diff file by net disk. step1 on source machine, step2 on target machine.
	ContainerMergeDiffType    MessageType = "ContainerMergeDiffType"    // payload: ContainerPerformMigrate{}
	ContainerMergeOssDiffType MessageType = "ContainerMergeOssDiffType" // payload: ContainerPerformMigrate{}

	// ContainerCleanDiffType clean tar file in net disk after mig succeed.
	ContainerCleanDiffType MessageType = "ContainerCleanDiffType" // payload: source container_id string

	// ContainerReInitType ContainerRestartType:
	// server send, agent receive. multiple steps, need to freeze status
	ContainerReInitType  MessageType = "ContainerReInitType"
	ContainerRestartType MessageType = "ContainerRestartType"

	// ContainerStatusType
	// agent send, server receive
	ContainerStatusType MessageType = "ContainerStatusType"
	ContainerUsageType  MessageType = "ContainerUsageType"

	// ContainerCronCheckType : server send, agent receive. server cron check status.
	ContainerCronCheckType MessageType = "ContainerCronCheckType"

	// commit container and push repository
	ContainerSaveType         MessageType = "ContainerSaveType"
	ContainerSaveProgressType MessageType = "ContainerSaveProgressType"
	ContainerCancelSavesType  MessageType = "ContainerCancelSaveType"

	// MachineHealthStatusType MachineRegisterType
	MachineHealthStatusType   MessageType = "MachineHealthStatusType"
	MachineRegisterType       MessageType = "MachineRegisterType"
	MachineIntranetIpSyncType MessageType = "MachineIntranetIpSyncType"

	// RegionRegisterType : storage agent register
	RegionRegisterType           MessageType = "RegionRegisterType"           // nfs agent -> server register
	RegionRegisterResultType     MessageType = "RegionRegisterResultType"     // server -> nfs agent confirm
	RegionSyncUsageType          MessageType = "RegionSyncUsageType"          // nfs agent 主动发送使用量信息
	RegionSyncDiskAlertsType     MessageType = "RegionSyncDiskAlertsType"     // nfs agent 主动发送磁盘预警信息
	StorageAgentFrpcRequest      MessageType = "StorageAgentFrpcRequest"      // nfs agent -> server 获取 frpc proxy
	StorageAgentFrpcResponse     MessageType = "StorageAgentFrpcResponse"     // server -> nfs agent 返回 frpc proxy
	ADFSStorageAgentFrpcRequest  MessageType = "ADFSStorageAgentFrpcRequest"  // adfs agent -> server 获取 frpc proxy
	ADFSStorageAgentFrpcResponse MessageType = "ADFSStorageAgentFrpcResponse" // server -> adfs agent 返回 frpc proxy
	AutoFsFrpcRequest            MessageType = "AutoFsFrpcRequest"            // autofs agent -> server 获取 frpc proxy

	// NetDiskInitType : net disk storage
	NetDiskInitType                MessageType = "NetDiskInitType"                // server -> nfs agent 发出初始化用户目录的请求
	NetDiskInitResultType          MessageType = "NetDiskInitResultType"          // nfs agent -> server 返回初始化结果
	NetDiskAdminSetQuotaType       MessageType = "NetDiskAdminSetQuotaType"       // 管理员针对单个用户设置网盘大小
	NetDiskAdminSetQuotaResultType MessageType = "NetDiskAdminSetQuotaResultType" // 返回

	// ADFS 网盘相关
	FileStorageInitType       MessageType = "FileStorageInitType"       // server -> adfs agent 发出初始化用户目录的请求
	AdfsMkdirType             MessageType = "AdfsMkdirType"             // server -> adfs agent 发出初始化用户目录的请求
	FileStorageInitResultType MessageType = "FileStorageInitResultType" // adfs agent -> server 初始化结果
	FileStorageSetQuotaType   MessageType = "FileStorageSetQuotaType"   // server -> adfs agent 发出设置 quota 请求

	// AutoFS
	AutoFsInitType         MessageType = "AutoFsInitType"
	AutoFsInitResultType   MessageType = "AutoFsInitResultType"
	AutoFsSetQuotaType     MessageType = "AutoFsSetQuotaType"
	AutoFsSyncUsageRequest MessageType = "AutoFsSyncUsageRequest"

	// DataTransferType 数据转移， 包括宿主机数据、容器upper dir、容器volume等
	DataTransferType       MessageType = "DataTransferType"
	DataTransferCancelType MessageType = "DataTransferCancelType"

	// PongType
	// server send, agent receive
	PongType MessageType = "PongType"

	// PingType HostRegisterType HostHealthType
	// agent send, server receive
	PingType         MessageType = "PingType"
	HostRegisterType MessageType = "HostRegisterType"
	HostHealthType   MessageType = "HostHealthType"

	// DefaultSkipType : useless or failed message to be skipped in mailbox.
	DefaultSkipType MessageType = "DefaultSkipType"
)

func (mt MessageType) DontNeedResend() bool {
	switch mt {
	case ContainerCronCheckType, DefaultSkipType, RegionSyncUsageType:
		return true
	}

	return false
}

func (mt MessageType) CanSplitWithContainer() bool {
	switch mt {
	case ContainerStatusType, ContainerUsageType:
		return true
		// and so on...
	}
	return false
}

func (mt MessageType) Priority() int {
	switch mt {
	case ContainerStatusType:
		return 10
	}
	return 0
}

// Message 发送/接收的消息格式, 所有类型必须幂等(server可能存在多次发送, 防止信息传递丢失/client异常)
type Message struct {
	MsgID   string
	Type    MessageType
	Payload string
}

func (i *Message) ParseFromString(s []byte) error {
	return json.Unmarshal(s, i)
}

func (i *Message) Marshal() ([]byte, error) {
	return json.Marshal(i)
}
