package agent_files

import (
	"io"
	"os"
	"path/filepath"
	"server/pkg/logger"

	"github.com/pkg/errors"
)

type ContainerInitRequiredParams struct {
	MachineID  string
	CpuName    string
	PreCmd     string
	OnlyPreCmd bool
	// 在容器启动命令中, 使用chpasswd命令, 修改root密码
	RootPassword string

	// proxy server info
	ProxyHosts []string
	ProxtHost  string
	ProxyPort  string
	ProxyToken string

	// 容器内部service对应到proxy的info
	JupyterToken      string
	JupyterPort       int
	TensorboardPort   int
	SSHPort           int
	JupyterDomain     string
	TensorboardDomain string

	// 用户ssh公钥
	SSHPublicKey   string
	AutopanelToken string

	Env []string
}

func WriteContainerInitFilesToDirectory(directoryPath string, params ContainerInitRequiredParams) error {
	statInfo, err := os.Stat(directoryPath)
	if err != nil {
		return errors.Wrapf(err, "stat directory path [%s] failed when write container init files", directoryPath)
	}
	if !statInfo.IsDir() {
		return errors.Errorf("check directory path [%s] is directory failed when write container init files", directoryPath)
	}

	// bootFiles 包含了许多固定逻辑, 比如set password等
	writeFiles := bootFiles
	if params.OnlyPreCmd {
		// onlyPreCmdFiles 只包含了pre cmd, 简单干净
		writeFiles = onlyPreCmdFiles
	}

	for dirName, files := range writeFiles {
		dirAbsPath := filepath.Join(directoryPath, dirName)
		err = os.MkdirAll(dirAbsPath, os.ModePerm)
		if err != nil {
			return errors.Wrapf(err, "create directory path [%s/%s] failed when write container init files", directoryPath, dirName)
		}

		for _, f := range files {
			if len(f.filePath) != 0 {
				var fileCpFinished bool

				for _, fp := range f.filePath {
					openedFile, statErr := os.Open(fp)
					if os.IsNotExist(statErr) {
						continue
					} else if statErr != nil {
						logger.NewLogger("Files.writer").Error("failed to open file of [%s], [%s]", fp, statErr.Error())
						continue
					}
					dstFile, createErr := os.OpenFile(filepath.Join(dirAbsPath, f.fileName), os.O_RDWR|os.O_CREATE|os.O_TRUNC, os.ModePerm)
					if createErr != nil {
						logger.NewLogger("Files.writer").Error("failed to create file of [%s], [%s]", filepath.Join(dirAbsPath, f.fileName), createErr.Error())
						continue
					}
					_, copyErr := io.Copy(dstFile, openedFile)
					if copyErr != nil {
						logger.NewLogger("Files.writer").Error("failed to copy file of [%s], [%s]", filepath.Join(dirAbsPath, f.fileName), copyErr.Error())
						continue
					}

					// 此文件执行完毕, 执行其他文件
					fileCpFinished = true
					break
				}

				if !fileCpFinished {
					// 没完成这个文件的copy
					logger.NewLogger("Files.writer").Error("can not found source file of [%s]", filepath.Join(dirAbsPath, f.fileName))
				}
				continue
			}
			if f.formatContent == nil {
				logger.NewLogger("Files.writer").Error("failed to get format content cause func of [%s] is nil", f.fileName)
				continue
			}
			if f.dontCreateIfNull {
				if len(f.formatContent(params)) != 0 {
					err = os.WriteFile(filepath.Join(dirAbsPath, f.fileName), []byte(f.formatContent(params)), os.ModePerm)
					if err != nil {
						return errors.Wrapf(err, "create directory file [%s/%s/%s] failed when write container init files", directoryPath, dirName, f.fileName)
					}
				}
			} else {
				err = os.WriteFile(filepath.Join(dirAbsPath, f.fileName), []byte(f.formatContent(params)), os.ModePerm)
				if err != nil {
					return errors.Wrapf(err, "create directory file [%s/%s/%s] failed when write container init files", directoryPath, dirName, f.fileName)
				}
			}

		}
	}

	return nil
}
