package agent_constant

import (
	"context"
	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/network"
	volumetypes "github.com/docker/docker/api/types/volume"
	specs "github.com/opencontainers/image-spec/specs-go/v1"
	"io"
	"time"
)

type DockerClient interface {
	Info(ctx context.Context) (types.Info, error)
	Ping(ctx context.Context) (types.Ping, error)

	ContainerInspect(ctx context.Context, containerID string) (types.ContainerJSON, error)
	ContainerInspectWithRaw(ctx context.Context, containerID string, getSize bool) (types.ContainerJSON, []byte, error)
	ContainerStatsOneShot(ctx context.Context, containerID string) (types.ContainerStats, error)
	ContainerCreate(ctx context.Context, config *container.Config, hostConfig *container.HostConfig, networkingConfig *network.NetworkingConfig, platform *specs.Platform, containerName string) (container.ContainerCreateCreatedBody, error)
	ContainerUpdate(ctx context.Context, containerID string, updateConfig container.UpdateConfig) (container.ContainerUpdateOKBody, error)
	ContainerStart(ctx context.Context, containerID string, options types.ContainerStartOptions) error
	ContainerStop(ctx context.Context, containerID string, timeout *time.Duration) error
	ContainerRemove(ctx context.Context, containerID string, options types.ContainerRemoveOptions) error
	ContainerExport(ctx context.Context, containerID string) (io.ReadCloser, error)
	ContainerList(ctx context.Context, options types.ContainerListOptions) ([]types.Container, error)
	ContainerExecCreate(ctx context.Context, containerID string, config types.ExecConfig) (types.IDResponse, error)
	ContainerExecStart(ctx context.Context, execID string, config types.ExecStartCheck) error

	ImagePull(ctx context.Context, refStr string, options types.ImagePullOptions) (io.ReadCloser, error)
	ImageList(ctx context.Context, options types.ImageListOptions) ([]types.ImageSummary, error)
	ImageInspectWithRaw(ctx context.Context, imageID string) (types.ImageInspect, []byte, error)

	VolumeRemove(ctx context.Context, volumeID string, force bool) error
	VolumeCreate(ctx context.Context, options volumetypes.VolumeCreateBody) (types.Volume, error)
	VolumeInspect(ctx context.Context, volumeID string) (types.Volume, error)
}

type DockerSpecifiedDeviceDriverType string

func (t DockerSpecifiedDeviceDriverType) String() string {
	return string(t)
}

const (
	DriverTypeOfShmSize   DockerSpecifiedDeviceDriverType = "autodl-shm-size"
	DriverTypeOfPorts     DockerSpecifiedDeviceDriverType = "autodl-ports"
	DriverTypeOfHuaweiNPU DockerSpecifiedDeviceDriverType = "autodl-huawei-npu"
	DriverTypeOfMthreads  DockerSpecifiedDeviceDriverType = "autodl-mthreads"

	DriverTypeOfHuaweiNPUEnvKey string = "ASCEND_VISIBLE_DEVICES"

	MthreadsVisibleDevices     = "MTHREADS_VISIBLE_DEVICES"     //环境变量可以指定特定的 GPU 设备到容器中,通过 GPU 的设备编号或者 GPU UUID 指定。
	MthreadsDriverCapabilities = "MTHREADS_DRIVER_CAPABILITIES" //用于控制容器中用户态驱动程序的能力 可以传入多个驱动能力，以 “,” 为分割符。
)
