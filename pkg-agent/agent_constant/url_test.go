package agent_constant_test

import (
	"context"
	"github.com/minio/minio-go/v7"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	. "server/pkg-agent/agent_constant"
	"server/pkg/libs"
	"testing"
)

var testCases = map[string]struct {
	Len         int
	WsUrls      []string
	SaveLogUrls []string
}{
	"example.gpuhub.com": {
		Len:         2,
		WsUrls:      []string{"wss://example.gpuhub.com/" + WebsocketApiPrefix, "ws://example.gpuhub.com/" + WebsocketApiPrefix},
		SaveLogUrls: []string{"https://example.gpuhub.com/" + SaveLogApiPrefix, "http://example.gpuhub.com/" + SaveLogApiPrefix},
	},
	"http://example.gpuhub.com": {
		Len:         2,
		WsUrls:      []string{"wss://example.gpuhub.com/" + WebsocketApiPrefix, "ws://example.gpuhub.com/" + WebsocketApiPrefix},
		SaveLogUrls: []string{"https://example.gpuhub.com/" + SaveLogApiPrefix, "http://example.gpuhub.com/" + SaveLogApiPrefix},
	},
	"https://example.gpuhub.com": {
		Len:         1,
		WsUrls:      []string{"wss://example.gpuhub.com/" + WebsocketApiPrefix},
		SaveLogUrls: []string{"https://example.gpuhub.com/" + SaveLogApiPrefix},
	},
	"ws://example.gpuhub.com": {
		Len:         2,
		WsUrls:      []string{"wss://example.gpuhub.com/" + WebsocketApiPrefix, "ws://example.gpuhub.com/" + WebsocketApiPrefix},
		SaveLogUrls: []string{"https://example.gpuhub.com/" + SaveLogApiPrefix, "http://example.gpuhub.com/" + SaveLogApiPrefix},
	},
	"wss://example.gpuhub.com": {
		Len:         1,
		WsUrls:      []string{"wss://example.gpuhub.com/" + WebsocketApiPrefix},
		SaveLogUrls: []string{"https://example.gpuhub.com/" + SaveLogApiPrefix},
	},
	"example.gpuhub.com:5231": {
		Len:         2,
		WsUrls:      []string{"wss://example.gpuhub.com:5231/" + WebsocketApiPrefix, "ws://example.gpuhub.com:5231/" + WebsocketApiPrefix},
		SaveLogUrls: []string{"https://example.gpuhub.com:5231/" + SaveLogApiPrefix, "http://example.gpuhub.com:5231/" + SaveLogApiPrefix},
	},
	"http://example.gpuhub.com:5231": {
		Len:         2,
		WsUrls:      []string{"wss://example.gpuhub.com:5231/" + WebsocketApiPrefix, "ws://example.gpuhub.com:5231/" + WebsocketApiPrefix},
		SaveLogUrls: []string{"https://example.gpuhub.com:5231/" + SaveLogApiPrefix, "http://example.gpuhub.com:5231/" + SaveLogApiPrefix},
	},
	"https://example.gpuhub.com:5231": {
		Len:         1,
		WsUrls:      []string{"wss://example.gpuhub.com:5231/" + WebsocketApiPrefix},
		SaveLogUrls: []string{"https://example.gpuhub.com:5231/" + SaveLogApiPrefix},
	},
	"ws://example.gpuhub.com:5231": {
		Len:         2,
		WsUrls:      []string{"wss://example.gpuhub.com:5231/" + WebsocketApiPrefix, "ws://example.gpuhub.com:5231/" + WebsocketApiPrefix},
		SaveLogUrls: []string{"https://example.gpuhub.com:5231/" + SaveLogApiPrefix, "http://example.gpuhub.com:5231/" + SaveLogApiPrefix},
	},
	"wss://example.gpuhub.com:5231": {
		Len:         1,
		WsUrls:      []string{"wss://example.gpuhub.com:5231/" + WebsocketApiPrefix},
		SaveLogUrls: []string{"https://example.gpuhub.com:5231/" + SaveLogApiPrefix},
	},
	"*************:43001": {
		Len:         2,
		WsUrls:      []string{"wss://*************:43001/" + WebsocketApiPrefix, "ws://*************:43001/" + WebsocketApiPrefix},
		SaveLogUrls: []string{"https://*************:43001/" + SaveLogApiPrefix, "http://*************:43001/" + SaveLogApiPrefix},
	},
	"http://*************:43001": {
		Len:         2,
		WsUrls:      []string{"wss://*************:43001/" + WebsocketApiPrefix, "ws://*************:43001/" + WebsocketApiPrefix},
		SaveLogUrls: []string{"https://*************:43001/" + SaveLogApiPrefix, "http://*************:43001/" + SaveLogApiPrefix},
	},
	"https://*************:43001": {
		Len:         1,
		WsUrls:      []string{"wss://*************:43001/" + WebsocketApiPrefix},
		SaveLogUrls: []string{"https://*************:43001/" + SaveLogApiPrefix},
	},
	"ws://*************:43001": {
		Len:         2,
		WsUrls:      []string{"wss://*************:43001/" + WebsocketApiPrefix, "ws://*************:43001/" + WebsocketApiPrefix},
		SaveLogUrls: []string{"https://*************:43001/" + SaveLogApiPrefix, "http://*************:43001/" + SaveLogApiPrefix},
	},
	"wss://*************:43001": {
		Len:         1,
		WsUrls:      []string{"wss://*************:43001/" + WebsocketApiPrefix},
		SaveLogUrls: []string{"https://*************:43001/" + SaveLogApiPrefix},
	},
}

var _ = Describe("Url", func() {
	It("should parse ", func() {
		for u, v := range testCases {
			u1p := ParseUrl(u)
			Expect(len(u1p.GetWebsocketConnectUrls())).Should(Equal(v.Len))
			Expect(u1p.GetWebsocketConnectUrls()).
				Should(ContainElements(v.WsUrls))
			Expect(u1p.GetSaveLogUrls()).
				Should(ContainElements(v.SaveLogUrls))

		}
	})
})

func TestNewMinioClient(t *testing.T) {
	bucket := "user-private-image"
	cli, err := NewMinioClient(MinioCredentials{
		Endpoint:        "oss-cn-neimeng.autodl.com:80",
		AccessKeyID:     "TestEnvAutodlMinioAccessUser",
		SecretAccessKey: "test22e2a-d496-4b04-85e0-59e9ad2a99fa",
	})
	if err != nil {
		t.Fatalf("new failed %s", err.Error())
	}
	ch := cli.ListObjects(context.Background(), bucket, minio.ListObjectsOptions{})
	for v := range ch {
		if v.Err != nil {
			t.Fatalf("get obj failed %s", v.Err.Error())
		}
		t.Logf("obj: %s", libs.IndentString(v))
	}
}
