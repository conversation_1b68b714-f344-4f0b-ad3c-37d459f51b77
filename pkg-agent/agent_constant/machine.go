package agent_constant

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"
)

type MachineHardwareInfo struct {
	MachineName string `json:"machine_name"`
	MachineID   string `json:"machine_id"`

	AgentVersion  string       `json:"agent_version"`
	AgentFeature  AgentFeature `json:"agent_feature"`
	KernelVersion string       `json:"kernel_version"`
	DockerVersion string       `json:"docker_version"`
	NvidiaDocker  bool         `json:"nvidia_docker"`

	GPUType      string `json:"gpu_type"`
	GPUName      string `json:"gpu_name"`
	GPUCount     int64  `json:"gpu_count"`
	GpuMemory    int64  `json:"gpu_memory"`
	GpuUsed      int64  `json:"gpu_used"`
	MEMCountInMB int64  `json:"mem_count_in_mb"`
	ChipCorp     string `json:"chip_corp"`

	CpuNum            int64  `json:"cpu_num"`
	CpuName           string `json:"cpu_name"`
	CpuBasicFrequency string `json:"cpu_basic_frequency"`
	CpuArch           string `json:"cpu_arch"`

	Memory   int64 `json:"memory"`
	DiskSize int64 `json:"disk_size"` //单位为byte

	HealthStatus MachineHealthStatus `json:"health_status"`
	RegionName   string              `json:"region_name"`
	IpAddress    string              `json:"ip_address"`
	OsName       string              `json:"os_name"`
	RestartTime  time.Time           `json:"restart_time"`

	GpuOccupation []*GpuOccupationInfo `json:"gpu_occupation"`
	// 此条信息的生产日期
	ValidAt time.Time `json:"valid_at"`

	RegionSign string `json:"region_sign"`
}

type MachineHealthInfo struct {
	MachineID    string              `json:"machine_id"`
	HealthStatus MachineHealthStatus `json:"health_status"`
	DiskInfo     *DiskInfo           `json:"disk_info"`
	// 此条信息的生产日期
	ValidAt time.Time `json:"valid_at"`
}

func (c *MachineHealthInfo) String() (string, error) {
	out, err := json.Marshal(c)
	return string(out), err
}

func (c *MachineHardwareInfo) String() (string, error) {
	out, err := json.Marshal(c)
	return string(out), err
}

type GpuOccupationInfo struct {
	GpuName string `json:"gpu_name"`
	GpuUUID string `json:"gpu_uuid"`
	Index   int    `json:"index"`
	Used    int    `json:"used"`
}

type AgentFeature string

var featureOfAutoPanel = "autopanel"
var features = []string{
	featureOfAutoPanel,
}

func NewFeatureDesc(cudaV, driverV string) AgentFeature {
	if cudaV != "" {
		UpdateOrAppendFeature(fmt.Sprintf("cuda_version=%s", cudaV))
	}

	if driverV != "" {
		UpdateOrAppendFeature(fmt.Sprintf("driver_version=%s", driverV))
	}

	return AgentFeature(strings.Join(features, ","))
}

func (a AgentFeature) HasAutoPanel() bool {
	return strings.Contains(string(a), featureOfAutoPanel)
}

func (a AgentFeature) HasCudaVersion() bool {
	targetField := "cuda_version="
	return strings.Contains(string(a), targetField) && a.GetCudaVersion() != ""
}

func (a AgentFeature) GetCudaVersion() string {
	fields := strings.Split(string(a), ",")
	for _, field := range fields {
		if strings.HasPrefix(field, "cuda_version=") {
			return strings.TrimPrefix(field, "cuda_version=")
		}
	}

	// 如果是空值，返回 99 。一般是CPU机器
	return "99"
}

func (a AgentFeature) GetDriverVersion() string {
	fields := strings.Split(string(a), ",")
	for _, field := range fields {
		if strings.HasPrefix(field, "driver_version=") {
			return strings.TrimPrefix(field, "driver_version=")
		}
	}

	return ""
}

func UpdateOrAppendFeature(feature string) {
	for i, f := range features {
		if strings.HasPrefix(f, strings.Split(feature, "=")[0]) {
			features[i] = feature
			return
		}
	}
	features = append(features, feature)
}
