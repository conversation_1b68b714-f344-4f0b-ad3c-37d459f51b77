package agent_constant

import (
	"encoding/json"
	"time"
)

type ContainerStatus struct {
	// Basic
	ContainerID ContainerID `json:"container_id"`

	Status ContainerState `json:""`

	// 此条信息的生产日期
	ValidAt time.Time `json:""`

	RestartCount int `json:""`

	CreatedAt  time.Time         `json:""`
	StartedAt  time.Time         `json:""`
	FinishedAt time.Time         `json:""`
	Device     []ContainerDevice `json:""`

	// sentTimes 标识该消息被发送给server的次数, 用于保证消息的冗余可靠性, <=3才发送
	sentTimes int
	sentAt    time.Time
}

type ContainerDevice struct {
	Driver    string   `json:"driver"`
	DeviceIDs []string `json:"device_ids"`
}

func (c *ContainerStatus) String() (string, error) {
	out, err := json.Marshal(c)
	return string(out), err
}

// Abstruct 用于比对两个对象是否相等的摘要信息, 剔除掉无关信息的序列化结果
func (c *ContainerStatus) Abstruct() string {
	out, _ := json.Marshal(&ContainerStatus{
		ContainerID:  c.ContainerID,
		Status:       c.Status,
		RestartCount: c.RestartCount,
		CreatedAt:    c.CreatedAt,
		StartedAt:    c.StartedAt,
		FinishedAt:   c.FinishedAt,
	})
	return string(out)
}

// send操作间隔会线性增长, 在状态发生后的第0, 30s后继续发送
func (c *ContainerStatus) CannotSend() bool {
	return c.sentTimes > 0
}

func (c *ContainerStatus) BeforeDoSend() {
	c.sentTimes++
}

func (c *ContainerStatus) IsFirstSend() bool {
	return c.sentTimes == 0
}

type ContainerState struct {
	Status    ContainerStatePhase `json:"status"`
	OOMKilled bool                `json:"oom_killed"`
	Pid       int                 `json:"pid"`
	ExitCode  int                 `json:"exit_code"`
	Error     string              `json:"error"`
}

type UploadOssInfo struct {
	ContainerID ContainerID `json:"container_id"`
	ImageUUID   string      `json:"image_uuid"`
	Finished    bool        `json:"finished"`
	ObjectSize  int64       `json:"object_size"`
	ImageSize   int64       `json:"image_size"`
	Progress    float64     `json:"progress"`
	Error       string      `json:"error"`
	IsUploadNew bool        `json:"is_upload_new"`
}

func (u *UploadOssInfo) String() (string, error) {
	out, err := json.Marshal(u)
	return string(out), err
}

func (u *UploadOssInfo) ParseFromString(in string) error {
	err := json.Unmarshal([]byte(in), u)
	if err != nil {
		return err
	}
	return err
}

type CancelUploadImageInfo struct {
	ContainerID ContainerID `json:"container_id"`
	ImageUUID   string      `json:"image_uuid"`
	Finished    bool        `json:"finished"`
	Error       string      `json:"error"`
}

func (u *CancelUploadImageInfo) String() (string, error) {
	out, err := json.Marshal(u)
	return string(out), err
}

func (u *CancelUploadImageInfo) ParseFromString(in string) error {
	err := json.Unmarshal([]byte(in), u)
	if err != nil {
		return err
	}
	return err
}

type BasicUsageInfo struct {
	// 百分数, 91.56 = 91.56%, 保留了小数点后两位
	CPUUsagePercent float64 `json:"cpu_usage_percent"`
	MemUsagePercent float64 `json:"mem_usage_percent"`
	// MemUsage (byte)
	MemUsage int64 `json:"mem_usage"`
	// MemLimit (byte)
	MemLimit int64 `json:"mem_limit"`

	// RootFSUsedSize (byte), 容器写入到rootfs的空间大小, 其上限是rootfs.size_limit
	// server/pkg-agent/agent_constant/container_control.go:20 MaxWritableSizeInByte
	// The size of files that have been created or changed by this container.
	RootFSUsedSize int64 `json:"root_fs_used_size"`

	// RootFSTotalSize (byte), 容器整个rootfs空间大小, 包含镜像内容+容器创建内容
	RootFSTotalSize int64 `json:"root_fs_total_size"`
	// 获取数据盘大小
	DataDiskTotalSize uint64 `json:"data_disk_total_size"`
	DataDiskUsedSize  uint64 `json:"data_disk_used_size"`

	StorageFSUsage string `json:"storage_fs_usage"`
}

type ContainerUsage struct {
	// Basic
	ContainerID ContainerID `json:"container_id"`

	// 此条信息的生产日期
	ValidAt time.Time `json:"valid_at"`

	BasicUsageInfo

	// 拉取镜像的progress, [0, 1], 0.95 -> 95%
	PullImageProgress       float64 `json:"pull_image_progress"`
	DownloadImageProgress   float64 `json:"download_image_progress"`
	DownloadOssFileProgress float64 `json:"download_oss_file_progress"`

	// 标识需要发送的消息
	IsUsageNew bool `json:"is_new"`
}

func (c *ContainerUsage) String() (string, error) {
	out, err := json.Marshal(c)
	return string(out), err
}

// Abstract 用于比对两个对象是否相等的摘要信息, 剔除掉无关信息的序列化结果
func (c *ContainerUsage) Abstract() string {
	out, _ := json.Marshal(&ContainerUsage{
		ContainerID: c.ContainerID,
		BasicUsageInfo: BasicUsageInfo{
			CPUUsagePercent:   c.CPUUsagePercent,
			MemUsagePercent:   c.MemUsagePercent,
			MemUsage:          c.MemUsage,
			MemLimit:          c.MemLimit,
			RootFSUsedSize:    c.RootFSUsedSize,
			RootFSTotalSize:   c.RootFSTotalSize,
			DataDiskUsedSize:  c.DataDiskUsedSize,
			DataDiskTotalSize: c.DataDiskTotalSize,
			StorageFSUsage:    c.StorageFSUsage,
		},
		DownloadImageProgress: c.DownloadImageProgress,
	})
	return string(out)
}

type ContainerPrepareMigrate struct {
	SourceContainerID ContainerID `json:"source_container_id"`
	UploadPath        string      `json:"upload_path"`
}

func (c *ContainerPrepareMigrate) String() (string, error) {
	out, err := json.Marshal(c)
	return string(out), err
}

type ContainerPerformMigrate struct {
	// Basic
	SourceContainerID ContainerID `json:"source_container_id"`
	TargetContainerID ContainerID `json:"target_container_id"`

	DownloadPath string `json:"download_path"`

	MinioBucketInfo *MinioBucketInfo
	// 此条信息的生产日期
	ValidAt time.Time `json:"valid_at"`

	Msg string `json:"msg"`
}

func (c *ContainerPerformMigrate) String() (string, error) {
	out, err := json.Marshal(c)
	return string(out), err
}

type ContainerStatePhase string

const (
	Pulling    ContainerStatePhase = "pulling" // 人为定义的状态, 由 agent 手动发送, 用于表明在拉镜像, 除此之外都是 docker 容器状态.
	Pulled     ContainerStatePhase = "pulled"  // 人为定义的状态, 由 agent 手动发送, 用于表明镜像完成
	Created    ContainerStatePhase = "created"
	Running    ContainerStatePhase = "running"
	Paused     ContainerStatePhase = "paused"
	Restarting ContainerStatePhase = "restarting"
	Removing   ContainerStatePhase = "removing"
	Exited     ContainerStatePhase = "exited"
	Dead       ContainerStatePhase = "dead"
	NotFound   ContainerStatePhase = "not_found"
)
