package agent_constant

import (
	"context"
	"fmt"
	"github.com/docker/docker/api/types/container"
	"strings"
	"sync"
)

type ContainerID string

func NewContainerID(id string) ContainerID {
	return ContainerID(strings.Trim(id, "/"))
}

func (c ContainerID) String() string {
	// 去掉前面的斜杠, "/container_xxx" -> "container_xxx"
	return strings.Trim(string(c), "/")
}

func (c ContainerID) AdfsContainerName() string {
	// 去掉前面的斜杠, "/container_xxx" -> "container_xxx"
	return "adfs-" + c.String()
}

func (c ContainerID) IsAdfsContainerName() bool {
	return strings.HasPrefix(string(c), "adfs-")
}

type NewContainerParam struct {
	// Basic
	ContainerID ContainerID `json:"container_id"`
	Image       string      `json:"image"`
	PreCmd      []string    `json:"pre_cmd"`
	// OnlyPreCmd 用来标识容器是否只需要运行pre cmd, 比如一些任务不需要指定root password和port之类的
	OnlyPreCmd bool              `json:"only_pre_cmd"`
	WorkingDir string            `json:"working_dir"`
	Labels     map[string]string `json:"labels"`
	Env        []string          `json:"env"`

	// (byte) the amount of data (on disk) that is used for the writable layer of each container.
	MaxWritableSizeInByte  int64 `json:"max_writable_size_in_byte"`
	MaxLocalDiskSizeInByte int64 `json:"max_local_disk_size_in_byte"`

	// Timeout (in seconds) to stop a container
	StopTimeout *int `json:"stop_timeout"`

	// Both in create and start
	// (byte) ShmSize
	ShmSize int64 `json:"shm_size"`
	// Ports ["8080:9999"] means export the port 9999 in container to the host port 8080
	Ports []string `json:"ports"`

	// root password
	RootPassword string `json:"root_password"`

	// Proxy server info, ProxyHost 给container使用, proxyHostPublic给前端使用
	ProxyHosts      []string `json:"proxy_hosts"`       // 所有支持的proxy_host，现在最多只会有两个，第二个是热备，为后面方便扩展保存为数组
	ProxyHost       string   `json:"proxy_host"`        // 此处已经根据是否有内网做好了判断,region有则使用内网,没有则使用公网
	ProxyHostPublic string   `json:"proxy_host_public"` // 如果不为空, 则一定是公网地址
	ProxyPort       string   `json:"proxy_port"`
	ProxyToken      string   `json:"proxy_token"`

	// service expose ports
	JupyterToken      string `json:"jupyter_token"`
	JupyterPort       int    `json:"jupyter_port"`
	TensorboardPort   int    `json:"tensorboard_port"`
	JupyterDomain     string `json:"jupyter_domain"`
	TensorboardDomain string `json:"tensorboard_domain"`
	SSHPort           int    `json:"ssh_port"`

	// Start required
	// (当container使用了gpu时, 一定要指定如下参数) 运行时有可能修改了GPU数量或者index, 控制容器挂载的gpu device list
	GPUUUIDList     []string `json:"gpu_uuid_list"`
	GPUCapabilities []string `json:"gpu_capabilities"`
	// CpuLimit 1 means 1cpu core, 0.1 means 0.1cpu core
	CpuLimit float64 `json:"cpu_limit"`
	// MemLimitInByte
	MemLimitInByte int64 `json:"mem_limit_in_byte"`

	// ExtraBindingPathList 上层可以额外指定将宿主机的某个目录挂载到容器内
	// []string{"/path/in/host:/path/in/container:rw,nocopy"}
	ExtraBindingPathList []string `json:"extra_binding_path_list"`
	// note: 纯业务逻辑。由上层指定，ExtraBindingPathList中的，哪些Target路径不允许挂载传播
	//  解决的问题是，当挂载子目录时，adfs由于无法直接通过挂载容器，将子目录直接挂载到宿主机上
	//  只能通过将根目录挂载到宿主机上，再把宿主机上的对应的子目录，直接挂载到容器内。
	//  agent中的逻辑是，只要dockerd支持挂载传播，就会默认使用，并对source和target目录做一些调整
	//  也就是, 正常的挂载传播，要挂的宿主机目录为/storage/uid:/autodl-fs, 用户容器内部，再把/autodl-fs/data -> /root/autodl-fs
	//  而子目录，没有下面的一层data目录，也就不可以使用挂载传播
	NotAllowMountShareList []string `json:"not_allow_mount_share_list"`

	// SSHPublicKey 上层指定的用户公钥，组织好内容，写入 /init/ssh/authorized_keys2中
	// agent 不校验数量以及正确性，由server保证。因此此处只需要string类型
	SSHPublicKey   string `json:"ssh_public_key"`
	AutopanelToken string `json:"autopanel_token"`

	PreStart []PreStartFunc `json:"pre_start"`

	ServicePortProtocol string `json:"service_port_protocol"` // 协议 http，tcp
}

func (p *NewContainerParam) cleanEnv() {
	var cleandEnv []string
	keyMapping := make(map[string]int)
	for _, v := range p.Env {
		parse := strings.Split(v, "=")
		if len(parse) == 0 {
			continue
		}

		if _, ok := keyMapping[parse[0]]; ok {
			continue
		}
		keyMapping[parse[0]]++
		cleandEnv = append(cleandEnv, v)
	}
}

func (p *NewContainerParam) SetEnv(key, value string) {
	p.cleanEnv()
	payload := fmt.Sprintf("%s=%s", key, value)

	for i, v := range p.Env {
		if strings.HasPrefix(v, key) {
			p.Env[i] = payload
			return
		}
	}

	p.Env = append(p.Env, payload)
}

func (p *NewContainerParam) GetEnv(key string) string {
	for _, v := range p.Env {
		kv := strings.Split(v, "=")
		if len(kv) < 2 {
			continue
		}

		if kv[0] == key {
			return kv[1]
		}
	}

	return ""
}

type AdditionalPortReflect struct {
	HostIP        string `json:"host_ip"`
	HostPort      string `json:"host_port"`
	ContainerPort int    `json:"container_port"`
	Proto         string `json:"proto"` // tcp/udp, default: tcp
}

func (p AdditionalPortReflect) Compare(n AdditionalPortReflect) bool {
	if n.HostIP == p.HostIP &&
		n.HostPort == p.HostPort &&
		n.ContainerPort == p.ContainerPort &&
		n.Proto == p.Proto {
		return true
	}
	return false
}

// ip:public:private/proto
func (p AdditionalPortReflect) String() string {
	if len(p.HostIP) != 0 {
		return fmt.Sprintf("%s:%s:%d", p.HostIP, p.HostPort, p.ContainerPort)
	}
	return fmt.Sprintf("%s:%d", p.HostPort, p.ContainerPort)
}

// StringWithProto : 格式为 ip:public:private/proto
func (p AdditionalPortReflect) StringWithProto() string {
	if len(p.Proto) == 0 {
		p.Proto = "tcp" // DEFAULT
	}

	if len(p.HostIP) != 0 {
		return fmt.Sprintf("%s:%s:%d/%s", p.HostIP, p.HostPort, p.ContainerPort, p.Proto)
	}
	return fmt.Sprintf("%s:%d/%s", p.HostPort, p.ContainerPort, p.Proto)
}

type NewContainerRuntimeParam struct {
	// Basic required, 可以使用 container id/container name
	ContainerID ContainerID `json:"container_id"`

	// If the timeout is nil, the container's StopTimeout value is used, if set,
	// otherwise the engine default. A negative timeout value can be specified,
	// meaning no timeout, i.e. no forceful termination is performed.
	StopTimeout *int `json:"stop_timeout"`
	// 要执行的命令，如：echo "123"
	Cmd string `json:"cmd"`
}

type ContainerExclusiveOption struct {
	ContainerID     ContainerID
	PreRunningMutex sync.Mutex
	RunningMutex    sync.Mutex
	MsgUUID         []string
	CancelFunc      []context.CancelFunc
}

type PreStartCreateADFSContainer struct {
	Image      string                `json:"image"`
	Command    string                `json:"command"`
	Privileged bool                  `json:"privileged"`
	Network    container.NetworkMode `json:"network"`
	// []string{"/path/in/host:/path/in/container:rw,nocopy"}
	BindingPathList []string `json:"binding_path_list"`
	WorkingDir      string   `json:"working_dir"`
	Env             []string `json:"env"`
}

type PreStartFunc struct {
	CreateContainer *PreStartCreateADFSContainer `json:"create_container"`
}
