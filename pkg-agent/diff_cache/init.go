package diff_cache

import (
	"context"
	"github.com/redis/go-redis/v9"
	"server/pkg-agent/agent_constant"
	"server/pkg/logger"
	"server/plugin/redis_plugin"
)

var cli *redis.Client
var localIntranetIP string
var localIntranetIPFromMachine string

func InitLocalRedis(ctx context.Context, l *logger.Logger, localRedisHost string, ossFileChecker bool) {
	if localRedisHost == "" {
		l.Info("localRedisHost is empty, continue...")
	}
	var err error

	cli, err = redis_plugin.AgentSimpleRedisClientProvider(&redis_plugin.RedisClientOption{
		Addr:     localRedisHost,
		Password: "autodl666", // default password
	})
	if err != nil {
		l.<PERSON>ield("host", localRedisHost).Info("localRedisHost init failed with error: %v, continue...", err)
		return
	}

	l.<PERSON><PERSON><PERSON>("host", localRedisHost).Info("localRedis init success...")

	RunOssFileReporter(l, agent_constant.FakeIntranetIP)
	go runOssFileDeleter(ctx)
	if ossFileChecker {
		go runOssFileChecker(ctx)
	}
}
