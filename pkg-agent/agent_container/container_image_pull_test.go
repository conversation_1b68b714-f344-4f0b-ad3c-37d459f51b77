package agent_container_test

import (
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	constant "server/pkg-agent/agent_constant"
	//. "server/pkg-agent/agent_container"
	//"server/pkg-agent/messenger"
	//"server/pkg/logger"
	//"strconv"
	//"strings"
	//"time"
	//"server/pkg/logger"
)

var _ = Describe("Container", func() {
	It("拉取镜像", func() {
		dockerClient, err := constant.NewDockerClient()
		Ω(err).Should(Succeed())
		Ω(dockerClient).ShouldNot(Equal(nil))

		//err = PullImage(context.Background(), logger.NewLogger("test"), dockerClient, "mysql", func(progress float64) {
		//	fmt.Println("------------------>", progress*100, "%")
		//})
		//Ω(err).Should(Succeed())
	})

})
