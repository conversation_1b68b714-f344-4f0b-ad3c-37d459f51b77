package agent_container

import (
	"context"
	"encoding/json"
	"github.com/docker/docker/api/types"
	"github.com/docker/docker/pkg/jsonmessage"
	"github.com/shopspring/decimal"
	"io"
	constant "server/pkg-agent/agent_constant"
	"server/pkg/logger"
	"strings"
	"time"
)

// PullImage 拉取镜像, 并且持续执行进度hook
func PullImage(ctx context.Context, l *logger.Logger, dockerClient constant.DockerClient, imageName string, progressHook func(pullProgress, downloadProgress float64)) (err error) {
	var responseBody io.ReadCloser
	responseBody, err = dockerClient.ImagePull(ctx, imageName, types.ImagePullOptions{
		All: false,
	})
	if err != nil {
		l.ErrorE(err, "begin pull image %s", imageName)
		return
	}
	defer func() {
		if progressHook != nil {
			progressHook(1, 0)
		}
		_ = responseBody.Close()
	}()
	var (
		dec = json.NewDecoder(responseBody)
		ids = make(map[string]int64)
	)

	// 上次执行progress hook的时间, 每3s执行一次
	// dockerd需要一定的时间返回给client所有layer的信息, 初步定为3s, 即3s后认为已经收集到了所有layer的id
	var begin time.Time
	// dockerd返回的第一条记录是镜像的名称, 不能算做 layer id
	firstLoop := true

	var lastProgress float64

	for {
		var jm jsonmessage.JSONMessage
		if err = dec.Decode(&jm); err != nil {
			if err == io.EOF {
				err = nil
				break
			}
			return err
		}
		if firstLoop {
			begin = time.Now()
			firstLoop = false
			l.Info("begin pull first layer of image %s", imageName)
			continue
		}

		// pull image完成前的最后几个log的id为空, 不能算做 layer id, 忽略之
		if len(jm.ID) < 10 {
			continue
		}

		// 为id初始化
		if _, ok := ids[jm.ID]; !ok {
			ids[jm.ID] = 0
		}

		// 记录精确计算的进度, 一些case被忽略了, 比如layer check sum, 所以只记录>0的进度
		if p := calculateLayerProgress(jm); p > ids[jm.ID] {
			ids[jm.ID] = p
		}

		if time.Now().After(begin.Add(time.Second * 3)) {
			begin = time.Now()
			var progressSum int64
			for _, p := range ids {
				progressSum += p
			}

			pro, _ := decimal.NewFromFloat(float64(progressSum) / float64(len(ids)*100)).Round(4).Float64()
			if lastProgress != pro {
				lastProgress = pro
				if progressHook != nil {
					progressHook(pro, 0)
				}
				l.Info("pull progress of image %s is %.4f", imageName, pro)
			}
		}
	}

	return
}

// calculateLayerProgress return [0, 100]
func calculateLayerProgress(jm jsonmessage.JSONMessage) (progress int64) {
	if strings.Contains(jm.Status, "Downloading") {
		// 下载中, [0, 50]
		if jm.Progress != nil {
			progress = int64(float64(jm.Progress.Current) / float64(jm.Progress.Total) * 50)
			return
		}
	} else if strings.Contains(jm.Status, "Extracting") {
		// 解压中, [50, 99]
		if jm.Progress != nil {
			progress = 50 + int64(float64(jm.Progress.Current)/float64(jm.Progress.Total)*48)
			return
		}

	} else if strings.Contains(jm.Status, "complete") {
		// 完成, [100]
		return 99
	}

	return
}
