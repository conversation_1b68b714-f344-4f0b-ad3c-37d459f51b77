package agent_container

import (
	"context"
	"fmt"
	"github.com/docker/docker/api/types"
	"github.com/docker/docker/client"
	"github.com/pkg/errors"
	"io"
	"os"
	"path/filepath"
	constant "server/pkg-agent/agent_constant"
	"server/pkg/logger"
	"strings"
	"time"
)

// ContainerRuntime 定义容器实例的运行时
type ContainerRuntime struct {
	dockerClient constant.DockerClient
	param        *constant.NewContainerRuntimeParam
	logger       *logger.Logger
}

func NewContainerRuntime(param *constant.NewContainerRuntimeParam, dockerClient constant.DockerClient) *ContainerRuntime {
	return &ContainerRuntime{
		dockerClient,
		param,
		logger.NewLogger(fmt.Sprintf("Container [%s]", param.ContainerID)),
	}
}

func (c *ContainerRuntime) validate() error {
	return validateContainerRuntimeParams(c.param, c.dockerClient)
}

func (c *ContainerRuntime) Stop(ctx context.Context) (err error) {
	err = c.validate()
	if err != nil {
		return err
	}

	var timeout time.Duration
	if c.param.StopTimeout != nil {
		timeout = time.Duration(*c.param.StopTimeout) * time.Second
	}
	if err = c.ExecCmd(ctx); err != nil {
		c.logger.WarnE(err, "Failed to exec cmd, ContainerID : %+v, cmd : %+v", c.param.ContainerID, c.param.Cmd)
	}

	err = c.dockerClient.ContainerStop(ctx, string(c.param.ContainerID), &timeout)
	if err != nil {
		// 找不到正好, 视为已经完成了
		if client.IsErrNotFound(err) {
			return nil
		}
		return err
	}
	// 正常停止了实例容器后，才停止 FS 容器。
	c.StopADFSContainer(ctx)
	return nil
}

func (c *ContainerRuntime) ExecCmd(ctx context.Context) (err error) {
	if c.param.Cmd == "" {
		return nil
	}

	c.logger.Info("container [%s] will run [%s] before exit", c.param.ContainerID, c.param.Cmd)

	// 在容器中执行命令
	execConfig := types.ExecConfig{
		Cmd:          []string{"bash", "-c", c.param.Cmd},
		AttachStdin:  false,
		AttachStdout: true,
		AttachStderr: true,
		Tty:          false,
	}

	subCtx, _ := context.WithTimeout(ctx, time.Second*15)

	// 创建执行实例
	exec, err := c.dockerClient.ContainerExecCreate(subCtx, string(c.param.ContainerID), execConfig)
	if err != nil {
		c.logger.WarnE(err, "Failed to create exec instance, ContainerID : %+v", c.param.ContainerID)
		return err
	}
	// 开始执行
	if err = c.dockerClient.ContainerExecStart(subCtx, exec.ID, types.ExecStartCheck{}); err != nil {
		c.logger.WarnE(err, "Failed to start exec instance, ContainerID : %+v, execId : %+v", c.param.ContainerID, exec.ID)
		return err
	}
	return
}

func (c *ContainerRuntime) Remove(ctx context.Context) (err error) {
	err = c.validate()
	if err != nil {
		return err
	}

	err = c.dockerClient.ContainerRemove(ctx, string(c.param.ContainerID), types.ContainerRemoveOptions{
		RemoveVolumes: true,
		Force:         true,
	})
	if err != nil {
		// 找不到正好, 视为已经完成了
		if client.IsErrNotFound(err) {
			return nil
		}
		return err
	}

	var removeVolumeErr error
	removeVolumeErr = c.dockerClient.VolumeRemove(ctx, getBootVolumeName(c.param.ContainerID), true)
	if removeVolumeErr != nil && !client.IsErrNotFound(removeVolumeErr) {
		c.logger.WarnE(removeVolumeErr, "remove volume of %s failed", c.param.ContainerID)
	}
	removeVolumeErr = c.dockerClient.VolumeRemove(ctx, GetStorageVolumeName(c.param.ContainerID), true)
	if removeVolumeErr != nil && !client.IsErrNotFound(removeVolumeErr) {
		c.logger.WarnE(removeVolumeErr, "remove volume of %s failed", c.param.ContainerID)
	}

	c.StopADFSContainer(ctx)
	c.RemoveADFSContainer(ctx)
	return
}

func (c *ContainerRuntime) RemoveButRetainVolumes(ctx context.Context) (err error) {
	err = c.validate()
	if err != nil {
		return err
	}

	err = c.dockerClient.ContainerRemove(ctx, string(c.param.ContainerID), types.ContainerRemoveOptions{
		RemoveVolumes: false,
		Force:         true,
	})
	// 找不到正好, 视为已经完成了
	if client.IsErrNotFound(err) {
		return nil
	}
	if err != nil {
		return
	}

	// 此处不删除卷, 给 create 复用.

	return
}

func (c *ContainerRuntime) Export(ctx context.Context) (err error) {
	err = c.validate()
	if err != nil {
		return err
	}

	var exportFile io.WriteCloser
	exportFile, err = os.Create(filepath.Join(os.TempDir(), "export_docker_image.tar"))
	if err != nil {
		return errors.Wrap(err, "create export file failed")
	}
	defer func() {
		_ = exportFile.Close()
	}()

	var srcBody io.ReadCloser
	srcBody, err = c.dockerClient.ContainerExport(ctx, string(c.param.ContainerID))
	if err != nil {
		return errors.Wrap(err, "container export failed")
	}
	defer func() {
		_ = srcBody.Close()
	}()

	_, err = io.Copy(exportFile, srcBody)
	if err != nil {
		return errors.Wrap(err, "io copy failed")
	}
	// TODO: move image to registry
	return
}

// StopADFSContainer 用于停止 FS 容器。
func (c *ContainerRuntime) StopADFSContainer(ctx context.Context) {
	timeout := time.Second * 3

	info, err := c.dockerClient.ContainerInspect(ctx, c.param.ContainerID.AdfsContainerName())
	if err == nil {
		for _, v := range info.Mounts {
			if v.Destination == "/cache" {
				if strings.HasPrefix(v.Source, constant.ADFSOnMachineCachePath) {
					if err = os.RemoveAll(v.Source); err != nil {
						c.logger.WarnE(err, "failed to remove fs cache directory")
					} else {
						c.logger.Info("removed fs cache directory")
					}
				}
			}
		}
	}

	err = c.dockerClient.ContainerStop(ctx, c.param.ContainerID.AdfsContainerName(), &timeout)
	if err != nil {
		if !client.IsErrNotFound(err) {
			c.logger.ErrorE(err, "failed to stop fs container %s", c.param.ContainerID.AdfsContainerName())
		}
	} else {
		c.logger.Info("stopped fs container %s", c.param.ContainerID.AdfsContainerName())
	}
}

// RemoveADFSContainer 用于删除 FS 容器。
func (c *ContainerRuntime) RemoveADFSContainer(ctx context.Context) {
	info, err := c.dockerClient.ContainerInspect(ctx, c.param.ContainerID.AdfsContainerName())
	if err == nil {
		for _, v := range info.Mounts {
			if v.Destination == "/cache" {
				if strings.HasPrefix(v.Source, constant.ADFSOnMachineCachePath) {
					if err = os.RemoveAll(v.Source); err != nil {
						c.logger.WarnE(err, "failed to remove fs cache directory")
					} else {
						c.logger.Info("removed fs cache directory")
					}
				}
			}
		}
	}

	err = c.dockerClient.ContainerRemove(ctx, c.param.ContainerID.AdfsContainerName(), types.ContainerRemoveOptions{
		Force: true,
	})
	if err != nil {
		if !client.IsErrNotFound(err) {
			c.logger.ErrorE(err, "failed to force remove fs container")
		}
	} else {
		c.logger.Info("force removed fs container %s", c.param.ContainerID.AdfsContainerName())
	}
}
