package mthreads

import (
	"fmt"
	"github.com/pkg/errors"
	"server/pkg-agent/agent_constant"
	"server/pkg-agent/hardware/resource"
	"strconv"
	"strings"
)

type GpuMthreadsDriveInfo struct {
}

func (MthreadsDrive GpuMthreadsDriveInfo) IsLibraryNotFound(err error) bool {
	if err == nil {
		return false
	}
	return err.Error() == ErrLibraryNotLoaded.Error()
}

func (MthreadsDrive GpuMthreadsDriveInfo) GpuKind() (kind agent_constant.GPUType) {
	return agent_constant.MThreadsGPUKey
}

func (MthreadsDrive GpuMthreadsDriveInfo) Initialize() (driverVersion string, err error) {
	var d *GPUQueryResult
	d, err = NewGPUInfo(int(0))
	if err != nil {
		err = ErrLibraryNotLoaded
		return
	}
	return d.DriverVersion, nil
}
func (MthreadsDrive GpuMthreadsDriveInfo) DeviceCount() (deviceCount uint, err error) {
	var d *GPUQueryResult
	d, err = NewGPUInfo(int(0))
	if err != nil {
		return
	}
	count, err := strconv.Atoi(d.AttachedGPUs)
	return uint(count), err
}
func (MthreadsDrive GpuMthreadsDriveInfo) GetDevice(deviceID uint) (device resource.GpuDevice, err error) {
	var d *GPUQueryResult
	d, err = NewGPUInfo(int(deviceID))
	if err != nil {
		return
	}
	if len(d.GPU) == 0 {
		err = errors.New(fmt.Sprintf("mthreads GetDevice %v err", deviceID))
		return
	}
	device = &GpuMthreadsDevice{d.GPU[0]}
	return
}
func (MthreadsDrive GpuMthreadsDriveInfo) Shutdown() (err error) {
	return nil
}

type GpuMthreadsDevice struct {
	GPU GPU
}

func (MthreadsDevice *GpuMthreadsDevice) UUID() (string, error) {
	return MthreadsDevice.GPU.UUID, nil
}
func (MthreadsDevice *GpuMthreadsDevice) Name() (string, error) {
	return MthreadsDevice.GPU.ProductName, nil
}
func (MthreadsDevice *GpuMthreadsDevice) MinorNumber() (uint, error) {
	MinorNumber, _ := strconv.Atoi(MthreadsDevice.GPU.Index)
	return uint(MinorNumber), nil
}
func (MthreadsDevice *GpuMthreadsDevice) MemoryInfo() (uint64, uint64, error) {
	mem := MthreadsDevice.GPU.FBMemoryUsage
	total := parseMemory(mem.Total)
	used := parseMemory(mem.Used)
	return total, used, nil
}
func (MthreadsDevice *GpuMthreadsDevice) UtilizationRates() (uint, uint, error) {
	mem := MthreadsDevice.GPU.Utilization
	GpuUsage := parsePercentage(mem.Gpu)
	MemoryUsage := parsePercentage(mem.Memory)
	return uint(GpuUsage), uint(MemoryUsage), nil
}
func (MthreadsDevice *GpuMthreadsDevice) PowerUsage() (uint, error) {
	PowerReadings := MthreadsDevice.GPU.PowerReadings
	PowerDraw := parsePowerDraw(PowerReadings.PowerDraw)
	return uint(PowerDraw), nil
}

// 返回速度最快的那个
func (MthreadsDevice *GpuMthreadsDevice) FanSpeed() (uint, error) {
	if len(MthreadsDevice.GPU.Fan.Channel) == 0 || len(MthreadsDevice.GPU.Fan.Speed) == 0 {
		return 0, errors.New("get gpu fan err")
	}
	fanSpeed := uint64(0)
	for _, speed := range MthreadsDevice.GPU.Fan.Speed {
		speedi := parsePercentage(speed)
		if speedi > fanSpeed {
			fanSpeed = speedi
		}
	}
	return uint(fanSpeed), nil
}
func (MthreadsDevice *GpuMthreadsDevice) Temperature() (uint, error) {
	tempStr := MthreadsDevice.GPU.Temperature
	temp := parseTemperature(tempStr.GPUCurrentTemp)
	return uint(temp), nil
}

// 辅助函数
func parseMemory(memStr string) uint64 {
	val, _ := strconv.ParseUint(strings.TrimSuffix(memStr, "MiB"), 10, 64)
	return val
}

func parsePercentage(memStr string) uint64 {
	val, _ := strconv.ParseUint(strings.Trim(memStr, "%"), 10, 64)
	return val
}

func parsePowerDraw(memStr string) float64 {
	val, _ := strconv.ParseFloat(strings.Trim(memStr, "W"), 64)
	return val
}

func parseTemperature(memStr string) uint64 {
	val, _ := strconv.ParseUint(strings.Trim(memStr, "C"), 10, 64)
	return val
}
