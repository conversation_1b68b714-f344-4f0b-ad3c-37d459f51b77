package mthreads

import (
	"bytes"
	"encoding/json"
	"fmt"
	"os/exec"
)

type GPUQueryResult struct {
	Timestamp     string `json:"Timestamp"`      //时间戳
	DriverVersion string `json:"Driver Version"` //驱动版本
	AttachedGPUs  string `json:"Attached GPUs"`  //总的卡数 n
	GPU           []GPU  `json:"GPU"`            //特定显卡的信息
}
type GPU struct {
	Index        string `json:"Index"`         //显卡索引顺序 0到n-1
	ProductName  string `json:"Product Name"`  //"MTT S4000"
	ProductBrand string `json:"Product Brand"` //"MTT"
	UUID         string `json:"GPU UUID"`      //gpu_uuid "89602407-c72e-0606-b557-d93ac9f48d3e
	SerialNumber string `json:"Serial Number"` //序列
	//PCI          struct {
	//	BusID       string `json:"Bus ID"`
	//	GPULinkInfo struct {
	//		PCIeGeneration struct {
	//			Max     string `json:"Max"`
	//			Current string `json:"Current"`
	//		} `json:"PCIe Generation"`
	//		LinkWidth struct {
	//			Max     string `json:"Max"`
	//			Current string `json:"Current"`
	//		} `json:"Link Width"`
	//	} `json:"GPU Link Info"`
	//} `json:"PCI"`
	FBMemorySpec struct { //内存信息
		Type      string `json:"Type"`      //显存类型 gddr6
		Vendor    string `json:"Vendor"`    //显存制造商
		Speed     string `json:"Speed"`     //单颗显存颗粒数据速率
		Bandwidth string `json:"Bandwidth"` //显存总带宽
		BusWidth  string `json:"Bus Width"` //显存总线位宽
	} `json:"FB Memory Spec"`
	FBMemoryUsage struct {
		Total string `json:"Total"` //物理显存总容量 49152MiB
		Used  string `json:"Used"`  //当前已使用的显存 0MiB
		Free  string `json:"Free"`  //剩余可用显存 49152MiB
	} `json:"FB Memory Usage"`
	Utilization struct { //gpu和memory的利用率
		Gpu    string `json:"Gpu"`    //"0%",
		Memory string `json:"Memory"` // "0%",
	} `json:"Utilization"`
	Temperature struct { //温度
		GPUCurrentTemp string `json:"GPU Current Temp"` // "43C"
	} `json:"Temperature"`
	PowerReadings struct { //功率
		PowerDraw string `json:"Power Draw "` //"150.85W"
	} `json:"Power Readings"`
	Fan struct {
		Channel string            `json:"Channel"` //通道数
		Speed   map[string]string `json:"Speed"`   //Fan0 应该是按照顺序展示的 "Fan0": "100%", "Fan1": "100%"
	} `json:"Fan"`
	Clocks struct {
		Graphics string `json:"Graphics"` //核心渲染频率  "1600MHz",
		Memory   string `json:"Memory"`   // 显存等效频率  "2003MHz",
	} `json:"Clocks"`
}

func NewGPUInfo(gpuIndex int) (*GPUQueryResult, error) {
	output, err := execCommand("bash", "-c", fmt.Sprintf("mthreads-gmi -q -i %d --json", gpuIndex))
	if err != nil {
		return nil, fmt.Errorf("command execution failed: %v", err)
	}
	var result GPUQueryResult
	if err := json.Unmarshal([]byte(output), &result); err != nil {
		return nil, fmt.Errorf("json unmarshal failed: %v", err)
	}
	return &result, nil
}

func execCommand(command string, args ...string) (string, error) {
	cmd := exec.Command(command, args...)
	var out bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &stderr
	err := cmd.Run()
	if err != nil {
		//log.Errorf("execCommand cmd.Run() failed with %s, err out: %s\n", err, stderr.String())
		return "", err
	}
	return out.String(), nil
}
