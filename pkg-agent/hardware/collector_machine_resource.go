package hardware

import (
	"net"
	"server/pkg-agent/agent_constant"
	"server/pkg-agent/hardware/gpu_detect_provider"
	"server/pkg/businesserror"
	"server/pkg/libs"
	"server/pkg/logger"
	"sync"
	"time"

	log "github.com/sirupsen/logrus"
)

var MachineInfo ResourceSummary

func getClientIp() (string, error) {
	address, err := net.InterfaceAddrs()
	if err != nil {
		log.WithField("err", err).Error("get machine ip address failed")
		err = businesserror.ErrInternalError
		return "", err
	}
	for _, addr := range address {
		// 检查ip地址判断是否回环地址
		if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				return ipnet.IP.String(), nil
			}

		}
	}
	return "", businesserror.ErrInternalError

}

func CollectNodeResourceInfo(l *logger.Logger) (rs *ResourceSummary, err error) {
	gpuInfo := &GpuInfo{}
	gpuInfo, err = GetGpuInfo()
	if err != nil {
		l.WarnE(err, "no gpu detected continue")
		return
	}

	var cpuInfo *CpuInfo
	gpuType, _ := DetectGpu()
	if gpuType == agent_constant.AscendAclGPUKey {
		cpuInfo, err = GetCpuInfoHuawei()
	} else {
		cpuInfo, err = GetCpuInfo()
	}
	if err != nil {
		l.WithField("err", err).Error("get cpu info failed")
		cpuInfo = &CpuInfo{}
	}

	var memInfo *MemInfo
	memInfo, err = GetMemInfo()
	if err != nil {
		l.WithField("err", err).Error("get mem info failed")
		memInfo = &MemInfo{}
	}

	var diskInfo []*agent_constant.DiskInfo
	diskInfo, err = GetDiskInfo()
	if err != nil {
		l.WithField("err", err).Error("get disk info failed")
		diskInfo = []*agent_constant.DiskInfo{}
	}

	var hostInfo *HostInfo
	hostInfo, err = GetHostInfo()
	if err != nil {
		l.WithField("err", err).Error("get host info failed")
		err = businesserror.ErrMachineHardwareInfo
		return
	}

	var versionInfo *VersionInfo
	if gpuType == agent_constant.AscendAclGPUKey {
		versionInfo = &VersionInfo{
			CudaVersion:   "", //gpuInfo.GPUS[0].DriverVersion,
			DriverVersion: gpuInfo.GPUS[0].DriverVersion,
		}
	} else if gpuType == agent_constant.MThreadsGPUKey {
		versionInfo = &VersionInfo{
			CudaVersion:   "", //gpuInfo.GPUS[0].DriverVersion,
			DriverVersion: gpuInfo.GPUS[0].DriverVersion,
		}
	} else {
		versionInfo, err = GetVersionInfo()
		if err != nil {
			l.WithField("err", err).Error("get cuda version and driver version failed")
			versionInfo = &VersionInfo{}
			// 当agent运行在cpu机器上时，应当允许跳过获取nvidia driver步骤，否则cpu agent将启动失败
			err = nil
		}
	}

	rs = &ResourceSummary{
		HostInfo:    hostInfo,
		CpuInfo:     cpuInfo,
		GpuInfo:     gpuInfo,
		MemInfo:     memInfo,
		DiskInfo:    diskInfo,
		VersionInfo: versionInfo,
		UpdatedAt:   time.Now(),
	}

	MachineInfo = *rs
	return
}

var detectFinishedResult bool
var detectedGpuTypeResult agent_constant.GPUType

func DetectGpu() (detectedGpuType agent_constant.GPUType, err error) {
	defer func() {
		detectFinishedResult = true
		detectedGpuTypeResult = detectedGpuType
	}()
	if detectFinishedResult {
		return detectedGpuTypeResult, nil
	}

	gpuFactory := gpu_detect_provider.GetGpuResourceFactory()

	for _, v := range gpuFactory {
		_, err = v.Initialize()
		if err != nil {
			// skip this type of gpu
			if v.IsLibraryNotFound(err) {
				continue
			}
			v.Shutdown()
			return
		}
		v.Shutdown()

		detectedGpuType = v.GpuKind()
		return
	}

	return detectedGpuType, nil
}

var giLock sync.RWMutex
var gi *GpuInfo

func GetGpuInfo() (gpuInfos *GpuInfo, err error) {
	if gi != nil {
		giLock.RLock()
		gii := *gi
		giLock.RUnlock()
		return &gii, nil
	}

	var gpuType agent_constant.GPUType
	gpuType, err = DetectGpu()
	if err != nil {
		return
	}

	gpuFactory := gpu_detect_provider.GetGpuResourceFactory()

	for _, gf := range gpuFactory {
		if gf.GpuKind() != gpuType {
			continue
		}

		gii, err := GetGpuResourcesSummary(gf)
		if err != nil {
			log.Error("collect gpu info the first time;", err)
			return nil, err
		}

		giLock.Lock()
		gi = gii
		giLock.Unlock()

		log.Info("collect gpu info the first time", libs.IndentString(gi))

		return gii, nil
	}

	return
}
