package agent_guard

import (
	"context"
	constant "server/pkg-agent/agent_constant"
	"server/pkg-agent/messenger"
	"server/pkg/logger"
	"server/pkg/serializer"
)

func (g *Guard) removeHandler(ctx context.Context, in messenger.Message, writer chan<- messenger.Message) {
	l := logger.NewLogger("Guard.Handler.Remove")

	var writeMessage *messenger.Message
	defer func() {
		if writeMessage != nil {
			writer <- *writeMessage
		} else {
			writer <- messenger.Message{
				MsgID:   in.MsgID,
				Type:    messenger.DefaultSkipType,
				Payload: "",
			}
		}
	}()

	payload := &constant.ContainerRuntimeParam{}
	parseErr := payload.ParseFromString(in.Payload)
	if parseErr != nil {
		if parseErr != constant.EchoMessageSkippedError {
			l.WithField("msg", serializer.IndentString(in)).WarnE(parseErr, "parse payload failed, skip")
		}
		return
	}

	defer l.<PERSON><PERSON>ield("container_id", payload.ContainerID).Info("Container remove handler finished.")

	release := g.killAndWaitContainerOptions(payload.ContainerID, in.MsgID)
	defer release()

	resp := constant.ContainerOperateResponse{
		ContainerID:           payload.ContainerID,
		ResponseFromAgentFlag: true,
	}

	rmContainerErr := g.booter.RemoveContainer(ctx, *payload)
	if rmContainerErr != nil {
		resp.Code = constant.CodeErr
		resp.ErrMsg = rmContainerErr.Error()
	} else {
		go g.gpuOccupy.unset(payload.ContainerID.String(), true, false, true)
	}

	payloadString, toErr := resp.String()
	if toErr != nil {
		l.WithField("resp", resp).Warn("resp msg marshal failed")
		return
	}

	writeMessage = &messenger.Message{
		MsgID:   in.MsgID,
		Type:    in.Type,
		Payload: payloadString,
	}
	return
}
