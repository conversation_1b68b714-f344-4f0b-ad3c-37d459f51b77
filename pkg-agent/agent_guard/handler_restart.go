package agent_guard

import (
	"context"
	constant "server/pkg-agent/agent_constant"
	"server/pkg-agent/messenger"
	"server/pkg/logger"
	"server/pkg/serializer"
	"time"
)

func (g *Guard) restartHandler(ctx context.Context, in messenger.Message, writer chan<- messenger.Message) {
	l := logger.NewLogger("Guard.Handler.Restart")

	var writeMessage *messenger.Message
	defer func() {
		if writeMessage != nil {
			writer <- *writeMessage
		} else {
			writer <- messenger.Message{
				MsgID:   in.MsgID,
				Type:    messenger.DefaultSkipType,
				Payload: "",
			}
		}
	}()

	payload := &constant.ContainerCreateAndRuntimeParam{}
	parseErr := payload.ParseFromString(in.Payload)
	if parseErr != nil {
		if parseErr != constant.EchoMessageSkippedError {
			l.With<PERSON>ield("msg", serializer.IndentString(in)).WarnE(parseErr, "parse payload failed, skip")
		}
		return
	}
	l = l.With<PERSON>ield("container_id", payload.RuntimeParam.ContainerID)

	defer l.Info("Container restart handler finished.")

	resp := constant.ContainerOperateResponse{
		ContainerID:           payload.RuntimeParam.ContainerID,
		ResponseFromAgentFlag: true,
	}

	// restart 过程中需要冻结container的所有操作, 否则由于先 stop -> 再 start的操作, 会造成严重的误判(外界认为该container已经被 shutdown)
	g.freezeMutex.Lock()
	g.freezeSet[payload.RuntimeParam.ContainerID] = true
	g.freezeMutex.Unlock()
	defer func() {
		g.freezeMutex.Lock()
		g.freezeSet[payload.RuntimeParam.ContainerID] = false
		g.freezeMutex.Unlock()
	}()

	waitTime := time.Second * 5
	err := g.booter.StopContainer(ctx, payload.RuntimeParam)
	if err != nil {
		waitTime = time.Second * 10
		l.ErrorE(err, "container restart: stop container failed, but will try to start container 10 seconds after")
	}

	time.Sleep(waitTime)
	err = g.booter.StartContainer(ctx, payload.Param)
	if err != nil {
		l.ErrorE(err, "container restart: start container failed after [%d] seconds", waitTime/time.Second)
	}

	if err != nil {
		resp.Code = constant.CodeErr
		resp.ErrMsg = err.Error()
	}

	payloadString, toErr := resp.String()
	if toErr != nil {
		l.WithField("resp", resp).Warn("resp msg marshal failed")
		return
	}

	writeMessage = &messenger.Message{
		MsgID:   in.MsgID,
		Type:    in.Type,
		Payload: payloadString,
	}
}
