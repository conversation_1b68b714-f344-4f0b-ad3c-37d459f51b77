package kv_plugin

import (
	"encoding/json"
	"fmt"
	"server/pkg-agent/file_transfer"
	"server/pkg/constant"
	"strconv"
	"strings"
	"time"
)

type Key string

const (
	FileTransferStatus Key = "Ft.status"

	FileTransferUserLimit Key = "Ft.ul"

	TaskTypeFileTransfer   string = "FileTransfer"
	TaskTypeCloneContainer string = "CloneContainer"
)

// ft

func FileTransferStatusGenerateUUID(srcInstanceUUID, dstInstanceUUID constant.InstanceUUIDType) string {
	return fmt.Sprintf("%s_%s_%s", FileTransferStatus, srcInstanceUUID, dstInstanceUUID)
}

func FileTransferUserLimitGenerateUUID(userUUID string, thisDay int) string {
	return fmt.Sprintf("%s_%s_%d", FileTransferUserLimit, userUUID, thisDay)
}

// clone container

func FileTransferCloneContainerStatusGenerateUUID(srcRuntimeUUID, dstRuntimeUUID constant.ContainerRuntimeUUID) string {
	return fmt.Sprintf("%s_clone_%s_%s", FileTransferStatus, srcRuntimeUUID, dstRuntimeUUID)
}

func FileTransferCloneContainerUserLimitGenerateUUID(userUUID string, thisDay int) string {
	return fmt.Sprintf("%s_clone_%s_%d", FileTransferUserLimit, userUUID, thisDay)
}

func GenPrivateImageDeletedKey(t time.Time) string {
	return fmt.Sprintf("%s-%d-%d-%d", "deleted-tar", t.Month(), t.Day(), t.Hour())
}

// parse data

func FileTransferStatusGetValue(payload string) (status file_transfer.TaskStatus) {
	if len(payload) != 0 {
		_ = json.Unmarshal([]byte(payload), &status)
	}
	return
}

func FileTransferUserLimitGetValue(payload string) (times int) {
	if len(payload) != 0 {
		times, _ = strconv.Atoi(payload)
	}
	return
}

type KeyInfo struct {
	TaskType string
	Src      string
	Dst      string
}

func ParseKeyFromTaskID(taskID string) (*KeyInfo, bool) {
	info := &KeyInfo{}
	if strings.Contains(taskID, "clone") {
		info.TaskType = TaskTypeCloneContainer
	} else {
		info.TaskType = TaskTypeFileTransfer
	}

	fields := strings.Split(taskID, "_")
	switch info.TaskType {
	case TaskTypeFileTransfer:
		if len(fields) != 3 {
			return nil, false
		}
		info.Src = fields[1]
		info.Dst = fields[2]
	case TaskTypeCloneContainer:
		if len(fields) != 4 {
			return nil, false
		}
		info.Src = fields[2]
		info.Dst = fields[3]
	}
	return info, true
}
