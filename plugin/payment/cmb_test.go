package payment

import (
	"fmt"
	"server/pkg/constant"
	"server/pkg/libs"
	"strconv"
	"strings"
	"testing"
	"time"
	"unicode"
)

// "transDate": "********"
// "valueDate": "********"
// calueData是到账日期？比如15号的转账，但是16号到的，那我这个列表查16号能查到吗

func TestCmbBank_CmbTradeList2(t *testing.T) {
	uid := "U006073461"
	sm2 := "NBtl7WnuUtA2v5FaebEkU0/Jj1IodLGT6lQqwkzmd2E="
	sm4 := "VuAzSWQhsoNqzn0K"
	cardNbr := "***************"

	now := time.Now().AddDate(-1, 0, 0)
	beginDate := now.Format("********")
	endDate := now.AddDate(1, 0, 0).Format("********")
	cmb := NewCmbBank(true, uid, sm2, sm4, cardNbr)

	ti := time.NewTimer(time.Second * 1)

	req := &CmbTradeListReq{
		TRANSQUERYBYBREAKPOINT_X1: []TRANSQUERYBYBREAKPOINT_X1{{
			CardNbr:             cmb.CardNbr,
			BeginDate:           beginDate,
			EndDate:             endDate,
			TransactionSequence: "1",
			QueryAcctNbr:        "",
			LoanCode:            constant.LoanCode_C,
		}},
	}

	for {
		select {
		case <-ti.C:
			res, err := cmb.CmbTradeList(req)
			if err != nil {
				t.Fatal(err)
				return
			}

			req.TRANSQUERYBYBREAKPOINT_X1[0].QueryAcctNbr = res.TRANSQUERYBYBREAKPOINT_Y1[0].AcctNbr
			req.TRANSQUERYBYBREAKPOINT_Y1 = res.TRANSQUERYBYBREAKPOINT_Y1

			for _, v := range res.TRANSQUERYBYBREAKPOINT_Z2 {
				if v.LoanCode == constant.LoanCode_D {
					continue
				}
				if strings.Contains(v.CtpBankName, "中科视拓") ||
					strings.Contains(v.CtpAcctName, "视拓云") {
					continue
				}

				if strings.TrimSpace(v.VirtualNbr) != "" {
					t.Log(v)
				}

				//t.Log("----------------")
				//t.Log("----------------")
				//t.Log(v.GetTransAmountForADL())
				//t.Log(v.TransAmount)
				//t.Log(v.TransSequenceIdn)
				//t.Log(v.CtpAcctName)
				//t.Log(v.CtpAcctNbr)
				//t.Log(v.GetTransTimeForADL())
				//t.Log(v.RemarkTextClt)
				//t.Log(libs.ParseADLRemittanceCode(v.RemarkTextClt))
			}

			//s, _ := json.MarshalIndent(res, " ", "  ")
			//t.Log(string(s))

			next := time.Second
			//if len(res.TRANSQUERYBYBREAKPOINT_Z1) == 0 || res.TRANSQUERYBYBREAKPOINT_Z1[0].CtnFlag == constant.CtnFlag_N {
			//	next = time.Second * 5
			//}

			ti.Reset(next)
		}
	}

}

func GetADL(str string) string {
	/*
		以ADL开头
		长度大于等于
		识别码长度共11位，ADL+8位数字
			是只截取前面11位，还是字符串只能有11位
	*/

	l := strings.Fields(str)

	code := ""
	for _, v := range l {
		// 必须包含ADL
		index := strings.Index(v, "ADL")
		if index < 0 {
			continue
		}

		// 识别码长度11位
		if len(v) < 11 {
			continue
		}

		// 是不是备注连起来的情况,第12位不可以是数字，是数字无法识别（万一输错了，就充错了）
		// ADL12345678,hahah
		// ADL12345678hahah
		// ADL12345678123hahah
		if len(v) > 11 {
			if unicode.IsDigit(rune(v[11])) { // 是数字
				continue
			}
		}
		v = v[:11] // 保留前11位

		_, err := strconv.Atoi(v[3:])
		if err != nil {
			continue
		}

		// 识别到的识别码不止一个，且不是同一个
		if code != "" && code != v {
			return ""
		}
		code = v
	}

	return code
}

func Test_GetTransAmountForADL(t *testing.T) {
	t.Log(strings.Trim("1.00", "."))
	f, err := strconv.ParseFloat("1.00", 64)
	if err != nil {
		t.Fatal()
	}

	f *= float64(1000)
	t.Log(f)
	t.Log(int64(f))

	amount, err := strconv.Atoi(strings.TrimSpace(strings.Trim("1.00", ".")) + "0")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(amount)
}

func TestADL(t *testing.T) {
	n1 := "123545"
	n2 := "ADL00000001" // V
	n3 := "ADL000000013434"
	n4 := "ADL00000001 fjdaj"
	n5 := "ADL00000001 ADL00000001 fjdaj"
	n6 := "ADL00000001 ADL00000fdf fjdaj"
	n7 := "ADL00000001ADL00000fdf fjdaj"
	n8 := "ADL00000001,ADL00000fdf fjdaj"
	n9 := "ADL0a000001,ADL00000fdf fjdaj"
	n10 := "款ADL0001转ADL00000001"

	t.Log("n1:")
	t.Log(libs.ParseADLRemittanceCode(n1))
	t.Log("n2:")
	t.Log(libs.ParseADLRemittanceCode(n2))
	t.Log("n3:")
	t.Log(libs.ParseADLRemittanceCode(n3))
	t.Log("n4:")
	t.Log(libs.ParseADLRemittanceCode(n4))
	t.Log("n5:")
	t.Log(libs.ParseADLRemittanceCode(n5))
	t.Log("n6:")
	t.Log(libs.ParseADLRemittanceCode(n6))
	t.Log("n7:")
	t.Log(libs.ParseADLRemittanceCode(n7))
	t.Log("n8:")
	t.Log(libs.ParseADLRemittanceCode(n8))
	t.Log("n9:")
	t.Log(libs.ParseADLRemittanceCode(n9))
	t.Log("n10:")
	t.Log(libs.ParseADLRemittanceCode(n10))
}

func Test2(t *testing.T) {
	n := "00001a"
	uid, err := strconv.Atoi(n)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(uid)
}

func Test3(t *testing.T) {
	fmt.Printf("%0*d", 5, 123)
}
