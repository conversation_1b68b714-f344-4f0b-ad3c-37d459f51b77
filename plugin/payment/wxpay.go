package payment

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"server/conf"
	"server/pkg/constant"
	"strings"
	"time"

	"github.com/wechatpay-apiv3/wechatpay-go/services/refunddomestic"

	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/notify"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/native"
)

// https://pay.weixin.qq.com/docs/merchant/products/native-payment/introduction.html

const wxNotifyRouter = "/api/v1/pay/weixin/callback"
const wxRefundNotifyRouter = "/api/v1/pay/weixin/refund/callback"

type WxPayPlugin struct {
	client *core.Client
	nh     *notify.Handler
}

func NewWxPayPluginProvider(client *core.Client, nh *notify.Handler) *WxPayPlugin {
	return &WxPayPlugin{nh: nh, client: client}
}

type CreateOrderParams struct {
	BillUUID string
	Amount   int64
}

type PrepayWithRequestPaymentResponse struct {
	// 预支付交易会话标识
	PrepayId string `json:"prepay_id"` // revive:disable-line:var-naming
	// 应用ID
	Appid string `json:"appId"`
	// 时间戳
	TimeStamp string `json:"timeStamp"`
	// 随机字符串
	NonceStr string `json:"nonceStr"`
	// 订单详情扩展字符串
	Package string `json:"package"`
	// 签名方式
	SignType string `json:"signType"`
	// 签名
	PaySign string `json:"paySign"`
}

func (c *CreateOrderParams) Check() error {
	if c.BillUUID == "" ||
		c.Amount < 10 || c.Amount > ********** {
		return errors.New("请求参数错误, 请检查参数")
	}
	return nil
}

// 创建支付链接(二维码)
func (w *WxPayPlugin) Create(ctx context.Context, params *CreateOrderParams) (string, error) {
	err := params.Check()
	if err != nil {
		return "", err
	}

	// 因为平台单位换算原因, 这里要 /10
	amount := params.Amount / 10
	api := native.NativeApiService{Client: w.client}
	expireTime := time.Now().Add(constant.RechargeExpireDuration)
	req := native.PrepayRequest{
		Appid:       core.String(WxPayAppID),
		Mchid:       core.String(WxPayMchID),
		Description: core.String("AutoDL服务器租赁"),                                                                              // 描述, 不能为空, 否则会报错
		OutTradeNo:  core.String(params.BillUUID),                                                                            // 内部uuid
		TimeExpire:  &expireTime,                                                                                             // 订单超时时间, 不指定默认为2小时
		NotifyUrl:   core.String(constant.BuildHttpUrl(conf.GetGlobalGsConfig().App.BusinessCallbackDomain, wxNotifyRouter)), // 回调地址
		Amount:      &native.Amount{Total: &amount, Currency: core.String("CNY")},                                            // 交易金额, 单位为分
	}

	resp, result, err := api.Prepay(ctx, req)
	if err != nil {
		return "", err
	}
	if result.Response.StatusCode < 200 || result.Response.StatusCode >= 300 {
		return "", errors.New(result.Response.Status)
	}
	return *resp.CodeUrl, nil
}

// 关闭支付
func (w *WxPayPlugin) Close(ctx context.Context, billUUID string) error {
	api := native.NativeApiService{Client: w.client}
	result, err := api.CloseOrder(ctx, native.CloseOrderRequest{
		OutTradeNo: core.String(billUUID),
		Mchid:      core.String(WxPayMchID),
	})
	if err != nil {
		return err
	}

	// 关闭成功会返回 204 No Content
	if result.Response.StatusCode < 200 && result.Response.StatusCode >= 300 {
		return errors.New(result.Response.Status)
	}
	return nil
}

// 主动查询接口
func (w *WxPayPlugin) Query(ctx context.Context, billUUID string) (*payments.Transaction, error) {
	api := native.NativeApiService{Client: w.client}
	resp, result, err := api.QueryOrderByOutTradeNo(ctx, native.QueryOrderByOutTradeNoRequest{
		OutTradeNo: core.String(billUUID),
		Mchid:      core.String(WxPayMchID),
	})
	if err != nil {
		return nil, err
	}
	if result.Response.StatusCode != 200 {
		return nil, errors.New(result.Response.Status)
	}

	// 因为平台单位换算原因, 这里直接 *10
	if resp != nil && resp.Amount != nil && resp.Amount.Total != nil && resp.Amount.PayerTotal != nil {
		*resp.Amount.Total = *resp.Amount.Total * 10
		*resp.Amount.PayerTotal = *resp.Amount.PayerTotal * 10
	}
	return resp, nil
}

// 通知解密
func (w *WxPayPlugin) ParseNotify(ctx context.Context, request *http.Request) (content *payments.Transaction, err error) {
	content = new(payments.Transaction)
	_, err = w.nh.ParseNotifyRequest(ctx, request, content)
	if err != nil {
		return nil, err
	}

	// 因为平台单位换算原因, 这里直接 *10
	if content.Amount != nil && content.Amount.Total != nil && content.Amount.PayerTotal != nil {
		*content.Amount.Total = *content.Amount.Total * 10
		*content.Amount.PayerTotal = *content.Amount.PayerTotal * 10
	}
	return

}

type WxRefundParams struct {
	BillUUID string `json:"bill_uuid"`
	// refund-{BillUUID}[-num]
	RefundBillUUID string `json:"refund_bill_uuid"`
	Total          int64  `json:"total"`
	Refund         int64  `json:"amount"`
}

func (w *WxPayPlugin) Refund(ctx context.Context, params *WxRefundParams) error {
	api := refunddomestic.RefundsApiService{Client: w.client}

	_, _, err := api.Create(ctx, refunddomestic.CreateRequest{
		OutTradeNo:   &params.BillUUID,
		OutRefundNo:  &params.RefundBillUUID,
		Reason:       core.String("AutoDL用户提现"),
		NotifyUrl:    core.String(constant.BuildHttpUrl(conf.GetGlobalGsConfig().App.BusinessCallbackDomain, wxRefundNotifyRouter)),
		FundsAccount: nil,
		Amount: &refunddomestic.AmountReq{
			Refund:   core.Int64(params.Refund / 10),
			From:     nil,
			Total:    core.Int64(params.Total / 10),
			Currency: core.String("CNY"),
		},
		GoodsDetail: nil,
	})
	if err != nil {
		errStr := err.Error()
		index := strings.Index(errStr, "Header:\n")
		errStr = errStr[:index]
		return fmt.Errorf("refund request fail, %s", errStr)
	}

	return nil
}

func (w *WxPayPlugin) RefundQuery(ctx context.Context, refundUUID string) (resp *refunddomestic.Refund, err error) {
	api := refunddomestic.RefundsApiService{Client: w.client}
	resp, _, err = api.QueryByOutRefundNo(ctx, refunddomestic.QueryByOutRefundNoRequest{
		OutRefundNo: core.String(refundUUID),
	})
	if err != nil {
		errStr := err.Error()
		index := strings.Index(errStr, "Header:\n")
		errStr = errStr[:index]
		return nil, fmt.Errorf("query refund failed, %s", errStr)
	}

	// 因为平台单位换算原因, 这里直接 *10
	if resp != nil && resp.Amount != nil && resp.Amount.Total != nil && resp.Amount.PayerTotal != nil {
		*resp.Amount.Total = *resp.Amount.Total * 10
		*resp.Amount.PayerTotal = *resp.Amount.PayerTotal * 10
		*resp.Amount.Refund = *resp.Amount.Refund * 10
	}

	return resp, nil
}

type RefundNotify struct {
	*refunddomestic.Refund
}

// 通知解密 -- 退款
func (w *WxPayPlugin) ParseRefundNotify(ctx context.Context, request *http.Request) (content *RefundNotify, err error) {
	content = new(RefundNotify)
	_, err = w.nh.ParseNotifyRequest(ctx, request, content)
	if err != nil {
		return nil, err
	}

	// 因为平台单位换算原因, 这里直接 *10
	if content.Amount != nil && content.Amount.Total != nil && content.Amount.PayerTotal != nil {
		*content.Amount.Total = *content.Amount.Total * 10
		*content.Amount.PayerTotal = *content.Amount.PayerTotal * 10
		*content.Amount.Refund = *content.Amount.Refund * 10
	}

	return
}
