package redis_queue

import (
	"github.com/redis/go-redis/v9"
	"server/pkg/logger"
	"server/plugin/queue_interface"
	"time"
)

type MessageStatusType string

const (
	MessageIsCreated      = "Created"
	MessageIsInQueue      = "InQueue"
	MessageIsReQueued     = "ReQueued"
	MessageIsHandling     = "Handling"
	MessageIsFinished     = "Finished"
	MessageIsDropped      = "Dropped"
	MessageIsOverMaxTimes = "OverMaxTimes"

	MaxRetryTimes   = 20
	QueuePayloadKey = "payload"
)

type ElementPayload struct {
	Type    queue_interface.QueueElementType
	MsgUUID string    // in DB model.Message UUID
	Payload string    // ElementPayloadContent
	ValidAt time.Time // 消息的创建时间, 用于在消费处通过lock的方式, 来判断+丢弃旧消息
}

type ConsumerHandlerInterface interface {
	Ack() error
	Nack() error
	Reject() error
	Drop()
}

type ConsumerHandler struct {
	cli      *redis.Client
	id       uint64
	channel  string
	msgUUID  string
	handling func(msgUUID string, statusType MessageStatusType)

	// 当前消息重试次数, 检测如果>阈值, 将不再requeue
	retryTimes int64

	logger *logger.Logger
}

func (r *ConsumerHandler) Ack() (err error) {
	err = r.cli.XDel(r.channel, r.msgUUID).Err()
	if err != nil {
		return
	}
	if r.handling != nil {
		r.handling(r.msgUUID, MessageIsFinished)
	}
	return
}

func (r *ConsumerHandler) Nack() (err error) {
	if r.handling != nil {
		r.handling(r.msgUUID, MessageIsReQueued)
	}
	return
}

func (r *ConsumerHandler) Reject() (err error) {
	if r.handling != nil {
		r.handling(r.msgUUID, MessageIsReQueued)
	}
	return
}

func (r *ConsumerHandler) Drop() {
	if r.handling != nil {
		r.handling(r.msgUUID, MessageIsDropped)
	}
	//_ = r.channel.Reject(r.id, false)
}

type QueueLock interface {
	Lock(duration time.Duration) (bool, error)
	LockWithNoRetry(duration time.Duration) (bool, error)
	LockDelay(seconds int64) error
	UnLock() error
}

type SubHandlerStatus struct {
	StartAt        time.Time
	QuitChan       chan int
	LockDelayTimes int64
}
