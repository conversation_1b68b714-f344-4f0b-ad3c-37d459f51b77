package redis_queue

import (
	"github.com/redis/go-redis/v9"
	"server/conf"
	"server/conf/test"
	"testing"
)

func TestConnect(t *testing.T) {
	test.InitConfigForTest()
	cli := redis.NewClient(&redis.Options{Addr: conf.GetGlobalGsConfig().Redis.Host})
	defer cli.Close()
	s, err := cli.Get(context.Background(), "testkey").Result()
	if err != nil {
		t.Log(err)
	}
	t.Log(s)
}

//func TestSub(t *testing.T) {
//	test.InitConfigForTest()
//	cli := redis.NewClient(&redis.Options{Addr: conf.GetGlobalGsConfig().Redis.Host})
//	defer cli.Close()
//
//	//go func() {
//	//	for {
//	//		s, err := cli.Get(context.Background(),"test").Result()
//	//		t.Log(err)
//	//		t.Log(s)
//	//		time.Sleep(time.Second)
//	//	}
//	//
//	//}()
//
//	readResult, err := cli.XRead(&redis.XReadArgs{
//		Streams: []string{"testsub", "0-0"}, // todo：此处是不是可以一次多监听几个channel
//		Count:   1,
//		//Block:   0, //todo: 此处block会导致redis进程锁住吗
//		Block: 0, //todo: 此处block会导致redis进程锁住吗
//	}).Result()
//	if err != nil {
//		t.Log(err)
//	}
//	t.Log(readResult)
//
//	msgUUID := readResult[0].Messages[0].ID
//
//	time.Sleep(time.Second * 5)
//
//	t.Log("xack--------------------")
//	i, err := cli.XDel("testsub", msgUUID).Result()
//	t.Log(err)
//	t.Log(i)
//
//}
//
//func TestSub1(t *testing.T) {
//	test.InitConfigForTest()
//	cli := redis.NewClient(&redis.Options{Addr: conf.GetGlobalGsConfig().Redis.OnlineHost})
//	defer cli.Close()
//
//	readResult, err := cli.XRead(&redis.XReadArgs{
//		Streams: []string{"testsub1", "0-0"}, // todo：此处是不是可以一次多监听几个channel
//		Count:   1,
//		//Block:   0, //todo: 此处block会导致redis进程锁住吗
//		Block: -1, //todo: 此处block会导致redis进程锁住吗
//	}).Result()
//	if err != nil {
//		t.Log(err)
//	} else {
//		t.Log(readResult)
//	}
//}
