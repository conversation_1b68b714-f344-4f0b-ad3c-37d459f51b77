package redis_plugin

import (
	"context"
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/libs"
	"server/pkg/logger"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
)

const DefaultPortSetKey = "ports_available"

/**
 * 由于采用 pop 模式, 且不要求顺序, 所以不再需要加锁了.
 * 需要在服务启动的时候将数据库中的记录同步到 redis 中.
 * redis 存在即可用, 弹出即绑定.
 * 因为涉及 db, 改由调用者 do telnet. 发现占用之后补充到 db 中. 不在这个 plugin 中集成太多功能.
 */

type PortPlugin struct {
	client    *redis.Client
	keyPrefix string
	l         *logger.Logger
}

const (
	portRegionPrefix     = "port_region_"
	PortLockWaitDuration = time.Second * 50
	secNamePortPlugin    = "port_plugin"
)

func NewPortPlugin(
	redisClient *redis.Client,
) *PortPlugin {
	return &PortPlugin{
		client:    redisClient,
		keyPrefix: portRegionPrefix,
		l:         logger.NewLogger(secNamePortPlugin),
	}
}

// redis CRUD ---------------------------------------------------------------------------

// PopPortsFromSet redis存在即可用, 弹出即绑定. 由于端口分配不严格要求顺序, 所以送回去的过程不要求严格同步
func (hash *PortPlugin) PopPortsFromSet(region constant.RegionSignType, amount int) (ports []int, err error) {
	// 采用 pop, 每次从 set 中 pop 三个出来, 以此避免各种锁
	portStr, err := hash.client.SPopN(context.Background(), hash.key(region), int64(amount)).Result()
	if err != nil {
		hash.l.WithField("region", region).WithError(err).Info("SPop ports from set failed.")
		return
	}

	hash.l.WithField("region", region).Info("Get ports by SPopN: %s", portStr)

	// 集合中string类型转换为int类型
	ports, err = libs.ArrAtoi(portStr)
	if err != nil {
		hash.l.WithField("region", region).WithError(err).Info("SPop ports from set failed.")
		return
	}

	if len(ports) != amount {
		hash.l.WithField("region", region).WithError(err).Warn("ErrPortHasBeenExhausted.")
		return ports, biz.ErrPortHasBeenExhausted
	}

	return ports, nil
}

// AddPortsToSet 将 ports 加入 redis中
func (hash *PortPlugin) AddPortsToSet(region constant.RegionSignType, ports ...int) (err error) {
	if len(ports) == 0 {
		return // nothing happened.
	}

	var strPorts = make([]string, 0)
	for i := range ports {
		strPorts = append(strPorts, strconv.Itoa(ports[i]))
	}

	err = hash.client.SAdd(context.Background(), hash.key(region), strPorts).Err()
	if err != nil {
		return err
	}

	return
}

// IsPortInSet 判断port是否在 redis 集合中
func (hash *PortPlugin) IsPortInSet(region constant.RegionSignType, port int) (isMember bool, err error) {
	isMember, err = hash.client.SIsMember(context.Background(), hash.key(region), port).Result()
	if err != nil {
		return false, err
	}

	return
}

// ClearPortsInSet 清除集合中的端口
func (hash *PortPlugin) ClearPortsInSet(region constant.RegionSignType) (err error) {
	// NOTE: 数量过大, 分段 Rem 再 Del.
	err = hash.client.Del(context.Background(), hash.key(region)).Err()
	if err != nil {
		hash.l.WarnE(err, "ClearPortsInSet() Del [%s] failed.", hash.key(region))
		return err
	}
	return nil
}

// LengthOfSet 获取 redis 集合中对应key的长度
func (hash *PortPlugin) LengthOfSet(region constant.RegionSignType) (length int64, err error) {
	length, err = hash.client.SCard(context.Background(), hash.key(region)).Result()
	if err != nil {
		return
	}
	return
}

// IsSetEmpty 测试key中是否为空
func (hash *PortPlugin) IsSetEmpty(region constant.RegionSignType) (zero bool, err error) {
	str, err := hash.client.SMembers(context.Background(), hash.key(region)).Result()
	if err != nil {
		return
	}
	zero = len(str) == 0
	return
}

// RemovePortsInSet 在集合中删除 ports
func (hash *PortPlugin) RemovePortsInSet(region constant.RegionSignType, ports ...int) (err error) {
	if len(ports) == 0 {
		return nil // nothing happened.
	}

	var strPorts = make([]string, 0)
	for i := range ports {
		strPorts = append(strPorts, strconv.Itoa(ports[i]))
	}
	err = hash.client.SRem(context.Background(), hash.key(region), strPorts).Err()
	if err != nil {
		return err
	}
	return
}

func (hash *PortPlugin) key(region constant.RegionSignType) string {
	return hash.keyPrefix + string(region)
}
