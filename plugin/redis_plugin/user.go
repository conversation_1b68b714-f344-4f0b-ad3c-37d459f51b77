package redis_plugin

import (
	"context"
	"encoding/json"
	"fmt"
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/logger"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
)

type UserPlugin struct {
	client *redis.Client
	l      *logger.Logger
}

const ModuleName = "user_plugin"

func NewUserPluginProvider(client *redis.Client) *UserPlugin {
	return &UserPlugin{client: client, l: logger.NewLogger(ModuleName)}
}

func defineRedisKey(redisPrefix RedisPrefix, data string) string {
	return fmt.Sprintf("%s%s", redisPrefix, data)
}

// SetTicket 设置用户ticket
func (up *UserPlugin) SetTicket(ticket string, id int) (err error) {
	err = up.client.Set(context.Background(), defineRedisKey(TicketKeyPrefix, ticket), strconv.Itoa(id), constant.TicketExpiredTimeOut).Err()
	return
}

func (up *UserPlugin) SetTicketSubUser(ticket string, subName string) (err error) {
	err = up.client.Set(context.Background(), defineRedisKey(TicketKeyPrefix, ticket), subName, constant.TicketExpiredTimeOut).Err()
	return
}

// GetTicket 获取用户ticket
func (up *UserPlugin) GetTicket(ticket string) (id int, err error) {
	var content string
	content, err = up.client.Get(context.Background(), defineRedisKey(TicketKeyPrefix, ticket)).Result()
	if err != nil {
		up.l.WarnE(err, "get ticket from redis failed.")
		return
	}
	id, err = strconv.Atoi(content)
	if err != nil {
		up.l.WarnE(err, "uid not found by ticket")
		return
	}
	return
}

// GetTicketSubUser 获取用户ticket
func (up *UserPlugin) GetTicketSubUser(ticket string) (subUser string, err error) {
	subUser, err = up.client.Get(context.Background(), defineRedisKey(TicketKeyPrefix, ticket)).Result()
	if err != nil {
		up.l.WarnE(err, "get ticket from redis failed.")
		return
	}
	return
}

func (up *UserPlugin) SetSSOTicket(ticket string, id int) (err error) {
	err = up.client.Set(context.Background(), defineRedisKey(SSOTicketKeyPrefix, ticket), strconv.Itoa(id), constant.SSOTicketExpiredTimeOut).Err()
	return
}

func (up *UserPlugin) GetSSOTicket(ticket string) (id int, err error) {
	var content string
	content, err = up.client.Get(context.Background(), defineRedisKey(SSOTicketKeyPrefix, ticket)).Result()
	if err != nil {
		up.l.WarnE(err, "get ticket from redis failed.")
		return
	}
	id, err = strconv.Atoi(content)
	if err != nil {
		up.l.WarnE(err, "uid not found by ticket")
		return
	}
	return
}

func (up *UserPlugin) SetSSOTicketV2(ticket string, id int, subName string) (err error) {
	payload := SSOTickerPayload{
		UID:     id,
		SubName: subName,
	}
	b, _ := json.Marshal(payload)
	err = up.client.Set(context.Background(), defineRedisKey(SSOTicketKeyPrefix, ticket), string(b), constant.SSOTicketExpiredTimeOut).Err()
	return
}

func (up *UserPlugin) GetSSOTicketV2(ticket string) (payload SSOTickerPayload, err error) {
	var content string
	content, err = up.client.Get(context.Background(), defineRedisKey(SSOTicketKeyPrefix, ticket)).Result()
	if err != nil {
		up.l.WarnE(err, "get ticket from redis failed.")
		return
	}
	err = json.Unmarshal([]byte(content), &payload)
	if err != nil {
		up.l.WarnE(err, "parse payload by ticket")
		return
	}
	return
}

// SetUserOnline 用户登录, 设置 online token. 此处token为30分钟
func (up *UserPlugin) SetUserOnline(uuid string) (err error) {
	err = up.client.Set(context.Background(), defineRedisKey(OnlineKeyPrefix, uuid), time.Now().String(), constant.UserOnlineTimeOut).Err()
	return
}

// CheckUserOnline 检查用户是否在线. 如果在线, 延长 token 时间.
func (up *UserPlugin) CheckUserOnline(uuid string) (isOnline bool, err error) {
	if uuid == "" {
		err = biz.ErrInvalidRequestParams
		up.l.WarnE(err, "uuid can not be null when check user online")
		return
	}

	isOnline, err = up.client.SetXX(context.Background(), defineRedisKey(OnlineKeyPrefix, uuid), time.Now().String(), constant.UserOnlineTimeOut).Result()
	if err != nil {
		up.l.WarnE(err, "redis setxx for user failed.")
		return
	}
	return
}

// SetUserOffline 下线用户.
func (up *UserPlugin) SetUserOffline(uuid string) (err error) {
	if uuid == "" {
		err = biz.ErrInvalidRequestParams
		up.l.WarnE(err, "uuid can not be null when check user online")
		return
	}
	err = up.client.Del(context.Background(), defineRedisKey(OnlineKeyPrefix, uuid)).Err()
	if err != nil {
		up.l.WithFields(logger.Fields{
			"UUID": uuid,
		}).WithError(err).Error("Set user offline failed.")
		return err
	}

	up.l.WithFields(logger.Fields{
		"UUID": uuid,
	}).Info("Set a user offline.")
	return
}

// 用户登录错误次数
func (up *UserPlugin) PV(prefix RedisPrefix, data string) (pv int, err error) {
	if len(data) == 0 {
		return
	}
	var content string
	content, err = up.client.Get(context.Background(), defineRedisKey(prefix, data)).Result()
	if errors.Is(err, redis.Nil) {
		return 0, nil
	}
	pv, err = strconv.Atoi(content)
	if err != nil {
		up.l.ErrorE(err, "trans pv from string to int failed")
		return
	}
	return
}

func mergeOpenIdAndSessionKey(sessionKey, openId string) string {
	return fmt.Sprintf("%s**%s", sessionKey, openId)
}

// PVIncrement 查询并自增
func (up *UserPlugin) PVIncrement(redisPrefix RedisPrefix, data string, expire time.Duration) (err error) {
	if len(data) == 0 {
		return
	}
	multi := up.client.TxPipeline()
	multi.Incr(context.Background(), defineRedisKey(redisPrefix, data))
	multi.Expire(context.Background(), defineRedisKey(redisPrefix, data), expire)
	_, err = multi.Exec(context.Background())
	return
}

// 清除之前的登录次数记录
func (up *UserPlugin) PVClear(phone string) (err error) {
	if len(phone) == 0 {
		return
	}
	err = up.client.Del(context.Background(), defineRedisKey(IpPVKeyPrefix, phone)).Err()
	return
}

// SetLoginLimit 设置用户登录限制
func (up *UserPlugin) SetLoginLimit(phone string, expire time.Duration) (err error) {
	return up.setLoginLimit(LoginLimitKeyPrefix, phone, expire)
}

func (up *UserPlugin) setLoginLimit(prefix RedisPrefix, phone string, expire time.Duration) (err error) {
	if phone == "" {
		return
	}
	_, err = up.client.Set(context.Background(), defineRedisKey(prefix, phone), phone, expire).Result()
	return
}

// CheckLoginLimit 检查用户登录是否被限制
func (up *UserPlugin) CheckLoginLimit(phone string) (limit bool, err error) {
	return up.checkLoginLimit(LoginLimitKeyPrefix, phone)
}

func (up *UserPlugin) checkLoginLimit(prefix RedisPrefix, phone string) (limit bool, err error) {
	if phone == "" {
		return
	}
	_, err = up.client.Get(context.Background(), defineRedisKey(prefix, phone)).Result()
	if err != nil {
		if err == redis.Nil {
			err = nil
			return
		}
		up.l.ErrorE(err, "checkLoginLimit from redis failed.")
		return
	}

	limit = true
	return
}

// ClearLoginLimit 清除用户登录次数
func (up *UserPlugin) ClearLoginLimit(phone string) (err error) {
	return up.clearLoginLimit(LoginLimitKeyPrefix, phone)
}

func (up *UserPlugin) clearLoginLimit(prefix RedisPrefix, phone string) (err error) {
	if phone == "" {
		return
	}
	err = up.client.Del(context.Background(), defineRedisKey(prefix, phone)).Err()
	return
}

// SetWxCallBackResult 微信回调的结果
func (up *UserPlugin) SetWxCallBackResult(key, value string) (err error) {
	err = up.client.Set(context.Background(), key, value, constant.WxCycleRechargeTimeOut).Err()
	if err != nil {
		up.l.WarnE(err, "set wechat callback result failed.")
		return
	}
	return
}

// GetWxCallBackResult 获取微信回到的结果
func (up *UserPlugin) GetWxCallBackResult(key string) (value string, err error) {
	if key == "" {
		return
	}
	value, err = up.client.Get(context.Background(), key).Result()
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			up.l.WarnE(err, "get wechat callback result failed.")
		}
		return
	}
	return
}

// -------------------------------------------- StdMessage ---------------------------------------------------------------

// SetVCode 给用户发送短信(有效期:10minutes)
func (up *UserPlugin) SetVCode(phone string, vCode string) (err error) {
	err = up.client.Set(context.Background(), defineRedisKey(VCodeSendKeyPrefix, phone), vCode, constant.VCodeExpireTimeOut).Err()
	if err != nil {
		up.l.WarnE(err, "set user vCode expire timeout failed.")
		return
	}
	return
}

// CheckVCodeValueIsRight 检查短信验证码是否正确
func (up *UserPlugin) CheckVCodeValueIsRight(phone, vCode string) (right bool, err error) {
	//if conf.GetGlobalGsConfig().App.DebugApi {
	//if vCode == "666666" {
	//	return true, nil
	//}
	//}

	var content string
	content, err = up.client.Get(context.Background(), defineRedisKey(VCodeSendKeyPrefix, phone)).Result()
	if err != nil {
		return
	}

	right = strings.EqualFold(content, vCode)
	if right {
		up.client.Del(context.Background(), defineRedisKey(VCodeSendKeyPrefix, phone))
	}
	return
}

// SetSendVCodeTime 存储发送短信验证码时间
func (up *UserPlugin) SetSendVCodeTime(phone string, timeNow int64) (err error) {
	err = up.client.Set(context.Background(), defineRedisKey(VCodeKeyPrefix, phone), timeNow, 1*time.Minute).Err()
	if err != nil {
		up.l.WarnE(err, "set vCode time left failed.")
		return
	}
	return
}

func (up *UserPlugin) GetLastSendVCodeTime(phone string) (sendTime int64, err error) {
	sendTime, err = up.client.Get(context.Background(), defineRedisKey(VCodeKeyPrefix, phone)).Int64()
	if err != nil {
		return
	}
	return
}

// SetSendEmailInvalidTimes 存储发送短信验证码时间
func (up *UserPlugin) SetSendEmailInvalidTimes(uid int, subName string, times int) (err error) {
	now := time.Now().Format(constant.FormatDateString)
	err = up.client.Set(context.Background(), defineRedisKey(EmailSendInvalidKeyPrefix, strconv.Itoa(uid)+subName+now), times, time.Hour*24).Err()
	if err != nil {
		up.l.WarnE(err, "set vCode time left failed.")
		return
	}
	return
}

func (up *UserPlugin) GetSendEmailInvalidTimes(uid int, subName string) int {
	now := time.Now().Format(constant.FormatDateString)
	times, _ := up.client.Get(context.Background(), defineRedisKey(EmailSendInvalidKeyPrefix, strconv.Itoa(uid)+subName+now)).Int()
	return times
}

// -------------------------------------------- Email ---------------------------------------------------------------

// SetEmail 给用户发送邮件(有效期:10minutes)
func (up *UserPlugin) SetEmail(email string, vCode string) (err error) {
	err = up.client.Set(context.Background(), defineRedisKey(EmailSendKeyPrefix, email), vCode, constant.EmailCodeExpireTimeOut).Err()
	if err != nil {
		err = errors.Wrap(err, "Set user email code expire timeout failed")
		return
	}
	return
}

// CheckEmailCodeValueIsRight 检查邮箱验证码是否正确
func (up *UserPlugin) CheckEmailCodeValueIsRight(email, vCode string) (right bool, err error) {
	//if conf.GetGlobalGsConfig().App.DebugApi {
	//	if vCode == "666666" {
	//		return true, nil
	//	}
	//}
	var content string
	content, err = up.client.Get(context.Background(), defineRedisKey(EmailSendKeyPrefix, email)).Result()
	if err != nil {
		return
	}
	right = strings.EqualFold(content, vCode)
	if right {
		up.client.Del(context.Background(), defineRedisKey(EmailSendKeyPrefix, email))
	}
	return
}

// SetSendEmailTime 存储发送邮件时间
func (up *UserPlugin) SetSendEmailTime(email string) (err error) {
	err = up.client.Set(context.Background(), defineRedisKey(EmailKeyPrefix, email), time.Now().Unix(), 1*time.Minute).Err()
	if err != nil {
		up.l.WarnE(err, "set vCode time left failed.")
		return
	}
	return
}

// GetSendEmailTime 获取发送邮件的时间
func (up *UserPlugin) GetSendEmailTime(email string) (sendTime int64, err error) {
	if email == "" {
		return
	}
	sendTime, err = up.client.Get(context.Background(), defineRedisKey(EmailKeyPrefix, email)).Int64()
	if err != nil {
		return
	}
	return
}

func (up *UserPlugin) Now() int64 {
	now, err := up.client.Time(context.Background()).Result()
	if err != nil {
		up.l.WarnE(err, "get time now failed.")
		return time.Now().Unix()
	}
	return now.Unix()
}

type UserCloneExpireTimePlugin struct {
	client *redis.Client
}

func (uc *UserCloneExpireTimePlugin) tableName() string {
	return "UserCloneExpireTimeHashTable"
}

func (uc *UserCloneExpireTimePlugin) HSet(field string, value string) error {
	return uc.client.HSet(context.Background(), uc.tableName(), field, value).Err()
}

func (uc *UserCloneExpireTimePlugin) HGetAll() (map[string]string, error) {
	return uc.client.HGetAll(context.Background(), uc.tableName()).Result()
}

func (uc *UserCloneExpireTimePlugin) HDel(id string) error {
	return uc.client.HDel(context.Background(), uc.tableName(), id).Err()
}
