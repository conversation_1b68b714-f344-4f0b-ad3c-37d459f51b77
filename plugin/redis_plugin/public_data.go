package redis_plugin

import (
	"context"
	"github.com/redis/go-redis/v9"
	"strconv"
)

type PublicDataPlugin struct {
	client *redis.Client
}

func (p *PublicDataPlugin) tableName() string {
	return "GpuHubPublicDataList"
}

func (p *PublicDataPlugin) Exist() (bool, error) {
	rows, err := p.client.Exists(context.Background(), p.tableName()).Result()
	if err != nil {
		return false, err
	}
	if rows == 0 {
		return false, nil
	}
	return true, nil
}

func (p *PublicDataPlugin) RPush(list []string) error {
	err := p.client.Del(context.Background(), p.tableName()).Err()
	if err != nil {
		return err
	}
	err = p.client.Del(context.Background(), p.tableNameFixStarNum()).Err()
	if err != nil {
		return err
	}
	err = p.client.RPush(context.Background(), p.tableName(), list).Err()
	return err
}

func (p *PublicDataPlugin) LLen() (int64, error) {
	// llen 不会返回错误
	result, err := p.client.LLen(context.Background(), p.tableName()).Result()
	if err != nil {
		return 0, err
	}
	return result, nil
}

func (p *PublicDataPlugin) GetPage(offset, pageSize int) ([]string, error) {
	result, err := p.client.LRange(context.Background(), p.tableName(), int64(offset), int64(offset+pageSize)-1).Result()
	if err != nil {
		return []string{}, err
	}

	return result, nil
}

func (p *PublicDataPlugin) tableNameFixStarNum() string {
	return "GpuHubPublicDataFixStarNum"
}

func (p *PublicDataPlugin) StarNumIncr(id int) (err error) {
	return p.client.HIncrBy(context.Background(), p.tableNameFixStarNum(), strconv.Itoa(id), 1).Err()
}

func (p *PublicDataPlugin) StarNumDecr(id int) (err error) {
	return p.client.HIncrBy(context.Background(), p.tableNameFixStarNum(), strconv.Itoa(id), -1).Err()
}

func (p *PublicDataPlugin) GetStarNumFix() (map[string]string, error) {
	return p.client.HGetAll(context.Background(), p.tableNameFixStarNum()).Result()
}

/*
共享数据列表缓存
	list
	每一条数据作为一项，根据id顺序rpush，这样可以进行简单的分页查询

用户id -> 手机号
用户id -> 用户名
用户手机号 -> id
	hash
	简单的映射

消息通知记录哪些用户通知过
	set
	消息的id做key，使用set类型，每个消息独立，设置过期时间。在消息创建出来的时候就在redis中设置好.key名称 message1,message2
	用户查询来的时候先去redis中查看有多少生效中的message，再分别查每个message中该用户是否已经点击过
		点击过，则过滤这条消息
		没点击过，则回数据库查询发给用户
			用户点击“知道了”，对相应的message中添加记录，同时计数
	关于计数
		对每个message做一个计数用的string类型，利用其自增和getset命令定时存到数据库中
*/
