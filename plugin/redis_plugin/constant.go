package redis_plugin

type RedisPrefix string

const (
	CaptchaKeyPrefix                   RedisPrefix = "GpuHubUserCaptcha_"
	CaptchaCheckResultPrefix           RedisPrefix = "GpuHubCaptchaCheck_"
	TicketKeyPrefix                    RedisPrefix = "GpuHubUserTicket_"
	OnlineKeyPrefix                    RedisPrefix = "GpuHubUserOnline_"
	IpPVKeyPrefix                      RedisPrefix = "GpuHubUserIPPV_"
	VCodeKeyPrefix                     RedisPrefix = "GpuHubUserVCode_"
	VCodeSendKeyPrefix                 RedisPrefix = "GpuHubUserVCodeSend_"
	VCodeSendLimitPrefix               RedisPrefix = "GpuHubVCodeSendLimit_"
	LoginLimitKeyPrefix                RedisPrefix = "GpuHubUserLoginLimit_"
	EmailKeyPrefix                     RedisPrefix = "GpuHubUserEmail_"
	EmailSendKeyPrefix                 RedisPrefix = "GpuHubUserEduEmailSend_"
	EmailSendInvalidKeyPrefix          RedisPrefix = "GpuHubUserEduEmailInvalidSend_"
	StorageQuotaPrefix                 RedisPrefix = "GpuHubStorageQuota_"
	PersonalTokenKeyPerDayPrefix       RedisPrefix = "GpuHub_PersonalToken_PerDay_"
	PersonalTokenKeyPerMinPrefix       RedisPrefix = "GpuHub_PersonalToken_PerMin_"
	AppletLoginPrefix                  RedisPrefix = "GpuHubAppletLogin_"
	GlobalKVPrefix                     RedisPrefix = "GpuHubGlobalKV_"
	GpuHubAutoFsRegionDailyMaxUsageFmt string      = "GpuHubAutoFsRegionDailyMaxUsage_%s_%s"
	SSOTicketKeyPrefix                 RedisPrefix = "GpuHubUserSSOTicket_"
	DeploymentLockPrefix               RedisPrefix = "GpuHubDeploymentLock_"
	DeploymentStopContainerPrefix      RedisPrefix = "GpuHubDeploymentStopContainer_"
	InstanceLastPowerOnTimePrefix      RedisPrefix = "InstanceLastPowerOnTimePrefix_"
	SmsErrSendFeishuRemindPrefix       RedisPrefix = "SmsErrSendFeishuRemindPrefix_"
)

/**
 * 用于 NewRedisMutex() 的 type, 在此处用常量统一表示
 */

type MutexType string

const (
	InitModelsMutex                       MutexType = "init_models"                      // 单实例的某种 worker, 后面还需要接后缀.
	MutexReduceUpdateVoucherUsedTimesType MutexType = "reduce_update_voucher_used_times" // 异步统计更新已使用字段
	MutexSingleWorkerType                 MutexType = "single_worker"                    // 单实例的某种 worker, 后面还需要接后缀.
	MutexDoChargingType                   MutexType = "corn_do_charging"                 // 多实例定时扣费锁
	MutexUpdateUserWalletType             MutexType = "update_wallet_lock"               // 更新用户钱包
	MutexUpdateRechargeRecordType         MutexType = "update_recharge_record_lock"      // 更新充值记录
	MutexUpdateRefundRecordType           MutexType = "update_refund_record_lock"        // 更新退款记录
	MutexRegisterChargingType             MutexType = "register_charging_lock"           // 注册计费
	MutexUpdateChargingTableType          MutexType = "update_charging_table_lock"       // 修改计费表
	MutexUpdateVoucherType                MutexType = "update_voucher"                   // 修改voucher
	MutexRenewalInstanceType              MutexType = "renewal_instance_lock"            // 实例续费时, 防止被worker自动释放
	MutexGpuStockReserveOption            MutexType = "gpu_stock_reserve"                // 机器库存占用
	MutexUpdateContainerStatus            MutexType = "update_container_status"          // 更新容器状态
	MutexForPortDistribute                MutexType = "port_distribute"                  // 实例分配端口
	MutexIdleJobRunning                   MutexType = "idle_job_running"                 // running job加锁
	MutexUserRegister                     MutexType = "user_register_lock"               // 用户注册加锁
	MutexUpdatePrivateImageField          MutexType = "private_image_field"              // 更新私有镜像状态加锁
	MutexSavePrivateImageField            MutexType = "save_image_field"                 // 保存私有镜像状态加锁
	MutexUserInvitedRegister              MutexType = "user_register_lock"               // 用户被邀请注册
	MutexUpdateCouponType                 MutexType = "update_coupon"                    // 修改coupon
	MutexUserPrizeDraw                    MutexType = "user_prize_draw"                  // 用户抽奖
	MutexUserPrizeDrawNew                 MutexType = "user_prize_draw_new"              // 用户抽奖(新规则)
	MutexUpdateOrderStatus                MutexType = "update_order_status"              // 更新order状态
	MutexUpdateDDSStock                   MutexType = "update_dds_stock"                 // core 层更新数据盘库存
	MutexUpdateBusinessDDSStock           MutexType = "update_business_dds_stock"        // 业务层更新数据盘库存
	MutexAsyncChargingSettle              MutexType = "async_charging_settle"            // 异步整点结算
	MutexUpdatePublicData                 MutexType = "update_public_data"               // 更新公共数据集
	MutexUpdateSysNotice                  MutexType = "update_sys_notice"                // 更新notice的readCount
	MutexUpdateUserMember                 MutexType = "update_user_member"               // 更新user_meber
	MutexUpdateSubUserQuota               MutexType = "update_subuser_quota"             // 更新user_meber
	MutexPrepayToPayg                     MutexType = "prepay_to_payg"
	MutexDeploymentUpdateBindingNum       MutexType = "deployment_binding_num"             // 更新部署绑定的实例状态
	MutexInitFileStorageFinal             MutexType = "init_file_storage_final"            // 初始化文件存储最后步骤
	MutexAppendDeletedTarNameForKV        MutexType = "append_deleted_tar_name_for_kv"     // 私有镜像删除后清理tar包，为简化消息传输逻辑，每个小时使用一个key，因此要加锁
	MutexSubDCC                           MutexType = "sub_dcc"                            // 获取弹性部署复用容器，避免冲突，同一个人同一个镜像加锁
	MutexPubDCC                           MutexType = "pub_dcc"                            // 获取弹性部署复用容器，避免冲突，同一个人同一个镜像加锁
	MutexSyncRuntimeToCommunity           MutexType = "sync_runtime_to_community"          //
	MutexCreateWorkOrderMachine           MutexType = "create_work_order_machine"          // 创建工单机器
	MutexCreateWorkOrderInstanceReceipt   MutexType = "create_work_order_instance_receipt" // 创建工单实例回执
	MutexCloneCode                        MutexType = "clone_code"                         // 克隆码
	MutexBTSubunitDistribute              MutexType = "bt_subunit_distribute"              // 生成专属汇款账号
	// ...
)

func (m MutexType) String() string {
	return string(m)
}

type SSOTickerPayload struct {
	UID     int    `json:"uid"`
	SubName string `json:"sub_name"`
}

const RedisNil = "redis: nil"
