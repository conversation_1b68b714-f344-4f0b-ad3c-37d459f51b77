package redis_plugin

import (
	"context"
	"errors"
	"fmt"
	"github.com/redis/go-redis/v9"
	"server/pkg/logger"
)

type SPACPlugin struct {
	client *redis.Client
	l      *logger.Logger
}

func (s *SPACPlugin) tableName() string {
	return "autodl-storage-path-access-control"
}

func NewSPacPluginProvider(client *redis.Client) *SPACPlugin {
	return &SPACPlugin{client: client, l: logger.NewLogger("pac_plugin")}
}

func (s *SPACPlugin) PacSet(uid int, subName, subPath string) error {
	field := fmt.Sprintf("%d-%s", uid, subName)
	return s.client.HSet(context.Background(), s.tableName(), field, subPath).Err()
}

func (s *SPACPlugin) PACGet(uid int, subName string) (string, error) {
	field := fmt.Sprintf("%d-%s", uid, subName)
	path, err := s.client.HGet(context.Background(), s.tableName(), field).Result()
	if err != nil { // 报错直接返回错误
		if errors.Is(err, redis.Nil) {
			return "", nil
		}
		return "", err
	}
	return path, nil
}
