package tencent_oss

import (
	"context"
	"fmt"
	"github.com/pkg/errors"
	"github.com/tencentyun/cos-go-sdk-v5"
	"net/http"
	"net/url"
	"server/pkg/constant"
	"strings"
)

const (
	testBucketHost = "https://autodl-test-1310972338.cos.ap-beijing.myqcloud.com"
	testBucket     = "autodl-test-1310972338"

	onlineBucketHost = "https://autodl-1310972338.cos.ap-beijing.myqcloud.com"
	onlineBucket     = "autodl-1310972338"
)

var (
	host         string
	bucketHost   string
	accessKeyID  string
	accessSecret string
)

/*
	tencent oss go sdk
	https://cloud.tencent.com/document/product/436/31215
*/

var cli *cos.Client

func NewOss(bh, h, id, secret string) error {
	bucketHost = bh
	host = h
	accessKeyID = id
	accessSecret = secret

	return newOss()
}

func newOss() (err error) {
	if cli != nil {
		return
	}

	bucketUrl, _ := url.Parse(bucketHost)
	serviceUrl, _ := url.Parse(host)
	cli = cos.NewClient(
		&cos.BaseURL{BucketURL: bucketUrl, ServiceURL: serviceUrl},
		&http.Client{Transport: &cos.AuthorizationTransport{SecretID: accessKeyID, SecretKey: accessSecret}},
	)

	exist, err := cli.Bucket.IsExist(context.Background())
	if err != nil {
		return
	}

	if !exist {
		err = errors.New("bucket not exist in tencent cos")
		return err
	}
	return nil
}

// Upload 上传
func Upload(params *constant.UploadParams) (err error) {
	/* size:单位 字节 */
	err = newOss()
	if err != nil {
		return
	}
	_, err = cli.Object.Put(context.Background(), params.ObjName, params.File, &cos.ObjectPutOptions{
		ACLHeaderOptions: nil,
		ObjectPutHeaderOptions: &cos.ObjectPutHeaderOptions{
			ContentLength:      params.Size,
			ContentType:        params.ContentType,
			ContentDisposition: fmt.Sprintf(`attachment;filename="%s"`, params.ObjName),
		},
	})
	if err != nil {
		err = errors.Wrap(err, "upload failed")
		return
	}
	return
}
func Download(objName string) (obj *cos.Response, err error) {
	err = newOss()
	if err != nil {
		return
	}
	return cli.Object.Get(context.Background(), objName, nil)
}

func Remove(objName string) (err error) {
	err = newOss()
	if err != nil {
		return
	}

	_, err = cli.Object.Delete(context.Background(), objName)
	return err
}

func RemoveFolder(folder string) (err error) {
	err = newOss()
	if err != nil {
		return
	}

	objList, err := ListFolderObj(folder)
	if err != nil {
		return
	}

	for _, v := range objList {
		err = Remove(v.Key)
		if err != nil {
			return err
		}
	}
	return err
}

func listObj(objName string) (list *cos.BucketGetResult, err error) {
	// https://cloud.tencent.com/document/product/436/65647

	opt := &cos.BucketGetOptions{
		Prefix:  objName,
		MaxKeys: 1000,
	}
	list, _, err = cli.Bucket.Get(context.Background(), opt)
	if err != nil {
		return
	}
	return
}

func ListFolderObj(folder string) (list []cos.Object, err error) {
	/*
		https://cloud.tencent.com/document/product/436/65647
	*/

	folder = strings.TrimSuffix(folder, "/")

	list = []cos.Object{}
	var marker string
	isTruncated := true
	opt := &cos.BucketGetOptions{
		Prefix:    folder + "/",
		Delimiter: "/",
		MaxKeys:   1000,
	}

	for isTruncated {
		opt.Marker = marker
		v, _, err := cli.Bucket.Get(context.Background(), opt)
		if err != nil {
			return list, err
		}

		list = append(list, v.Contents...)
		isTruncated = v.IsTruncated // 是否还有数据
		marker = v.NextMarker       // 设置下次请求的起始 key
	}
	return
}
