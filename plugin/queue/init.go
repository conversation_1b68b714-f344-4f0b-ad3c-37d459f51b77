package queue

import (
	"fmt"
	"github.com/streadway/amqp"
	"server/conf"
	"server/pkg/libs"
	"server/pkg/logger"
	"server/plugin/queue_interface"
	"sync"
)

const (
	DefaultRabbitMQExchange = ""
	DeadLetterExchange      = "dead_letter"
	DeadLetterQueue         = "dead_letter_queue"

	MaxRetryTimes = 20
)

type MQOption struct {
	Host       string
	User       string
	Password   string
	GetMsgList func(msgUUID ...string) ([]ElementPayload, error)
	Handling   func(msgUUID string, statusType MessageStatusType)
}

// Q
// 以注册方式来管理所有的sub handler, 解放开发上重复的代码
// 以全新的方式支持了动态声明和监听MsgType, 即可以随时注册handler, Q会自动开始运行
// 前提: 接收到的msg会以goroutine方式执行, 如果需要保证顺序, 需要指定MsgType qos=1, 并且MsgType需要有MutexLock
type Q struct {
	ops      *MQOption
	conn     *amqp.Connection
	connPool map[*amqp.Connection]int
	connMux  *sync.Mutex

	// getMsgList, 获取msgUUID对应body的方法, 将mq的pub与本地消息表解除耦合
	getMsgList func(msgUUID ...string) ([]ElementPayload, error)
	// handling, 用于在msgUUID发生ack/nack/drop等情况时, 更新本地消息表的钩子
	handling func(msgUUID string, statusType MessageStatusType)

	// for sub
	// TODO: MsgType需要有MutexLock 以及 Lock()方法
	mutex       sync.RWMutex
	subs        []RegisterInfo
	runningSubs map[queue_interface.QueueElementType]*SubHandlerStatus

	// for pub
	declareMutex sync.RWMutex
	declareQueue map[string]bool

	l *logger.Logger
}

func NewQ(ops *MQOption) (q *Q, err error) {
	q = &Q{
		ops:          ops,
		getMsgList:   ops.GetMsgList,
		handling:     ops.Handling,
		runningSubs:  make(map[queue_interface.QueueElementType]*SubHandlerStatus),
		declareQueue: make(map[string]bool),
		connPool:     make(map[*amqp.Connection]int),
		connMux:      &sync.Mutex{},
		l:            logger.NewLogger("MQ"),
	}
	q.conn, err = q.dial()
	if err != nil {
		return
	}
	err = q.init()
	return
}

// New 防止模块间互相影响
func (q *Q) New() *Q {
	return &Q{
		conn:         q.conn,
		ops:          q.ops,
		getMsgList:   q.ops.GetMsgList,
		handling:     q.ops.Handling,
		runningSubs:  make(map[queue_interface.QueueElementType]*SubHandlerStatus),
		declareQueue: make(map[string]bool),
		connPool:     q.connPool,
		connMux:      q.connMux,
	}
}

func (q *Q) Close() (err error) {
	return q.conn.Close()
}

func (q *Q) dial() (conn *amqp.Connection, err error) {
	return amqp.Dial(fmt.Sprintf("amqp://%s:%s@%s/%s", q.ops.User, q.ops.Password, q.ops.Host, DefaultRabbitMQExchange))
}

func (q *Q) getConnection() (err error) {
	if q.conn == nil || q.conn.IsClosed() {
		q.conn, err = q.dial()
		if err != nil {
			return
		}
	}
	return
}

func (q *Q) getChannel() (channel *amqp.Channel, err error) {
	if err = q.getConnection(); err != nil {
		return
	}
	channel, err = q.conn.Channel()
	if err != nil {
		if !conf.GetGlobalGsConfig().App.DebugApi {
			if sendErr := libs.FeiShuRobotSendMSg(libs.AutodlServiceAlert, fmt.Sprintf("RabbitMQ 异常：%v", err)); sendErr != nil {
				q.l.ErrorE(sendErr, "Failed to FeiShuRobotSendMSg")
			}
		}
	}
	return
}

func (q *Q) getConnectionForSub() (conn *amqp.Connection, err error) {
	q.connMux.Lock()
	defer q.connMux.Unlock()
	for conn, num := range q.connPool {
		if num >= 1500 { // 先设置为1500，用于线上立即测试。测试通过后，设置为2040. 单conn最大为2047
			continue
		}
		if conn.IsClosed() {
			delete(q.connPool, conn)
			continue
		}

		q.connPool[conn]++
		return conn, nil
	}

	conn, err = q.dial()
	if err != nil {
		return nil, err
	}

	q.connPool[conn]++
	return
}

func (q *Q) getChannelForSub() (channel *amqp.Channel, err error) {
	conn, err := q.getConnectionForSub()
	if err != nil {
		return nil, err
	}

	channel, err = conn.Channel()
	if err != nil {
		if !conf.GetGlobalGsConfig().App.DebugApi {
			if sendErr := libs.FeiShuRobotSendMSg(libs.AutodlServiceAlert, fmt.Sprintf("RabbitMQ 异常：%v", err)); sendErr != nil {
				q.l.ErrorE(sendErr, "Failed to FeiShuRobotSendMSg")
			}
		}
	}
	return
}

// 声明死信exchange
func (q *Q) init() (err error) {
	var ch *amqp.Channel
	ch, err = q.getChannel()
	if err != nil {
		return err
	}
	err = ch.ExchangeDeclare(DeadLetterExchange, "direct", true, false, false, false, nil)
	if err != nil {
		return
	}
	_, err = ch.QueueDeclare(DeadLetterQueue, true, false, false, false, nil)
	if err != nil {
		return
	}
	err = ch.QueueBind(DeadLetterQueue, "#", DeadLetterExchange, false, nil)
	return
}
