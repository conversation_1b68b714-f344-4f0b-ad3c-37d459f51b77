package influxdb_plugin

import (
	"context"
	"fmt"
	_ "github.com/go-sql-driver/mysql"
	influxdb2 "github.com/influxdata/influxdb-client-go/v2"
	"github.com/influxdata/influxdb-client-go/v2/domain"
	"github.com/pkg/errors"
	"strings"
)

type InfluxDBOptions struct {
	Host  string // http://localhost:8086
	Token string
}

func InfluxDBProvider(o *InfluxDBOptions) (influxdb2.Client, error) {
	if !strings.Contains(o.Host, "http") {
		o.Host = "http://" + o.Host
	}
	client := influxdb2.NewClient(o.Host, o.Token)
	ready, err := client.Ready(context.Background())
	if err != nil {
		return nil, err
	}
	if ready != nil && *ready.Status != domain.ReadyStatusReady {
		err = errors.New(fmt.Sprintf("influxdb is not ready, %T", err))
	}
	return client, err
}
