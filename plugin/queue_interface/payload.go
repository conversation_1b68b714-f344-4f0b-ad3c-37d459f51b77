package queue_interface

import (
	"encoding/json"
	"server/pkg-agent/messenger"
	"server/pkg/constant"
	"time"
)

// --------------------- go_test ----------------------

type NewQueueForTestPayload struct {
	MsgUUID string           `json:"msg_uuid"`
	Index   int              `json:"index"`
	Message string           `json:"message"`
	SubType QueueElementType `json:"sub_type"`
}

func (p *NewQueueForTestPayload) Type() QueueElementType {
	return p.SubType
}

func (p *NewQueueForTestPayload) ToString() string {
	content, _ := json.Marshal(p)
	return string(content)
}

func (p *NewQueueForTestPayload) ParseFromContent(content string) error {
	err := json.Unmarshal([]byte(content), p)
	if err != nil {
		return err
	}
	return nil
}

// -------------------- 创建计费记录 ----------------------
type NewQueueForBCCharging struct {
	OrderUUID   string              `json:"order_uuid"`
	PaygPrice   int64               `json:"payg_price"`   // 这里会员打折后的按量付费的总价  单价 × 卡数
	OriginPrice int64               `json:"origin_price"` // 这里是按量付费的总价  单价 × 卡数
	Operate     ChargingOperate     `json:"start"`
	ChargeType  constant.ChargeType `json:"charge_type"` // 特殊增加了无卡模式，该模式不会记录到order中，需要手动指定。
	SubName     string              `json:"sub_name"`    // 子帐号
	T           time.Time           `json:"t"`
}

func (p *NewQueueForBCCharging) Type() QueueElementType {
	return BCCharging
}

func (p *NewQueueForBCCharging) ToString() string {
	content, _ := json.Marshal(p)
	return string(content)
}

func (p *NewQueueForBCCharging) ParseFromContent(content string) error {
	err := json.Unmarshal([]byte(content), p)
	if err != nil {
		return err
	}
	return nil
}

// -------------------- 更新钱包余额 -----------------------

// --------------------------用户被禁用---------------------------------

type NewQueueForAfterDisableUser struct {
	UserId    int              `json:"user_id"`
	UserPhone string           `json:"user_phone"`
	SubType   QueueElementType `json:"sub_type"`
}

func (p *NewQueueForAfterDisableUser) Type() QueueElementType {
	return AfterDisableUser
}

func (p *NewQueueForAfterDisableUser) ToString() string {
	content, _ := json.Marshal(p)
	return string(content)
}

func (p *NewQueueForAfterDisableUser) ParseFromContent(content string) error {
	err := json.Unmarshal([]byte(content), p)
	if err != nil {
		return err
	}
	return nil
}

/*
 * GPU machine
 */

type NewQueueForAgentOnMachine struct {
	MachineID string            `json:"machine_id"` // 必填
	Message   messenger.Message `json:"message"`    // MsgID = time.Now().Format(time.RFC3339Nano)
}

func (p *NewQueueForAgentOnMachine) Type() QueueElementType {
	return GetAgentOnMachineType(p.MachineID)
}

func (p *NewQueueForAgentOnMachine) ToString() string {
	content, _ := json.Marshal(p)
	return string(content)
}

func (p *NewQueueForAgentOnMachine) ParseFromContent(content string) error {
	err := json.Unmarshal([]byte(content), p)
	if err != nil {
		return err
	}
	return nil
}

/*
 * NFS machine
 */

type NewQueueForStorageAgentOnMachine struct {
	RegionSign constant.RegionSignType `json:"region_sign"` // 必填
	Message    messenger.Message       `json:"message"`     // MsgID = time.Now().Format(time.RFC3339Nano)
}

func (p *NewQueueForStorageAgentOnMachine) Type() QueueElementType {
	return GetStorageAgentOnMachineType(p.RegionSign)
}

func (p *NewQueueForStorageAgentOnMachine) ToString() string {
	content, _ := json.Marshal(p)
	return string(content)
}

func (p *NewQueueForStorageAgentOnMachine) ParseFromContent(content string) error {
	err := json.Unmarshal([]byte(content), p)
	if err != nil {
		return err
	}
	return nil
}

/*
 * ADFS machine
 */

type NewQueueForADFSStorageAgentOnMachine struct {
	RegionSign constant.RegionSignType `json:"region_sign"` // 必填
	Message    messenger.Message       `json:"message"`     // MsgID = time.Now().Format(time.RFC3339Nano)
}

func (p *NewQueueForADFSStorageAgentOnMachine) Type() QueueElementType {
	return GetADFSStorageAgentOnMachineType(p.RegionSign)
}

func (p *NewQueueForADFSStorageAgentOnMachine) ToString() string {
	content, _ := json.Marshal(p)
	return string(content)
}

func (p *NewQueueForADFSStorageAgentOnMachine) ParseFromContent(content string) error {
	err := json.Unmarshal([]byte(content), p)
	if err != nil {
		return err
	}
	return nil
}

/*
 * AutoFS machine
 */

type NewQueueForAutoFsStorageAgentWithRegion struct {
	RegionSign      constant.RegionSignType `json:"region_sign"`       // 必填
	FsConfigVersion string                  `json:"fs_config_version"` // 非必填
	Message         messenger.Message       `json:"message"`           // MsgID = time.Now().Format(time.RFC3339Nano)
}

func (p *NewQueueForAutoFsStorageAgentWithRegion) Type() QueueElementType {
	return GetAutoFsStorageAgentWithRegionType(p.RegionSign, p.FsConfigVersion)
}

func (p *NewQueueForAutoFsStorageAgentWithRegion) ToString() string {
	content, _ := json.Marshal(p)
	return string(content)
}

func (p *NewQueueForAutoFsStorageAgentWithRegion) ParseFromContent(content string) error {
	err := json.Unmarshal([]byte(content), p)
	if err != nil {
		return err
	}
	return nil
}

/*
 * Instance
 */

type NewQueueForInstanceOnMachine struct {
	MachineID   string                        `json:"machine_id"` // 必填
	RuntimeType constant.ContainerRuntimeType // 非必填，默认为instance，为了deployment复用该channel所加字段
	OptReqs     []constant.OptInstanceReq     `json:"opt_reqs"` // 控制 container 的命令
}

func (p *NewQueueForInstanceOnMachine) Type() QueueElementType {
	return GetInstanceOnMachineChannelType(p.MachineID)
}

func (p *NewQueueForInstanceOnMachine) ToString() string {
	content, _ := json.Marshal(p)
	return string(content)
}

func (p *NewQueueForInstanceOnMachine) ParseFromContent(content string) error {
	err := json.Unmarshal([]byte(content), p)
	if err != nil {
		return err
	}
	return nil
}

/*
 * Container
 */

type NewQueueForContainerOnMachine struct {
	// 本身控制实例所需要的参数
	MachineID string                     `json:"machine_id"` // 必填
	OptReqs   []constant.OptContainerReq `json:"opt_reqs"`   // 控制实例的命令
}

func (p *NewQueueForContainerOnMachine) Type() QueueElementType {
	return GetContainerOnMachineChannelType(p.MachineID)
}

func (p *NewQueueForContainerOnMachine) ToString() string {
	content, _ := json.Marshal(p)
	return string(content)
}

func (p *NewQueueForContainerOnMachine) ParseFromContent(content string) error {
	err := json.Unmarshal([]byte(content), p)
	if err != nil {
		return err
	}
	return nil
}

// --------------------------------------- user recharge ---------------------------------------

type NewQueueForUserRecharge struct {
	UserId    int              `json:"user_id"`
	UserPhone string           `json:"user_phone"`
	Asset     int64            `json:"asset"`
	SubType   QueueElementType `json:"sub_type"`
}

func (p *NewQueueForUserRecharge) Type() QueueElementType {
	return AfterUserRecharge
}

func (p *NewQueueForUserRecharge) ToString() string {
	content, _ := json.Marshal(p)
	return string(content)
}

func (p *NewQueueForUserRecharge) ParseFromContent(content string) error {
	err := json.Unmarshal([]byte(content), p)
	if err != nil {
		return err
	}
	return nil
}

// ------------------------- ddsRecharge ---------------------------

type NewQueueForDDSRecharge struct {
	MachineID    string                    `json:"machine_id"`
	InstanceUUID constant.InstanceUUIDType `json:"instance_uuid"`
	Operate      ChargingOperate           `json:"start"`
	ChargeType   constant.ChargeType       `json:"charge_type"`
	T            time.Time                 `json:"t"`
}

func (p *NewQueueForDDSRecharge) Type() QueueElementType {
	return DataDiskCharging
}

func (p *NewQueueForDDSRecharge) ToString() string {
	content, _ := json.Marshal(p)
	return string(content)
}

func (p *NewQueueForDDSRecharge) ParseFromContent(content string) error {
	err := json.Unmarshal([]byte(content), p)
	if err != nil {
		return err
	}
	return nil
}

// --------------------------------------- stock change ---------------------------------------

type NewQueueForMachineStockChanged struct {
	MachineID string `json:"machine_id"`
	From      string `json:"from"`
}

func (p *NewQueueForMachineStockChanged) Type() QueueElementType {
	return AfterMachineStockChanged
}

func (p *NewQueueForMachineStockChanged) ToString() string {
	content, _ := json.Marshal(p)
	return string(content)
}

func (p *NewQueueForMachineStockChanged) ParseFromContent(content string) error {
	err := json.Unmarshal([]byte(content), p)
	if err != nil {
		return err
	}
	return nil
}

// -------------------------------- bc charging settle --------------------------
type NewQueueForBCChargingAsyncSettle struct {
	UID            int                              `json:"uid"`
	OperateType    constant.UpdateWalletOperateType `json:"operate_type"` // 操作类型, 定时结算, 关机结算
	ChargingIDList []int                            `json:"charging_id_list"`
	SettleTime     time.Time                        `json:"settle_time"` // 本次结算时间. 定时结算与结束结算使用同一个接口
}

func (p *NewQueueForBCChargingAsyncSettle) Type() QueueElementType {
	return AsyncChargingSettle
}

func (p *NewQueueForBCChargingAsyncSettle) ToString() string {
	content, _ := json.Marshal(p)
	return string(content)
}

func (p *NewQueueForBCChargingAsyncSettle) ParseFromContent(content string) error {
	err := json.Unmarshal([]byte(content), p)
	if err != nil {
		return err
	}
	return nil
}

// -------------------------------- core to business update container status --------------------------

type NewQueueForCoreToBusinessUpdateContainerStatus struct {
	Tenant string                                              `json:"tenant"` // 必填
	Reqs   []constant.MQCoreToBusinessUpdateContainerStatusReq `json:"reqs"`
}

func (p *NewQueueForCoreToBusinessUpdateContainerStatus) Type() QueueElementType {
	return GetCoreToBusinessUpdateContainerStatusChannelType(p.Tenant)
}

func (p *NewQueueForCoreToBusinessUpdateContainerStatus) ToString() string {
	content, _ := json.Marshal(p)
	return string(content)
}

func (p *NewQueueForCoreToBusinessUpdateContainerStatus) ParseFromContent(content string) error {
	err := json.Unmarshal([]byte(content), p)
	if err != nil {
		return err
	}
	return nil
}

// -------------------------------- core to business modify machine info --------------------------

type NewQueueForCoreToBusinessModifyData struct {
	Tenant string                                 `json:"tenant"` // 必填
	Req    constant.MQCoreToBusinessModifyDataReq `json:"reqs"`
}

func (p *NewQueueForCoreToBusinessModifyData) Type() QueueElementType {
	return GetCoreToBusinessModifyDataChannelType(p.Tenant)
}

func (p *NewQueueForCoreToBusinessModifyData) ToString() string {
	content, _ := json.Marshal(p)
	return string(content)
}

func (p *NewQueueForCoreToBusinessModifyData) ParseFromContent(content string) error {
	err := json.Unmarshal([]byte(content), p)
	if err != nil {
		return err
	}
	return nil
}

// ------------------------------------ telnet machine ---------------------------------
type TelnetMachineOperate string

const (
	// 解析为 coreContainerModel.DoHookParams

	UpdateInstanceStatus TelnetMachineOperate = "UpdateInstanceStatus"
	UpdateProductStatus  TelnetMachineOperate = "UpdateProductStatus" // 此处刻意抽出product这一产品形态，统一指代instance,idleJobInstance,DC
)

type NewQueueForTelnetMachine struct {
	Tenant    string               `json:"tenant"` // 必填
	MachineID string               `json:"machine_id"`
	Opt       TelnetMachineOperate `json:"opt"` // 必填
	Payload   string               `json:"payload"`
}

func (p *NewQueueForTelnetMachine) Type() QueueElementType {
	return GetTelnetMachineChannelType(p.Tenant, p.MachineID)
}

func (p *NewQueueForTelnetMachine) ToString() string {
	content, _ := json.Marshal(p)
	return string(content)
}

func (p *NewQueueForTelnetMachine) ParseFromContent(content string) error {
	err := json.Unmarshal([]byte(content), p)
	if err != nil {
		return err
	}
	return nil
}
