package queue_interface

import (
	"strings"
	"time"
)

type Queue interface {
	Pub(payload ElementPayloadContent) (msgUUID string, err error)
	Sub(stopCh <-chan bool, tp QueueElementType) (payloadCh <-chan ElementPayload, consumerHandler ConsumerHandlerInterface, err error)
	Close() error
}

type ConsumerHandlerInterface interface {
	Ack(id int, msgUUID string) error
	Nack(id int, msgUUID string) error
	Reject(id int, msgUUID string) error
	Drop(id int, msgUUID string)
}

type QueueElementType string

func (q QueueElementType) ToString() string {
	return string(q)
}

func (q QueueElementType) MessageIsInOrder() bool {
	if strings.Contains(string(q), string(QueueForTestQos1)) {
		return true
	}
	if strings.Contains(string(q), string(InstanceOnMachinePrefix)) {
		return true
	}
	if strings.Contains(string(q), string(ContainerOnMachinePrefix)) {
		return true
	}
	if strings.Contains(string(q), string(AgentOnMachinePrefix)) {
		return true
	}
	return false
}

const (
	QueueForTest     QueueElementType = "QueueForTest"     // only for go_test
	QueueForTestQos1 QueueElementType = "QueueForTestQos1" // only for go_test

	// BC
	BCCharging          QueueElementType = "BCCharging"
	DataDiskCharging    QueueElementType = "DataDiskCharging"
	UpdateWallet        QueueElementType = "UpdateWallet"        // 更新钱包
	AsyncChargingSettle QueueElementType = "AsyncChargingSettle" // 异步进行整点扣费处理

	AfterDisableUser  QueueElementType = "AfterDisableUser"  // 用户被禁用后的操作
	AfterUserRecharge QueueElementType = "AfterUserRecharge" // 用户充值之后的操作

	// TODO: ytb 重构所有使用的位置为AgentOnMachineFmt
	OptInstance QueueElementType = "OptInstance" // 操作实例, 更新实例的统称

	InstanceOnMachinePrefix QueueElementType = "InstanceOnMachine_" // 需要拼接 machineID 的频道, instance.
	IdleJobOnMachinePrefix  QueueElementType = "IdleJobOnMachine_"  // 需要拼接 machineID 的频道, idle_job.

	ContainerOnMachinePrefix           QueueElementType = "ContainerOnMachine_"        // 需要拼接 machineID 的频道, 用于 agent 控制此 machine 上的 container.
	AgentOnMachinePrefix               QueueElementType = "AgentOnMachine_"            // 需要拼接 machineID 的频道, 用于 container 控制此 machine 上的 gpu agent
	StorageAgentOnMachinePrefix        QueueElementType = "StorageAgentOnMachine_"     // 需要拼接 machineID 的频道, 用于控制此 machine 上的 nfs storage agent
	ADFSStorageAgentOnMachinePrefix    QueueElementType = "ADFSStorageAgentOnMachine_" // 需要拼接 machineID 的频道, 用于控制此 machine 上的 adfs storage agent
	AutoFsStorageAgentWithRegionPrefix QueueElementType = "AutoFsStorageAgentWith_"    // 拼接region_sign

	AfterMachineStockChanged QueueElementType = "AfterMachineStockChanged" // 机器库存信息变更

	TelnetMachine QueueElementType = "TelnetMachine_%s_%s" // core更新业务层实例状态的消息，再经此分流，并发执行

	// core
	CoreToBusinessUpdateContainerStatusPrefix QueueElementType = "CoreToBusinessUpdateContainerStatus_" // core 与某个业务层关于容器状态更新的频道
	CoreToBusinessModifyDataPrefix            QueueElementType = "CoreToBusinessModifyData_"            // core 与某个业务层关于信息变更的频道

	// dda
	ReleaseDDA QueueElementType = "ReleaseDataDiskAllocate" // 释放数据盘
)

type ElementPayload struct {
	ID      int       // in rabbit mq: DeliveryTag
	MsgUUID string    // in DB model.Message UUID
	Payload string    // ElementPayloadContent
	ValidAt time.Time // 消息的创建时间, 用于在消费处通过lock的方式, 来判断+丢弃旧消息
}

type ElementPayloadContent interface {
	Type() QueueElementType
	ToString() string
	ParseFromContent(content string) error
}
