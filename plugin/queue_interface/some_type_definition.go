package queue_interface

import (
	"fmt"
	"server/pkg/constant"
	"strings"
)

/**
 * Wallet
 */

type ChargingOperate int

const ChargingStart ChargingOperate = 1
const ChargingStop ChargingOperate = 0

/**
 * Machine 统一约定此方法拼接不同 machine queue
 */

func GetAgentOnMachineType(machineID string) QueueElementType {
	return QueueElementType(AgentOnMachinePrefix.ToString() + machineID) // 'AgentOnMachine_ + xxx'
}

func GetStorageAgentOnMachineType(RegionSign constant.RegionSignType) QueueElementType {
	return QueueElementType(StorageAgentOnMachinePrefix.ToString() + RegionSign.String()) // 'StorageAgentOnMachine_ + huabei_01'
}

func GetADFSStorageAgentOnMachineType(RegionSign constant.RegionSignType) QueueElementType {
	return QueueElementType(ADFSStorageAgentOnMachinePrefix.ToString() + RegionSign.String()) // 'StorageAgentOnMachine_ + huabei_01'
}

func GetAutoFsStorageAgentWithRegionType(RegionSign constant.RegionSignType, fsConfigVersion string) QueueElementType {
	val := AutoFsStorageAgentWithRegionPrefix.ToString() + RegionSign.String()

	fsConfigVersion = strings.TrimSpace(fsConfigVersion)
	if fsConfigVersion != "" {
		val = val + "_" + fsConfigVersion
	}

	return QueueElementType(val) // 'StorageAgentOnMachine_huabei_01' or 'StorageAgentOnMachine_huabei_01_1'
}

func GetInstanceOnMachineChannelType(machineID string) QueueElementType {
	return QueueElementType(InstanceOnMachinePrefix.ToString() + machineID) // 'InstanceOnMachine_ + xxx'
}

func GetIdleJobOnMachineChannelType(machineID string) QueueElementType {
	return QueueElementType(IdleJobOnMachinePrefix.ToString() + machineID) // 'IdleJobOnMachine_ + xxx'
}

func GetContainerOnMachineChannelType(machineID string) QueueElementType {
	return QueueElementType(ContainerOnMachinePrefix.ToString() + machineID) // 'ContainerOnMachine_ + xxx'
}

func GetCoreToBusinessUpdateContainerStatusChannelType(tenant string) QueueElementType {
	return QueueElementType(CoreToBusinessUpdateContainerStatusPrefix.ToString() + tenant)
}

func GetCoreToBusinessModifyDataChannelType(tenant string) QueueElementType {
	return QueueElementType(CoreToBusinessModifyDataPrefix.ToString() + tenant)
}

func GetTelnetMachineChannelType(tenant, machineID string) QueueElementType {
	return QueueElementType(fmt.Sprintf(TelnetMachine.ToString(), tenant, machineID))
}
