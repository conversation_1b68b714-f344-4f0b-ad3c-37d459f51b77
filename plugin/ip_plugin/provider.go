package ip_plugin

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"server/pkg/logger"
	"strings"
	"time"
)

type IpPlugin struct {
	l *logger.Logger
}

func NewIpPluginProvider() *IpPlugin {
	return &IpPlugin{l: logger.NewLogger("ip_plugin")}
}

type IPLocationResponse struct {
	Code    int    `json:"code"`
	Success bool   `json:"success"`
	Msg     string `json:"msg"`
	Data    struct {
		OrderNo  string `json:"order_no"` //订单编号
		Nation   string `json:"nation"`   //国家
		Province string `json:"province"` //省份
		City     string `json:"city"`     //城市
		Isp      string `json:"isp"`      //运营商
		Ip       string `json:"ip"`       //ip地址
	} `json:"data"`
}

func (i *IpPlugin) GetClientIP(r *http.Request) string {
	//1 优先从X-Forwarded-For获取第一个非内网IP
	xForwardedFor := r.Header.Get("X-Forwarded-For")
	ips := strings.Split(xForwardedFor, ", ")
	for _, ip := range ips {
		if ip != "" && !IsPrivateIP(net.ParseIP(ip)) {
			return ip
		}
	}

	//2 次选X-Real-IP
	if realIP := r.Header.Get("X-Real-IP"); realIP != "" {
		return realIP
	}

	//3 最后从RemoteAddr解析
	remoteIP, _, _ := net.SplitHostPort(r.RemoteAddr)
	return remoteIP
}

// 判断是否为内网IP
func IsPrivateIP(ip net.IP) bool {
	return ip.IsLoopback() || ip.IsPrivate()
}

func (i *IpPlugin) GetIpRegion(ip string) (ipRegionInfo *IPLocationResponse, err error) {
	//1、接口参数配置
	apiURL := "https://kzipglobal.market.alicloudapi.com/api/ip/query" //接口api
	appCode := "0675993862c245eeaef326388b4097c2"                      // AppCode
	targetIP := ip                                                     // 要查询的IP地址

	//2、构建请求体
	formData := url.Values{}
	formData.Set("ip", targetIP)
	body := bytes.NewBufferString(formData.Encode())

	//3、创建HTTP客户端
	client := &http.Client{
		Timeout: 10 * time.Second, // 设置超时时间10sec
	}

	//4、创建请求对象
	req, err := http.NewRequest("POST", apiURL, body)
	if err != nil {
		return nil, fmt.Errorf("http.NewRequest err: %v", err)
	}

	//5、设置请求头
	req.Header.Set("Authorization", "APPCODE "+appCode)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")

	//6、发送请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("client.Do err: %v", err)
	}
	defer resp.Body.Close()

	//7、处理响应
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("status code not ok, status code is %v", resp.StatusCode)
	}

	//8、解析响应
	if err := json.NewDecoder(resp.Body).Decode(&ipRegionInfo); err != nil {
		return nil, fmt.Errorf("json decode err: %v", err)
	}

	//9、处理响应
	if ipRegionInfo.Code != 0 && ipRegionInfo.Code != 200 { // 400=请输入有效的ip, 403=参数错误
		return nil, fmt.Errorf("find ip region err: %v, code: %v", ipRegionInfo.Msg, ipRegionInfo.Code)
	}

	return ipRegionInfo, err
}
