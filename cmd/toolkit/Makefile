GOPROXY=export GO111MODULE=on && export GOPRIVATE=gitlab-gpuhub.autodl.com && export GOPROXY=https://goproxy.cn
GOCMD=$(GOPROXY) && go
GORUN=$(GOCMD) run
DOCKER=docker
DockerImageName=hub.kce.ksyun.com/gpuhub-test/agent:latest
now:=$(shell date +%Y%m%d%H%M%S)

# 动态获取的项目根目录的绝对路径
ROOT_PATH:=$(abspath $(dir $(abspath $(firstword $(MAKEFILE_LIST))))../../)

.PHONY: docker_build docker

all: docker_build
build:
	$(GOCMD) build -o toolkit .

docker_build:
	-@rm ./toolkit
	$(DOCKER) run --rm -w /code/cmd/toolkit -v /tmp/go_mod:/root/go/pkg/mod -v ${ROOT_PATH}:/code registry.cn-beijing.aliyuncs.com/work77/golang:1.9-ubuntu20.04 sh -c "make build"
