package pkg

import (
	"fmt"
	"github.com/levigross/grequests"
	"server/pkg/db_helper"
	mc "server/pkg/machine/controller"
	mm "server/pkg/machine/model"
)

func GetMachineList(token string) (list []*mm.MachineInfo, err error) {
	req := mc.MachineListRequest{
		/*GetMachineListParams: &mm.GetMachineListParams{
			DefaultOrder: true,
		},*/
		GetPagedRangeRequest: &db_helper.GetPagedRangeRequest{
			PageSize:  10,
			PageIndex: 1,
		},
	}
	var res struct {
		Code string `json:"code"`
		Data struct {
			List        []*mm.MachineInfo `json:"list"`
			PageIndex   int               `json:"page_index"`
			PageSize    int               `json:"page_size"`
			Offset      int               `json:"offset"`
			MaxPage     int               `json:"max_page"`
			ResultTotal int               `json:"result_total"`
			CurrentPage int               `json:"page"`
		} `json:"data"`
		Msg string `json:"msg"`
	}
	var r *grequests.Response

	r, err = request(POST, Api+"/machine/list", token, &req)
	if err != nil {
		ErrLog("GetMachineList: request")
		return
	}
	err = r.JSON(&res)
	if err != nil {
		fmt.Println(r.RawResponse)
		ErrLog("GetMachineList: parse json failed")
		return
	}

	if res.Code != "Success" {
		ErrLog(IndentString(res))
		err = fmt.Errorf("res.Code != Success")
		return
	}

	if len(res.Data.List) == 0 {
		err = fmt.Errorf("get machine list: empty list")
		return
	}
	list = res.Data.List
	return
}
