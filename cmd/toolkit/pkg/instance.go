package pkg

import (
	"bytes"
	"crypto/sha1"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	log "github.com/sirupsen/logrus"
	"io"
	"net/http"
	"net/http/httptrace"
	"net/url"
	"server/cmd/toolkit/pkg/model"
	constant "server/pkg-agent/agent_constant"
	"server/pkg/libs"
	"strings"
	"time"
)

func NewInstanceTester(baseUrl string) *InstanceTester {
	req := NewInstanceRequestHelper(baseUrl)
	return NewInstanceTesterProvider(req)
}

// ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////

type InstanceTester struct {
	token string
	r     *requestHelper

	// flags
	instanceUUID      string   // -i
	instanceMachineID string   // -m
	instanceOpts      []string // -o
	instanceUsername  string   // -u
	instancePassword  string   // -p
	instanceGpus      []string // -g
	InstanceImage     string   // -e
	instanceDebug     bool     // -d
}

func NewInstanceTesterProvider(request *requestHelper) *InstanceTester {
	return &InstanceTester{r: request}
}

func (t *InstanceTester) BuildFlags(
	instanceUUID string,
	instanceMachineID string,
	instanceOpts []string,
	instanceUsername string,
	instancePassword string,
	instanceGpus []string,
	InstanceImage string,
	instanceDebug bool,
) {
	t.instanceUUID = instanceUUID
	t.instanceMachineID = instanceMachineID
	t.instanceOpts = instanceOpts
	t.instanceUsername = instanceUsername
	t.instancePassword = instancePassword
	t.instanceGpus = instanceGpus
	t.InstanceImage = InstanceImage
	t.instanceDebug = instanceDebug

	fmt.Println(".................................................")
	fmt.Println("▶", "instance_uuid:", instanceUUID)
	fmt.Println("▶", "machine_id:", instanceMachineID)
	fmt.Println("▶", "options:", instanceOpts)
	fmt.Println("▶", "login_phone:", instanceUsername)
	fmt.Println("▶", "login_password:", instancePassword)
	fmt.Println("▶", "required gpu(s):", instanceGpus)
	fmt.Println("▶", "use image:", InstanceImage)
	fmt.Println("▶", "debug mode:", instanceDebug)
	fmt.Println(".................................................")
}

func (t *InstanceTester) Run() {
	println("Testing instance...")

	err := t.Login()
	if err != nil {
		panic(err)
	}

	// var iUUID string = "instance_for_debug_aa004"

	_, err = t.CreateOneWithoutOrderForDebug()
	if err != nil {
		panic(err)
	}

	// err = t.ChangeName(iUUID)
	// if err != nil {
	// 	panic(err)
	// }

	// err = t.List("www")
	// if err != nil {
	// 	panic(err)
	// }

	// err = t.StartOneWithoutOrderForDebug(iUUID)
	// if err != nil {
	// 	panic(err)
	// }

	// err = t.StopOne(iUUID)
	// if err != nil {
	// 	panic(err)
	// }

	// err = t.ReleaseOne(iUUID)
	// if err != nil {
	// 	panic(err)
	// }
}

// ---------------------------------------------------------------------------------------------------------------------

func (t *InstanceTester) Login() (err error) {
	// 1. 获取验证码
	rr := t.r.New(ReqOpt{
		V:      GET,
		Suffix: captchaGetSuffix,
		JSON:   nil,
	}).Trace().Do() // 不带 Trace 就不会输出响应时间等信息
	if err := rr.GetError(); err != nil {
		panic(err)
	}

	captcha := &model.CaptchaResponse{}
	err = rr.Unmarshal(captcha)
	if err != nil {
		return err
	}

	if captcha.Code != "Success" {
		return errors.New(rr.Raw())
	}

	// 2. 获取 ticket
	s1 := sha1.New()
	_, err = io.WriteString(s1, t.instancePassword)
	if err != nil {
		return err
	}

	rr = t.r.New(ReqOpt{
		V:      POST,
		Suffix: loginSuffix,
		JSON: &model.LoginRequest{
			Phone:    t.instanceUsername, // admin
			Password: fmt.Sprintf("%x", s1.Sum(nil)),
			CaptchaRequest: model.CaptchaRequest{
				CaptchaID:    captcha.Data.ID,
				CaptchaValue: captcha.Data.Value,
			},
		},
	}).Trace().Do()
	if err := rr.GetError(); err != nil {
		panic(err)
	}

	login := &model.LoginResponse{}
	err = rr.Unmarshal(login)
	if err != nil {
		return err
	}

	if login.Code != "Success" {
		return errors.New(rr.Raw())
	}

	// 3. 获取 token
	rr = t.r.New(ReqOpt{
		V:      POST,
		Suffix: passportSuffix,
		JSON: &model.PassportRequest{
			Ticket: login.Data.Ticket,
		},
	}).Trace().Do()
	if err := rr.GetError(); err != nil {
		panic(err)
	}

	token := &model.PassportResponse{}
	err = rr.Unmarshal(token)
	if err != nil {
		return err
	}

	if token.Code != "Success" {
		return errors.New(rr.Raw())
	}

	// 4. 成功取到 token, 并存入自身
	t.token = token.Data.Token
	println("▶ Token:", t.token)

	return nil
}

func (t *InstanceTester) List(markUUID string) (err error) {
	rr := t.r.New(ReqOpt{
		V:      GET,
		Suffix: listInstanceSuffix,
		Token:  t.token,
		JSON:   nil,
	}).Trace().Do() // 不带 Trace 就不会输出响应时间等信息
	if err := rr.GetError(); err != nil {
		panic(err)
	}

	list := &model.ListInstanceResponse{}
	err = rr.Unmarshal(list)
	if err != nil {
		panic(err)
	}

	// rr.Raw()

	//for i := range list.Data.List {
	//	if list.Data.List[i].UUID == markUUID {
	//		fmt.Print("")
	//	}
	//
	//	fmt.Printf("instance: %s | name: %s | status: %s | simple_status: %s | summary: %s |\n",
	//		list.Data.List[i].UUID,
	//		list.Data.List[i].Name,
	//		list.Data.List[i].LatestStatus.Status,
	//		list.Data.List[i].SimpleStatus,
	//		list.Data.List[i].Summary,
	//	)
	//}

	return nil
}

func (t *InstanceTester) ChangeName(instanceUUID string) (err error) {
	rr := t.r.New(ReqOpt{
		V:      PUT,
		Suffix: updateNameSuffix,
		Token:  t.token,
		JSON: struct {
			InstanceUUID string `json:"instance_uuid"`
			InstanceName string `json:"instance_name"`
		}{
			InstanceUUID: instanceUUID,
			InstanceName: libs.GenRandomStrByUUID(10),
		},
	}).Trace().Do() // 不带 Trace 就不会输出响应时间等信息
	if err := rr.GetError(); err != nil {
		panic(err)
	}

	rr.Raw()
	return nil
}

func (t *InstanceTester) CreateOneWithoutOrderForDebug() (instanceUUID string, err error) {
	// 在 .16 机器上启动一个实例

	instanceUUID = "instance_for_debug_" + libs.GenRandomStrByUUID(5)
	rr := t.r.New(ReqOpt{
		V:      POST,
		Suffix: debugCreateSuffix,
		Token:  t.token,
		JSON: &model.DebugCreateInstanceRequest{
			InstanceUUID: instanceUUID,
			MachineID:    t.instanceMachineID, // seeta
			ContainerCreateParam: constant.ContainerCreateParam{
				NewContainerParam: constant.NewContainerParam{
					Image: t.InstanceImage, // for test
				},
			},
		},
	}).Trace().Do() // 不带 Trace 就不会输出响应时间等信息
	if err := rr.GetError(); err != nil {
		panic(err)
	}

	rr.Raw()

	return
}

func (t *InstanceTester) StartOneWithoutOrderForDebug(instanceUUID string) (err error) {
	rr := t.r.New(ReqOpt{
		V:      POST,
		Suffix: debugStartSuffix,
		Token:  t.token,
		JSON: struct {
			InstanceUUID string `json:"instance_uuid"`
		}{
			InstanceUUID: instanceUUID,
		},
	}).Trace().Do() // 不带 Trace 就不会输出响应时间等信息
	if err := rr.GetError(); err != nil {
		panic(err)
	}

	rr.Raw()
	return nil
}

func (t *InstanceTester) StopOne(instanceUUID string) (err error) {
	rr := t.r.New(ReqOpt{
		V:      POST,
		Suffix: normalStopSuffix,
		Token:  t.token,
		JSON: struct {
			InstanceUUID string `json:"instance_uuid"`
		}{
			InstanceUUID: instanceUUID,
		},
	}).Trace().Do() // 不带 Trace 就不会输出响应时间等信息
	if err := rr.GetError(); err != nil {
		panic(err)
	}

	rr.Raw()
	return nil
}

func (t *InstanceTester) ReleaseOne(instanceUUID string) (err error) {
	rr := t.r.New(ReqOpt{
		V:      POST,
		Suffix: normalReleaseSuffix,
		Token:  t.token,
		JSON: struct {
			InstanceUUID string `json:"instance_uuid"`
		}{
			InstanceUUID: instanceUUID,
		},
	}).Trace().Do() // 不带 Trace 就不会输出响应时间等信息
	if err := rr.GetError(); err != nil {
		panic(err)
	}

	rr.Raw()
	return nil
}

// //////////////////////////////////////////////////////////////////////////////////////////////////////////

type requestHelper struct {
	baseUrl string
	token   string

	trace bool
	cost  time.Duration
	err   error

	res        []byte
	statusCode int

	ReqOpt
}

const (
	captchaGetSuffix    = "/api/v1/debug/captcha/get"
	loginSuffix         = "/api/v1/login"
	passportSuffix      = "/api/v1/passport"
	debugCreateSuffix   = "/api/v1/debug/instance/create"
	debugStartSuffix    = "/api/v1/debug/instance/start"
	normalStopSuffix    = "/api/v1/instance/power_off"
	normalReleaseSuffix = "/api/v1/instance/release"
	listInstanceSuffix  = "/api/v1/instance"
	updateNameSuffix    = "/api/v1/instance/name"
)

func NewInstanceRequestHelper(baseUrl string) *requestHelper {
	return &requestHelper{baseUrl: baseUrl}
}

type ReqOpt struct {
	V      Verb
	Token  string
	Suffix string // url suffix
	JSON   interface{}
	Params map[string]string
}

// 基本方法

func (rh *requestHelper) New(opt ReqOpt) *requestHelper {
	return &requestHelper{
		baseUrl: rh.baseUrl, // 只保留这个
		token:   opt.Token,
		ReqOpt:  opt,
	}
}

func (rh *requestHelper) Trace() *requestHelper {
	rh.trace = true
	return rh
}

// Do 参考 grequests 实现
func (rh *requestHelper) Do() *requestHelper {
	var finalURL = rh.baseUrl + rh.Suffix
	var err error
	var req *http.Request

	defer func() {
		rh.err = err
	}()

	if len(rh.Params) != 0 {
		// 可能的 GET 参数
		if finalURL, err = buildURLParams(finalURL, rh.Params); err != nil {
			return rh
		}
		// QueryStruct 同理.
	}

	if rh.JSON != nil {
		// 可能的序列化 interface payload 到 body
		req, err = rh.newJSONRequest(finalURL)
		if err != nil {
			return rh
		}
		req.Header.Set("Content-Type", "application/json")
	} else {
		// 构建普通 http 请求
		req, err = http.NewRequest(rh.V.String(), finalURL, nil)
		if err != nil {
			return rh
		}
	}

	req = rh.tryToAddHeaderToken(req)

	if rh.trace {
		// 带时间追踪的 http 请求
		err = rh.doTracedRequest(req)
		if err != nil {
			return rh
		}
	} else {
		// 普通 http 请求
		err = rh.doSimpleRequest(req)
		if err != nil {
			return rh
		}
	}

	return rh
}

func (rh *requestHelper) Raw() string {
	println(string(rh.res))
	return string(rh.res)
}

func (rh *requestHelper) Unmarshal(userStruct interface{}) error {
	var err error
	defer func() {
		rh.err = err
	}()

	if len(rh.res) == 0 {
		err = errors.New("response is nil")
		return err
	}

	err = json.Unmarshal(rh.res, userStruct)
	if err != nil {
		log.WithError(err).Error("Unmarshal failed.")
		return err
	}

	return nil
}

func (rh *requestHelper) GetError() error {
	return rh.err
}

func (rh *requestHelper) Code() int {
	return rh.statusCode
}

// ---------------------------------------------------------------------------------------------------------------------

func (rh *requestHelper) newJSONRequest(finalURL string) (req *http.Request, err error) {
	var reader io.Reader
	defer func() {
		rh.err = err
	}()

	switch rh.JSON.(type) {
	case string:
		reader = strings.NewReader(rh.JSON.(string))
	case []byte:
		reader = bytes.NewReader(rh.JSON.([]byte))
	default:
		var byteSlice []byte
		byteSlice, err = json.Marshal(rh.JSON)
		if err != nil {
			rh.err = err
			return nil, err
		}
		reader = bytes.NewReader(byteSlice)
	}

	req, err = http.NewRequest(rh.V.String(), finalURL, reader)
	if err != nil {
		return nil, err
	}

	return
}

func (rh *requestHelper) tryToAddHeaderToken(req *http.Request) *http.Request {
	if len(rh.token) != 0 {
		req.Header.Set("Authorization", rh.token)
	}
	return req
}

// http-tracing: https://blog.golang.org/http-tracing
func (rh *requestHelper) doTracedRequest(req *http.Request) error {
	var start, connect, dns, tlsHandshake time.Time
	var cost time.Duration

	trace := &httptrace.ClientTrace{
		DNSStart: func(dsi httptrace.DNSStartInfo) {
			dns = time.Now()
		},
		DNSDone: func(ddi httptrace.DNSDoneInfo) {
			fmt.Printf("> DNS Done: %v\n", time.Since(dns))
		},
		TLSHandshakeStart: func() {
			tlsHandshake = time.Now()
		},
		TLSHandshakeDone: func(cs tls.ConnectionState, err error) {
			fmt.Printf("> TLS Handshake: %v\n", time.Since(tlsHandshake))
		},
		ConnectStart: func(network, addr string) {
			connect = time.Now()
			fmt.Printf("> Connect started. Network: %s | Addr: %s \n", network, addr)
		},
		ConnectDone: func(network, addr string, err error) {
			fmt.Printf("> Connect done time: %v | Network: %s | Addr: %s \n", time.Since(connect), network, addr)
		},
		GotFirstResponseByte: func() {
			fmt.Printf("> Time from start to first response byte: %v\n", time.Since(start))
		},
		PutIdleConn: func(err error) {
			fmt.Printf("> The connection was returned to the idle pool. err: %v\n", err)
		},
		GotConn: func(info httptrace.GotConnInfo) {
			fmt.Printf("> Got connection. Reused: %v | WasIdle: %v | IdleTime:%v\n", info.Reused, info.WasIdle, info.IdleTime)
		},
	}

	// DO request
	req = req.WithContext(httptrace.WithClientTrace(req.Context(), trace))
	start = time.Now()

	var res *http.Response
	var err error
	defer func() {
		rh.err = err
	}()

	res, err = http.DefaultTransport.RoundTrip(req)
	if err != nil {
		rh.err = err
		return err
	}
	defer res.Body.Close()

	cost = time.Since(start)

	// 打印统计结果
	fmt.Printf("| %3d | %-13v | %-6s | %s \n", res.StatusCode, cost, req.Method, req.URL.String())

	// 后处理
	r, err := io.ReadAll(res.Body)
	if err != nil {
		log.WithError(err).Error("Read from http body failed.")
		return err
	}

	rh.cost = cost
	rh.res = r
	rh.statusCode = res.StatusCode
	return nil
}

func (rh *requestHelper) doSimpleRequest(req *http.Request) error {
	var res *http.Response
	var err error
	defer func() {
		rh.err = err
	}()

	client := &http.Client{}
	res, err = client.Do(req)
	if err != nil {
		rh.err = err
		return err
	}
	defer res.Body.Close()

	// 后处理
	r, err := io.ReadAll(res.Body)
	if err != nil {
		log.WithError(err).Error("Read from http body failed.")
		return err
	}

	rh.res = r
	rh.statusCode = res.StatusCode
	return nil
}

// ---------------------------------------------------------------------------------------------------------------------

func buildURLParams(userURL string, params map[string]string) (string, error) {
	parsedURL, err := url.Parse(userURL)

	if err != nil {
		return "", err
	}

	parsedQuery, err := url.ParseQuery(parsedURL.RawQuery)

	if err != nil {
		return "", nil
	}

	for key, value := range params {
		parsedQuery.Set(key, value)
	}

	return addQueryParams(parsedURL, parsedQuery), nil
}

func addQueryParams(parsedURL *url.URL, parsedQuery url.Values) string {
	return strings.Join([]string{strings.Replace(parsedURL.String(), "?"+parsedURL.RawQuery, "", -1), parsedQuery.Encode()}, "?")
}
