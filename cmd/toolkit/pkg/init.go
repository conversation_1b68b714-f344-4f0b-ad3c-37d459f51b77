package pkg

import (
	"encoding/json"
	"fmt"
	"github.com/levigross/grequests"
	"runtime"
	"time"
)

// ------------------- var const -----------------------
type Verb string

func (v Verb) String() string {
	return string(v)
}

const (
	GET     Verb = "GET"
	PUT     Verb = "PUT"
	PATCH   Verb = "PATCH"
	DELETE  Verb = "DELETE"
	POST    Verb = "POST"
	HEAD    Verb = "HEAD"
	OPTIONS Verb = "OPTIONS"
)

const (
	Api      = "http://192.168.1.126:33000/api/v1"
	AdminApi = "http://192.168.1.126:33000/admin/v1"
)

// ------------------------ struct --------------------------
type GetPageRequest struct {
	DateRange string `form:"date_range" json:"date_range"`
	DateFrom  string `form:"date_from" json:"date_from"`
	DateTo    string `form:"date_to" json:"date_to"`
	PageIndex int    `form:"page_index" json:"page_index"`
	PageSize  int    `form:"page_size" json:"page_size"`
}

type GChan struct {
	Num int
	C   chan struct{} //
	EC  chan struct{} // error chan, 失败的个数
	R   []string
}

func NewGChan(num int) *GChan {
	return &GChan{
		Num: num,
		C:   make(chan struct{}, num),
		EC:  make(chan struct{}, num),
		R:   make([]string, 0),
	}
}

func (g *GChan) Result(t time.Time) {
	c, ec := 0, 0
	for {
		select {
		case <-g.C:
			c++
			if g.result(c, ec, t) {
				return
			}
		case <-g.EC:
			ec++
			if g.result(c, ec, t) {
				return
			}
		}
	}
}

func (g *GChan) result(c, ec int, t time.Time) bool {
	count := c + ec
	if count == g.Num/5 {
		g.R = append(g.R, fmt.Sprintf("\n%d 请求完成, 失败 %d, 完成度 20, 耗时 %s\n", count, ec, time.Since(t).String()))
	} else if count == g.Num/2 {
		g.R = append(g.R, fmt.Sprintf("%d 请求完成, 失败 %d, 完成度 50, 耗时 %s\n", count, ec, time.Since(t).String()))
	} else if count == g.Num {
		g.R = append(g.R, fmt.Sprintf("%d 请求完成, 失败 %d, 完成度 100, 耗时 %s\n", count, ec, time.Since(t).String()))
		fmt.Println("接口返回耗时测试:")
		fmt.Println(g.R)
		return true
	}
	return false
}

type DoAsUserChan struct {
	GetMachineListChan               chan time.Duration
	GetImageListChan                 chan time.Duration
	CreateOrderForCreateInstanceChan chan time.Duration
	PayOrderChan                     chan time.Duration
	CheckInstanceStatusChan          chan time.Duration // 检查实例状态
	GetOrderChan                     chan time.Duration
	ConfirmBillChan                  chan time.Duration
	Stop                             chan struct{}
}

func NewFoAsUserChan(num int) *DoAsUserChan {
	return &DoAsUserChan{
		GetMachineListChan:               make(chan time.Duration, num),
		GetImageListChan:                 make(chan time.Duration, num),
		CreateOrderForCreateInstanceChan: make(chan time.Duration, num),
		PayOrderChan:                     make(chan time.Duration, num),
		CheckInstanceStatusChan:          make(chan time.Duration, num),
		GetOrderChan:                     make(chan time.Duration, num),
		ConfirmBillChan:                  make(chan time.Duration, num),
		Stop:                             make(chan struct{}, 0),
	}

}

type DoAsUserResult struct {
	GetMachineListTimes               int
	GetImageListTimes                 int
	CreateOrderForCreateInstanceTimes int
	PayOrderTimes                     int
	CheckInstanceStatusTimes          int
	GetOrderTimes                     int
	ConfirmBillTimes                  int

	GetMachineListDuration               time.Duration
	GetImageListDuration                 time.Duration
	CreateOrderForCreateInstanceDuration time.Duration
	PayOrderDuration                     time.Duration
	CheckInstanceStatusDuration          time.Duration
	GetOrderDuration                     time.Duration
	ConfirmBillDuration                  time.Duration
}

func (d *DoAsUserChan) Result() {
	result := &DoAsUserResult{}
	for {
		select {
		case t := <-d.GetMachineListChan:
			result.GetMachineListTimes++
			result.GetMachineListDuration += t
		case t := <-d.GetImageListChan:
			result.GetImageListTimes++
			result.GetImageListDuration += t
		case t := <-d.CreateOrderForCreateInstanceChan:
			result.CreateOrderForCreateInstanceTimes++
			result.CreateOrderForCreateInstanceDuration += t
		case t := <-d.PayOrderChan:
			result.PayOrderTimes++
			result.PayOrderDuration += t
		case t := <-d.CheckInstanceStatusChan:
			result.CheckInstanceStatusTimes++
			result.CheckInstanceStatusDuration += t
		case t := <-d.GetOrderChan:
			result.GetOrderTimes++
			result.GetOrderDuration += t
		case t := <-d.ConfirmBillChan:
			result.ConfirmBillTimes++
			result.ConfirmBillDuration += t
		case <-d.Stop:
			fmt.Printf("获取机器列表    Url: %s, 请求 %d 次, 共耗时 %s.\n", Api+"/machine/list", result.GetMachineListTimes, result.GetMachineListDuration.String())
			fmt.Printf("获取镜像列表    Url: %s, 请求 %d 次, 共耗时 %s.\n", Api+"/image/get", result.GetImageListTimes, result.GetImageListDuration.String())
			fmt.Printf("创建订单实例    Url: %s, 请求 %d 次, 共耗时 %s.\n", Api+"/order/instance/create", result.CreateOrderForCreateInstanceTimes, result.CreateOrderForCreateInstanceDuration.String())
			fmt.Printf("付款            Url: %s, 请求 %d 次, 共耗时 %s.\n", Api+"/order/pay", result.PayOrderTimes, result.PayOrderDuration.String())
			fmt.Printf("检查实例状态    Url: %s, 请求 %d 次, 共耗时 %s.\n", Api+"////", result.CheckInstanceStatusTimes, result.CheckInstanceStatusDuration.String())
			fmt.Printf("查看订单状态    Url: %s, 请求 %d 次, 共耗时 %s.\n", Api+"/order?order_uuid=", result.GetOrderTimes, result.GetOrderDuration.String())
			fmt.Printf("查看账单状态    Url: %s, 请求 %d 次, 共耗时 %s.\n", Api+"/bill/confirm?bill_uuid=", result.ConfirmBillTimes, result.ConfirmBillDuration.String())
			return
		}
	}
}

// ------------------------ func ----------------------------
func IndentString(x interface{}) string {
	s, _ := json.MarshalIndent(x, "", " ")
	return string(s)
}

func request(v Verb, url, token string, payload interface{}) (res *grequests.Response, err error) {
	return grequests.Req(string(v), url, &grequests.RequestOptions{
		Headers: map[string]string{
			"Authorization": token,
		},
		JSON:           payload,
		RequestTimeout: time.Second * 5,
	})
}

func ErrLog(msg ...interface{}) {
	_, filename, line, _ := runtime.Caller(1)
	fmt.Println(filename, line, ":", msg)
}
