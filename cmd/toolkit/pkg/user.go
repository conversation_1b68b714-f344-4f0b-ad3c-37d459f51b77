package pkg

import (
	"fmt"
	"github.com/levigross/grequests"
	"server/pkg/db_helper"
	"server/pkg/libs"
	userModel "server/pkg/user/model"
	"strconv"
	"sync"
	"time"
)

type User struct {
	Phone    string `json:"phone"`
	Password string `json:"password"`
	Token    string `json:"token"`
}

type LoginCaptchaRes struct {
	Code string `json:"code"`
	Data struct {
		ID    string `json:"id"`
		Value string `json:"value"`
	} `json:"data"`
	Msg string `json:"msg"`
}

type LoginLoginReq struct {
	Phone        string `json:"phone"`
	Password     string `json:"password"`
	CaptchaID    string `json:"captcha_id"`
	CaptchaValue string `json:"captcha_value"`
}

type LoginLoginRes struct {
	Code string `json:"code"`
	Data struct {
		Ticket string `json:"ticket"`
	} `json:"data"`
	Msg string `json:"msg"`
}

type LoginTokenReq struct {
	Ticket string `json:"ticket"`
}

type LoginTokenRes struct {
	Code string `json:"code"`
	Data struct {
		Token string `json:"token"`
	} `json:"data"`
	Msg string `json:"msg"`
}

func Login(user *User) (err error) {
	// step 1
	captcha := &grequests.Response{}
	capRes := &LoginCaptchaRes{}
	captcha, err = grequests.Get(Api+"/debug/captcha/get", &grequests.RequestOptions{
		RequestTimeout: time.Second * 10,
	})
	if err != nil {
		ErrLog("login: step 1, get captcha failed.")
		return err
	}

	err = captcha.JSON(&capRes)
	if err != nil {
		ErrLog("login: step 1, parse json failed")
	}

	if capRes.Code != "Success" {
		ErrLog("login: step 1, url:", Api+"/debug/captcha/get")
		fmt.Println(IndentString(capRes))
		err = fmt.Errorf("capRes.Code != \"Success\"\n")
		return
	}
	// step 2
	loginReq := &LoginLoginReq{
		Phone:        user.Phone,
		Password:     user.Password,
		CaptchaID:    capRes.Data.ID,
		CaptchaValue: capRes.Data.Value,
	}
	loginRes := &LoginLoginRes{}
	login := &grequests.Response{}
	login, err = grequests.Post(Api+"/login", &grequests.RequestOptions{
		JSON:           loginReq,
		RequestTimeout: time.Second * 10,
	})
	if err != nil {
		ErrLog("login: step 2, login post failed")
		return err
	}

	err = login.JSON(loginRes)
	if err != nil {
		ErrLog("login: step 2, parse json failed")
		return err
	}

	if loginRes.Code != "Success" {
		ErrLog("login: step 2, url:", Api+"/login")
		fmt.Println(IndentString(loginRes))
		err = fmt.Errorf("loginRes.Code != \"Success\"")
		return
	}

	// step 3
	tokenReq := &LoginTokenReq{Ticket: loginRes.Data.Ticket}
	tokenRes := &LoginTokenRes{}
	token := &grequests.Response{}
	token, err = grequests.Post(Api+"/passport", &grequests.RequestOptions{
		JSON:           tokenReq,
		RequestTimeout: time.Second * 10,
	})
	if err != nil {
		ErrLog("login: step 3, ticker to token, failed")
		return err
	}

	err = token.JSON(tokenRes)
	if err != nil {
		ErrLog("login: step 3, parse json failed")
		return err
	}

	if tokenRes.Code != "Success" {
		ErrLog("login: step 3, url:", Api+"/passport")
		fmt.Println(IndentString(tokenRes))
		err = fmt.Errorf("tokenRes.Code != \"Success\"")
		return
	}

	user.Token = tokenRes.Data.Token
	return nil
}

/*
c 多用户充值并发测试
n 单用户并发测试
u 用户phone
a 充值金额
*/
func Recharge(c, n int, a int64, u string) {
	// step1, login
	user := &User{
		Phone:    "16666666666",
		Password: libs.Sha1("Admin123"),
	}
	err := Login(user)
	if err != nil {
		ErrLog("recharge step1: login failed.")
		return
	}

	// step2, 指定充值用户
	list := make([]*userModel.UserListReply, 0)
	list, err = AdminGetUserList(user.Token, u)
	if err != nil {
		ErrLog("AdminGetUserList failed")
		fmt.Println(IndentString(err))
		return
	}
	userNum := len(list)
	if userNum == 0 {
		ErrLog("AdminGetUserList: empty user list.")
		err = fmt.Errorf("AdminGetUserList: userNum == 0")
		return
	}
	var sourceBalance int64
	for _, v := range list {
		sourceBalance += v.Assets
	}

	// step3, 分不同情况进行测试
	if n != 0 {
		// 单用户并发充值
		uid := 0
		if u != "" {
			for _, v := range list {
				if v.Phone == u {
					uid = v.ID
					break
				}
			}
		} else {
			uid = list[0].ID
		}

		fmt.Printf("test: 测试模式: 单用户并发充值. 充值次数: %d, 充值uid: %d.\n", n, uid)
		gc := NewGChan(n)
		begin := time.Now()
		for i := 0; i < n; i++ {
			go func(gc *GChan) {
				err = UserRecharge(user.Token, uid, a)
				if err != nil {
					ErrLog("recharge failed")
					gc.EC <- struct{}{}
					return
				}
				gc.C <- struct{}{}
				return
			}(gc)
		}
		gc.Result(begin)
	}

	if c != 0 {
		fmt.Printf("test: 测试模式: 多用户并发充值. 充值次数: %d, 用户数 %d.\n", c, userNum)
		gc := NewGChan(c)
		begin := time.Now()
		for i := 0; i < c; i++ {
			go func(gc *GChan, uid int) {
				err = UserRecharge(user.Token, uid, a)
				if err != nil {
					fmt.Println("recharge failed")
					gc.EC <- struct{}{}
					return
				}
				gc.C <- struct{}{}
				return
			}(gc, list[i%userNum].ID)
		}
		go gc.Result(begin)

		// 统计异步到帐时间
		t := time.NewTicker(time.Second)
		var balance int64
		targetBalance := sourceBalance + int64(c)*a
		as := make([]string, 0)
		for {
			select {
			case <-t.C:
				balance, err = GetBalanceTotal()
				if err != nil {
					ErrLog(err)
					return
				}
				if balance >= targetBalance {
					as = append(as, fmt.Sprintf("异步充值, 进度 100, 耗时 %s.\n", time.Since(begin).String()))
					fmt.Println("异步充值结束:")
					fmt.Println(as)
					return
				} else if balance >= targetBalance/2 {
					as = append(as, fmt.Sprintf("异步充值, 进度 50, 耗时 %s.\n", time.Since(begin).String()))
				} else if balance >= targetBalance/5 {
					as = append(as, fmt.Sprintf("异步充值, 进度 20, 耗时 %s.\n", time.Since(begin).String()))
				}
			}
		}
	}

}

type GetUserListResData struct {
	Code string `json:"code"`
	Data *struct {
		List        []*userModel.UserListReply `json:"list"`
		PageIndex   int                        `json:"page_index"`
		PageSize    int                        `json:"page_size"`
		Offset      int                        `json:"offset"`
		MaxPage     int                        `json:"max_page"`
		ResultTotal int                        `json:"result_total"`
		CurrentPage int                        `json:"page"`
	} `json:"data"`
	Msg string `json:"msg"`
}

func AdminGetUserList(token, phone string) (list []*userModel.UserListReply, err error) {
	var userListReq struct {
		Username string `form:"username" json:"username"`
		Phone    string `form:"phone" json:"phone"`
		db_helper.GetPagedRangeRequest
	}
	userListResData := &GetUserListResData{}
	userListReq.Phone = phone
	userListRes := &grequests.Response{}

	userListRes, err = request(GET, AdminApi+"/user/list", token, &userListReq)
	if err != nil {
		ErrLog("AdminGetUserList: request failed")
		return
	}

	err = userListRes.JSON(userListResData)
	if err != nil {
		ErrLog("AdminGetUserList: parse json failed")
		return
	}

	if userListResData.Code != "Success" {
		ErrLog("AdminGetUserList url: ", AdminApi+"/user/list")
		fmt.Println(IndentString(userListResData))
	}
	list = userListResData.Data.List
	return
}

// UserRecharge 给单个用户充值
func UserRecharge(token string, uid int, asset int64) (err error) {
	var rj struct {
		ID    int   `json:"id"`
		Asset int64 `json:"asset"`
	}
	var rr struct {
		Code string `json:"code"`
		Data string `json:"data"`
		Msg  string `json:"msg"`
	}

	rj.ID = uid
	rj.Asset = asset
	res := &grequests.Response{}
	res, err = request(POST, AdminApi+"/wallet/recharge", token, &rj)
	if err != nil {
		ErrLog("recharge: request failed")
		return err
	}

	err = res.JSON(&rr)
	if err != nil {
		ErrLog("recharge: parse json failed")
		return err
	}

	if rr.Code != "Success" {
		ErrLog("recharge url: ", AdminApi+"/wallet/recharge")
		fmt.Println(IndentString(rr))
		err = fmt.Errorf("rr.Code != \"Success\"")
	}
	return
}

// AdminCreateUserN 管理员创建多个用户
func AdminCreateUserN(n int, token string) (userList []*userModel.User, err error) {

	var wg sync.WaitGroup
	for i := 0; i < n; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			user, err := createUser(token)
			if err != nil {
				ErrLog("Create a user failed.")
				return
			}
			userList = append(userList, user)
		}()
	}
	wg.Wait()
	return userList, nil
}

// createUser 创建单个用户,随机生成用户名密码加电话号
func createUser(token string) (user *userModel.User, err error) {
	var createReq userModel.ParamsCreateUser
	var createRes struct {
		Code string `json:"code"`
		Data struct {
			ID int `json:"id"`
		} `json:"data"`
		Msg string `json:"msg"`
	}
	createReq.Phone, createReq.Password, createReq.Username = RandomUserInfo()
	createReq.Email, createReq.IsAdmin = "", false
	createReq.Password = libs.Sha1(createReq.Password)
	res := &grequests.Response{}
	res, err = request(POST, AdminApi+"/user/create", token, &createReq)
	if err != nil {
		fmt.Println("Admin create user failed")
		ErrLog(err)
		return
	}

	err = res.JSON(&createRes)
	if err != nil {
		fmt.Println("Parse json failed")
		ErrLog(err)
		return
	}
	if createRes.Code != "Success" {
		fmt.Println(IndentString(createRes))
		ErrLog(createRes.Code)
		return
	}
	user, err = GetUserDetail(token, createRes.Data.ID)
	if err != nil {
		ErrLog("Get user detail failed.")
		return
	}
	return user, nil
}

// GetUserDetail 获取用户详情
func GetUserDetail(token string, id int) (user *userModel.User, err error) {
	var DetailRes struct {
		Code string          `json:"code"`
		Data *userModel.User `json:"data"`
		Msg  string          `json:"msg"`
	}
	res := &grequests.Response{}

	res, err = request(GET, AdminApi+"/user/detail?id="+strconv.Itoa(id), token, nil)
	if err != nil {
		ErrLog("Admin create user failed")
		return
	}
	err = res.JSON(&DetailRes)
	if err != nil {
		ErrLog("Parse json failed")
		return
	}

	if DetailRes.Code != "Success" {
		fmt.Println(IndentString(DetailRes))
		fmt.Println(DetailRes.Code)
		return
	}
	user = DetailRes.Data
	return
}

func GetBalanceTotal() (balance int64, err error) {
	var rr struct {
		Code string `json:"code"`
		Data int64  `json:"data"`
		Msg  string `json:"msg"`
	}
	res := &grequests.Response{}
	res, err = request(GET, Api+"/test/wallet/total", "", nil)
	if err != nil {
		ErrLog("GetBalanceTotal: request failed")
		return 0, err
	}

	err = res.JSON(&rr)
	if err != nil {
		ErrLog("GetBalanceTotal: parse json failed")
		return 0, err
	}

	if rr.Code != "Success" {
		ErrLog("rr.Code != \"Success\"")
		err = fmt.Errorf("rr.Code != \"Success\"")
	}
	balance = rr.Data
	return
}

func GetBalance(uid int) (balance int) {
	return
}

func RandomUserInfo() (phone, password, username string) {
	suffix := strconv.Itoa(int(time.Now().UnixNano()))
	return "13" + suffix[10:], "Aa" + suffix[13:], suffix
}
