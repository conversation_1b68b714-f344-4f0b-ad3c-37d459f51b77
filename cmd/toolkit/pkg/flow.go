package pkg

import (
	"fmt"
	"math/rand"
	bc "server/pkg/billing_center/controller"
	bcm "server/pkg/billing_center/model"
	"server/pkg/constant"
	imm "server/pkg/image/model"
	mm "server/pkg/machine/model"
	"time"
)

func DoAsAUser(u User, dc *DoAsUserChan) (err error) {
	msg := make([]string, 0)
	defer func() {
		fmt.Println(IndentString(msg))
	}()

	msg = append(msg, "操作用户: "+u.Phone)
	order := &bcm.Order{}

	var doTry = func(m *[]string) (order *bcm.Order, err error) {
		// 1. 获取机器列表,找一个有闲置gpu的机器
		mList := make([]*mm.MachineInfo, 0)
		mid := ""
		t1 := time.Now()
		mList, err = GetMachineList(u.Token)
		dc.GetMachineListChan <- time.Since(t1)
		if err != nil {
			ErrLog(err)
			return
		}
		canChooseMachine := make([]string, 0)
		for _, m := range mList {
			if m.GpuNumber-m.GpuUsed != 0 {
				canChooseMachine = append(canChooseMachine, m.MachineID)
			}
		}
		if len(canChooseMachine) == 0 {
			ErrLog("no gpu is free")
			err = fmt.Errorf("no gpu is free")
			return
		}

		// 随机获取可用机器列表中的一个
		rand.Seed(time.Now().Unix())
		number := rand.Intn(len(canChooseMachine))
		mid = canChooseMachine[number]
		msg = append(msg, fmt.Sprint("1.获取到机器ID:", mid))

		// 2. 获取image, 找到可用的image
		imList := make([]*imm.Image, 0)
		image := ""
		t2 := time.Now()
		imList, err = GetImageList(u.Token)
		dc.GetImageListChan <- time.Since(t2)
		if err != nil {
			ErrLog(err)
			return
		}
		if len(imList) == 0 {
			ErrLog("no image find")
			err = fmt.Errorf("no image find")
			return
		}
		image = imList[0].ImageName
		msg = append(msg, fmt.Sprint("2.获取到image:", image))

		// 3. 下单实例
		req := &bc.CreateOrderForCreateInstanceRequest{
			InstanceInfo: constant.CreateContainerTaskRequest{
				Image:        image,
				MachineID:    mid,
				ChargeType:   constant.ChargeTypePayg,
				ReqGPUAmount: 1,
			},
			PriceInfo: bcm.PriceInfo{
				MachineID:  mid,
				ChargeType: constant.ChargeTypePayg,
				Duration:   1,
				Num:        1,
			},
		}

		t3 := time.Now()
		order, err = CreateOrderForCreateInstance(u.Token, req)
		dc.CreateOrderForCreateInstanceChan <- time.Since(t3)
		if err != nil {
			ErrLog(err)
			return
		}
		msg = append(msg, fmt.Sprint("3.下单, 创建实例. 订单id:", order.UUID, " 实例ID: ", order.ProductUUID))
		return
	}

	for i := 0; i < 6; i++ {
		if i == 5 {
			ErrLog("已经尝试下单 5 次, 失败. 退出")
			return
		}
		order, err = doTry(&msg)
		if err == nil {
			break
		}

		err = nil
		rand.Seed(time.Now().Unix())
		time.Sleep(time.Millisecond * time.Duration(rand.Intn(100)))
	}

	// 4. 支付订单
	billUUID := ""
	t4 := time.Now()
	billUUID, err = PayOrder(u.Token, order.UUID)
	dc.PayOrderChan <- time.Since(t4)
	if err != nil {
		ErrLog(err)
		return err
	}
	msg = append(msg, fmt.Sprint("4.支付订单成功. 账单id:", billUUID))
	time.Sleep(time.Second * 2)

	// 5. 检查实例状态(错了也没关系)

	// 6. 查看订单状态
	t6 := time.Now()
	order, err = GetOrder(u.Token, order.UUID)
	dc.GetOrderChan <- time.Since(t6)
	if err != nil {
		ErrLog(err)
		return err
	}
	// 判断订单状态,选择是否跳转
	if order.Status == constant.OrderStatusSuccess {
		msg = append(msg, fmt.Sprint("6.支付状态确认: 已创建实例"))
	} else if order.Status == constant.OrderStatusUnpaid {
		msg = append(msg, fmt.Sprint("6.支付状态确认: 未付款"))
	}

	// 7. 查看账单状态
	ok := false
	t7 := time.Now()
	ok, err = ConfirmBill(u.Token, billUUID)
	dc.ConfirmBillChan <- time.Since(t7)
	if err != nil {
		return err
	}
	if ok {
		msg = append(msg, fmt.Sprint("7. 账单已确认."))
	} else {
		msg = append(msg, fmt.Sprint("7. 账单未确认."))
	}

	return nil

}
