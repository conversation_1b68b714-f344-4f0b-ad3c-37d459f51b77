package cmd

import (
	"fmt"
	"github.com/pkg/errors"
	"github.com/spf13/cobra"
	"gorm.io/gorm"
	"gorm.io/sharding"
	"server/conf"
	"server/pkg/billing_center/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/logger"
	"server/plugin/mysql_plugin"
	"server/plugin/redis_plugin"
	"strconv"
	"time"
)

var (
	beginTime    = "2024-01-30 19:20"
	endTime      = "2024-01-31 10:50"
	beginOrderID = 5690900
	endOrderID   = 5709230
)

var fixBillOrderWalletCmd = &cobra.Command{
	Use:   "fix-bill-order-wallet",
	Short: "Check daily bill",
	Run: func(cmd *cobra.Command, args []string) {
		l := logger.NewLogger("fix-bill-order-wallet")
		l.Info("toolkit start running ... ")
		_, _ = beginTime, endTime

		l.Info("sqlUpdate: %v", sqlUpdate)
		l.Info("limit: %v", sqlLimit)
		l.Info("online: %v", online)

		globalConf := conf.GetGlobalGsConfig()

		// mysql
		dbConn, err := mysql_plugin.MySQLProvider(&mysql_plugin.MySQLOptions{
			URL: fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
				globalConf.MySQL.User,
				globalConf.MySQL.Password,
				globalConf.MySQL.Host,
				globalConf.MySQL.DBName,
			),
			Debug: globalConf.App.DebugLog,
		})
		if err != nil {
			l.Error("connect to db err: %v", err)
			return
		}
		db_helper.InitDatabaseConnection(dbConn, dbConn, l)
		dbConn.Use(sharding.Register(sharding.Config{
			ShardingKey:    "uid",
			NumberOfShards: 64,
			// 使用自增ID
			PrimaryKeyGenerator: sharding.PKCustom,
			PrimaryKeyGeneratorFn: func(tableIdx int64) int64 {
				return 0
			},
		}, "bill"))

		// redis
		var mu *redis_plugin.MutexRedis
		if online {
			redisClient, err := redis_plugin.RedisClientProvider(&redis_plugin.RedisClientOption{
				Addr:     globalConf.Redis.Host,
				Password: globalConf.Redis.Password,
			})
			if err != nil {
				err = errors.Wrap(err, "init redis failed")
				return
			}
			mu = redis_plugin.NewMutexRedisProvider(redisClient)
		}

		l.Info("init finished, begin fix ... ")
		// begin
		orderUidSumList := make([]orderUidSum, 0)
		if online {
			err = dbConn.Raw("select uid,sum(deal_price) as sum from  orders where  id > 5690950 and id < 5709230 and ((order_type in ('create_instance','clone_instance')  and charge_type = 'payg') or order_type in ('data_disk_expand','data_disk_reduce')) and deal_price != 0 group by uid").Find(&orderUidSumList).Error
		} else {
			err = dbConn.Raw("select uid,sum(deal_price) as sum from  orders where   ((order_type in ('create_instance','clone_instance')  and charge_type = 'payg') or order_type in ('data_disk_expand','data_disk_reduce')) and deal_price != 0 group by uid").Find(&orderUidSumList).Error
		}

		if err != nil {
			l.ErrorE(err, "get orderUidSumList failed")
			return
		}
		i := 0

		l.Info("total num: %d.", len(orderUidSumList))
		for _, v := range orderUidSumList {
			if v.UID == 310955 {
				continue
			}

			if specialUser != 0 {
				if v.UID != specialUser {
					continue
				}
				l.Info("specialUser, %d", specialUser)
			}

			i = i + 1
			if i > sqlLimit {
				return
			}

			l.Info("begin running func.....")
			func(uid int, orderSum int64) {
				if online {
					lock := mu.NewRedisMutex(redis_plugin.MutexUpdateUserWalletType, "update_"+strconv.Itoa(v.UID))
					var locked bool
					locked, err = lock.LockWithNoRetry(time.Minute * 10)
					if err != nil || !locked {
						err = businesserror.ErrServerBusy
						return
					}
					defer lock.UnLock()
				}

				var err error
				var billSum int64 // 使用bill计算出来的差错的金额
				defer func() {
					//	_ = lock.UnLock()
					//if err == nil {
					//	l.Info("[%d] is orderSum:[%d], billSum:[%d], voucherSum[%d] ok!", uid, orderSum, billSum, voucherSum)
					//}
				}()

				// 找到uid对应的这段时间的bill列表
				billList := make([]model.Bill, 0)
				billListSql := fmt.Sprintf("select * from bill_%02d where uid = %d and created_at > '2024-01-28 19:20' order by id", uid%64, uid)
				err = dbConn.Raw(billListSql).Find(&billList).Error
				if err != nil {
					l.ErrorE(err, "get [%d] bill list failed", uid)
					return
				}

				// 修正billList顺序
				for i := 0; i < len(billList)-1; i++ {
					j := i + 1
					if billList[i].Type != constant.BillTypeCharge || billList[i].SubType != constant.BillSubTypeChangeChargeType || billList[j].SubType != constant.BillSubTypeChangeChargeType {
						continue
					}

					if billList[i].Balance < billList[j].Balance && billList[i].ChargeType != billList[j].ChargeType {
						billList[i], billList[j] = billList[j], billList[i]
					}
				}

				// 先校验一遍，差的金额对不对（假设第一条的金额是对的）
				// 如果只有一条记录 ？
				var correctAsset int64
				var lastBalance int64
				if len(billList) == 0 {
					// 如果这个期间没有账单，也就是没有错误账单，那么只需要补齐wallet
					// 创建按量付费实例，但是创建失败了。就会出现这种情况
					l.Info("user [%d] order sum is [%d], but have no bill list", uid, orderSum)
					billSum = orderSum
					return
				} else {
					lastCorrectBill := model.Bill{}
					lastCorrectBillSql := fmt.Sprintf("select * from bill_%02d where uid=%d and id<%d order by id desc limit 1", uid%64, uid, billList[0].ID)
					err = dbConn.Raw(lastCorrectBillSql).Find(&lastCorrectBill).Error
					if err != nil {
						if errors.Is(err, gorm.ErrRecordNotFound) {
							correctAsset = billList[0].Balance
							lastBalance = billList[0].Balance
							billList = billList[1:]
						}
						l.ErrorE(err, "[%d] get lastCorrectBill failed.", uid)
						return
					} else {
						if uid == 26417 {
							correctAsset = 23880
							lastBalance = 23880
						} else if uid == 184924 {
							correctAsset = 2747980
							lastBalance = 2747980
						} else if uid == 312014 {
							correctAsset = 32260
							lastBalance = 32260
						} else {
							correctAsset = lastCorrectBill.Balance
							lastBalance = lastCorrectBill.Balance
						}

					}
				}

				billBalanceMap := map[int]int64{}
				beginAsset := correctAsset
				for _, bill := range billList {
					// 做金额计算
					var thisTimeBalance int64
					switch bill.Type {
					case constant.BillTypeRecharge:
						correctAsset += bill.PayByBalance
						thisTimeBalance = lastBalance + bill.PayByBalance
					case constant.BillTypeRefund:
						correctAsset += bill.PayByBalance
						thisTimeBalance = lastBalance + bill.PayByBalance
					case constant.BillTypeCharge:
						correctAsset -= bill.PayByBalance
						thisTimeBalance = lastBalance - bill.PayByBalance
					case constant.BillTypeWithdraw:
						correctAsset -= bill.PayByBalance
						thisTimeBalance = lastBalance - bill.PayByBalance
					}

					if correctAsset != bill.Balance {
						billBalanceMap[bill.ID] = correctAsset
					}

					if thisTimeBalance != bill.Balance {
						if thisTimeBalance < bill.Balance {
							l.Warn("user:[%d], bill:[%d] thisTimeBalance:[%d] < bill.Balance:[%d], lastBalance:[%d]", uid, bill.ID, thisTimeBalance, bill.Balance, lastBalance)
							err = errors.New("hahah")
							return
						}
						billSum += thisTimeBalance - bill.Balance
					}

					lastBalance = bill.Balance
				}

				if (billSum == 0 || len(billBalanceMap) == 0) && online {
					return
				}

				if billSum > orderSum {
					// 考虑代金券付款，bill中只能比order中小
					l.Info("user:[%d], billSum:[%d] orderSum:[%d],beginAsset:[%d], finally asset: [%d]", uid, billSum, orderSum, beginAsset, correctAsset)
					return
				}

				wallet := model.UserWallet{}
				err = dbConn.Raw("select * from user_wallet where uid = ?", uid).Find(&wallet).Error
				if err != nil {
					l.ErrorE(err, "get user wallet [%d] failed", uid)
				}

				if correctAsset < wallet.Assets {
					l.Error("correctAsset < wallet.Assets, correctAsset:[%d], wallet.Assets:[%d]", correctAsset, wallet.Assets)
					return
				}

				if sqlUpdate {
					err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) error {
						// 金额对了之后，修改每一条有错误的bill
						for id, balance := range billBalanceMap {
							sql := fmt.Sprintf("update bill_%02d set balance = %d where id = %d", uid%64, balance, id)
							err = tx.Exec(sql).Error
							if err != nil {
								return err
							}
						}

						// 修改所有order
						orderSql := fmt.Sprintf("update orders set deal_price = 0 ,price_info = JSON_SET(price_info,'$.deal_price', 0), pay_by_balance=0 where uid = %d and id > 5690950 and id < 5709230 and ((order_type in ('create_instance','clone_instance')  and charge_type = 'payg') or order_type in ('data_disk_expand','data_disk_reduce')) and deal_price != 0", uid)
						if !online {
							orderSql = fmt.Sprintf("update orders set deal_price = 0 ,price_info = JSON_SET(price_info,'$.deal_price', 0), pay_by_balance=0 where uid = %d and  ((order_type in ('create_instance','clone_instance')  and charge_type = 'payg') or order_type in ('data_disk_expand','data_disk_reduce')) and deal_price != 0", uid)
						}
						err = tx.Exec(orderSql).Error
						if err != nil {
							return err
						}

						// 修改wallet
						walletSql := fmt.Sprintf("update user_wallet set assets = %d ,accumulate = %d, assets_cc = assets_cc+1 where  uid = %d and assets_cc = %d", correctAsset, wallet.Accumulate-(correctAsset-wallet.Assets), uid, wallet.AssetsCC)
						q := tx.Exec(walletSql)
						//if q.RowsAffected == 0 {
						//	return errors.New("update wallet is 0")
						//}
						err = q.Error
						if err != nil {
							return err
						}
						return nil
					})
					if err != nil {
						l.ErrorE(err, "user[%d] tx update failed", uid)
					}
				} else {
					l.Info("user:[%d] correct bill map: %+v", uid, billBalanceMap)
				}

			}(v.UID, v.Sum)
		}
	},
}

var sqlUpdate bool
var sqlLimit int
var online bool
var specialUser int

type orderUidSum struct {
	UID int
	Sum int64
}

func init() {
	rootCmd.AddCommand(fixBillOrderWalletCmd)
	fixBillOrderWalletCmd.PersistentFlags().IntVarP(&sqlLimit, "limit", "", 1, "limit")
	fixBillOrderWalletCmd.PersistentFlags().IntVarP(&specialUser, "user", "", 0, "specialUser")
	fixBillOrderWalletCmd.PersistentFlags().BoolVarP(&sqlUpdate, "update", "", false, "update")
	fixBillOrderWalletCmd.PersistentFlags().BoolVarP(&online, "online", "", false, "online")
}
