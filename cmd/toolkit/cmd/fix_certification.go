package cmd

import (
	"fmt"
	"github.com/spf13/cobra"
	"gorm.io/gorm"
	"server/conf"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/logger"
	userModel "server/pkg/user/model"
	"server/plugin/mysql_plugin"
	"time"
)

// loginCmd represents the login command
var fixCertificationCmd = &cobra.Command{
	Use:   "fix-certification",
	Short: "fix certification",
	Run: func(cmd *cobra.Command, args []string) {
		l := logger.NewLogger("toolkit fix-certification")

		globalConf := conf.GetGlobalGsConfig()
		dbConn, err := mysql_plugin.MySQLProvider(&mysql_plugin.MySQLOptions{
			URL: fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
				globalConf.MySQL.User,
				globalConf.MySQL.Password,
				globalConf.MySQL.Host,
				globalConf.MySQL.DBName,
			),
			Timer: nil,
		})
		if err != nil {
			l.<PERSON>rror("connect to db err: %v", err)
			return
		}
		db_helper.InitDatabaseConnection(dbConn, nil, l)

		addRealNameRecords := 0
		addStudentRecords := 0
		addEnterpriseRecords := 0
		updateRealNameRecords := 0
		updateStudentRecords := 0
		updateEnterpriseRecords := 0

		umList := []userModel.UserMember{}
		db_helper.GlobalDBConn().FindInBatches(&umList, 200, func(tx *gorm.DB, batch int) error {
			for _, um := range umList {
				uc := &userModel.UserCertification{}

				if um.RealName != constant.MemberIdentifyStatusNone {
					var ucRealNamePassCount int64
					ucRealNamePassCount, err = uc.UCCount(&db_helper.QueryFilters{
						EqualFilters: map[string]interface{}{
							"uid":       um.UID,
							"status":    constant.UserCertificationPass,
							"auth_type": constant.UserCertificationPersonal,
						},
						CompareFilters: []db_helper.Compare{{Key: "expired_at", Sign: db_helper.BiggerThan, CompareValue: time.Now()}},
						NullField:      []string{"deleted_at"},
					})
					if err != nil {
						l.ErrorE(err, "uc count real name pass certification record failed")
					}

					if ucRealNamePassCount == 0 {
						u := &userModel.User{}
						err = u.UserGet(&db_helper.QueryFilters{
							EqualFilters: map[string]interface{}{"id": um.UID},
							NullField:    []string{"deleted_at"},
						})
						if err != nil {
							l.WithField("uid", um.UID).ErrorE(err, "get user info by uid failed")
						}
						addRealNameUc := &userModel.UserCertification{
							CreatedAt: time.Now(),
							ExpiredAt: um.RealNameDeadline,
							UID:       um.UID,
							UUID:      u.UUID,
							Username:  u.Username,
							Phone:     u.Phone,
							AuthType:  constant.UserCertificationPersonal,
							Status:    constant.UserCertificationPass,
						}
						if um.RealName == constant.MemberIdentifyStatusAdminSet {
							addRealNameUc.Status = constant.UserCertificationAdminPoint
						} else if um.RealName == constant.MemberIdentifyStatusAdminBan {
							addRealNameUc.Status = constant.UserCertificationAdminAbort
						}
						err = addRealNameUc.UCCreate(tx)
						if err != nil {
							l.WithField("addRealNameUc", addRealNameUc).ErrorE(err, "uc create real name certification failed")
						} else {
							addRealNameRecords++
							l.WithField("addRealNameUc", addRealNameUc).Info("add No.%d real name ", addRealNameRecords)
						}
					} else if ucRealNamePassCount != 0 {
						updateRealNameUc := map[string]interface{}{}
						updateRealNameUc["updated_at"] = time.Now()
						if um.RealName == constant.MemberIdentifyStatusAdminBan {
							updateRealNameUc["status"] = constant.UserCertificationAdminAbort
						} else if um.RealName == constant.MemberIdentifyStatusAdminSet {
							updateRealNameUc["status"] = constant.UserCertificationAdminPoint
						}
						err = uc.UCUpdate(tx, &db_helper.QueryFilters{
							EqualFilters: map[string]interface{}{
								"uid":       um.UID,
								"status":    constant.UserCertificationPass,
								"auth_type": constant.UserCertificationPersonal,
							},
							NullField: []string{"deleted_at"},
						}, updateRealNameUc)
						if err != nil {
							l.WithField("updateRealNameUc", updateRealNameUc).ErrorE(err, "uc update real name certification failed")
						} else {
							updateRealNameRecords++
							l.WithField("updateRealNameUc", updateRealNameUc).Info("update No.%d real name", updateRealNameRecords)
						}
					}
				} else {
					var passCount int64
					passCount, err = uc.UCCount(&db_helper.QueryFilters{
						EqualFilters: map[string]interface{}{
							"uid":       um.UID,
							"status":    constant.UserCertificationPass,
							"auth_type": constant.UserCertificationPersonal,
						},
						NullField: []string{"deleted_at"},
					})
					if passCount != 0 {
						err = uc.UCUpdate(tx, &db_helper.QueryFilters{
							EqualFilters: map[string]interface{}{
								"uid":       um.UID,
								"status":    constant.UserCertificationPass,
								"auth_type": constant.UserCertificationPersonal,
							},
							NullField: []string{"deleted_at"},
						}, map[string]interface{}{"status": constant.UserCertificationExpired})
						if err != nil {
							l.ErrorE(err, "uc update real name certification failed")
						} else {
							updateRealNameRecords++
							l.Info("update No.%d real name", updateRealNameRecords)
						}
					}
				}

				if um.Student != constant.MemberIdentifyStatusNone {
					var ucStudentPassCount int64
					ucStudentPassCount, err = uc.UCCount(&db_helper.QueryFilters{
						EqualFilters: map[string]interface{}{
							"uid":       um.UID,
							"status":    constant.UserCertificationPass,
							"auth_type": constant.UserCertificationPersonalStudent,
						},
						CompareFilters: []db_helper.Compare{{Key: "expired_at", Sign: db_helper.BiggerThan, CompareValue: time.Now()}},
						NullField:      []string{"deleted_at"},
					})
					if err != nil {
						l.ErrorE(err, "uc count student pass certification record failed")
					}

					if ucStudentPassCount == 0 {
						u := &userModel.User{}
						err = u.UserGet(&db_helper.QueryFilters{
							EqualFilters: map[string]interface{}{"id": um.UID},
							NullField:    []string{"deleted_at"},
						})
						if err != nil {
							l.WithField("uid", um.UID).ErrorE(err, "get user info by uid failed")
						}
						addStudentUc := &userModel.UserCertification{
							CreatedAt:  time.Now(),
							ExpiredAt:  um.StudentDeadline,
							UID:        um.UID,
							UUID:       u.UUID,
							Username:   u.Username,
							Phone:      u.Phone,
							AuthType:   constant.UserCertificationPersonalStudent,
							AuthEntity: userModel.UserCertificationAuthEntity{},
							Status:     constant.UserCertificationPass,
						}
						if um.Student == constant.MemberIdentifyStatusAdminSet {
							addStudentUc.Status = constant.UserCertificationAdminPoint
						} else if um.Student == constant.MemberIdentifyStatusAdminBan {
							addStudentUc.Status = constant.UserCertificationAdminAbort
						}
						err = addStudentUc.UCCreate(tx)
						if err != nil {
							l.WithField("addStudentUc", addStudentUc).ErrorE(err, "uc create student certification failed")
						} else {
							addStudentRecords++
							l.WithField("addStudentUc", addStudentUc).Info("add No.%d student ", addStudentRecords)
						}
					} else if ucStudentPassCount != 0 {
						updateStudentUc := map[string]interface{}{}
						updateStudentUc["updated_at"] = time.Now()
						if um.Student == constant.MemberIdentifyStatusAdminBan {
							updateStudentUc["status"] = constant.UserCertificationAdminAbort
						} else if um.RealName == constant.MemberIdentifyStatusAdminSet {
							updateStudentUc["status"] = constant.UserCertificationAdminPoint
						}
						err = uc.UCUpdate(tx, &db_helper.QueryFilters{
							EqualFilters: map[string]interface{}{
								"uid":       um.UID,
								"status":    constant.UserCertificationPass,
								"auth_type": constant.UserCertificationPersonalStudent,
							},
							NullField: []string{"deleted_at"},
						}, updateStudentUc)
						if err != nil {
							l.WithField("updateStudentUc", updateStudentUc).ErrorE(err, "uc update student certification failed")
						} else {
							updateStudentRecords++
							l.WithField("updateStudentUc", updateStudentUc).Info("update No.%d student", updateStudentRecords)
						}
					}
				} else {
					var passCount int64
					passCount, err = uc.UCCount(&db_helper.QueryFilters{
						EqualFilters: map[string]interface{}{
							"uid":       um.UID,
							"status":    constant.UserCertificationPass,
							"auth_type": constant.UserCertificationPersonalStudent,
						},
						NullField: []string{"deleted_at"},
					})
					if passCount != 0 {
						err = uc.UCUpdate(tx, &db_helper.QueryFilters{
							EqualFilters: map[string]interface{}{
								"uid":       um.UID,
								"status":    constant.UserCertificationPass,
								"auth_type": constant.UserCertificationPersonalStudent,
							},
							NullField: []string{"deleted_at"},
						}, map[string]interface{}{"status": constant.UserCertificationExpired})
						if err != nil {
							l.ErrorE(err, "uc update student certification failed")
						} else {
							updateStudentRecords++
							l.Info("update No.%d student", updateStudentRecords)
						}
					}
				}

				if um.Enterprise != constant.MemberIdentifyStatusNone {
					var ucEnterprisePassCount int64
					ucEnterprisePassCount, err = uc.UCCount(&db_helper.QueryFilters{
						EqualFilters: map[string]interface{}{
							"uid":    um.UID,
							"status": constant.UserCertificationPass,
						},
						CompareFilters: []db_helper.Compare{{Key: "expired_at", Sign: db_helper.BiggerThan, CompareValue: time.Now()}},
						InFilters: []db_helper.In{
							{
								Key:   "auth_type",
								InSet: []constant.UserCertificationType{constant.UserCertificationEnterpriseBankAccount, constant.UserCertificationEnterpriseCertificate},
							},
						},
						NullField: []string{"deleted_at"},
					})
					if err != nil {
						l.ErrorE(err, "uc count enterprise pass certification record failed")
					}

					if ucEnterprisePassCount == 0 {
						u := &userModel.User{}
						err = u.UserGet(&db_helper.QueryFilters{
							EqualFilters: map[string]interface{}{"id": um.UID},
							NullField:    []string{"deleted_at"},
						})
						if err != nil {
							l.WithField("uid", um.UID).ErrorE(err, "get user info by uid failed")
						}

						addEnterpriseUc := &userModel.UserCertification{
							CreatedAt: time.Now(),
							ExpiredAt: um.EnterpriseDeadline,
							UID:       um.UID,
							UUID:      u.UUID,
							Username:  u.Username,
							Phone:     u.Username,
							AuthType:  constant.UserCertificationEnterpriseCertificate,
							Status:    constant.UserCertificationPass,
						}
						if um.Enterprise == constant.MemberIdentifyStatusAdminSet {
							addEnterpriseUc.Status = constant.UserCertificationAdminPoint
						} else if um.Enterprise == constant.MemberIdentifyStatusAdminBan {
							addEnterpriseUc.Status = constant.UserCertificationAdminAbort
						}
						err = addEnterpriseUc.UCCreate(tx)
						if err != nil {
							l.WithField("addEnterpriseUc", addEnterpriseUc).ErrorE(err, "uc create enterprise certification failed")
						} else {
							addEnterpriseRecords++
							l.WithField("addEnterpriseUc", addEnterpriseUc).Info("add No.%d enterprise ", addEnterpriseRecords)
						}
					} else if ucEnterprisePassCount != 0 {
						updateEnterpriseUc := map[string]interface{}{}
						updateEnterpriseUc["updated_at"] = time.Now()
						if um.Enterprise == constant.MemberIdentifyStatusAdminBan {
							updateEnterpriseUc["status"] = constant.UserCertificationAdminAbort
						} else if um.RealName == constant.MemberIdentifyStatusAdminSet {
							updateEnterpriseUc["status"] = constant.UserCertificationAdminPoint
						}
						err = uc.UCUpdate(tx, &db_helper.QueryFilters{
							EqualFilters: map[string]interface{}{
								"uid":    um.UID,
								"status": constant.UserCertificationPass,
							},
							InFilters: []db_helper.In{
								{
									Key:   "auth_type",
									InSet: []constant.UserCertificationType{constant.UserCertificationEnterpriseBankAccount, constant.UserCertificationEnterpriseCertificate},
								},
							},
							NullField: []string{"deleted_at"},
						}, updateEnterpriseUc)
						if err != nil {
							l.WithField("updateEnterpriseUc", updateEnterpriseUc).ErrorE(err, "uc update enterprise certification failed")
						} else {
							updateEnterpriseRecords++
							l.WithField("updateEnterpriseUc", updateEnterpriseUc).Info("update No.%d enterprise", updateEnterpriseRecords)
						}
					}
				} else {
					var passCount int64
					passCount, err = uc.UCCount(&db_helper.QueryFilters{
						EqualFilters: map[string]interface{}{
							"uid":    um.UID,
							"status": constant.UserCertificationPass,
						},
						InFilters: []db_helper.In{
							{
								Key:   "auth_type",
								InSet: []constant.UserCertificationType{constant.UserCertificationEnterpriseBankAccount, constant.UserCertificationEnterpriseCertificate},
							},
						},
						NullField: []string{"deleted_at"},
					})
					if passCount != 0 {
						err = uc.UCUpdate(tx, &db_helper.QueryFilters{
							EqualFilters: map[string]interface{}{
								"uid":    um.UID,
								"status": constant.UserCertificationPass,
							},
							InFilters: []db_helper.In{
								{
									Key:   "auth_type",
									InSet: []constant.UserCertificationType{constant.UserCertificationEnterpriseBankAccount, constant.UserCertificationEnterpriseCertificate},
								},
							},
							NullField: []string{"deleted_at"},
						}, map[string]interface{}{"status": constant.UserCertificationExpired})
						if err != nil {
							l.ErrorE(err, "uc update enterprise certification failed")
						} else {
							updateEnterpriseRecords++
							l.Info("update No.%d enterprise", updateEnterpriseRecords)
						}
					}
				}
			}

			return nil
		})

		l.Info("add %d realName records\n add %d student records\n add %d enterprise records\n update %d realName records\n update %d student records\n update %d enterprise records", addRealNameRecords, addStudentRecords, addEnterpriseRecords, updateRealNameRecords, updateStudentRecords, updateEnterpriseRecords)
	},
}

func init() {
	rootCmd.AddCommand(fixCertificationCmd)
}
