# 如何更新huawei agent以及相应组件
因为华为的npu, 无法挂载到多个容器内, 所以agent将不以docker的形式运行, 而是通过systemd管理
为了使ga程序能兼容host和container运行的模式
需要将ga, frpc, autopanel, supervisord 都拷贝host的/bin目录下

## 各组件下载地址(arm64版本)
- dockerd-27.2.0-umd19-arm64: https://github.com/work712/moby/releases/download/dockerd-27.2.0-umd19/dockerd-27.2.0-umd19-arm64
- ga_arm64: https://autodl-online.oss-cn-beijing.aliyuncs.com/release/ga_arm64
- autopanel_arm64: https://autodl-online.oss-cn-beijing.aliyuncs.com/release/autopanel_arm64
- frpc_arm64: https://autodl-online.oss-cn-beijing.aliyuncs.com/release/frpc_arm64
- supervisord_arm64: https://autodl-online.oss-cn-beijing.aliyuncs.com/release/supervisord_arm64
  - 原始文件: https://github.com/ochinchina/supervisord/releases/download/v0.7.3/supervisord_0.7.3_Linux_ARM64.tar.gz
