GOPROXY=export GO111MODULE=on && export GOPRIVATE=gitlab-gpuhub.autodl.com && export GOPROXY=https://goproxy.cn
GOCMD=$(GOPROXY) && go
GORUN=$(GOCMD) run
DeployYAMLPath=deploy
KUBENETES=kubectl
REGISTRY=localhost:5000/gpuhub
DOCKER=docker
now:=$(shell date +%Y%m%d%H%M%S)
formatNow=$(shell date +%Y-%m-%d/%H:%M:%S)
ldflags="-X 'server/pkg/libs.BuiltTime=${formatNow}' -X 'server/pkg/libs.GitCommit=`git describe --all --long`' -X 'server/pkg/libs.GoVersion=`go version`'"

# 动态获取的项目根目录的绝对路径
ROOT_PATH:=$(abspath $(dir $(abspath $(firstword $(MAKEFILE_LIST))))../../)

.PHONY: build

build:
	$(GOCMD) build -race -o gs -ldflags ${ldflags} .

build_in_docker:
	-@rm ./gs
	$(DOCKER) run --rm -w /code/cmd/server -v /tmp/go_mod:/root/go/pkg/mod -v ${ROOT_PATH}:/code registry.cn-beijing.aliyuncs.com/work77/golang:1.24.0-ubuntu20.04 sh -c "make build"

# api_server agent_server worker* core_api_server core_worker
redeploy_all: build_in_docker docker_build docker_push delete_all rewrite_yaml create_all recover_yaml
# api_server
redeploy_api: build_in_docker docker_build docker_push apply_api_server
# agent_server
redeploy_agent_server: build_in_docker docker_build docker_push apply_agent_server
# api_server worker*
redeploy_api_worker: build_in_docker docker_build docker_push delete_deployment rewrite_yaml apply_api_server apply_worker recover_yaml
# api_server worker* agent_server
update: build_in_docker docker_build docker_push rewrite_yaml apply_deployment recover_yaml

# worker
redeploy_worker5: build_in_docker docker_build docker_push rewrite_yaml apply_worker5 recover_yaml
redeploy_worker6: build_in_docker docker_build docker_push rewrite_yaml apply_worker6 recover_yaml
redeploy_worker1_5: build_in_docker docker_build docker_push rewrite_yaml apply_worker1 apply_worker2 apply_worker3 apply_worker4 apply_worker5 recover_yaml

# core_api_server core_worker
redeploy_core_api_worker: build_in_docker docker_build docker_push delete_core_api_server delete_core_worker delete_core_worker2 rewrite_yaml create_core_api_server create_core_worker create_core_worker2 recover_yaml
update_core_api_worker: build_in_docker docker_build docker_push delete_core_worker delete_core_worker2 rewrite_yaml apply_core_api_server create_core_worker create_core_worker2 recover_yaml

# api_server agent_server worker* core_api_server core_worker
delete_all: delete_api_server delete_agent_server delete_worker delete_core_api_server delete_core_worker delete_core_worker2
create_all: create_api_server create_agent_server create_worker create_core_api_server create_core_worker create_core_worker2
deploy_all: build_in_docker docker_build docker_push rewrite_yaml create_all recover_yaml

# worker*
delete_deployment: delete_worker
# api_server worker* agent_server
create_deployment: create_api_server create_worker create_agent_server
apply_deployment: apply_agent_server apply_api_server apply_worker

#delete_all: delete_api_server delete_agent_server delete_worker

# work_order_api
redeploy_work_order_api: build_in_docker docker_build docker_push delete_work_order_api rewrite_yaml create_work_order_api recover_yaml
reapply_work_order_api: build_in_docker docker_build docker_push rewrite_yaml apply_work_order_api recover_yaml

# user_center
user_center_redeploy_all: build_in_docker docker_build docker_push delete_user_center rewrite_yaml create_user_center recover_yaml
user_center_update: build_in_docker docker_build docker_push rewrite_yaml apply_user_center recover_yaml

############################################

create_agent_server:
	@$(KUBENETES) create -f $(DeployYAMLPath)/agent-server-deployment.yaml || $(call panic_and_recover_yaml)
	@$(KUBENETES) create -f $(DeployYAMLPath)/agent-server-svc.yaml || $(call panic_and_recover_yaml)
apply_agent_server:
	@echo patch:$(REGISTRY)/poye-gs-v1:$(now)
	$(KUBENETES) -n kpl patch deployment poye-agent-server --patch '{"spec": {"template": {"spec": {"containers": [{"name": "poye-agent-server","image":"$(REGISTRY)/poye-gs-v1:$(now)", "resources": {"limits": {"cpu": "3000m", "memory": "3Gi"}}}]}}}}'
apply_api_server:
	@echo patch:$(REGISTRY)/poye-gs-v1:$(now)
	$(KUBENETES) -n kpl patch deployment poye-api-server --patch '{"spec": {"template": {"spec": {"containers": [{"name": "poye-api-server","image":"$(REGISTRY)/poye-gs-v1:$(now)"}]}}}}'
delete_agent_server:
	-@$(KUBENETES) delete -f $(DeployYAMLPath)/agent-server-deployment.yaml
	-@$(KUBENETES) delete -f $(DeployYAMLPath)/agent-server-svc.yaml

create_api_server:
	@$(KUBENETES) create -f $(DeployYAMLPath)/api-server-deployment.yaml || $(call panic_and_recover_yaml)
	@$(KUBENETES) create -f $(DeployYAMLPath)/api-server-svc.yaml || $(call panic_and_recover_yaml)
delete_api_server:
	-@$(KUBENETES) delete -f $(DeployYAMLPath)/api-server-deployment.yaml
	-@$(KUBENETES) delete -f $(DeployYAMLPath)/api-server-svc.yaml

create_worker:
	@$(KUBENETES) create -f $(DeployYAMLPath)/worker-deployment.yaml || $(call panic_and_recover_yaml)
	@$(KUBENETES) create -f $(DeployYAMLPath)/worker2-deployment.yaml || $(call panic_and_recover_yaml)
	@$(KUBENETES) create -f $(DeployYAMLPath)/worker3-deployment.yaml || $(call panic_and_recover_yaml)
	@$(KUBENETES) create -f $(DeployYAMLPath)/worker4-deployment.yaml || $(call panic_and_recover_yaml)
	@$(KUBENETES) create -f $(DeployYAMLPath)/worker5-deployment.yaml || $(call panic_and_recover_yaml)
	@$(KUBENETES) create -f $(DeployYAMLPath)/worker6-deployment.yaml || $(call panic_and_recover_yaml)
apply_worker: apply_worker1 apply_worker2 apply_worker3 apply_worker4 apply_worker5 apply_worker6
delete_worker:
	-@$(KUBENETES) delete -f $(DeployYAMLPath)/worker-deployment.yaml
	-@$(KUBENETES) delete -f $(DeployYAMLPath)/worker2-deployment.yaml
	-@$(KUBENETES) delete -f $(DeployYAMLPath)/worker3-deployment.yaml
	-@$(KUBENETES) delete -f $(DeployYAMLPath)/worker4-deployment.yaml
	-@$(KUBENETES) delete -f $(DeployYAMLPath)/worker5-deployment.yaml
	-@$(KUBENETES) delete -f $(DeployYAMLPath)/worker6-deployment.yaml

apply_worker1:
	@$(KUBENETES) apply -f $(DeployYAMLPath)/worker-deployment.yaml --record || $(call panic_and_recover_yaml)
apply_worker2:
	@$(KUBENETES) apply -f $(DeployYAMLPath)/worker2-deployment.yaml --record || $(call panic_and_recover_yaml)
apply_worker3:
	@$(KUBENETES) apply -f $(DeployYAMLPath)/worker3-deployment.yaml --record || $(call panic_and_recover_yaml)
apply_worker4:
	@$(KUBENETES) apply -f $(DeployYAMLPath)/worker4-deployment.yaml --record || $(call panic_and_recover_yaml)
apply_worker5:
	@$(KUBENETES) apply -f $(DeployYAMLPath)/worker5-deployment.yaml --record || $(call panic_and_recover_yaml)
apply_worker6:
	@$(KUBENETES) apply -f $(DeployYAMLPath)/worker6-deployment.yaml --record || $(call panic_and_recover_yaml)

create_core_api_server:
	@$(KUBENETES) create -f $(DeployYAMLPath)/core-api-server-deployment.yaml || $(call panic_and_recover_yaml)
	@$(KUBENETES) create -f $(DeployYAMLPath)/core-api-server-svc.yaml || $(call panic_and_recover_yaml)
apply_core_api_server:
	@echo patch:$(REGISTRY)/poye-gs-v1:$(now)
	$(KUBENETES) -n kpl patch deployment poye-core-api-server --patch '{"spec": {"template": {"spec": {"containers": [{"name": "poye-core-api-server","image":"$(REGISTRY)/poye-gs-v1:$(now)"}]}}}}'
delete_core_api_server:
	-@$(KUBENETES) delete -f $(DeployYAMLPath)/core-api-server-deployment.yaml
	-@$(KUBENETES) delete -f $(DeployYAMLPath)/core-api-server-svc.yaml

create_core_worker:
	@$(KUBENETES) create -f $(DeployYAMLPath)/core-worker-deployment.yaml || $(call panic_and_recover_yaml)
apply_core_worker:
	@$(KUBENETES) apply -f $(DeployYAMLPath)/core-worker-deployment.yaml --record || $(call panic_and_recover_yaml)
delete_core_worker:
	-@$(KUBENETES) delete -f $(DeployYAMLPath)/core-worker-deployment.yaml

create_core_worker2:
	@$(KUBENETES) create -f $(DeployYAMLPath)/core-worker2-deployment.yaml || $(call panic_and_recover_yaml)
apply_core_worker2:
	@$(KUBENETES) apply -f $(DeployYAMLPath)/core-worker2-deployment.yaml --record || $(call panic_and_recover_yaml)
delete_core_worker2:
	-@$(KUBENETES) delete -f $(DeployYAMLPath)/core-worker2-deployment.yaml

docker_build:
	@echo "build tag is: "$(now)
	$(DOCKER) build -f Dockerfile -t $(REGISTRY)/poye-gs-v1:$(now) .

docker_push:
	$(DOCKER) push $(REGISTRY)/poye-gs-v1:$(now)

rewrite_yaml:
	@sed -i "s#DOCKER_REGISTRY#$(REGISTRY)#g" `find $(DeployYAMLPath) -type f -name "*.yaml"`
	@sed -i "s#latest#$(now)#g" `find $(DeployYAMLPath) -type f -name "*.yaml"`

recover_yaml:
	@sed -i "s#$(now)#latest#g" `find $(DeployYAMLPath) -type f -name "*.yaml"`
	@sed -i "s#$(REGISTRY)#DOCKER_REGISTRY#g" `find $(DeployYAMLPath) -type f -name "*.yaml"`

delete_user_center:
	-@$(KUBENETES) delete -f $(DeployYAMLPath)/user-center-deployment.yaml
	-@$(KUBENETES) delete -f $(DeployYAMLPath)/user-center-svc.yaml

create_user_center:
	@$(KUBENETES) create -f $(DeployYAMLPath)/user-center-deployment.yaml || $(call panic_and_recover_yaml)
	@$(KUBENETES) create -f $(DeployYAMLPath)/user-center-svc.yaml || $(call panic_and_recover_yaml)

apply_user_center:
	@echo patch:$(REGISTRY)/poye-gs-v1:$(now)
	$(KUBENETES) -n kpl patch deployment poye-user-center --patch '{"spec": {"template": {"spec": {"containers": [{"name": "poye-user-center","image":"$(REGISTRY)/poye-gs-v1:$(now)"}]}}}}'

create_work_order_api:
	@$(KUBENETES) create -f $(DeployYAMLPath)/work-order-api-deployment.yaml || $(call panic_and_recover_yaml)
	@$(KUBENETES) create -f $(DeployYAMLPath)/work-order-api-svc.yaml || $(call panic_and_recover_yaml)
apply_work_order_api:
	@echo patch:$(REGISTRY)/poye-gs-v1:$(now)
	$(KUBENETES) -n kpl patch deployment poye-work-order-api --patch '{"spec": {"template": {"spec": {"containers": [{"name": "poye-work-order-api","image":"$(REGISTRY)/poye-gs-v1:$(now)"}]}}}}'
delete_work_order_api:
	-@$(KUBENETES) delete -f $(DeployYAMLPath)/work-order-api-deployment.yaml
	-@$(KUBENETES) delete -f $(DeployYAMLPath)/work-order-api-svc.yaml

clean:
	/bin/rm kpl--*

define panic_and_recover_yaml
	(sed -i "s#$(now)#latest#g" `find $(DeployYAMLPath) -type f -name "*.yaml"` && sed -i "s#$(REGISTRY)#DOCKER_REGISTRY#g" `find $(DeployYAMLPath) -type f -name "*.yaml"` && exit 1)
endef

ifdef HARBORHOST
$(info HARBORHOST defined $(HARBORHOST))
REGISTRY = $(HARBORHOST)/seetaas
endif
