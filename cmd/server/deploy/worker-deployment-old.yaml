apiVersion: extensions/v1beta1
kind: Deployment
metadata:
  name: poye-worker
  namespace: kpl
spec:
  replicas: 1
  template:
    metadata:
      labels:
        app: poye-worker
    spec:
      imagePullSecrets:
        - name: "harbor-secret"
      serviceAccountName: kpl-serviceaccount
      nodeSelector:
        internal_service_node: "true"
        bigmem: "true"
      containers:
        - name: poye-worker
          image: DOCKER_REGISTRY/poye-gs-v1:latest
          imagePullPolicy: Always
          env:
            - name: "GODEBUG"
              value: "madvdontneed=1"
          volumeMounts:
            - mountPath: /mnt/kpl-pvc
              name: kpl-volume
            - name: config
              mountPath: /etc/gs/gs.yaml
              subPath: gs.yaml
            - name: payment-cert
              mountPath: /etc/payment-cert
          command: ["gs"]
          args: ["worker"]
          ports:
            - containerPort: 8000
              name: port-8000
            - containerPort: 8555
              name: port-8555
          livenessProbe:
            initialDelaySeconds: 10
            timeoutSeconds: 4
            failureThreshold: 5
            exec:
              command: ["gs", "health"]
          resources:
            requests:
              cpu: "4000m"
              memory: "5Gi"
            limits:
              cpu: "6000m"
              memory: "8Gi"
      volumes:
        - name: kpl-volume
          persistentVolumeClaim:
            claimName: kpl-pvc
            readOnly: false
        - name: config
          configMap:
            name: cfg-poye-gs
        - name: payment-cert
          secret:
            secretName: payment-cert
