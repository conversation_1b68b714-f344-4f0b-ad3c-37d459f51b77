apiVersion: extensions/v1beta1
kind: Deployment
metadata:
  name: poye-user-center
  namespace: kpl
spec:
  replicas: 2
  template:
    metadata:
      labels:
        app: poye-user-center
    spec:
      imagePullSecrets:
      - name: "harbor-secret"
      serviceAccountName: kpl-serviceaccount
      nodeSelector:
        internal_service_node: "true"
      containers:
      - name: poye-user-center
        image: DOCKER_REGISTRY/poye-gs-v1:latest
        imagePullPolicy: Always
        env:
        - name: "GOGC"
          value: "150"
        volumeMounts:
        - name: config
          mountPath: /etc/gs/gs.yaml
          subPath: gs.yaml
        - name: payment-cert
          mountPath: /etc/payment-cert
        command: ["gs"]
        args: ["user-center"]
        ports:
          - containerPort: 8000
            name: port-8000
          - containerPort: 8555
            name: port-8555
        livenessProbe:
          initialDelaySeconds: 10
          timeoutSeconds: 4
          failureThreshold: 5
          tcpSocket:
            port: port-8000
        resources:
          requests:
            cpu: "400m"
            memory: "512Mi"
          limits:
            cpu: "1000m"
            memory: "1Gi"
      volumes:
      - name: config
        configMap:
          name: cfg-poye-gs
      - name: payment-cert
        secret:
          secretName: payment-cert
