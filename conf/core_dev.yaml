app:
  debug_log: true
  # debug_api 用作进行debug的接口组, 生产环境严禁开启, 会有发生越权操作的可能
  debug_api: true
  # docs_api 开启swagger api 接口列表
  docs_api: true
  # 共享存储在服务容器内的rootfs path
  storage_root_path: /mnt

mysql:
  host: "0.0.0.0:9910"
  user: "gas"
  password: "gas"
  db_name: "gas"

redis:
  host: "0.0.0.0:9920"
  password: ""

rabbit_mq:
  host: "0.0.0.0:9930"
  user: "gas"
  password: "gas"

influxdb:
  host: "http://*************:8086"
  token: "ftebdKMQ_pH1-UpRUlJvVTgXc409eN9sW0M3gc7WFehavdpR-tzjrpaMTDlwNQ2NvgUkiOO5rClTBTxJhtASRQ=="
